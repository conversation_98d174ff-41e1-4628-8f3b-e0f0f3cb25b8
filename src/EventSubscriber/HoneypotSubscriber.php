<?php

namespace App\EventSubscriber;

use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\KernelEvents;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;

/**
 * Event subscriber to check for honeypot fields in form submissions
 * 
 * This subscriber checks if the honeypot field is filled in the request,
 * which indicates a bot submission. If a bot is detected, it logs the
 * attempt and throws an AccessDeniedException.
 */
class HoneypotSubscriber implements EventSubscriberInterface
{
    /**
     * Name of the honeypot field
     */
    private const HONEYPOT_FIELD = 'website';

    public function __construct(
        private LoggerInterface $securityLogger
    ) {
    }

    /**
     * Check for honeypot fields in form submissions
     */
    public function onKernelRequest(RequestEvent $event): void
    {
        $request = $event->getRequest();
        
        // Only check POST requests to the login route
        if (!$event->isMainRequest() || 
            !$request->isMethod('POST') || 
            !str_contains($request->getPathInfo(), '/manager/dtnib/admin/login')) {
            return;
        }

        // Check if the honeypot field is filled
        $honeypotValue = $request->request->get(self::HONEYPOT_FIELD);
        if ($honeypotValue) {
            // Log the bot attempt
            $this->securityLogger->warning('Bot detected via honeypot field', [
                'ip' => $request->getClientIp(),
                'user_agent' => $request->headers->get('User-Agent'),
                'honeypot_value' => $honeypotValue,
            ]);
            
            // Return a generic error page to avoid revealing that a honeypot was triggered
            $response = new Response(
                '<html><body><h1>Error</h1><p>An error occurred. Please try again later.</p></body></html>',
                Response::HTTP_FORBIDDEN
            );
            
            $event->setResponse($response);
        }
    }

    /**
     * {@inheritdoc}
     */
    public static function getSubscribedEvents(): array
    {
        return [
            KernelEvents::REQUEST => ['onKernelRequest', 10], // High priority to run early
        ];
    }
}