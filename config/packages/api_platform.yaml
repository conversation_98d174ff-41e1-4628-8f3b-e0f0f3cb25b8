api_platform:
    title: Hello SUPRA
    version: 1.0.0


    docs_formats:
        jsonld: [ 'application/ld+json' ]
        jsonopenapi: [ 'application/vnd.openapi+json' ]
        multipart: [ 'multipart/form-data' ]
    #        html: ['text/html']

    swagger:
        api_keys:
            JWT:
                name: Authorization
                type: header
    enable_re_doc: true
    enable_swagger_ui: false # remet moi a true si tu veux l'interface Swagger UI et te passer de Postman :)
    enable_entrypoint: false


    defaults:
        pagination_enabled: true
        pagination_items_per_page: 10   # <= METS UN NOMBRE ICI !
        pagination_client_items_per_page: true  # <= pour autoriser le client à changer la taille
        stateless: true




        normalization_context:
            skip_null_values: false
        cache_headers:
            vary: ['Content-Type', 'Authorization', 'Origin']

