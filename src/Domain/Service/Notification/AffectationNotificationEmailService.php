<?php

namespace App\Domain\Service\Notification;

use App\Entity\Structure\EntiteJuridique;
use Psr\Log\LoggerInterface;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Email;
use Twig\Environment;

/**
 * Service pour l'envoi d'emails de notification liés aux affectations.
 * 
 * Ce service utilise les emails configurés dans les paramètres de l'EntiteJuridique :
 * - adminEmail : pour les notifications générales
 * - damResponssableDataAffectionEmail : pour les notifications spécifiques aux affectations
 */
class AffectationNotificationEmailService
{
    public function __construct(
        private readonly MailerInterface $mailer,
        private readonly Environment $twig,
        private readonly LoggerInterface $logger
    ) {}

    /**
     * Envoie une notification de succès d'importation automatique d'affectations
     */
    public function sendAffectationAutoImportSuccessNotification(
        EntiteJuridique $entiteJuridique,
        array $importResult
    ): void {
        $recipients = [];

        // Ajouter l'admin seulement si les notifications sont activées
        if ($entiteJuridique->isNotifierErreurAuAdminPourAffectation()) {
            $recipients[] = $entiteJuridique->getAdminEmail();
        }

        // Ajouter le responsable DAM seulement si les notifications sont activées
        if ($entiteJuridique->isNotifierErreurAuResponssableDamAffectation()) {
            $recipients[] = $entiteJuridique->getDamResponssableDataAffectionEmail();
        }

        // Si aucune notification n'est activée, ne pas envoyer d'email
        if (empty($recipients)) {
            $this->logger->info(
                'Notifications désactivées pour l\'entité juridique, aucun email de succès envoyé',
                ['entiteJuridique' => $entiteJuridique->getCode()]
            );
            return;
        }

        $subject = sprintf(
            '[%s] Importation automatique d\'affectations réussie',
            $entiteJuridique->getCode()
        );

        $context = [
            'entiteJuridique' => $entiteJuridique,
            'result' => $importResult,
            'timestamp' => new \DateTime()
        ];

        $this->sendEmailToMultipleRecipients(
            $recipients,
            $subject,
            'emails/affectation/auto_import_success.html.twig',
            $context
        );
    }

    /**
     * Envoie une notification d'erreur d'importation automatique d'affectations
     */
    public function sendAffectationAutoImportErrorNotification(
        EntiteJuridique $entiteJuridique,
        array $errors,
        ?string $filePath = null
    ): void {
        $recipients = [];

        // Ajouter l'admin seulement si les notifications sont activées
        if ($entiteJuridique->isNotifierErreurAuAdminPourAffectation()) {
            $recipients[] = $entiteJuridique->getAdminEmail();
        }

        // Ajouter le responsable DAM seulement si les notifications sont activées
        if ($entiteJuridique->isNotifierErreurAuResponssableDamAffectation()) {
            $recipients[] = $entiteJuridique->getDamResponssableDataAffectionEmail();
        }

        // Si aucune notification n'est activée, ne pas envoyer d'email
        if (empty($recipients)) {
            $this->logger->info(
                'Notifications d\'erreur désactivées pour l\'entité juridique, aucun email envoyé',
                ['entiteJuridique' => $entiteJuridique->getCode(), 'errors' => count($errors)]
            );
            return;
        }

        $subject = sprintf(
            '[%s] ERREUR - Importation automatique d\'affectations',
            $entiteJuridique->getCode()
        );

        $context = [
            'entiteJuridique' => $entiteJuridique,
            'errors' => $errors,
            'filePath' => $filePath,
            'timestamp' => new \DateTime()
        ];

        $this->sendEmailToMultipleRecipients(
            $recipients,
            $subject,
            'emails/affectation/auto_import_error.html.twig',
            $context
        );
    }

    /**
     * Envoie une notification pour un fichier CSV invalide
     */
    public function sendInvalidCsvFileNotification(
        EntiteJuridique $entiteJuridique,
        string $filePath,
        string $errorMessage
    ): void {
        // Pour les fichiers CSV invalides, on notifie seulement le responsable DAM si activé
        $recipients = [];

        if ($entiteJuridique->isNotifierErreurAuResponssableDamAffectation()) {
            $recipients[] = $entiteJuridique->getDamResponssableDataAffectionEmail();
        }

        // Si les notifications au responsable DAM ne sont pas activées, ne pas envoyer d'email
        if (empty($recipients)) {
            $this->logger->info(
                'Notifications au responsable DAM désactivées, aucun email CSV invalide envoyé',
                ['entiteJuridique' => $entiteJuridique->getCode(), 'filePath' => $filePath]
            );
            return;
        }

        $subject = sprintf(
            '[%s] Fichier CSV d\'affectation invalide',
            $entiteJuridique->getCode()
        );

        $context = [
            'entiteJuridique' => $entiteJuridique,
            'filePath' => $filePath,
            'errorMessage' => $errorMessage,
            'timestamp' => new \DateTime(),
            'csvExample' => $this->getCsvExampleStructure()
        ];

        $this->sendEmailToMultipleRecipients(
            $recipients,
            $subject,
            'emails/affectation/invalid_csv_file.html.twig',
            $context
        );
    }

    /**
     * Envoie un rapport hebdomadaire des importations automatiques
     */
    public function sendWeeklyAffectationReport(
        EntiteJuridique $entiteJuridique,
        array $weeklyStats
    ): void {
        // Pour les rapports hebdomadaires, on notifie seulement l'admin si activé
        $recipients = [];

        if ($entiteJuridique->isNotifierErreurAuAdminPourAffectation()) {
            $recipients[] = $entiteJuridique->getAdminEmail();
        }

        // Si les notifications admin ne sont pas activées, ne pas envoyer d'email
        if (empty($recipients)) {
            $this->logger->info(
                'Notifications admin désactivées, aucun rapport hebdomadaire envoyé',
                ['entiteJuridique' => $entiteJuridique->getCode()]
            );
            return;
        }

        $subject = sprintf(
            '[%s] Rapport hebdomadaire - Importations d\'affectations',
            $entiteJuridique->getCode()
        );

        $context = [
            'entiteJuridique' => $entiteJuridique,
            'stats' => $weeklyStats,
            'weekStart' => new \DateTime('-7 days'),
            'weekEnd' => new \DateTime(),
            'timestamp' => new \DateTime()
        ];

        $this->sendEmailToMultipleRecipients(
            $recipients,
            $subject,
            'emails/affectation/weekly_report.html.twig',
            $context
        );
    }

    /**
     * Envoie un email à plusieurs destinataires
     */
    private function sendEmailToMultipleRecipients(
        array $recipients,
        string $subject,
        string $template,
        array $context
    ): void {
        // Filtrer les emails vides ou invalides
        $validRecipients = array_filter($recipients, function($email) {
            return !empty($email) && filter_var($email, FILTER_VALIDATE_EMAIL);
        });

        if (empty($validRecipients)) {
            $this->logger->warning(
                'Aucun destinataire valide pour l\'envoi d\'email',
                ['subject' => $subject, 'recipients' => $recipients]
            );
            return;
        }

        try {
            $htmlBody = $this->twig->render($template, $context);
            
            $email = (new Email())
                ->subject($subject)
                ->html($htmlBody);

            // Ajouter tous les destinataires
            foreach ($validRecipients as $recipient) {
                $email->addTo($recipient);
            }

            $this->mailer->send($email);

            $this->logger->info(
                'Email de notification envoyé avec succès',
                [
                    'subject' => $subject,
                    'recipients' => $validRecipients,
                    'template' => $template
                ]
            );

        } catch (\Exception $e) {
            $this->logger->error(
                'Erreur lors de l\'envoi d\'email de notification',
                [
                    'subject' => $subject,
                    'recipients' => $validRecipients,
                    'template' => $template,
                    'error' => $e->getMessage()
                ]
            );
        }
    }

    /**
     * Retourne la structure d'exemple pour les fichiers CSV
     */
    private function getCsvExampleStructure(): array
    {
        return [
            'headers' => 'uConnexion;matricule;date_debut;date_fin;code_uf;type_grade',
            'example1' => 'jdupont;12345;2024-01-01;2024-12-31;UF001;Médecin',
            'example2' => 'mmartin;67890;2024-02-01;;UF002;Infirmier'
        ];
    }
}
