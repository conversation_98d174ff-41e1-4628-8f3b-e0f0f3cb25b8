framework:
    cache:
        app: cache.adapter.redis
        default_redis_provider: '%env(CACHE_DSN)%'
        prefix_seed: 'supra_ght/search'




#framework:
#    cache:
#        # Utilise FilesystemCache pour stocker les données sur le disque
#        app: cache.adapter.filesystem
#        # Chemin où les fichiers de cache seront stockés
#        directory: '%kernel.cache_dir%/search_cache'
#        # Préfixe pour éviter les conflits de clés (optionnel)
#        prefix_seed: 'supra_ght/search'
#
#        # Options supplémentaires (commentées pour référence)
#        # Pour utiliser ArrayCache (en mémoire, développement)
#        # app: cache.adapter.array
#
#        # Pour utiliser Redis (à activer plus tard)
#        # app: cache.adapter.redis
#        # default_redis_provider: redis://localhost
#
#        # Pour APCu (si disponible)
#        # app: cache.adapter.apcu