<?php

namespace App\Command;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\ArrayInput;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\Process\Process;


// php bin/console app:import:year-2024
// php bin/console app:import:year-2024 --start-date=2024-06-01 --end-date=2024-07-01
// php bin/console app:import:year-2024 --log-dir=/chemin/vers/logs
#[AsCommand(
    name: 'app:import:year-2024',
    description: 'Importe toutes les données de l\'année 2024',
)]
class ImportYear2024Command extends Command
{
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $io->title('Import des actes - Année 2024');

        // Vérifier que le serveur Symfony est démarré
        $this->checkServer($io);

//        $startDate = new \DateTime('2024-01-22');
//        $endDate = new \DateTime('2024-01-27');

        $startDate = new \DateTime('2025-09-01');
        $endDate = new \DateTime('2024-01-04');

        $totalDays = $startDate->diff($endDate)->days;
        $io->progressStart($totalDays);

        $currentDate = clone $startDate;
        $dayCount = 0;
        $errors = [];

        while ($currentDate < $endDate) {
            $dateStr = $currentDate->format('Y-m-d');
            $dayCount++;

            $io->progressAdvance();
            $io->section("[$dayCount/$totalDays] Import du $dateStr");

            // Lancer la commande d'import
            $command = $this->getApplication()->find('app:import:actes');
            $arguments = new ArrayInput([
                '--date' => $dateStr,
                '--page-size' => 100,
                '--batch-size' => 50,
                '--memory-warning' => 2000,
                '--memory-critical' => 3000,
            ]);

            try {
                $returnCode = $command->run($arguments, $output);

                if ($returnCode !== Command::SUCCESS) {
                    $errors[] = $dateStr;
                    $io->warning("Erreur lors de l'import du $dateStr");
                }
            } catch (\Exception $e) {
                $errors[] = $dateStr;
                $io->error("Exception lors de l'import du $dateStr: " . $e->getMessage());
            }

            // Nettoyer le cache
            $io->text('Nettoyage du cache...');
            $this->clearCache($io);

//            // Redémarrer le serveur tous les 7 jours
//            if ($dayCount % 7 === 0) {
//                $io->text('Redémarrage du serveur (tous les 7 jours)...');
//                $this->restartServer($io);
//            }

            // Redémarrer le serveur après chaque jour
            $io->text('Redémarrage du serveur...');
            $this->restartServer($io);

            // Passer au jour suivant
            $currentDate->add(new \DateInterval('P1D'));

            // Petite pause
            usleep(500000); // 0.5 seconde
        }

        $io->progressFinish();

        // Rapport final
        $io->success("Import terminé pour l'année 2024");
        $io->table(
            ['Métrique', 'Valeur'],
            [
                ['Total jours importés', $dayCount],
                ['Erreurs', count($errors)],
            ]
        );

        if (!empty($errors)) {
            $io->error('Dates en erreur : ' . implode(', ', $errors));
        }

        // Redémarrage final
        $io->text('Redémarrage final du serveur...');
        $this->restartServer($io);

        return Command::SUCCESS;
    }

    private function checkServer(SymfonyStyle $io): void
    {
        $io->warning('Pas besoin de vérifier si le serveur est démarré, c\'est juste une commande symfony console.');
        $io->success('Pas besoin de vérifier si le serveur est démarré, c\'est juste une commande symfony console.');
        $io->success('Pas besoin de vérifier si le serveur est démarré, c\'est juste une commande symfony console.');
        $io->success('Pas besoin de vérifier si le serveur est démarré, c\'est juste une commande symfony console.');
        $io->success('Pas besoin de vérifier si le serveur est démarré, c\'est juste une commande symfony console.');
        $io->success('Pas besoin de vérifier si le serveur est démarré, c\'est juste une commande symfony console.');
        $io->success('Pas besoin de vérifier si le serveur est démarré, c\'est juste une commande symfony console.');
        $io->warning('Pas besoin de vérifier si le serveur est démarré, c\'est juste une commande symfony console.');
//        $process = new Process(['pgrep', '-f', 'symfony serve']);
//        $process->run();
//
//        if (!$process->isSuccessful()) {
//            $io->warning('Le serveur Symfony n\'est pas démarré. Démarrage...');
//            $this->startServer($io);
//        }
    }

    private function startServer(SymfonyStyle $io): void
    {
        $process = new Process(['symfony', 'serve', '-d', '--no-tls']);
        $process->run();



        if (!$process->isSuccessful()) {
            $io->error('Impossible de démarrer le serveur');
            throw new \RuntimeException('Server start failed');
        }

        sleep(5); // Attendre que le serveur démarre
        $io->success('Serveur démarré');
    }

    private function restartServer(SymfonyStyle $io): void
    {
        // Arrêter le serveur
        $stopProcess = new Process(['symfony', 'server:stop']);
        $stopProcess->run();
        sleep(2);

        // Redémarrer
        $this->startServer($io);
    }

    private function clearCache(SymfonyStyle $io): void
    {
//        $process = new Process(['php', 'bin/console', 'cache:clear']);
//        $process->run();
//
//        if (!$process->isSuccessful()) {
//            $io->warning('Erreur lors du nettoyage du cache dev import year command');
//        }

        $io->warning(' Allez rm -rf le var/cache/dev et var/cache/prod manuellement si nécessaire ');
        $io->success(' Allez rm -rf le var/cache/dev et var/cache/prod manuellement si nécessaire ');
        $io->success(' Allez rm -rf le var/cache/dev et var/cache/prod manuellement si nécessaire ');
        $io->warning(' Allez rm -rf le var/cache/dev et var/cache/prod manuellement si nécessaire ');

        sleep(30); // Attendre que le serveur démarre


    }
}