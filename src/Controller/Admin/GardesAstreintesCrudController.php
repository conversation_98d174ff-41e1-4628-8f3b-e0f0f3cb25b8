<?php

namespace App\Controller\Admin;

use App\Entity\Activite\GardesAstreintes;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\BooleanField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ChoiceField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateTimeField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use EasyCorp\Bundle\EasyAdminBundle\Field\IntegerField;

class GardesAstreintesCrudController extends AbstractCrudController
{
    public static function getEntityFqcn(): string
    {
        return GardesAstreintes::class;
    }

    public function configureActions(Actions $actions): Actions
    {
        return $actions
            ->add(Crud::PAGE_INDEX, Action::DETAIL);
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setEntityLabelInSingular('Garde/Astreinte')
            ->setEntityLabelInPlural('Gardes/Astreintes')
            ->setPageTitle('index', 'Gestion des Gardes et Astreintes')
            ->setPageTitle('detail', 'Détail de la Garde/Astreinte')
            ->setPageTitle('new', 'Créer une nouvelle Garde/Astreinte')
            ->setPageTitle('edit', 'Modifier la Garde/Astreinte')
            ->showEntityActionsInlined();
    }

    public function configureFields(string $pageName): iterable
    {
        if ($pageName === Crud::PAGE_INDEX) {
            return [
                TextField::new('id')->hideOnForm(),
                BooleanField::new('isActif')->setLabel('Actif'),
                DateTimeField::new('dateDebut')->setLabel('Début')->setFormat('dd/MM/yyyy HH:mm'),
                DateTimeField::new('dateFin')->setLabel('Fin')->setFormat('dd/MM/yyyy HH:mm'),
                TextField::new('type_garde')->setLabel('Type'),
                AssociationField::new('agent')->setLabel('Agent'),
            ];
        }
        // Tous les champs pour la vue détail (show), edit et new
        return [
            TextField::new('id')->hideOnForm(),

            // BaseEntity fields
            BooleanField::new('isActif')->setLabel('Est actif'),
            DateTimeField::new('dateCreation')
                ->setLabel('Date de création')
                ->setDisabled(true),

            DateTimeField::new('dateDebut')
                ->setLabel('Date de début')
                ->setFormat('dd/MM/yyyy HH:mm')
                ->setHelp('Date et heure de début de la période'),

            DateTimeField::new('dateFin')
                ->setLabel('Date de fin')
                ->setFormat('dd/MM/yyyy HH:mm')
                ->setHelp('Date et heure de fin de la période'),

            DateTimeField::new('date_garde')
                ->setLabel('Date de la garde')
                ->setFormat('dd/MM/yyyy HH:mm')
                ->setHelp('Date et heure de la garde ou astreinte'),

            TextField::new('type_garde')
                ->setLabel('Type de garde'),

            IntegerField::new('totalGarde')
                ->setLabel('Total gardes')
                ->setHelp('Nombre total de gardes'),

            IntegerField::new('totalAstreinte')
                ->setLabel('Total astreintes')
                ->setHelp('Nombre total d\'astreintes'),

            AssociationField::new('agent')
                ->setLabel('Agent')
                ->setHelp('Agent effectuant la garde ou l\'astreinte')
                ->setRequired(true),

            DateTimeField::new('validFrom')
                ->setLabel('Validité - Début')
                ->hideOnForm(),

            DateTimeField::new('validTo')
                ->setLabel('Validité - Fin')
                ->hideOnForm(),

            ChoiceField::new('periodeType')
                ->setLabel('Période')
                ->setChoices([
                    'Hebdomadaire' => 'HEBDOMADAIRE',
                    'Mensuel' => 'MENSUEL',
                    'Annuel' => 'ANNUEL',
                ])
                ->renderAsBadges([
                    'HEBDOMADAIRE' => 'primary',
                    'MENSUEL' => 'success',
                    'ANNUEL' => 'warning',
                ]),

            TextField::new('source')
                ->setLabel('Source des données')
                ->setHelp('Origine de ces données de garde/astreinte')
                ->hideOnIndex(),
        ];
    }
    /**
     * Custom logic to handle entity creation
     * 
     * Cette méthode génère automatiquement les champs validFrom et validTo
     * à partir des champs dateDebut et dateFin lors de la création d'une garde/astreinte.
     */
    public function persistEntity(\Doctrine\ORM\EntityManagerInterface $entityManager, $entityInstance): void
    {
        if ($entityInstance instanceof \App\Entity\Activite\GardesAstreintes) {
            // Generate validFrom from dateDebut
            if ($entityInstance->getDateDebut() !== null) {
                $entityInstance->setValidFrom($entityInstance->getDateDebut());
            }
            
            // Generate validTo from dateFin
            if ($entityInstance->getDateFin() !== null) {
                $entityInstance->setValidTo($entityInstance->getDateFin());
            }
        }
        
        // Call parent method to persist the entity
        parent::persistEntity($entityManager, $entityInstance);
    }
    
    /**
     * Custom logic to handle entity updates
     * 
     * Cette méthode met à jour les champs validFrom et validTo
     * à partir des champs dateDebut et dateFin lors de la modification d'une garde/astreinte.
     */
    public function updateEntity(\Doctrine\ORM\EntityManagerInterface $entityManager, $entityInstance): void
    {
        if ($entityInstance instanceof \App\Entity\Activite\GardesAstreintes) {
            // Generate validFrom from dateDebut
            if ($entityInstance->getDateDebut() !== null) {
                $entityInstance->setValidFrom($entityInstance->getDateDebut());
            }
            
            // Generate validTo from dateFin
            if ($entityInstance->getDateFin() !== null) {
                $entityInstance->setValidTo($entityInstance->getDateFin());
            }
        }
        
        // Call parent method to update the entity
        parent::updateEntity($entityManager, $entityInstance);
    }
}