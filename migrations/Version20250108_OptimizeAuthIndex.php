<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Optimisation des index pour l'authentification admin
 */
final class Version20250108_OptimizeAuthIndex extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Ajoute des index optimisés pour l\'authentification admin';
    }

    public function up(Schema $schema): void
    {
        // Index sur email (PostgreSQL)
        $this->addSql('CREATE INDEX IF NOT EXISTS IDX_AGENT_EMAIL ON agent (email)');

        // Index GIN pour les requêtes JSON sur les rôles (PostgreSQL - type json)
        // Convertir en jsonb pour l'index ou utiliser un index fonctionnel
        $this->addSql('CREATE INDEX IF NOT EXISTS IDX_AGENT_ROLES_GIN ON agent USING GIN ((roles::jsonb))');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP INDEX IF EXISTS IDX_AGENT_EMAIL');
        $this->addSql('DROP INDEX IF EXISTS IDX_AGENT_ROLES_GIN');
    }
}
