<?php

namespace App\ApiResource\Structure\Partials;

use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Get;
use App\State\Partials\UfOrganisationStateProvider;

#[ApiResource(
    operations: [
        new Get(
            uriTemplate: '/structure/get-organisation-by-ufcode',
            provider: UfOrganisationStateProvider::class
        )
    ]
)]
class UfOrganisationResource
{
    public function __construct(
        public readonly string $code
    ) {}
}