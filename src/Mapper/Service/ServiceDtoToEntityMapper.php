<?php

namespace App\Mapper\Service;

use App\ApiResource\Structure\ServiceDto;
use App\Entity\Structure\Service;
use App\Repository\PoleRepository;
use App\Repository\ServiceRepository;
use Doctrine\DBAL\Exception;
use Symfonycasts\MicroMapper\AsMapper;
use Symfonycasts\MicroMapper\MapperInterface;

#[AsMapper(from: ServiceDto::class, to: Service::class)]
class ServiceDtoToEntityMapper implements MapperInterface
{
    public function __construct(
        private ServiceRepository $serviceRepository,
        private PoleRepository $poleRepository
    ) {}

    public function load(object $from, string $toClass, array $context): object
    {
        $dto = $from;
        assert($dto instanceof ServiceDto);

        // On récupère l'entité existante si un ID est fourni, sinon on en crée une nouvelle
        $service = $dto->id ? $this->serviceRepository->find($dto->id) : new Service();
        if (!$service) {
            throw new Exception('Service non trouvé');
        }

        return $service;
    }

    public function populate(object $from, object $to, array $context): object
    {
        $dto = $from;
        $service = $to;
        assert($dto instanceof ServiceDto);
        assert($service instanceof Service);

        $service->setEtab($dto->etab);
        $service->setSecode($dto->secode);
        $service->setDatdeb($dto->datdeb);
        $service->setDatfin($dto->datfin);
        $service->setLibelle($dto->libelle);
        $service->setCdcode($dto->cdcode);
        $service->setTacode($dto->tacode);
        $service->setPfuser($dto->pfuser);
        $service->setDmdacre($dto->dmdacre);
        $service->setDmdamaj($dto->dmdamaj);

        // Champs d'horodatage
        $service->setIsActif($dto->isActif);
        $service->setValidFrom($dto->validFrom);
        $service->setValidTo($dto->validTo);
        $service->setPeriodeType($dto->periodeType);
        $service->setSource($dto->source);


        return $service;
    }
}
