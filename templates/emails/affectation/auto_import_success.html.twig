<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Importation automatique réussie</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #28a745; color: white; padding: 20px; border-radius: 5px; text-align: center; }
        .content { background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-top: 20px; }
        .stats { background-color: white; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .stats-row { display: flex; justify-content: space-between; margin: 5px 0; }
        .label { font-weight: bold; }
        .value { color: #007bff; }
        .footer { text-align: center; margin-top: 20px; font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="header">
        <h1>✅ Importation automatique réussie</h1>
        <p>Entité Juridique: {{ entiteJuridique.code }}</p>
    </div>

    <div class="content">
        <h2>Résumé de l'importation</h2>
        <p>L'importation automatique des affectations a été effectuée avec succès le {{ timestamp|date('d/m/Y à H:i:s') }}.</p>

        <div class="stats">
            <h3>Statistiques</h3>
            <div class="stats-row">
                <span class="label">Fichiers traités :</span>
                <span class="value">{{ result.filesProcessed }}</span>
            </div>
            <div class="stats-row">
                <span class="label">Affectations créées :</span>
                <span class="value">{{ result.totalCreated }}</span>
            </div>
            <div class="stats-row">
                <span class="label">Affectations mises à jour :</span>
                <span class="value">{{ result.totalUpdated }}</span>
            </div>
            {% if result.errors is not empty %}
            <div class="stats-row">
                <span class="label">Erreurs rencontrées :</span>
                <span class="value" style="color: #dc3545;">{{ result.errors|length }}</span>
            </div>
            {% endif %}
        </div>

        {% if result.errors is not empty %}
        <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin-top: 15px;">
            <h4 style="color: #856404;">⚠️ Erreurs détectées</h4>
            <ul>
                {% for error in result.errors %}
                <li style="color: #856404;">{{ error }}</li>
                {% endfor %}
            </ul>
        </div>
        {% endif %}

        <div style="margin-top: 20px;">
            <p><strong>Dossier surveillé :</strong> {{ entiteJuridique.affectationAgentFilesPaths }}</p>
            <p><small>Les fichiers traités ont été déplacés vers le sous-dossier "processed".</small></p>
        </div>
    </div>

    <div class="footer">
        <p>Ce message a été généré automatiquement par le système SUPRA.<br>
        Entité Juridique: {{ entiteJuridique.code }}</p>
    </div>
</body>
</html>
