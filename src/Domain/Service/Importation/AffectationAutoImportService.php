<?php

namespace App\Domain\Service\Importation;

use App\Controller\Importation\Agent\Dto\AffectationAgentUfDto;
use App\Entity\Structure\EntiteJuridique;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use App\Domain\Service\Notification\AffectationNotificationEmailService;

/**
 * Service pour le chargement automatique des fichiers d'affectation.
 *
 * Ce service surveille les dossiers configurés pour chaque entité juridique
 * et traite automatiquement les nouveaux fichiers d'affectation si la fonctionnalité
 * est activée pour l'entité juridique.
 */
class AffectationAutoImportService
{
    public function __construct(
        private readonly EntityManagerInterface $em,
        private readonly AffectationImportService $importService,
        private readonly LoggerInterface $logger,
        private readonly AffectationNotificationEmailService $notificationService
    ) {}

    /**
     * Traite toutes les entités juridiques ayant le chargement automatique activé
     */
    public function processAllEntitesJuridiques(): array
    {
        $results = [];

        // Récupérer toutes les entités juridiques avec chargement automatique activé
        $entitesJuridiques = $this->em->getRepository(EntiteJuridique::class)
            ->createQueryBuilder('ej')
            ->where("JSON_EXTRACT(ej.parametre, '$.addresses.chargementAutomatiqueDesAffectationEstActif') = true")
            ->getQuery()
            ->getResult();

        foreach ($entitesJuridiques as $entiteJuridique) {
            $results[$entiteJuridique->getCode()] = $this->processEntiteJuridique($entiteJuridique->getCode());
        }

        return $results;
    }

    /**
     * Traite une entité juridique spécifique
     */
    public function processEntiteJuridique(string $ejCode): array
    {
        $entiteJuridique = $this->em->getRepository(EntiteJuridique::class)
            ->findOneBy(['code' => $ejCode]);

        if (!$entiteJuridique) {
            return ['error' => "Entité juridique non trouvée : {$ejCode}"];
        }

        if (!$entiteJuridique->isChargementAutomatiqueDesAffectationEstActif()) {
            return ['error' => "Le chargement automatique n'est pas activé pour l'entité juridique {$ejCode}"];
        }

        $folderPath = $entiteJuridique->getAffectationAgentFilesPaths();

        if (!is_dir($folderPath)) {
            return ['error' => "Le dossier configuré n'existe pas : {$folderPath}"];
        }

        if (!is_readable($folderPath)) {
            return ['error' => "Le dossier n'est pas accessible en lecture : {$folderPath}"];
        }

        $result = $this->processFilesInFolder($folderPath, $ejCode);

        // Envoyer les notifications d'email appropriées
        $this->sendNotifications($entiteJuridique, $result);

        return $result;
    }

    /**
     * Envoie les notifications d'email selon les résultats du traitement
     */
    private function sendNotifications(EntiteJuridique $entiteJuridique, array $result): void
    {
        try {
            // Si des fichiers ont été traités
            if ($result['filesProcessed'] > 0) {
                // Si il y a eu des erreurs importantes
                if (!empty($result['errors']) && count($result['errors']) > 0) {
                    $this->notificationService->sendAffectationAutoImportErrorNotification(
                        $entiteJuridique,
                        $result['errors']
                    );
                } else {
                    // Succès complet ou partiel
                    $this->notificationService->sendAffectationAutoImportSuccessNotification(
                        $entiteJuridique,
                        $result
                    );
                }
            }
            // Si aucun fichier trouvé, pas de notification (comportement normal)
        } catch (\Exception $e) {
            $this->logger->error(
                'Erreur lors de l\'envoi de notification email',
                [
                    'entiteJuridique' => $entiteJuridique->getCode(),
                    'error' => $e->getMessage()
                ]
            );
        }
    }

    /**
     * Traite tous les fichiers CSV dans un dossier
     */
    private function processFilesInFolder(string $folderPath, string $ejCode): array
    {
        $result = [
            'filesProcessed' => 0,
            'totalCreated' => 0,
            'totalUpdated' => 0,
            'errors' => []
        ];

        // Rechercher les fichiers CSV dans le dossier
        $files = glob($folderPath . '/*.csv');

        if (empty($files)) {
            $this->logger->info("Aucun fichier CSV trouvé dans le dossier : {$folderPath}");
            return $result;
        }

        $entiteJuridique = $this->em->getRepository(EntiteJuridique::class)
            ->findOneBy(['code' => $ejCode]);

        foreach ($files as $filePath) {
            try {
                $fileResult = $this->processFile($filePath, $ejCode);

                $result['filesProcessed']++;
                $result['totalCreated'] += $fileResult['created'];
                $result['totalUpdated'] += $fileResult['updated'];

                if (!empty($fileResult['errors'])) {
                    $result['errors'] = array_merge($result['errors'], $fileResult['errors']);
                }

                // Déplacer le fichier traité vers un dossier "processed"
                $this->moveProcessedFile($filePath);

                $this->logger->info("Fichier traité avec succès : {$filePath}", $fileResult);

            } catch (\Exception $e) {
                $error = "Erreur lors du traitement du fichier {$filePath} : {$e->getMessage()}";
                $result['errors'][] = $error;
                $this->logger->error($error, ['exception' => $e]);

                // Envoyer une notification spécifique pour fichier CSV invalide
                if ($entiteJuridique && str_contains($e->getMessage(), 'CSV')) {
                    try {
                        $this->notificationService->sendInvalidCsvFileNotification(
                            $entiteJuridique,
                            $filePath,
                            $e->getMessage()
                        );
                    } catch (\Exception $notifError) {
                        $this->logger->error('Erreur notification CSV invalide: ' . $notifError->getMessage());
                    }
                }

                // Déplacer le fichier en erreur vers un dossier "errors"
                $this->moveErrorFile($filePath);
            }
        }

        return $result;
    }

    /**
     * Traite un fichier CSV spécifique
     */
    private function processFile(string $filePath, string $ejCode): array
    {
        if (!file_exists($filePath)) {
            throw new \Exception("Le fichier n'existe pas : {$filePath}");
        }

        if (!is_readable($filePath)) {
            throw new \Exception("Le fichier n'est pas lisible : {$filePath}");
        }

        try {
            $dtos = $this->parseCsvFile($filePath);

            if (empty($dtos)) {
                throw new \Exception("Aucune donnée valide trouvée dans le fichier CSV");
            }

            // Utiliser le service d'importation existant
            return $this->importService->importAffectations($dtos, $ejCode);

        } catch (\Exception $e) {
            throw new \Exception("Erreur lors du traitement du fichier CSV : {$e->getMessage()}");
        }
    }

    /**
     * Parse un fichier CSV et retourne un tableau de DTOs
     *
     * @return AffectationAgentUfDto[]
     */
    private function parseCsvFile(string $filePath): array
    {
        $dtos = [];
        $handle = fopen($filePath, 'r');

        if ($handle === false) {
            throw new \Exception("Impossible d'ouvrir le fichier CSV : {$filePath}");
        }

        try {
            // Lire la première ligne pour les en-têtes
            $headers = fgetcsv($handle, 0, ';'); // Utilisation du point-virgule comme séparateur

            if ($headers === false || empty($headers)) {
                throw new \Exception("Impossible de lire les en-têtes du fichier CSV");
            }

            // Nettoyer les en-têtes (supprimer BOM et espaces)
            $headers = array_map(function($header) {
                return trim(str_replace("\xEF\xBB\xBF", '', $header));
            }, $headers);

            // Mapping des colonnes CSV vers les propriétés du DTO
            $columnMapping = $this->getColumnMapping();

            $lineNumber = 1;
            while (($data = fgetcsv($handle, 0, ';')) !== false) {
                $lineNumber++;

                // Ignorer les lignes vides
                if (empty(array_filter($data))) {
                    continue;
                }

                try {
                    $dto = $this->createDtoFromCsvRow($headers, $data, $columnMapping);
                    if ($dto !== null) {
                        $dtos[] = $dto;
                    }
                } catch (\Exception $e) {
                    $this->logger->warning("Erreur ligne {$lineNumber} du fichier {$filePath} : {$e->getMessage()}");
                    // Continuer le traitement des autres lignes
                }
            }

        } finally {
            fclose($handle);
        }

        return $dtos;
    }

    /**
     * Mapping des colonnes CSV vers les propriétés du DTO
     */
    private function getColumnMapping(): array
    {
        return [
            'uConnexion' => ['uconnexion', 'u_connexion', 'login', 'identifiant'],
            'matricule' => ['matricule', 'mat', 'numero_matricule'],
            'dateDebut' => ['date_debut', 'datedebut', 'debut', 'date_start'],
            'dateFin' => ['date_fin', 'datefin', 'fin', 'date_end'],
            'codeUf' => ['code_uf', 'codeuf', 'uf', 'unite_fonctionnelle'],
            'typeGrade' => ['type_grade', 'typegrade', 'grade_type'],
            'libelleGrade' => ['libelle_grade', 'libellegrade', 'grade_libelle', 'grade'],
            'rgt' => ['rgt', 'regime_temps'],
            'etpStatutaire' => ['etp_statutaire', 'etpstatutaire', 'etp'],
            'tauxAffectation' => ['taux_affectation', 'tauxaffectation', 'taux'],
            'sommeEtp' => ['somme_etp', 'sommeetp', 'total_etp'],
            'affectationPrincipale' => ['affectation_principale', 'affectationprincipale', 'principale'],
            'absences' => ['absences', 'absence']
        ];
    }

    /**
     * Crée un DTO à partir d'une ligne CSV
     */
    private function createDtoFromCsvRow(array $headers, array $data, array $columnMapping): ?AffectationAgentUfDto
    {
        // Vérifier que nous avons autant de données que d'en-têtes
        if (count($data) !== count($headers)) {
            throw new \Exception("Nombre de colonnes incohérent dans la ligne");
        }

        $rowData = array_combine($headers, $data);
        if ($rowData === false) {
            throw new \Exception("Impossible de combiner les en-têtes avec les données");
        }

        $dto = new AffectationAgentUfDto();

        // Mapper chaque propriété du DTO
        foreach ($columnMapping as $dtoProperty => $possibleColumns) {
            $value = $this->findValueInRow($rowData, $possibleColumns);
            if ($value !== null && $value !== '') {
                $dto->$dtoProperty = trim($value);
            }
        }

        // Vérifier que nous avons au moins les champs obligatoires
        if (empty($dto->uConnexion) || empty($dto->codeUf)) {
            throw new \Exception("Champs obligatoires manquants (uConnexion ou codeUf)");
        }

        return $dto;
    }

    /**
     * Trouve une valeur dans une ligne CSV en utilisant plusieurs noms de colonnes possibles
     */
    private function findValueInRow(array $rowData, array $possibleColumns): ?string
    {
        // Normaliser les clés de la ligne (minuscules, sans espaces, sans accents)
        $normalizedRowData = [];
        foreach ($rowData as $key => $value) {
            $normalizedKey = $this->normalizeColumnName($key);
            $normalizedRowData[$normalizedKey] = $value;
        }

        // Chercher la valeur avec les différents noms possibles
        foreach ($possibleColumns as $column) {
            $normalizedColumn = $this->normalizeColumnName($column);
            if (isset($normalizedRowData[$normalizedColumn])) {
                return $normalizedRowData[$normalizedColumn];
            }
        }

        return null;
    }

    /**
     * Normalise un nom de colonne pour la comparaison
     */
    private function normalizeColumnName(string $columnName): string
    {
        // Convertir en minuscules, supprimer espaces, underscores, tirets
        $normalized = strtolower($columnName);
        $normalized = str_replace([' ', '_', '-', '.'], '', $normalized);

        // Supprimer les accents
        $normalized = iconv('UTF-8', 'ASCII//TRANSLIT//IGNORE', $normalized);

        return $normalized;
    }

    /**
     * Déplace un fichier traité avec succès vers le dossier "processed"
     */
    private function moveProcessedFile(string $filePath): void
    {
        $this->moveFileToSubfolder($filePath, 'processed');
    }

    /**
     * Déplace un fichier en erreur vers le dossier "errors"
     */
    private function moveErrorFile(string $filePath): void
    {
        $this->moveFileToSubfolder($filePath, 'errors');
    }

    /**
     * Déplace un fichier vers un sous-dossier spécifique
     */
    private function moveFileToSubfolder(string $filePath, string $subfolder): void
    {
        $directory = dirname($filePath);
        $filename = basename($filePath);
        $targetDir = $directory . DIRECTORY_SEPARATOR . $subfolder;

        // Créer le dossier de destination s'il n'existe pas
        if (!is_dir($targetDir)) {
            mkdir($targetDir, 0755, true);
        }

        // Ajouter un timestamp au nom du fichier pour éviter les conflits
        $timestamp = date('Y-m-d_H-i-s');
        $pathInfo = pathinfo($filename);
        $newFilename = $pathInfo['filename'] . '_' . $timestamp . '.' . $pathInfo['extension'];

        $targetPath = $targetDir . DIRECTORY_SEPARATOR . $newFilename;

        if (!rename($filePath, $targetPath)) {
            $this->logger->warning("Impossible de déplacer le fichier {$filePath} vers {$targetPath}");
        } else {
            $this->logger->info("Fichier déplacé : {$filePath} -> {$targetPath}");
        }
    }
}
