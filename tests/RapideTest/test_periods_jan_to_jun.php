<?php
// Test script for the /api/actes endpoint with period parameters from January to June for 2025, 2024, 2023 (inverted order)
// This script tests the functionality of the ActesPeriodesExtension with the specified periods
// 
// CHANGES MADE:
// - Modified the period parameters to use the inverted order as specified in the issue description:
//   * p1: 2025-01-01 to 2025-06-30 (was 2023)
//   * p2: 2024-01-01 to 2024-06-30 (unchanged)
//   * p3: 2023-01-01 to 2023-06-30 (was 2025)
// - Updated all descriptions to reflect this inverted order
//
// The script tests the following scenarios:
// 1. Basic test with periods (January to June for 2025, 2024, 2023)
// 2. With periods and ejcode
// 3. With pagination (page 1, 10 items per page)
// 4. Only ejcode without period parameters

// Function to make a GET request and display the results
// Run with: php tests/RapideTest/test_periods_jan_to_jun.php

function testEndpoint($url, $bearerToken) {
    echo "Testing endpoint: $url\n";
    
    // Use cURL to make the request
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HEADER, true);
    
    // Add Authorization header with bearer token
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $bearerToken
    ]);
    
    $response = curl_exec($ch);
    
    // Check for cURL errors
    if ($response === false) {
        echo "cURL Error: " . curl_error($ch) . "\n";
        curl_close($ch);
        return;
    }
    
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
    $headers = substr($response, 0, $headerSize);
    $body = substr($response, $headerSize);
    
    curl_close($ch);
    
    echo "HTTP Status Code: $httpCode\n";
    
    // Display important headers
    $headerLines = explode("\n", $headers);
    echo "Response Headers:\n";
    foreach ($headerLines as $line) {
        if (preg_match('/^(Content-Type|Content-Length|X-Debug-Token|X-Debug-Token-Link):/i', $line)) {
            echo "  $line\n";
        }
    }
    
    // Parse the JSON response
    $data = json_decode($body, true);
    
    if ($data) {
        echo "JSON Structure Keys: " . implode(", ", array_keys($data)) . "\n";
        
        // Check for 'view' key (might be 'hydra:view' or just 'view')
        if (isset($data['hydra:view'])) {
            echo "Pagination information found (hydra:view):\n";
            echo "- Keys: " . implode(", ", array_keys($data['hydra:view'])) . "\n";
            echo "- First page: " . ($data['hydra:view']['hydra:first'] ?? 'N/A') . "\n";
            echo "- Last page: " . ($data['hydra:view']['hydra:last'] ?? 'N/A') . "\n";
            echo "- Next page: " . ($data['hydra:view']['hydra:next'] ?? 'N/A') . "\n";
            echo "- Previous page: " . ($data['hydra:view']['hydra:previous'] ?? 'N/A') . "\n";
        } elseif (isset($data['view'])) {
            echo "Pagination information found (view):\n";
            echo "- Keys: " . implode(", ", array_keys($data['view'])) . "\n";
            foreach ($data['view'] as $key => $value) {
                echo "- $key: $value\n";
            }
        } else {
            echo "No pagination information found in the response.\n";
        }
        
        // Check for 'totalItems' key (might be 'hydra:totalItems' or just 'totalItems')
        if (isset($data['hydra:totalItems'])) {
            echo "Total items (hydra:totalItems): " . $data['hydra:totalItems'] . "\n";
        } elseif (isset($data['totalItems'])) {
            echo "Total items (totalItems): " . $data['totalItems'] . "\n";
        } else {
            echo "No total items count found in the response.\n";
        }
        
        // Check for 'member' key (might be 'hydra:member' or just 'member')
        if (isset($data['hydra:member']) && is_array($data['hydra:member'])) {
            echo "Number of items in this page (hydra:member): " . count($data['hydra:member']) . "\n";
            if (count($data['hydra:member']) > 0) {
                echo "First item keys: " . implode(", ", array_keys($data['hydra:member'][0])) . "\n";
                
                // Display some details of the first few items
                echo "\nSample items (first 3 or less):\n";
                $sampleCount = min(3, count($data['hydra:member']));
                for ($i = 0; $i < $sampleCount; $i++) {
                    $item = $data['hydra:member'][$i];
                    echo "Item #" . ($i + 1) . ":\n";
                    echo "  - ID: " . ($item['id'] ?? 'N/A') . "\n";
                    echo "  - Code: " . ($item['code'] ?? 'N/A') . "\n";
                    echo "  - Type: " . ($item['typeActe'] ?? 'N/A') . "\n";
                    echo "  - Date: " . ($item['date_realisation'] ?? 'N/A') . "\n";
                    echo "  - Year: " . ($item['annee'] ?? 'N/A') . "\n";
                    echo "  - Month: " . ($item['mois'] ?? 'N/A') . "\n";
                    
                    // Check if UF information is available
                    if (isset($item['ufPrincipal']) && is_array($item['ufPrincipal'])) {
                        echo "  - UF Principal: " . ($item['ufPrincipal']['ufcode'] ?? 'N/A') . "\n";
                    }
                    
                    echo "\n";
                }
            }
        } elseif (isset($data['member']) && is_array($data['member'])) {
            echo "Number of items in this page (member): " . count($data['member']) . "\n";
            if (count($data['member']) > 0) {
                echo "First item keys: " . implode(", ", array_keys($data['member'][0])) . "\n";
            }
        } else {
            echo "No items found in the response.\n";
        }
    } else {
        echo "Failed to parse JSON response.\n";
        echo "Raw response body:\n";
        echo substr($body, 0, 500) . (strlen($body) > 500 ? "...[truncated]" : "") . "\n";
    }
    
    echo "\n";
}

// Base URL - replace with your actual API base URL if needed
$baseUrl = 'http://localhost:8000';

// Define period parameters for January to June for 2025, 2024, 2023 (inverted order)
$periodParams = "p1Start=2025-01-01&p1End=2025-06-30&p2Start=2024-01-01&p2End=2024-06-30&p3Start=2023-01-01&p3End=2023-06-30";

// Ejcode parameter
$ejcode = "ej0001";

// Bearer token for authentication (from the issue description)
$bearerToken = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ToYfqp5fsxBUHcC0dXXsr-Js-ylY-U62FEbYSBF2a4g';

echo "=== Testing ActesPeriodesExtension with January to June periods for 2025, 2024, 2023 (inverted order) ===\n\n";

// Test 1: Basic test with periods
echo "Test 1: Basic test with periods (January to June for 2025, 2024, 2023)\n";
echo "----------------------------------------------------------------\n";
testEndpoint("$baseUrl/api/actes?$periodParams", $bearerToken);

// Test 2: With periods and ejcode
echo "Test 2: With periods and ejcode\n";
echo "-----------------------------\n";
testEndpoint("$baseUrl/api/actes?$periodParams&ejcode=$ejcode", $bearerToken);

// Test 3: With pagination (page 1, 10 items per page)
echo "Test 3: With pagination (page 1, 10 items per page)\n";
echo "------------------------------------------------\n";
testEndpoint("$baseUrl/api/actes?$periodParams&page=1&itemsPerPage=10", $bearerToken);

// Test 4: Only ejcode without period parameters
echo "Test 4: Only ejcode without period parameters\n";
echo "------------------------------------------\n";
testEndpoint("$baseUrl/api/actes?ejcode=$ejcode", $bearerToken);

echo "Tests completed.\n";