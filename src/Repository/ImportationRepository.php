<?php

namespace App\Repository;

use App\Entity\Importation\Importation;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Importation>
 */
class ImportationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Importation::class);
    }

    /**
     * Trouve les dernières importations pour une entité juridique donnée
     *
     * @param string $entiteJuridiqueId ID de l'entité juridique
     * @param int $limit Nombre maximum de résultats à retourner
     * @return Importation[] Tableau d'objets Importation
     */
    public function findLatestByEntiteJuridique(string $entiteJuridiqueId, int $limit = 10): array
    {
        return $this->createQueryBuilder('i')
            ->andWhere('i.entiteJuridique = :ejId')
            ->setParameter('ejId', $entiteJuridiqueId)
            ->orderBy('i.dateImportation', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult()
        ;
    }

    /**
     * Trouve les dernières importations pour une ressource donnée
     *
     * @param string $nomRessource Nom de la ressource (Agent, Structure, Actes)
     * @param int $limit Nombre maximum de résultats à retourner
     * @return Importation[] Tableau d'objets Importation
     */
    public function findLatestByRessource(string $nomRessource, int $limit = 10): array
    {
        return $this->createQueryBuilder('i')
            ->andWhere('i.nomRessource = :ressource')
            ->setParameter('ressource', $nomRessource)
            ->orderBy('i.dateImportation', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult()
        ;
    }
    
    /**
     * Trouve la dernière importation réussie pour une ressource donnée
     *
     * @param string $nomRessource Nom de la ressource (Agent, Structure, Actes)
     * @return Importation|null L'importation la plus récente ou null si aucune n'est trouvée
     */
    public function findLatestSuccessfulByRessource(string $nomRessource): ?Importation
    {
        return $this->createQueryBuilder('i')
            ->andWhere('i.nomRessource = :ressource')
            ->andWhere('i.estReussie = :success')
            ->setParameter('ressource', $nomRessource)
            ->setParameter('success', true)
            ->orderBy('i.dateImportation', 'DESC')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
}