<?php

namespace App\Tests\Domain\Service\UserManagement;

use App\Domain\Service\UserManager;
use App\Entity\Praticien\Agent;
use App\Entity\Structure\EntiteJuridique;
use App\Repository\AgentRepository;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;

it('can be instantiated', function () {
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $passwordHasher = $this->createMock(UserPasswordHasherInterface::class);
    
    $service = new UserManager($entityManager, $passwordHasher);
    
    expect($service)->toBeInstanceOf(UserManager::class);
});

it('finds existing user by username', function () {
    // Create mocks
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $passwordHasher = $this->createMock(UserPasswordHasherInterface::class);
    $repository = $this->createMock(AgentRepository::class);
    $agent = $this->createMock(Agent::class);
    $entiteJuridique = $this->createMock(EntiteJuridique::class);
    
    // Configure mocks
    $entityManager->method('getRepository')
        ->with(Agent::class)
        ->willReturn($repository);
    
    $repository->method('findByIdentifier')
        ->with('<EMAIL>')
        ->willReturn($agent);
    
    $agent->method('getIsLdapInitialized')
        ->willReturn(true);
    
    $agent->method('getHopital')
        ->willReturn($entiteJuridique);
    
    // Create service and call method
    $service = new UserManager($entityManager, $passwordHasher);
    $result = $service->findOrCreateUser('<EMAIL>', $entiteJuridique);
    
    // Assertions
    expect($result)->toBeArray();
    expect($result)->toHaveKey('agent');
    expect($result)->toHaveKey('isNew');
    expect($result)->toHaveKey('needsLdapInit');
    expect($result['agent'])->toBe($agent);
    expect($result['isNew'])->toBeFalse();
    expect($result['needsLdapInit'])->toBeFalse();
});

it('creates new user when not found', function () {
    // Create mocks
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $passwordHasher = $this->createMock(UserPasswordHasherInterface::class);
    $repository = $this->createMock(AgentRepository::class);
    $entiteJuridique = $this->createMock(EntiteJuridique::class);
    
    // Configure mocks
    $entityManager->method('getRepository')
        ->with(Agent::class)
        ->willReturn($repository);
    
    $repository->method('findByIdentifier')
        ->with('<EMAIL>')
        ->willReturn(null);
    
    // Create service and call method
    $service = new UserManager($entityManager, $passwordHasher);
    $result = $service->findOrCreateUser('<EMAIL>', $entiteJuridique);
    
    // Assertions
    expect($result)->toBeArray();
    expect($result)->toHaveKey('agent');
    expect($result)->toHaveKey('isNew');
    expect($result)->toHaveKey('needsLdapInit');
    expect($result['agent'])->toBeInstanceOf(Agent::class);
    expect($result['isNew'])->toBeTrue();
    expect($result['needsLdapInit'])->toBeTrue();
});

it('creates new user with hrUser when username is not email', function () {
    // Create mocks
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $passwordHasher = $this->createMock(UserPasswordHasherInterface::class);
    $repository = $this->createMock(AgentRepository::class);
    $entiteJuridique = $this->createMock(EntiteJuridique::class);
    
    // Configure mocks
    $entityManager->method('getRepository')
        ->with(Agent::class)
        ->willReturn($repository);
    
    $repository->method('findByIdentifier')
        ->with('u123456')
        ->willReturn(null);
    
    // Create service and call method
    $service = new UserManager($entityManager, $passwordHasher);
    $result = $service->findOrCreateUser('u123456', $entiteJuridique);
    
    // Assertions
    expect($result)->toBeArray();
    expect($result)->toHaveKey('agent');
    expect($result)->toHaveKey('isNew');
    expect($result)->toHaveKey('needsLdapInit');
    expect($result['agent'])->toBeInstanceOf(Agent::class);
    expect($result['isNew'])->toBeTrue();
    expect($result['needsLdapInit'])->toBeTrue();
});

it('updates user from LDAP attributes', function () {
    // Create mocks
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $passwordHasher = $this->createMock(UserPasswordHasherInterface::class);
    $agent = $this->createMock(Agent::class);
    
    // LDAP attributes
    $attributes = [
        'sn' => 'Doe',
        'givenname' => 'John',
        'displayname' => 'John Doe',
        'mail' => '<EMAIL>',
        'cn' => 'u123456',
        'department' => 'IT Department'
    ];
    
    // Configure mocks
    $agent->method('getNom')->willReturn(null);
    $agent->method('getPrenom')->willReturn(null);
    $agent->method('getFullName')->willReturn(null);
    $agent->method('getEmail')->willReturn(null);
    $agent->method('getHrUser')->willReturn(null);
    $agent->method('getEtablissement')->willReturn(null);
    $agent->method('getCreateurFiche')->willReturn(null);
    
    // Expect agent methods to be called
    $agent->expects($this->once())->method('setNom')->with('Doe');
    $agent->expects($this->once())->method('setPrenom')->with('John');
    $agent->expects($this->once())->method('setFullName')->with('John Doe');
    $agent->expects($this->once())->method('setEmail')->with('<EMAIL>');
    $agent->expects($this->once())->method('setHrUser')->with('u123456');
    $agent->expects($this->once())->method('setEtablissement')->with('IT Departm');
    $agent->expects($this->once())->method('setCreateurFiche')->with('Via LDAP AD first login');
    $agent->expects($this->once())->method('updateLdapAttributes')->with($attributes);
    
    // Create service and call method
    $service = new UserManager($entityManager, $passwordHasher);
    $service->updateUserFromLdapAttributes($agent, $attributes);
});

it('does not update user fields that are already set', function () {
    // Create mocks
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $passwordHasher = $this->createMock(UserPasswordHasherInterface::class);
    $agent = $this->createMock(Agent::class);
    
    // LDAP attributes
    $attributes = [
        'sn' => 'Doe',
        'givenname' => 'John',
        'displayname' => 'John Doe',
        'mail' => '<EMAIL>',
        'cn' => 'u123456',
        'department' => 'IT Department'
    ];
    
    // Configure mocks - all fields already set
    $agent->method('getNom')->willReturn('Smith');
    $agent->method('getPrenom')->willReturn('Jane');
    $agent->method('getFullName')->willReturn('Jane Smith');
    $agent->method('getEmail')->willReturn('<EMAIL>');
    $agent->method('getHrUser')->willReturn('u654321');
    $agent->method('getEtablissement')->willReturn('HR Dept');
    $agent->method('getCreateurFiche')->willReturn('Manual');
    
    // Expect agent methods NOT to be called for already set fields
    $agent->expects($this->never())->method('setNom');
    $agent->expects($this->never())->method('setPrenom');
    $agent->expects($this->never())->method('setFullName');
    $agent->expects($this->never())->method('setEmail');
    $agent->expects($this->never())->method('setHrUser');
    $agent->expects($this->never())->method('setEtablissement');
    $agent->expects($this->never())->method('setCreateurFiche');
    
    // But updateLdapAttributes should still be called
    $agent->expects($this->once())->method('updateLdapAttributes')->with($attributes);
    
    // Create service and call method
    $service = new UserManager($entityManager, $passwordHasher);
    $service->updateUserFromLdapAttributes($agent, $attributes);
});

it('updates user password', function () {
    // Create mocks
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $passwordHasher = $this->createMock(UserPasswordHasherInterface::class);
    $agent = $this->createMock(Agent::class);
    
    // Configure mocks
    $passwordHasher->method('hashPassword')
        ->with($agent, 'plain-password')
        ->willReturn('hashed-password');
    
    // Expect agent methods to be called
    $agent->expects($this->once())->method('setPassword')->with('hashed-password');
    
    // Create service and call method
    $service = new UserManager($entityManager, $passwordHasher);
    $service->updateUserPassword($agent, 'plain-password');
});

it('saves user to database', function () {
    // Create mocks
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $passwordHasher = $this->createMock(UserPasswordHasherInterface::class);
    $agent = $this->createMock(Agent::class);
    
    // Expect entityManager methods to be called
    $entityManager->expects($this->once())->method('persist')->with($agent);
    $entityManager->expects($this->once())->method('flush');
    
    // Create service and call method
    $service = new UserManager($entityManager, $passwordHasher);
    $service->saveUser($agent);
});

it('updates connection statistics', function () {
    // Create mocks
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $passwordHasher = $this->createMock(UserPasswordHasherInterface::class);
    $agent = $this->createMock(Agent::class);
    
    // Expect agent methods to be called
    $agent->expects($this->once())->method('incrementConnectionStats');
    
    // Expect entityManager methods to be called
    $entityManager->expects($this->once())->method('flush');
    
    // Create service and call method
    $service = new UserManager($entityManager, $passwordHasher);
    $service->updateConnectionStats($agent);
});

it('marks user as LDAP initialized', function () {
    // Create mocks
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $passwordHasher = $this->createMock(UserPasswordHasherInterface::class);
    $agent = $this->createMock(Agent::class);
    
    // Expect agent methods to be called
    $agent->expects($this->once())->method('setIsLdapInitialized')->with(true);
    
    // Create service and call method
    $service = new UserManager($entityManager, $passwordHasher);
    $service->markAsLdapInitialized($agent);
});

it('checks if user needs LDAP initialization', function () {
    // Create mocks
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $passwordHasher = $this->createMock(UserPasswordHasherInterface::class);
    $agent = $this->createMock(Agent::class);
    
    // Configure mocks
    $agent->method('getIsLdapInitialized')
        ->willReturn(false);
    
    // Create service and call method
    $service = new UserManager($entityManager, $passwordHasher);
    $result = $service->needsLdapInitialization($agent);
    
    // Assertions
    expect($result)->toBeTrue();
    
    // Configure mocks for opposite case
    $agent = $this->createMock(Agent::class);
    $agent->method('getIsLdapInitialized')
        ->willReturn(true);
    
    // Call method again
    $result = $service->needsLdapInitialization($agent);
    
    // Assertions
    expect($result)->toBeFalse();
});