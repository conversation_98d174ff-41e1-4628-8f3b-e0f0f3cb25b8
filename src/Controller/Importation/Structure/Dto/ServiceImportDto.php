<?php

namespace App\Controller\Importation\Structure\Dto;


/*
 * Data Transfer Object pour l'importation des services.
 *
 * @see \App\Domain\Service\Importation\StructureImportService::importServices()
 */
class ServiceImportDto
{
    public ?string $etab = null;
    public ?string $seCode = null;
    public ?string $datDeb = null;
    public ?string $datFin = null;
    public ?string $libelle = null;
    public ?string $cdCode = null;
    public ?string $taCode = null;
    public ?string $pfUser = null;
    public ?string $dmdaCre = null;
    public ?string $dmdAcre = null;
    public ?string $dmdaMaj = null;
    public ?string $dmdAmaj = null;
}