{"type": "project", "license": "proprietary", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": ">=8.2", "ext-ctype": "*", "ext-iconv": "*", "ext-ldap": "*", "api-platform/doctrine-orm": "^4.0", "api-platform/symfony": "^4.0", "doctrine/dbal": "^3", "doctrine/doctrine-bundle": "^2.13", "doctrine/doctrine-migrations-bundle": "^3.3", "doctrine/orm": "^3.3", "easycorp/easyadmin-bundle": "*", "firebase/php-jwt": "^6.11", "league/csv": "^9.21", "nelmio/cors-bundle": "^2.5", "phpdocumentor/reflection-docblock": "^5.6", "phpstan/phpdoc-parser": "^2.0", "predis/predis": "*", "runtime/frankenphp-symfony": "^0.2.0", "symfony/asset": "7.1.*", "symfony/asset-mapper": "7.1.*", "symfony/cache": "7.1.*", "symfony/console": "7.1.*", "symfony/dotenv": "7.1.*", "symfony/expression-language": "7.1.*", "symfony/flex": "^2", "symfony/framework-bundle": "7.1.*", "symfony/ldap": "7.1.*", "symfony/mailer": "7.1.*", "symfony/property-access": "7.1.*", "symfony/property-info": "7.1.*", "symfony/rate-limiter": "7.1.*", "symfony/redis-messenger": "7.1.*", "symfony/runtime": "7.1.*", "symfony/security-bundle": "7.1.*", "symfony/serializer": "7.0.*", "symfony/twig-bundle": "7.1.*", "symfony/ux-live-component": "^2.23", "symfony/ux-turbo": "^2.23", "symfony/ux-twig-component": "^2.23", "symfony/validator": "7.1.*", "symfony/yaml": "7.1.*", "symfonycasts/micro-mapper": "^0.2.1", "symfonycasts/tailwind-bundle": "^0.7.1"}, "config": {"allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true, "symfony/flex": true, "symfony/runtime": true}, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*", "symfony/polyfill-php82": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "7.1.*"}}, "require-dev": {"doctrine/doctrine-fixtures-bundle": "^4.0", "pestphp/pest": "^3.6", "php-cs-fixer/shim": "^3.68", "phpstan/phpstan": "^2.1", "symfony/browser-kit": "7.1.*", "symfony/debug-bundle": "7.1.*", "symfony/maker-bundle": "^1.61", "symfony/monolog-bundle": "^3.0", "symfony/stopwatch": "7.1.*", "symfony/web-profiler-bundle": "7.1.*", "zenstruck/foundry": "^2.3"}}