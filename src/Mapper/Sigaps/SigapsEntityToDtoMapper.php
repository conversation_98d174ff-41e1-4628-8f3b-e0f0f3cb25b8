<?php

namespace App\Mapper\Sigaps;

use App\ApiResource\Activite\SigapsDto;
use App\Entity\Activite\Sigaps;
use App\Repository\AgentUfsRepository;
use App\Repository\CrRepository;
use Symfonycasts\MicroMapper\AsMapper;
use Symfonycasts\MicroMapper\MapperInterface;
use Symfonycasts\MicroMapper\MicroMapperInterface;

#[AsMapper(from: Sigaps::class, to: SigapsDto::class)]
class SigapsEntityToDtoMapper implements MapperInterface
{
    public function __construct(
        private AgentUfsRepository $agentUfsRepository
    ) {}

    public function load(object $from, string $toClass, array $context): object
    {
        $sigaps = $from;
        assert($sigaps instanceof Sigaps);

        $dto = new SigapsDto();
        $dto->id = $sigaps->getId();

        return $dto;
    }

    public function populate(object $from, object $to, array $context): object
    {
        $sigaps = $from;
        $dto = $to;
        assert($sigaps instanceof Sigaps);
        assert($dto instanceof SigapsDto);

        $dto->dateDebut = $sigaps->getDateDebut();
        $dto->dateFin = $sigaps->getDateFin();
        $dto->score = $sigaps->getScore();
        $dto->repartitionParCategorie = $sigaps->getRepartitionParCategorie();
        $dto->categorie = $sigaps->getCategorie();
        $dto->nombrePublication = $sigaps->getNombrePublication();

        // Champs du trait HorodatageTrait
        $dto->validFrom = $sigaps->getValidFrom();
        $dto->validTo = $sigaps->getValidTo();
        $dto->periodeType = $sigaps->getPeriodeType();
        $dto->source = $sigaps->getSource();

        // Informations de l'agent
        $agent = $sigaps->getAgent();
        $dto->agentHrU = $agent?->getHrUser();
        
        // Récupération de la dernière affectation de l'agent
        if ($dto->agentHrU) {
            $lastAffectation = $this->agentUfsRepository->findLastAffectationByHrUser($dto->agentHrU);
            if ($lastAffectation && $lastAffectation->getUfs()) {
                $ufs = $lastAffectation->getUfs();
                $ufCode = $ufs->getUfcode();
                $ufLibelle = $ufs->getLibelle();
                
                if ($ufCode && $ufLibelle) {
                    $dto->derniereAffectionDeAgent = $ufCode . ' - ' . $ufLibelle;
                } elseif ($ufCode) {
                    $dto->derniereAffectionDeAgent = $ufCode;
                } elseif ($ufLibelle) {
                    $dto->derniereAffectionDeAgent = $ufLibelle;
                }
            }
        }
        
        // Récupération du nom, prénom et titre de l'agent
        if ($agent) {
            $titre = $agent->getTitre();
            $prenom = $agent->getPrenom();
            $nom = $agent->getNom();
            
            $nomPrenomTitre = '';
            if ($titre) {
                $nomPrenomTitre .= $titre . ' ';
            }
            if ($prenom) {
                $nomPrenomTitre .= $prenom . ' ';
            }
            if ($nom) {
                $nomPrenomTitre .= $nom;
            }
            
            $dto->agentNomPrenomTitre = trim($nomPrenomTitre) ?: null;
        }

        return $dto;
    }
}
