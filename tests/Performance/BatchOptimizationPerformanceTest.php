<?php

namespace App\Tests\Performance;

use App\Domain\Service\Batch\BatchOptimizationConfigService;
use App\Domain\Service\Notification\BatchNotificationService;
use App\State\Batch\SigapsBatchProcessor;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;

/**
 * Tests de performance pour valider les optimisations batch
 * 
 * Ces tests permettent de :
 * - Mesurer les performances avant/après optimisation
 * - Valider la gestion mémoire
 * - Tester les gros volumes
 * - Vérifier les notifications optimisées
 */
class BatchOptimizationPerformanceTest extends KernelTestCase
{
    private EntityManagerInterface $entityManager;
    private BatchOptimizationConfigService $configService;
    private BatchNotificationService $notificationService;
    private LoggerInterface $logger;

    protected function setUp(): void
    {
        self::bootKernel();
        $container = static::getContainer();
        
        $this->entityManager = $container->get(EntityManagerInterface::class);
        $this->configService = $container->get(BatchOptimizationConfigService::class);
        $this->notificationService = $container->get(BatchNotificationService::class);
        $this->logger = $container->get(LoggerInterface::class);
    }

    /**
     * Test de configuration automatique selon le volume
     */
    public function testConfigurationOptimization(): void
    {
        // Test avec petit volume
        $smallConfig = $this->configService->getResourceConfig('Sigaps', 100);
        $this->assertFalse($smallConfig['disable_emails']);
        $this->assertFalse($smallConfig['enable_optimizations']);

        // Test avec gros volume
        $largeConfig = $this->configService->getResourceConfig('Sigaps', 5000);
        $this->assertTrue($largeConfig['disable_emails']);
        $this->assertTrue($largeConfig['enable_optimizations']);
        $this->assertTrue($largeConfig['is_large_batch']);

        // Vérifier les tailles de micro-batch
        $this->assertGreaterThan($smallConfig['micro_batch_size'], $largeConfig['micro_batch_size']);
    }

    /**
     * Test de performance avec données simulées
     */
    public function testBatchPerformanceWithSimulatedData(): void
    {
        $testSizes = [100, 500, 1000, 2000];
        $results = [];

        foreach ($testSizes as $size) {
            $startTime = microtime(true);
            $startMemory = memory_get_usage(true);

            // Simuler un batch de données
            $testData = $this->generateTestSigapsData($size);
            
            // Tester la configuration
            $config = $this->configService->getResourceConfig('Sigaps', $size);
            
            // Simuler le traitement (sans vraiment persister)
            $this->simulateBatchProcessing($testData, $config);

            $endTime = microtime(true);
            $endMemory = memory_get_usage(true);

            $results[$size] = [
                'duration' => $endTime - $startTime,
                'memory_used' => $endMemory - $startMemory,
                'memory_peak' => memory_get_peak_usage(true),
                'config' => $config,
            ];
        }

        // Vérifier que les performances s'améliorent avec les optimisations
        $this->assertPerformanceImprovement($results);
        
        // Log des résultats pour analyse
        $this->logger->info('Résultats tests de performance', ['results' => $results]);
    }

    /**
     * Test de gestion mémoire avec clear()
     */
    public function testMemoryManagement(): void
    {
        $initialMemory = memory_get_usage(true);
        
        // Simuler l'accumulation d'entités en mémoire
        $entities = [];
        for ($i = 0; $i < 1000; $i++) {
            $entities[] = $this->createMockEntity();
        }
        
        $beforeClearMemory = memory_get_usage(true);
        
        // Simuler le clear() de l'EntityManager
        unset($entities);
        if (function_exists('gc_collect_cycles')) {
            gc_collect_cycles();
        }
        
        $afterClearMemory = memory_get_usage(true);
        
        // Vérifier que la mémoire a été libérée
        $memoryFreed = $beforeClearMemory - $afterClearMemory;
        $this->assertGreaterThan(0, $memoryFreed, 'La mémoire devrait être libérée après clear()');
        
        $this->logger->info('Test gestion mémoire', [
            'initial' => $initialMemory,
            'before_clear' => $beforeClearMemory,
            'after_clear' => $afterClearMemory,
            'memory_freed' => $memoryFreed,
        ]);
    }

    /**
     * Test des notifications optimisées
     */
    public function testOptimizedNotifications(): void
    {
        // Test avec petit batch (notifications normales)
        $smallBatchId = $this->notificationService->startBatch('Sigaps', 100, false);
        $this->assertNotEmpty($smallBatchId);
        
        // Simuler quelques erreurs
        $mockEntiteJuridique = $this->createMockEntiteJuridique();
        $this->notificationService->addBatchError(
            $mockEntiteJuridique,
            new \Exception('Test error 1')
        );
        
        $stats = $this->notificationService->getCurrentBatchStats();
        $this->assertEquals(1, $stats['errors']);
        $this->assertFalse($stats['emails_disabled']);

        // Test avec gros batch (notifications désactivées)
        $largeBatchId = $this->notificationService->startBatch('Sigaps', 5000, true);
        $this->assertNotEmpty($largeBatchId);
        $this->assertNotEquals($smallBatchId, $largeBatchId);
        
        $largeStats = $this->notificationService->getCurrentBatchStats();
        $this->assertTrue($largeStats['emails_disabled']);
    }

    /**
     * Test de calcul des micro-batch optimaux
     */
    public function testOptimalMicroBatchCalculation(): void
    {
        $testCases = [
            ['resource' => 'Sigaps', 'total' => 100, 'expected_min' => 1, 'expected_max' => 3],
            ['resource' => 'Sigaps', 'total' => 1000, 'expected_min' => 3, 'expected_max' => 10],
            ['resource' => 'Sigaps', 'total' => 10000, 'expected_min' => 10, 'expected_max' => 50],
        ];

        foreach ($testCases as $case) {
            $transactionCount = $this->configService->calculateOptimalTransactionCount(
                $case['total'],
                $case['resource']
            );
            
            $this->assertGreaterThanOrEqual(
                $case['expected_min'],
                $transactionCount,
                "Transaction count should be at least {$case['expected_min']} for {$case['total']} records"
            );
            
            $this->assertLessThanOrEqual(
                $case['expected_max'],
                $transactionCount,
                "Transaction count should be at most {$case['expected_max']} for {$case['total']} records"
            );
        }
    }

    /**
     * Génère des données de test pour Sigaps
     */
    private function generateTestSigapsData(int $count): array
    {
        $data = [];
        $baseDate = new \DateTime();
        
        for ($i = 0; $i < $count; $i++) {
            $data[] = [
                'dateDebut' => $baseDate->format('Y-m-d'),
                'dateFin' => $baseDate->modify('+1 month')->format('Y-m-d'),
                'agentHrU' => 'AGENT_' . str_pad($i, 6, '0', STR_PAD_LEFT),
                'score' => rand(1, 100),
                'categorie' => ['A+', 'A', 'B', 'C', 'D'][rand(0, 4)],
                'nombrePublication' => rand(1, 10),
            ];
            $baseDate->modify('-1 month'); // Reset pour le prochain
        }
        
        return $data;
    }

    /**
     * Simule le traitement d'un batch sans persistance
     */
    private function simulateBatchProcessing(array $data, array $config): void
    {
        $microBatches = array_chunk($data, $config['micro_batch_size']);
        
        foreach ($microBatches as $batchIndex => $microBatch) {
            // Simuler le traitement de chaque élément
            foreach ($microBatch as $item) {
                // Simulation de validation et mapping
                $this->validateTestItem($item);
                $this->simulateEntityCreation($item);
            }
            
            // Simuler le clear() périodique
            if (($batchIndex + 1) % 3 === 0) {
                if (function_exists('gc_collect_cycles')) {
                    gc_collect_cycles();
                }
            }
        }
    }

    /**
     * Valide un élément de test
     */
    private function validateTestItem(array $item): void
    {
        $this->assertArrayHasKey('dateDebut', $item);
        $this->assertArrayHasKey('agentHrU', $item);
        $this->assertNotEmpty($item['agentHrU']);
    }

    /**
     * Simule la création d'une entité
     */
    private function simulateEntityCreation(array $item): object
    {
        return (object) $item;
    }

    /**
     * Crée une entité mock pour les tests mémoire
     */
    private function createMockEntity(): object
    {
        return (object) [
            'id' => uniqid(),
            'data' => str_repeat('x', 1000), // 1KB de données
            'timestamp' => new \DateTime(),
        ];
    }

    /**
     * Crée une EntiteJuridique mock
     */
    private function createMockEntiteJuridique(): object
    {
        return (object) [
            'code' => 'TEST_EJ',
            'adminEmail' => '<EMAIL>',
        ];
    }

    /**
     * Vérifie l'amélioration des performances
     */
    private function assertPerformanceImprovement(array $results): void
    {
        $sizes = array_keys($results);
        
        // Vérifier que les gros volumes utilisent les optimisations
        foreach ($sizes as $size) {
            if ($size >= 1000) {
                $this->assertTrue(
                    $results[$size]['config']['enable_optimizations'],
                    "Les optimisations devraient être activées pour {$size} enregistrements"
                );
            }
        }
        
        // Vérifier que l'utilisation mémoire reste raisonnable
        foreach ($results as $size => $result) {
            $memoryPerRecord = $result['memory_used'] / $size;
            $this->assertLessThan(
                1024 * 1024, // 1MB par enregistrement max
                $memoryPerRecord,
                "L'utilisation mémoire par enregistrement devrait être raisonnable"
            );
        }
    }
}
