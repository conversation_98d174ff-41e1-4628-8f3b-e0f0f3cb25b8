<?php

namespace App\Factory\Structure;

use App\Entity\Structure\EntiteJuridique;
use Zenstruck\Foundry\Persistence\PersistentProxyObjectFactory;

/**
 * @extends PersistentProxyObjectFactory<EntiteJuridique>
 */
final class EntiteJuridiqueFactory extends PersistentProxyObjectFactory
{
    /**
     * @see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#factories-as-services
     *
     * @todo inject services if required
     */
    public function __construct()
    {
    }

    public static function class(): string
    {
        return EntiteJuridique::class;
    }

    /**
     * @see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#model-factories
     *
     * @todo add your default values here
     */
    protected function defaults(): array|callable
    {
        return [
            'code' => 'EJ-' . self::faker()->unique()->numerify('#####'),
            'nom' => self::faker()->company(),
            'adresse' => self::faker()->address(),
            'telephone' => self::faker()->phoneNumber(),
            'email' => self::faker()->companyEmail(),
            'is_actif' => true,
            'parametre' => [
                "addresses" => [
                    "affectationAgentFilesPaths" => "/mnt/bo/files/agents",
                    "chargementAutomatiqueDesAffectationEstActif" => self::faker()->boolean(30)
                ],
                "notifications" => [
                    "adminEmail" => "<EMAIL>",
                    "damResponssableDataAffectionEmail" => "<EMAIL>",
                    "notifierErreurAuResponssableDamAffectation" => self::faker()->boolean(20),
                    "notifierErreurAuAdminPourAffectation" => self::faker()->boolean(20),
                    "NotifierAdminDesErreurProduite" => self::faker()->boolean(10)
                ],
                "apiEndpoints" => [
                    "dataIntegratorApiInfo" => "https://data-integrator.chru-nancy.fr/api/nancy/collecteur",
                    "backendUrlInfo" => "https://supra-backend.chru-nancy.fr/api/nancy"
                ]
            ],
            'ldapConfig' => [
                'host' => 'ldap://selve.chu-nancy.fr:389',
                'domain' => 'U001PRD',
                'base_dn' => 'DC=chu-nancy,DC=fr',
                'last_check' => null,
                'is_active' => true,
                'service_account_email' => '<EMAIL>',
                'service_account_password' => 'wos54chunancy'
            ],
        ];
    }

    /**
     * @see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#initialization
     */
    protected function initialize(): static
    {
        return $this
            // ->afterInstantiate(function(EntiteJuridique $hopital): void {})
        ;
    }
}
