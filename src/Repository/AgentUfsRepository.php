<?php

namespace App\Repository;

use App\Entity\Praticien\AgentUfs;
use App\Entity\Structure\Ufs;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<AgentUfs>
 */
class AgentUfsRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, AgentUfs::class);
    }
    
    /**
     * Compte le nombre d'agents affectés à une UFS spécifique avec affectation principale
     * pendant une période donnée
     * 
     * @param Ufs $ufs L'UFS pour laquelle compter les affectations
     * @param \DateTimeInterface|null $dateDebut Date de début de la période
     * @param \DateTimeInterface|null $dateFin Date de fin de la période
     * @return int Le nombre d'agents affectés
     */
    public function countEffectifForUfs(Ufs $ufs, ?\DateTimeInterface $dateDebut = null, ?\DateTimeInterface $dateFin = null): int
    {
        $qb = $this->createQueryBuilder('a')
            ->select('COUNT(a.id)')
            ->andWhere('a.ufs = :ufs')
            ->andWhere('a.affectationPrincipale = :affectationPrincipale')
            ->setParameter('ufs', $ufs)
            ->setParameter('affectationPrincipale', 'O');
            
        if ($dateDebut !== null) {
            $qb->andWhere('a.date_fin IS NULL OR a.date_fin >= :dateDebut')
               ->setParameter('dateDebut', $dateDebut);
        }
        
        if ($dateFin !== null) {
            $qb->andWhere('a.date_debut IS NULL OR a.date_debut <= :dateFin')
               ->setParameter('dateFin', $dateFin);
        }
        
        return (int) $qb->getQuery()->getSingleScalarResult();
    }

    //    /**
    //     * @return AgentUfs[] Returns an array of AgentUfs objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('p')
    //            ->andWhere('p.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('p.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    //    public function findOneBySomeField($value): ?AgentUfs
    //    {
    //        return $this->createQueryBuilder('p')
    //            ->andWhere('p.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->getQuery()
    //            ->getOneOrNullResult()
    //        ;
    //    }
    
    /**
     * Trouve la dernière affectation connue d'un agent basée sur son hrUser
     * 
     * @param string $hrUser L'identifiant hrUser de l'agent
     * @return AgentUfs|null La dernière affectation de l'agent ou null si aucune n'est trouvée
     */
    public function findLastAffectationByHrUser(string $hrUser): ?AgentUfs
    {
        return $this->createQueryBuilder('au')
            ->join('au.agent', 'a')
            ->andWhere('a.hrUser = :hrUser')
            ->setParameter('hrUser', $hrUser)
            ->orderBy('au.date_debut', 'DESC')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }
}
