<?php

namespace App\Domain\Service;

use App\Entity\Praticien\Agent;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Firebase\JWT\ExpiredException;
use Firebase\JWT\SignatureInvalidException;
use Firebase\JWT\BeforeValidException;
use DomainException;
use InvalidArgumentException;
use UnexpectedValueException;
use DateTime;
use Exception;

/**
 * Service for JWT token generation and validation
 *
 * This class handles the creation and validation of JWT tokens for user authentication.
 * It uses industry best practices for token security and includes comprehensive claims.
 */
class JwtTokenService
{
    /**
     * Secret key used for token signing
     */
    private string $secretKey;

    /**
     * Algorithm used for token signing
     */
    private string $algorithm = 'HS256';

    /**
     * Token validity period in seconds (30 days)
     */
    private int $tokenValidity = 30 * 24 * 60 * 60;

    /**
     * Token issuer name
     */
    private string $issuer = 'supra_by_rpa_dev';

    /**
     * Token audience
     */
    private string $audience = 'supra_api';

    /**
     * Constructor
     *
     * Initializes the service with the secret key from environment variables
     * or throws an exception if no key is provided
     *
     * @throws Exception If no secret key is available
     */
    public function __construct()
    {
        // Get secret key from environment variable
        $secretKey = $_ENV['JWT_SECRET'] ?? null;

        // Check if secret key is available
        if (!$secretKey) {
            // In production, we should throw an exception
            // For backward compatibility, we'll use a fallback key with a warning
            $secretKey = 'fallback-secret-key-' . bin2hex(random_bytes(16));
            trigger_error('JWT_SECRET environment variable not set. Using insecure fallback key.', E_USER_WARNING);
        }

        $this->secretKey = $secretKey;
    }

    /**
     * Generate a JWT token for a user
     *
     * Creates a secure JWT token with comprehensive claims including:
     * - Issuer (iss): Who created the token
     * - Subject (sub): User ID
     * - Audience (aud): Who the token is intended for
     * - Issued At (iat): When the token was issued
     * - Expiration (exp): When the token expires
     * - Not Before (nbf): When the token starts being valid
     * - JWT ID (jti): Unique identifier for the token
     * - Custom claims: username, roles, email, name, code_ej (hospital code)
     *
     * @param Agent $user The user to generate a token for
     * @param string|null $code_ej The hospital code (optional)
     * @return string The generated JWT token
     */
    public function generate(Agent $user, ?string $code_ej = null): string
    {
        // Get current time
        $issuedAt = time();
        $expirationTime = $issuedAt + $this->tokenValidity;

        // Create a unique token ID
        $tokenId = bin2hex(random_bytes(16));

        // Build comprehensive payload with standard and custom claims
        $payload = [
            // Standard claims
            'iss' => $this->issuer,           // Issuer
            'aud' => $this->audience,         // Audience
            'sub' => $user->getId(),          // Subject (user ID)
            'iat' => $issuedAt,               // Issued At
            'exp' => $expirationTime,         // Expiration
            'nbf' => $issuedAt,               // Not Before
            'jti' => $tokenId,                // JWT ID

            // Custom claims with user information
            'username' => $user->getUserIdentifier(),
            'roles' => $user->getRoles(),
            'email' => $user->getEmail(),
            'name' => $user->getFullName(),
        ];

        // Add hospital code if provided
        if ($code_ej !== null) {
            $payload['code_ej'] = $code_ej;
        }

        // Encode and sign the token
        try {
            return JWT::encode($payload, $this->secretKey, $this->algorithm);
        } catch (Exception $e) {
            // Log the error
            error_log('Error generating JWT token: ' . $e->getMessage());

            // For backward compatibility, we'll try with minimal payload
            $minimalPayload = [
                'iss' => $this->issuer,
                'sub' => $user->getId(),
                'username' => $user->getUserIdentifier(),
                'roles' => $user->getRoles(),
                'iat' => $issuedAt,
                'exp' => $expirationTime
            ];

            // Add hospital code if provided
            if ($code_ej !== null) {
                $minimalPayload['code_ej'] = $code_ej;
            }

            return JWT::encode($minimalPayload, $this->secretKey, $this->algorithm);
        }
    }

    /**
     * Validate a JWT token
     *
     * Verifies the token signature, expiration, and other claims
     *
     * @param string $token The JWT token to validate
     * @return array The decoded token payload
     * @throws Exception If the token is invalid
     */
    public function validate(string $token): array
    {
        try {
            // Decode and verify the token
            $decoded = JWT::decode($token, new Key($this->secretKey, $this->algorithm));

            // Convert object to array
            return (array) $decoded;
        } catch (ExpiredException $e) {
            throw new Exception('Token expired', 401);
        } catch (SignatureInvalidException $e) {
            throw new Exception('Invalid token signature', 401);
        } catch (BeforeValidException $e) {
            throw new Exception('Token not yet valid', 401);
        } catch (DomainException | InvalidArgumentException | UnexpectedValueException $e) {
            throw new Exception('Invalid token', 401);
        }
    }

    /**
     * Get user ID from token
     *
     * Extracts the user ID (subject) from a token
     *
     * @param string $token The JWT token
     * @return string The user ID
     * @throws Exception If the token is invalid or doesn't contain a subject
     */
    public function getUserIdFromToken(string $token): string
    {
        $payload = $this->validate($token);

        if (!isset($payload['sub'])) {
            throw new Exception('Token does not contain user ID', 401);
        }

        return $payload['sub'];
    }

    /**
     * Get token expiration time
     *
     * @param string $token The JWT token
     * @return DateTime The expiration time
     * @throws Exception If the token is invalid or doesn't contain an expiration time
     */
    public function getTokenExpiration(string $token): DateTime
    {
        $payload = $this->validate($token);

        if (!isset($payload['exp'])) {
            throw new Exception('Token does not contain expiration time', 401);
        }

        return (new DateTime())->setTimestamp($payload['exp']);
    }

    /**
     * Check if token is expired
     *
     * @param string $token The JWT token
     * @return bool True if the token is expired, false otherwise
     */
    public function isTokenExpired(string $token): bool
    {
        try {
            $expiration = $this->getTokenExpiration($token);
            return $expiration < new DateTime();
        } catch (Exception $e) {
            return true;
        }
    }

    /**
     * Set token validity period
     *
     * @param int $seconds Validity period in seconds
     * @return self
     */
    public function setTokenValidity(int $seconds): self
    {
        $this->tokenValidity = $seconds;
        return $this;
    }
}
