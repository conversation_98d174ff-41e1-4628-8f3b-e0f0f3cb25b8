<?php

namespace App\Factory\Structure;

use App\Entity\Structure\Cr;
use Zenstruck\Foundry\Persistence\PersistentProxyObjectFactory;

/**
 * @extends PersistentProxyObjectFactory<Cr>
 */
final class CrFactory extends PersistentProxyObjectFactory
{
    /**
     * @see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#factories-as-services
     *
     * @todo inject services if required
     */
    public function __construct()
    {
    }

    public static function class(): string
    {
        return Cr::class;
    }

    /**
     * @see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#model-factories
     *
     * @todo add your default values here
     */
    protected function defaults(): array|callable
    {
        return [
            'etab' => self::faker()->regexify('[A-Z]{3}'),
            'crcode' => self::faker()->unique()->regexify('[A-Z0-9]{4}'),
            'datdeb' => self::faker()->dateTimeBetween('-1 year', 'now'),
            'datfin' => self::faker()->dateTimeBetween('+1 year', '+10 years'),
            'libelle' => substr(self::faker()->sentence(3), 0, 50),
            'nomresp' => (self::faker()->boolean(80)) ? substr(self::faker()->firstName(), 0, 20) : null,
            'polecode' => self::faker()->regexify('[A-Z0-9]{3}'),
            'pfuser' => self::faker()->regexify('[A-Z0-9]{8}'),
            'dmdacre' => self::faker()->dateTimeBetween('-1 year', 'now'),
            'dmdamaj' => self::faker()->dateTimeBetween('-1 month', 'now'),
            'pole' => PoleFactory::random(),
            'is_actif' => true,
            'validFrom' => self::faker()->dateTimeBetween('-1 year', 'now'),
            'validTo' => self::faker()->optional(0.3)->dateTimeBetween('now', '+1 year'),
            'periodeType' => self::faker()->randomElement(['HEBDOMADAIRE', 'MENSUEL', 'ANNUEL']),
            'source' => self::faker()->optional()->randomElement(['import', 'manuel', 'migration']),
        ];
    }

    /**
     * @see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#initialization
     */
    protected function initialize(): static
    {
        return $this
            // ->afterInstantiate(function(Cr $cr): void {})
        ;
    }
}
