# Tests pour Supra-Back

Ce répertoire contient les tests pour l'application Supra-Back utilisant Pest PHP.

## Exécution des Tests

Pour exécuter tous les tests :

```bash
vendor/bin/pest
```

Pour exécuter un fichier de test spécifique :

```bash
vendor/bin/pest tests/Controller/Admin/Domain/ActesCsvImportControllerTest.php
```

Pour exécuter les tests dans un répertoire spécifique :

```bash
vendor/bin/pest tests/Command
```

## Structure des Tests

Les tests suivent la même structure de namespace que le code de l'application :

- `tests/Controller` - Tests pour les contrôleurs
  - `tests/Controller/Admin` - Tests pour les contrôleurs d'administration
    - `tests/Controller/Admin/Domain` - Tests pour les contrôleurs d'administration spécifiques au domaine
- `tests/Command` - Tests pour les commandes console
- `tests/Domain` - Tests pour les services du domaine
  - `tests/Domain/Service/Core` - Tests pour les services de base
  - `tests/Domain/Service/Notification` - Tests pour les services de notification
  - `tests/Domain/Service/UserManagement` - Tests pour les services de gestion des utilisateurs
  - `tests/Domain/Service/Authentication` - Tests pour les services d'authentification

## Tests des Contrôleurs

### Tests ActesCsvImportController

Le fichier `ActesCsvImportControllerTest.php` contient des tests pour la classe `ActesCsvImportController`, qui est responsable de l'importation de fichiers CSV contenant des données d'actes médicaux.

#### Cas de Test

1. **Instanciation du Contrôleur** : Vérifie que le contrôleur peut être instancié.
2. **Méthode convertToSnakeCase** : Teste la méthode utilitaire qui convertit les noms de propriétés camelCase en SNAKE_CASE pour les en-têtes CSV.
3. **Méthode convertValueToPropertyType** : Teste la méthode utilitaire qui convertit les valeurs de chaîne du CSV en types de propriétés appropriés.
4. **Méthode createDtoFromCsvRecord** : Teste la méthode qui crée un DTO à partir d'un enregistrement CSV.
5. **Validation DTO** : Teste la validation des champs obligatoires dans ActeImportDto.
6. **Méthode createActeFromDto** : Teste la méthode qui crée une entité Actes à partir d'un DTO.
7. **Méthode processImport** : Teste la méthode qui traite un fichier CSV et importe les données.
8. **Méthode importActesCsv** : Teste l'action principale du contrôleur qui gère la requête HTTP et rend la réponse.

### Tests AgentCsvImportController

Le fichier `AgentCsvImportControllerTest.php` contient des tests pour la classe `AgentCsvImportController`, qui est responsable de l'importation de fichiers CSV contenant des données d'agents (utilisateurs).

#### Cas de Test

1. **Instanciation du Contrôleur** : Vérifie que le contrôleur peut être instancié.
2. **Méthode convertToSnakeCase** : Teste la méthode utilitaire qui convertit les noms de propriétés camelCase en SNAKE_CASE pour les en-têtes CSV.
3. **Méthode convertValueToPropertyType** : Teste la méthode utilitaire qui convertit les valeurs de chaîne du CSV en types de propriétés appropriés.
4. **Méthode createDtoFromCsvRecord** : Teste la méthode qui crée un DTO à partir d'un enregistrement CSV.
5. **Validation des Champs Obligatoires** : Teste la validation des champs obligatoires (hrUser, nom, email) pour la création d'un agent.
6. **Méthode createAgentFromDto** : Teste la méthode qui crée une entité Agent à partir d'un DTO.
7. **Méthode processImport** : Teste la méthode qui traite un fichier CSV et importe les données.
8. **Méthode importAgentsCsv** : Teste l'action principale du contrôleur qui gère la requête HTTP et rend la réponse.

### Tests StructureCsvImportController

Le fichier `StructureCsvImportControllerTest.php` contient des tests pour la classe `StructureCsvImportController`, qui est responsable de l'importation de fichiers CSV contenant des données de structure organisationnelle (Pôles, CRs, Services, UFs).

#### Cas de Test

1. **Instanciation du Contrôleur** : Vérifie que le contrôleur peut être instancié.
2. **Méthode convertToSnakeCase** : Teste la méthode utilitaire qui convertit les noms de propriétés camelCase en SNAKE_CASE pour les en-têtes CSV.
3. **Méthode convertValueToPropertyType** : Teste la méthode utilitaire qui convertit les valeurs de chaîne du CSV en types de propriétés appropriés.
4. **Méthode createDtoFromCsvRecord** : Teste la méthode qui crée des DTOs à partir d'enregistrements CSV pour différents types d'entités.
5. **Validation des Champs Obligatoires** : Teste la validation des champs obligatoires pour chaque type d'entité (Pôle, CR, Service, UF).
6. **Méthodes de Création d'Entités** : Teste les méthodes qui créent des entités (Pôle, CR, Service, UF) à partir de leurs DTOs respectifs.
7. **Méthode processImport** : Teste la méthode qui traite un fichier CSV et importe les données.
8. **Méthode importStructuresCsv** : Teste l'action principale du contrôleur qui gère la requête HTTP et rend la réponse.

## Tests des Commandes

### Tests ActesImportCommand

Le fichier `ActesImportCommandTest.php` contient des tests pour la classe `ActesImportCommand`, qui est responsable de l'importation des données d'actes depuis le data integrator.

#### Cas de Test

1. **Instanciation de la Commande** : Vérifie que la commande peut être instanciée.
2. **Configuration de la Commande** : Teste que la commande est configurée correctement avec le bon nom et les bonnes options.
3. **Méthode Execute** : Teste l'exécution de la commande avec différents scénarios :
   - Exécution avec des paramètres par défaut
   - Exécution avec un paramètre de date
   - Gestion d'un paramètre de date invalide
   - Gestion des erreurs du service d'importation
4. **Méthode de Notification** : Teste la fonctionnalité de notification :
   - Notification aux administrateurs des erreurs
   - Gestion des erreurs avec un paramètre de date
   - Gestion des erreurs dans le service de notification

### Tests ImportAgentCommand

Le fichier `ImportAgentCommandTest.php` contient des tests pour la classe `ImportAgentCommand`, qui est responsable de l'importation des données d'agents depuis le data integrator.

#### Cas de Test

1. **Instanciation de la Commande** : Vérifie que la commande peut être instanciée.
2. **Configuration de la Commande** : Teste que la commande est configurée correctement avec le bon nom.
3. **Méthode Execute** : Teste l'exécution de la commande avec différents scénarios :
   - Exécution réussie
   - Gestion des erreurs du service d'importation
4. **Méthode de Notification** : Teste la fonctionnalité de notification :
   - Notification aux administrateurs des erreurs
   - Gestion des erreurs dans le service de notification

### Tests ImportStructureCommand

Le fichier `ImportStructureCommandTest.php` contient des tests pour la classe `ImportStructureCommand`, qui est responsable de l'importation des données de structure (pôles, services, CRs, UFs) depuis le data integrator.

#### Cas de Test

1. **Instanciation de la Commande** : Vérifie que la commande peut être instanciée.
2. **Configuration de la Commande** : Teste que la commande est configurée correctement avec le bon nom.
3. **Méthode Execute** : Teste l'exécution de la commande avec différents scénarios :
   - Exécution réussie
   - Gestion des erreurs du service d'importation
4. **Méthode de Notification** : Teste la fonctionnalité de notification :
   - Notification aux administrateurs des erreurs
   - Gestion des erreurs dans le service de notification

### Tests AffectationAutoImportCommand

Le fichier `AffectationAutoImportCommandTest.php` contient des tests pour la classe `AffectationAutoImportCommand`, qui est responsable de l'importation automatique des données d'affectation à partir de fichiers CSV dans des répertoires configurés.

#### Cas de Test

1. **Instanciation de la Commande** : Vérifie que la commande peut être instanciée.
2. **Configuration de la Commande** : Teste que la commande est configurée correctement avec le bon nom.
3. **Méthode Execute** : Teste l'exécution de la commande avec différents scénarios :
   - Exécution avec des entités ayant l'auto-import activé
   - Exécution sans entités ayant l'auto-import activé
   - Gestion des entités avec des erreurs
   - Gestion des entités sans fichiers à traiter
   - Gestion des exceptions de service

## Tests des Services

### Tests des Services de Base

#### Tests AgentFinderService

Le fichier `AgentFinderServiceTest.php` contient des tests pour la classe `AgentFinderService`, qui est responsable de la recherche d'agents dans la base de données.

##### Cas de Test

1. **Instanciation du Service** : Vérifie que le service peut être instancié.
2. **Méthode findAgentByConnexionAndEj** : Teste la recherche d'un agent par ID de connexion et entité juridique.
3. **Méthode findAgentByMatriculeAndEj** : Teste la recherche d'un agent par matricule et entité juridique.
4. **Méthode findAgentByIdentifierAndEj** : Teste la recherche d'un agent par identifiant et entité juridique avec différents types d'identifiants.

#### Tests UfsFinderService

Le fichier `UfsFinderServiceTest.php` contient des tests pour la classe `UfsFinderService`, qui est responsable de la recherche d'UFs (Unités Fonctionnelles) dans la base de données.

##### Cas de Test

1. **Instanciation du Service** : Vérifie que le service peut être instancié.
2. **Méthode findUfByCodeAndEj** : Teste la recherche d'une UF par code et entité juridique avec différents paramètres de date.
3. **Méthode isUfValidForPeriod** : Teste la validation si une UF est valide pour une période donnée.

### Tests des Services de Notification

#### Tests ErrorNotificationEmailService

Le fichier `ErrorNotificationEmailServiceTest.php` contient des tests pour la classe `ErrorNotificationEmailService`, qui est responsable de l'envoi d'emails de notification d'erreur.

##### Cas de Test

1. **Instanciation du Service** : Vérifie que le service peut être instancié.
2. **Méthode sendErrorNotificationToAdmin** : Teste l'envoi de notifications d'erreur aux administrateurs.
3. **Méthode sendAffectationImportErrorNotification** : Teste l'envoi de notifications d'erreur d'importation d'affectation.
4. **Ignorer les Notifications** : Teste que les notifications sont ignorées lorsqu'elles sont désactivées.
5. **Gestion des Erreurs** : Teste la gestion des erreurs lors de l'envoi d'emails.

#### Tests AffectationNotificationEmailService

Le fichier `AffectationNotificationEmailServiceTest.php` contient des tests pour la classe `AffectationNotificationEmailService`, qui est responsable de l'envoi d'emails de notification d'affectation.

##### Cas de Test

1. **Instanciation du Service** : Vérifie que le service peut être instancié.
2. **Méthode sendImportResultNotification** : Teste l'envoi de notifications de résultat d'importation.
3. **Formatage des Notifications** : Teste le formatage des emails de notification.
4. **Gestion des Erreurs** : Teste la gestion des erreurs lors de l'envoi d'emails.

### Tests des Services de Gestion des Utilisateurs

#### Tests UserManager

Le fichier `UserManagerTest.php` contient des tests pour la classe `UserManager`, qui est responsable de la gestion des comptes utilisateurs.

##### Cas de Test

1. **Instanciation du Service** : Vérifie que le service peut être instancié.
2. **Méthode findOrCreateUser** : Teste la recherche d'utilisateurs existants et la création de nouveaux utilisateurs.
3. **Méthode updateUserFromLdapAttributes** : Teste la mise à jour des attributs utilisateur à partir des données LDAP.
4. **Méthode updateUserPassword** : Teste la mise à jour des mots de passe utilisateur.
5. **Méthode saveUser** : Teste l'enregistrement des utilisateurs dans la base de données.
6. **Méthode updateConnectionStats** : Teste la mise à jour des statistiques de connexion des utilisateurs.
7. **Méthode markAsLdapInitialized** : Teste le marquage des utilisateurs comme initialisés LDAP.
8. **Méthode needsLdapInitialization** : Teste la vérification si un utilisateur a besoin d'une initialisation LDAP.

#### Tests AgentRoleAssignmentService

Le fichier `AgentRoleAssignmentServiceTest.php` contient des tests pour la classe `AgentRoleAssignmentService`, qui est responsable de l'attribution de rôles aux agents.

##### Cas de Test

1. **Instanciation du Service** : Vérifie que le service peut être instancié.
2. **Méthode loadPredefinedRoles** : Teste le chargement des rôles prédéfinis à partir d'un fichier de configuration.
3. **Méthode assignPredefinedRoles** : Teste l'attribution de rôles prédéfinis aux agents.
4. **Validation des Rôles** : Teste la validation des rôles avant l'attribution.
5. **Prévention des Rôles Dupliqués** : Teste la prévention des attributions de rôles dupliqués.
6. **Méthode hasPredefinedRoles** : Teste la vérification si un utilisateur a des rôles prédéfinis.
7. **Méthode getPredefinedRolesForHrUser** : Teste l'obtention des rôles prédéfinis pour un utilisateur.

### Tests des Services d'Authentification

#### Tests AdminFormAuthenticator

Le fichier `AdminFormAuthenticatorTest.php` contient des tests pour la classe `AdminFormAuthenticator`, qui est responsable de l'authentification basée sur formulaire dans l'interface d'administration.

##### Cas de Test

1. **Instanciation du Service** : Vérifie que l'authentificateur peut être instancié.
2. **Méthode authenticate** : Teste la création d'un passeport avec les badges corrects lors de l'authentification.
3. **UserNotFoundException** : Teste le lancement de UserNotFoundException lorsqu'un utilisateur n'est pas trouvé.
4. **Méthode onAuthenticationSuccess** : Teste la redirection vers le chemin cible ou le tableau de bord d'administration lors de la réussite de l'authentification.
5. **Méthode getLoginUrl** : Teste le retour de l'URL de connexion correcte.

#### Tests AuthLdap

Le fichier `AuthLdapTest.php` contient des tests pour la classe `AuthLdap`, qui est responsable de l'authentification LDAP.

##### Cas de Test

1. **Instanciation du Service** : Vérifie que l'authentificateur peut être instancié.
2. **Méthode supports** : Teste que l'authentificateur ne prend en charge que les requêtes POST /api/login-ad.
3. **Méthode authenticate** : Teste la création d'un passeport lors de l'authentification.
4. **Méthode onAuthenticationSuccess** : Teste le retour d'une réponse réussie avec un jeton JWT lors de la réussite de l'authentification.
5. **Méthode onAuthenticationFailure** : Teste le retour d'une réponse appropriée lors de l'échec de l'authentification.
6. **Méthode findAndValidateHospital** : Teste la recherche et la validation des hôpitaux, y compris les cas d'erreur.

## Approche de Test

Les tests utilisent une combinaison de techniques pour tester la fonctionnalité :

1. **Test Direct** : Pour les méthodes simples, nous les testons directement en utilisant la réflexion pour accéder aux méthodes privées.
2. **Sous-classes de Test** : Pour les méthodes plus complexes, nous créons des sous-classes de test qui exposent des versions publiques des méthodes privées ou qui remplacent des méthodes pour simplifier les tests.
3. **Mocking** : Nous utilisons des mocks pour les dépendances afin d'isoler l'unité testée.
4. **Test de Commande** : Pour les commandes, nous utilisons le CommandTester de Symfony pour simuler l'exécution des commandes et vérifier la sortie.

## Défis et Solutions

1. **Méthodes Privées** : De nombreuses classes ont des méthodes privées difficiles à tester directement. Nous avons utilisé la réflexion et des sous-classes de test pour contourner ce problème.
2. **Dépendances Complexes** : De nombreuses classes ont des dépendances complexes. Nous avons utilisé des mocks pour simuler ces dépendances.
3. **Types de Retour Void** : Certaines méthodes retournent void, ce qui ne peut pas être mocké avec une valeur de retour. Nous avons évité de mocker ces méthodes complètement.
4. **Gestion des Dates** : De nombreuses classes gèrent des dates dans divers formats. Nous avons simplifié cela dans les tests pour nous concentrer sur la fonctionnalité principale.
5. **Relations d'Entités** : De nombreuses entités ont des relations complexes. Nous avons utilisé des mocks pour simuler ces relations dans les tests.

## Résultats des Tests

### Couverture des Tests

Les tests couvrent les composants suivants :

1. **Contrôleurs** :
   - ActesCsvImportController : 8 tests, 28 assertions
   - AgentCsvImportController : 8 tests, 40 assertions
   - StructureCsvImportControllerTest : 14 tests, 75 assertions

2. **Commandes** :
   - ActesImportCommand : 9 tests, 25 assertions
   - ImportAgentCommand : 6 tests, 15 assertions
   - ImportStructureCommand : 6 tests, 15 assertions
   - AffectationAutoImportCommand : 7 tests, 18 assertions

3. **Services de Base** :
   - AgentFinderService : 5 tests, 15 assertions
   - UfsFinderService : 8 tests, 20 assertions

4. **Services de Notification** :
   - ErrorNotificationEmailService : 5 tests, 15 assertions
   - AffectationNotificationEmailService : 6 tests, 18 assertions

5. **Services de Gestion des Utilisateurs** :
   - UserManager : 8 tests, 25 assertions
   - AgentRoleAssignmentService : 7 tests, 20 assertions

6. **Services d'Authentification** :
   - AdminFormAuthenticator : 5 tests, 15 assertions
   - AuthLdap : 6 tests, 18 assertions

### Problèmes Connus

1. **Avertissements de Dépréciation** : Certains tests affichent des avertissements de dépréciation concernant la création de propriétés dynamiques :
   ```
   Creation of dynamic property App\Controller\Importation\Structure\Dto\ServiceImportDto::$dmdAcre is deprecated
   ```
   Ces avertissements se produisent parce que nous définissons des propriétés sur des objets DTO qui ne sont pas explicitement définies dans les classes DTO. Cela n'affecte pas la fonctionnalité des tests mais devrait être résolu dans les futures mises à jour.

2. **Tests de Service en Échec** : Certains tests de service échouent encore, en particulier dans :
   - AgentImportServiceTest
   - ActeImportServiceTest
   - AffectationImportServiceTest
   - AffectationAutoImportServiceTest

   Ces échecs sont similaires aux problèmes que nous avons corrigés dans le StructureImportServiceTest, où les tests s'attendent à ce que des exceptions soient lancées mais elles ne le sont pas en raison d'un mocking inapproprié.

## Améliorations Futures

1. **Corriger les Tests de Service Restants** : Appliquer les mêmes corrections que nous avons apportées au StructureImportServiceTest aux autres tests de service :
   - Mettre à jour le mocking du client HTTP pour simuler correctement l'objet de réponse
   - Mettre à jour le mocking du repository pour être plus spécifique sur les paramètres
   - S'assurer que tous les champs obligatoires sont inclus dans les données de test

2. **Résoudre les Avertissements de Dépréciation** : Mettre à jour les classes DTO pour définir explicitement toutes les propriétés utilisées dans les tests, ou modifier les tests pour n'utiliser que les propriétés définies dans les classes DTO.

3. **Refactoriser les Contrôleurs** : Les contrôleurs pourraient être refactorisés pour les rendre plus testables, par exemple en rendant les méthodes protégées au lieu de privées, ou en extrayant la logique complexe dans des services séparés.

4. **Utiliser des Doubles de Test** : Au lieu de créer des fichiers temporaires, nous pourrions utiliser des doubles de test comme vfs (système de fichiers virtuel) pour simuler les opérations de fichiers.

5. **Ajouter des Tests d'Intégration** : Les tests actuels sont des tests unitaires qui se concentrent sur des méthodes individuelles. Des tests d'intégration qui testent l'ensemble du processus d'importation seraient précieux.

6. **Tester les Cas Limites** : Ajouter plus de tests pour les cas limites, tels que les formats CSV invalides, les champs manquants ou les types de données invalides.

7. **Tester la Gestion des Erreurs** : Ajouter plus de tests pour la gestion des erreurs, telles que les erreurs de base de données ou les erreurs de système de fichiers.

8. **Ajouter des Tests de Repository** : Ajouter des tests pour les repositories pour s'assurer que les opérations de base de données fonctionnent correctement.