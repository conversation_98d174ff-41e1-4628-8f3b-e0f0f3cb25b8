<?php

namespace App\Command;

use App\Entity\Structure\Tenant;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

/**
 * Commande pour créer un nouveau Tenant.
 * @Command php bin/console app:create:tenant <code> <nom> [description]
 * i.e php bin/console app:create:tenant TENANT1 "Mon Premier Tenant" "Description du tenant"
 */
#[AsCommand(
    name: 'app:create:tenant',
    description: 'Crée un nouveau Tenant',
)]
class CreateTenantCommand extends Command
{
    public function __construct(
        private readonly EntityManagerInterface $em,
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addArgument('code', InputArgument::REQUIRED, 'Code du tenant')
            ->addArgument('nom', InputArgument::REQUIRED, 'Nom du tenant')
            ->addArgument('description', InputArgument::OPTIONAL, 'Description du tenant');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        try {
            // Vérification si le tenant existe déjà
            $existingTenant = $this->em->getRepository(Tenant::class)
                ->findOneBy(['code' => $input->getArgument('code')]);

            if ($existingTenant) {
                $io->error('Un Tenant avec ce code existe déjà');
                return Command::FAILURE;
            }

            // Création du Tenant
            $tenant = new Tenant();
            $tenant->setCode($input->getArgument('code'));
            $tenant->setNom($input->getArgument('nom'));
            $tenant->setDescription($input->getArgument('description') ?? $input->getArgument('nom'));

            $this->em->persist($tenant);
            $this->em->flush();

            $io->success('Tenant créé avec succès');
            return Command::SUCCESS;

        } catch (\Throwable $e) {
            $io->error('Erreur lors de la création : ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}