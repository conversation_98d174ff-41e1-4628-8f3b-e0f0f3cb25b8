# Importation des actes

L'importation des actes s'effectue exclusivement via la commande CLI (sauf pour l'import depuis le site public, dans ce cas voir le fichier `src/State/Batch/ActesImportBatch.php`).:

```bash
php bin/console app:import:actes [--date=YYYY-MM-DD]
```
## Fonctionnement
L'importation des actes est gérée par la classe `Command/ActesImportCommand`. Cette commande utilise le service `Domain/Service/Importation/ActeImportService` pour traiter les données.

### Process
- La commande appelle le service ActeImportService.
- Le service interroge l'API du data_integrator pour rafraîchir et récupérer les actes (pagination, filtrage par date possible).
- Pour chaque acte importé:
- * Recherche de l'entité juridique (ejCode)
- * Recherche de l'agent dans le bon tenant
- * Recherche des UFs (principal, demande, intervention) avec gestion temporelle
- * Création ou mise à jour de l'acte (UPSERT)
- * Liaison des relations (agent, UFs)
- À la fin, les statistiques d'import (créés, mis à jour, erreurs) sont affichées dans la console.