<?php

namespace App\Factory\Structure;

use App\Entity\Structure\Pole;
use Zenstruck\Foundry\Persistence\PersistentProxyObjectFactory;

/**
 * @extends PersistentProxyObjectFactory<Pole>
 */
final class PoleFactory extends PersistentProxyObjectFactory
{
    /**
     * @see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#factories-as-services
     *
     * @todo inject services if required
     */
    public function __construct()
    {
    }

    public static function class(): string
    {
        return Pole::class;
    }

    /**
     * @see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#model-factories
     *
     * @todo add your default values here
     */
    protected function defaults(): array|callable
    {
        // Années et mois pour les dates (janvier à juin pour 2023, 2024, 2025)
        $years = [2023, 2024, 2025];
        $startMonths = [1, 2, 3]; // Janvier à Mars pour datdeb
        $endMonths = [4, 5, 6]; // Avril à Juin pour datfin
        
        // Choisir une année et des mois aléatoires
        $year = $years[array_rand($years)];
        $startMonth = $startMonths[array_rand($startMonths)];
        $endMonth = $endMonths[array_rand($endMonths)];
        
        // Créer les dates de début et de fin
        $startDay = random_int(1, 28); // Éviter les problèmes avec février
        $endDay = random_int(1, 28);
        $datdeb = new \DateTime("$year-$startMonth-$startDay");
        $datfin = new \DateTime("$year-$endMonth-$endDay");
        
        // S'assurer que datfin est après datdeb
        if ($datfin <= $datdeb) {
            // Si même année et mois de fin <= mois de début, passer à l'année suivante
            if ($year < 2025) {
                $datfin = new \DateTime(($year + 1) . "-$endMonth-$endDay");
            } else {
                // Si on est déjà en 2025, ajuster le jour
                $datfin = clone $datdeb;
                $datfin->modify('+1 month');
            }
        }
        
        return [
            'etab' => self::faker()->regexify('[A-Z]{3}'),
            'polecode' => self::faker()->unique()->regexify('[A-Z0-9]{3}'),
            'datdeb' => $datdeb,
            'datfin' => $datfin,
            'libelle' => substr(self::faker()->word() . ' Pôle', 0, 50),
            'pfuser' => self::faker()->regexify('[A-Z0-9]{8}'),
            'dmdacre' => $datdeb, // Même date que datdeb
            'dmdamaj' => new \DateTime(), // Date actuelle
            'hopital' => EntiteJuridiqueFactory::random(),
            'is_actif' => true,
            'validFrom' => $datdeb, // Même date que datdeb
            'validTo' => $datfin, // Même date que datfin
            'periodeType' => self::faker()->randomElement(['HEBDOMADAIRE', 'MENSUEL', 'ANNUEL']),
            'source' => self::faker()->optional()->randomElement(['import', 'manuel', 'migration']),
        ];
    }

    /**
     * @see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#initialization
     */
    protected function initialize(): static
    {
        return $this
            // ->afterInstantiate(function(Pole $pole): void {})
        ;
    }
}
