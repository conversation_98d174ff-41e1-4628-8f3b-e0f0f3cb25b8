<?php

namespace App\ApiResource\Structure;

use ApiPlatform\Doctrine\Orm\Filter\OrderFilter;
use ApiPlatform\Doctrine\Orm\Filter\SearchFilter;
use ApiPlatform\Doctrine\Orm\State\Options;
use ApiPlatform\Metadata\ApiFilter;
use ApiPlatform\Metadata\ApiProperty;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\GetCollection;
use ApiPlatform\Metadata\Link;
use App\ApiResource\Base\BaseDto;
use App\ApiResource\Trait\HorodatageDtoTrait;
use App\Entity\Structure\Pole;
use App\State\EntityClassDtoStateProcessor;
use App\State\EntityToDtoStateProvider;

#[ApiResource(
    shortName: 'pole',
    operations: [
        new Get(),
        new GetCollection(
            uriTemplate: '/poles',
        ),
        new GetCollection(
            uriTemplate: '/{ejcode}/poles',
            uriVariables: [
                'ejcode' => new Link(
                    description: 'Code de l\'entité juridique',
                    schema: ['type' => 'string'],
                    required: false
                )
            ]
        )
    ],
    paginationItemsPerPage: 1000, // 1000 pour une pagination coté front car il n'ya pas beaucoup de pole
    provider: EntityToDtoStateProvider::class,
    processor: EntityClassDtoStateProcessor::class,
    stateOptions: new Options(entityClass: Pole::class),
)]
class PoleDto extends BaseDto
{
    #[ApiProperty(description: "Établissement", example: "CHU")]
    #[ApiFilter(SearchFilter::class, strategy: 'partial')]
    public ?string $etab = null;

    #[ApiProperty(description: "Code du pôle", example: "P01")]
    #[ApiFilter(SearchFilter::class, strategy: 'partial')]
    public ?string $polecode = null;

    #[ApiProperty(description: "Date de début", example: "2023-01-01")]
    public ?\DateTimeInterface $datdeb = null;

    #[ApiProperty(description: "Date de fin", example: "2033-01-01")]
    public ?\DateTimeInterface $datfin = null;

    #[ApiProperty(description: "Libellé du pôle", example: "Pôle Chirurgie")]
    #[ApiFilter(SearchFilter::class, strategy: 'partial')]
    public ?string $libelle = null;

    #[ApiProperty(description: "Utilisateur PF", example: "USER123")]
    public ?string $pfuser = null;

    #[ApiProperty(description: "Date de création", example: "2023-01-01")]
    public ?\DateTimeInterface $dmdacre = null;

    #[ApiProperty(description: "Date de mise à jour", example: "2023-01-01")]
    public ?\DateTimeInterface $dmdamaj = null;

    #[ApiProperty(description: "Lien vers l'hôpital associé", example: "/api/hopitals/1")]
    public ?EntiteJuridiqueDto $hopital = null;

    use HorodatageDtoTrait;
}
