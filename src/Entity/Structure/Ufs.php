<?php

namespace App\Entity\Structure;

use App\Entity\Activite\Actes;
use App\Entity\Base\BaseEntity;
use App\Entity\Praticien\AgentUfs;
use App\Entity\Trait\HorodatageTrait;
use App\Repository\UfsRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: UfsRepository::class)]
class Ufs extends BaseEntity
{
    use HorodatageTrait;
    #[ORM\Column(length: 5, nullable: true)]
    private ?string $etab = null;

    #[ORM\Column(length: 4, nullable: true)]
    private ?string $ufcode = null;

    #[ORM\Column(type: 'date', nullable: true)]
    private ?\DateTimeInterface $datdeb = null;

    #[ORM\Column(type: 'date', nullable: true)]
    private ?\DateTimeInterface $datfin = null;

    #[ORM\Column(type: 'date', nullable: true)]
    private ?\DateTimeInterface $datclos = null;

    #[ORM\Column(length: 100, nullable: true)]
    private ?string $libelle = null;

    #[ORM\Column(length: 2, nullable: true)]
    private ?string $tacode = null;

    #[ORM\Column(length: 5, nullable: true)]
    private ?string $cdcode = null;

    #[ORM\Column(length: 10, nullable: true)]
    private ?string $lettrebudg = null;

    #[ORM\Column(length: 10, nullable: true)]
    private ?string $secode = null;

    #[ORM\Column(length: 10, nullable: true)]
    private ?string $cgcode = null;

    #[ORM\Column(length: 10, nullable: true)]
    private ?string $umcode = null;

    #[ORM\Column(length: 20, nullable: true)]
    private ?string $pfuser = null;

    #[ORM\Column(type: 'date', nullable: true)]
    private ?\DateTimeInterface $dmdacre = null;

    #[ORM\Column(type: 'date', nullable: true)]
    private ?\DateTimeInterface $dmdamaj = null;

    #[ORM\Column(length: 1, nullable: true)]
    private ?string $topmedical = null;

    #[ORM\Column(length: 10, nullable: true)]
    private ?string $crcode = null;

    #[ORM\Column(length: 10, nullable: true)]
    private ?string $sacode = null;

    #[ORM\ManyToOne(inversedBy: 'ufs')]
    #[ORM\JoinColumn(nullable: true)]
    private ?Cr $cr = null;

    #[ORM\ManyToOne(inversedBy: 'ufs')]
    #[ORM\JoinColumn(nullable: true)]
    private ?Service $service = null;


    /**
     * @var Collection<int, AgentUfs>
     */
    #[ORM\OneToMany(targetEntity: AgentUfs::class, mappedBy: "ufs", cascade: ["persist", "remove"])]
    private Collection $praticiensUfs;

    /**
     * @var Collection<int, Actes>
     */
    #[ORM\OneToMany(targetEntity: Actes::class, mappedBy: 'ufPrincipal')]
    private Collection $actesPrincipal;

    /**
     * @var Collection<int, Actes>
     */
    #[ORM\OneToMany(targetEntity: Actes::class, mappedBy: 'ufDemande')]
    private Collection $actesDemande;

    /**
     * @var Collection<int, Actes>
     */
    #[ORM\OneToMany(targetEntity: Actes::class, mappedBy: 'ufIntervention')]
    private Collection $actesIntervention;

    public function __construct()
    {
        parent::__construct();
        $this->praticiensUfs = new ArrayCollection();
        $this->actesPrincipal = new ArrayCollection();
        $this->actesDemande = new ArrayCollection();
        $this->actesIntervention = new ArrayCollection();
        $this->datdeb = new \DateTime();
        $this->datfin = (new \DateTime())->modify('+10 years');
        $this->dmdacre = new \DateTime();
        $this->dmdamaj = new \DateTime();
    }
    public function getUfcode(): ?string
    {
        return $this->ufcode;
    }

    public function setUfcode(?string $ufcode): static
    {
        $this->ufcode = $ufcode;
        return $this;
    }

    public function getEtab(): ?string
    {
        return $this->etab;
    }

    public function setEtab(?string $etab): static
    {
        $this->etab = $etab;
        return $this;
    }

    public function getDatdeb(): ?\DateTimeInterface
    {
        return $this->datdeb;
    }

    public function setDatdeb(?\DateTimeInterface $datdeb): static
    {
        $this->datdeb = $datdeb;
        return $this;
    }

    public function getDatfin(): ?\DateTimeInterface
    {
        return $this->datfin;
    }

    public function setDatfin(?\DateTimeInterface $datfin): static
    {
        $this->datfin = $datfin;
        return $this;
    }

    public function getDatclos(): ?\DateTimeInterface
    {
        return $this->datclos;
    }

    public function setDatclos(?\DateTimeInterface $datclos): static
    {
        $this->datclos = $datclos;
        return $this;
    }

    public function getLibelle(): ?string
    {
        return $this->libelle;
    }

    public function setLibelle(?string $libelle): static
    {
        $this->libelle = $libelle;
        return $this;
    }

    public function getTacode(): ?string
    {
        return $this->tacode;
    }

    public function setTacode(?string $tacode): static
    {
        $this->tacode = $tacode;
        return $this;
    }

    public function getCdcode(): ?string
    {
        return $this->cdcode;
    }

    public function setCdcode(?string $cdcode): static
    {
        $this->cdcode = $cdcode;
        return $this;
    }

    public function getLettrebudg(): ?string
    {
        return $this->lettrebudg;
    }

    public function setLettrebudg(?string $lettrebudg): static
    {
        $this->lettrebudg = $lettrebudg;
        return $this;
    }

    public function getSecode(): ?string
    {
        return $this->secode;
    }

    public function setSecode(?string $secode): static
    {
        $this->secode = $secode;
        return $this;
    }

    public function getCgcode(): ?string
    {
        return $this->cgcode;
    }

    public function setCgcode(?string $cgcode): static
    {
        $this->cgcode = $cgcode;
        return $this;
    }

    public function getUmcode(): ?string
    {
        return $this->umcode;
    }

    public function setUmcode(?string $umcode): static
    {
        $this->umcode = $umcode;
        return $this;
    }

    public function getPfuser(): ?string
    {
        return $this->pfuser;
    }

    public function setPfuser(?string $pfuser): static
    {
        $this->pfuser = $pfuser;
        return $this;
    }

    public function getDmdacre(): ?\DateTimeInterface
    {
        return $this->dmdacre;
    }

    public function setDmdacre(?\DateTimeInterface $dmdacre): static
    {
        $this->dmdacre = $dmdacre;
        return $this;
    }

    public function getDmdamaj(): ?\DateTimeInterface
    {
        return $this->dmdamaj;
    }

    public function setDmdamaj(?\DateTimeInterface $dmdamaj): static
    {
        $this->dmdamaj = $dmdamaj;
        return $this;
    }

    public function getTopmedical(): ?string
    {
        return $this->topmedical;
    }

    public function setTopmedical(?string $topmedical): static
    {
        $this->topmedical = $topmedical;
        return $this;
    }

    public function getCrcode(): ?string
    {
        return $this->crcode;
    }

    public function setCrcode(?string $crcode): static
    {
        $this->crcode = $crcode;
        return $this;
    }

    public function getSacode(): ?string
    {
        return $this->sacode;
    }

    public function setSacode(?string $sacode): static
    {
        $this->sacode = $sacode;
        return $this;
    }

    public function getCr(): ?Cr
    {
        return $this->cr;
    }

    public function setCr(?Cr $cr): static
    {
        $this->cr = $cr;

        return $this;
    }

    public function getService(): ?Service
    {
        return $this->service;
    }

    public function setService(?Service $service): static
    {
        $this->service = $service;

        return $this;
    }

    // Raccourcis pour avoir le pole
    public function getPolecode(): ?string
    {
        return $this->cr?->getPole()?->getPolecode();
    }

    /**
     * @return Collection<int, AgentUfs>
     */
    public function getPraticiensUfs(): Collection
    {
        return $this->praticiensUfs;
    }

    public function addPraticiensUf(AgentUfs $praticiensUf): static
    {
        if (!$this->praticiensUfs->contains($praticiensUf)) {
            $this->praticiensUfs->add($praticiensUf);
            $praticiensUf->setUfs($this);
        }

        return $this;
    }

    public function removePraticiensUf(AgentUfs $praticiensUf): static
    {
        if ($this->praticiensUfs->removeElement($praticiensUf)) {
            // set the owning side to null (unless already changed)
            if ($praticiensUf->getUfs() === $this) {
                $praticiensUf->setUfs(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Actes>
     */
    public function getActesPrincipal(): Collection
    {
        return $this->actesPrincipal;
    }

    public function addActesPrincipal(Actes $acte): static
    {
        if (!$this->actesPrincipal->contains($acte)) {
            $this->actesPrincipal->add($acte);
            $acte->setUfPrincipal($this);
        }

        return $this;
    }

    public function removeActesPrincipal(Actes $acte): static
    {
        if ($this->actesPrincipal->removeElement($acte)) {
            // set the owning side to null (unless already changed)
            if ($acte->getUfPrincipal() === $this) {
                $acte->setUfPrincipal(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Actes>
     */
    public function getActesDemande(): Collection
    {
        return $this->actesDemande;
    }

    public function addActesDemande(Actes $acte): static
    {
        if (!$this->actesDemande->contains($acte)) {
            $this->actesDemande->add($acte);
            $acte->setUfDemande($this);
        }

        return $this;
    }

    public function removeActesDemande(Actes $acte): static
    {
        if ($this->actesDemande->removeElement($acte)) {
            // set the owning side to null (unless already changed)
            if ($acte->getUfDemande() === $this) {
                $acte->setUfDemande(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Actes>
     */
    public function getActesIntervention(): Collection
    {
        return $this->actesIntervention;
    }

    public function addActesIntervention(Actes $acte): static
    {
        if (!$this->actesIntervention->contains($acte)) {
            $this->actesIntervention->add($acte);
            $acte->setUfIntervention($this);
        }

        return $this;
    }

    public function removeActesIntervention(Actes $acte): static
    {
        if ($this->actesIntervention->removeElement($acte)) {
            // set the owning side to null (unless already changed)
            if ($acte->getUfIntervention() === $this) {
                $acte->setUfIntervention(null);
            }
        }

        return $this;
    }
    public function __toString(): string
    {
        return sprintf(
            '%s (%s)',
            $this->libelle,
            $this->ufcode)
            ;
    }
}
