# 🚀 Faire une Migration Proprement (Dev & Prod)

## 1️⃣ Générer une Migration (💻 Dev)
Avant de générer une migration, assure-toi que ton schéma est bien à jour.

```sh
php bin/console doctrine:migrations:diff

# ⚠️ Attention En prod ( plustôt que `diff` ) : php bin/console make:migration
```

V<PERSON>rifie le fichier généré dans `migrations/` et assure-toi qu’il ne contient pas d'erreurs.

## 2️⃣ Appliquer la Migration (💻 Dev)
```sh
php bin/console doctrine:migrations:migrate
php bin/console doctrine:schema:validate
```

## 3️⃣ Déployer la Migration (🌍 Prod)
Sur le serveur de production :

1. **Mettre à jour le code**
   ```sh
   git pull origin main
   composer install --no-dev --optimize-autoloader
   ```

2. **Exécuter les migrations en mode sécurisé**
   ```sh
   php bin/console doctrine:migrations:migrate --no-interaction --env=prod
   ```

3. **Vérifier le schéma**
   ```sh
   php bin/console doctrine:schema:validate --env=prod
   ```

✅ **Astuce** : **Ne jamais exécuter `schema:update --force` en prod !** Toujours utiliser `migrations:migrate`.

📌 **Suivre cette procédure garantit des mises à jour fiables en dev et en prod !**

## Astuce : Faire une Migration à la Main

Si tu dois faire une migration manuelle, voici comment procéder :
```shell
php bin/console doctrine:schema:update --dump-sql
```
Puis :
```shell
php bin/console doctrine:schema:update --force
```