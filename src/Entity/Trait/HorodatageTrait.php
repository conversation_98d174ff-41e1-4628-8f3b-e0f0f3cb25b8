<?php

namespace App\Entity\Trait;

use App\Domain\Enum\PeriodeType;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

/**
 * Trait HorodatageTrait
 *
 * Ce trait permet d'ajouter un horodatage (`validFrom` et `validTo`)
 * aux entités qui nécessitent une gestion de l'historisation des données.
 *
 * - `validFrom` : Indique la date de début de validité de l'enregistrement.
 * - `validTo` : Indique la date de fin de validité, `null` si l'enregistrement est toujours actif.
 * - `periodeType` : Type de période (hebdomadaire, mensuel, annuel)
 *
 * Ce trait est utile pour suivre l'évolution des données sans écraser les anciennes valeurs.
 * Il est destiné aux entités nécessitant un suivi temporel.
 */
trait HorodatageTrait
{
    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $validFrom = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $validTo = null;

    #[ORM\Column(type: Types::STRING, length: 15, options: ['default' => PeriodeType::HEBDOMADAIRE])]
    private string $periodeType = PeriodeType::HEBDOMADAIRE;

    #[ORM\Column(type: "string", length: 50, nullable: true)]
    private ?string $source = null;

    public function getValidFrom(): ?\DateTimeInterface
    {
        return $this->validFrom;
    }

    public function getValidTo(): ?\DateTimeInterface
    {
        return $this->validTo;
    }

    public function setValidFrom(\DateTimeInterface $validFrom): self
    {
        // 🔥 Vérifier seulement si validTo a déjà été définie et que la nouvelle valeur pose problème
        if ($this->validTo && $validFrom > $this->validTo) {
            throw new \LogicException("Impossible de modifier la date d'entrée car la date de fin est plus ancienne.");
        }

        $this->validFrom = $validFrom;
        return $this;
    }


    public function setValidTo(?\DateTimeInterface $validTo): self
    {
        // 🔥 Vérifier seulement si la nouvelle valeur est incohérente
        if ($validTo && $this->validFrom && $validTo < $this->validFrom) {
            throw new \LogicException("Impossible de modifier la date de fin car elle est antérieure à la date de debut.");
        }

        $this->validTo = $validTo;
        return $this;
    }


    public function getPeriodeType(): string
    {
        return $this->periodeType;
    }

    public function setPeriodeType(string $periodeType): self
    {
        if (!PeriodeType::isValid($periodeType)) {
            throw new \InvalidArgumentException("😉 Période invalide : $periodeType");
        }

        $this->periodeType = $periodeType;
        return $this;
    }

    public function isActive(): bool
    {
        return $this->validTo === null || $this->validTo > new \DateTime();
    }

    /**
     * @param string $type
     * @return bool
     * verifie si une periode est valide
     * i.e if ($acte->isPeriode(PeriodeType::HEBDOMADAIRE)) { ... }
     */
    public function isPeriode(string $type): bool
    {
        return $this->periodeType === $type;
    }

    public function getSource(): ?string
    {
        return $this->source;
    }

    public function setSource(?string $source): self
    {
        $this->source = $source;
        return $this;
    }

    // pour debuguer facilement les valeur de ce trais
    // par exemple avec juste un coup de dump($acte) affichere une info claire
    public function __toString(): string
    {
        return sprintf(
            "Période: %s, ValidFrom: %s, ValidTo: %s",
            $this->periodeType,
            $this->validFrom?->format('Y-m-d') ?? '∞',
            $this->validTo?->format('Y-m-d') ?? '∞'
        );
    }

}