<?php

/**
 * Script pour peupler la table agent avec 100 agents fictifs
 * 
 * Ce script génère 100 agents fictifs avec des données aléatoires
 * et les enregistre dans la base de données.
 * 
 * Utilisation: php scripts/populate_agents.php
 */

// Chargement de l'environnement Symfony
require dirname(__DIR__).'/vendor/autoload.php';
require dirname(__DIR__).'/config/bootstrap.php';

use App\Entity\Praticien\Agent;
use App\Entity\Structure\EntiteJuridique;
use App\Repository\EntiteJuridiqueRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Output\ConsoleOutput;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpKernel\KernelInterface;

$output = new ConsoleOutput();
$kernel = new \App\Kernel($_SERVER['APP_ENV'], (bool) $_SERVER['APP_DEBUG']);
$kernel->boot();
$container = $kernel->getContainer();

/** @var EntityManagerInterface $entityManager */
$entityManager = $container->get('doctrine.orm.entity_manager');

/** @var EntiteJuridiqueRepository $entiteJuridiqueRepository */
$entiteJuridiqueRepository = $entityManager->getRepository(EntiteJuridique::class);

// Récupération ou création d'une entité juridique
$entiteJuridique = $entiteJuridiqueRepository->findOneBy([]);
if (!$entiteJuridique) {
    $output->writeln('Aucune entité juridique trouvée. Création d\'une entité juridique par défaut...');
    $entiteJuridique = new EntiteJuridique();
    $entiteJuridique->setCode('EJ001');
    $entiteJuridique->setNom('Hôpital Fictif');
    $entityManager->persist($entiteJuridique);
    $entityManager->flush();
    $output->writeln('Entité juridique créée avec succès.');
}

// Titres possibles
$titres = ['Dr', 'Pr', 'M', 'Mme'];

// Catégories possibles
$categories = ['PH', 'MCU-PH', 'PU-PH', 'CCA', 'FFI', 'Interne'];

// Génération de 100 agents fictifs
$output->writeln('Génération de 100 agents fictifs...');

for ($i = 100; $i <= 200; $i++) {
    // Création d'un nouvel agent
    $agent = new Agent();
    
    // Génération d'un nom et prénom aléatoire
    $nom = generateRandomLastName();
    $prenom = generateRandomFirstName();
    
    // Génération d'un email basé sur le nom et prénom
    $email = strtolower(transliterateToAscii($prenom) . '.' . transliterateToAscii($nom) . '@hopital-fictif.fr');
    
    // Génération d'un hrUser unique
    $hrUser = 'USER' . str_pad($i, 4, '0', STR_PAD_LEFT);
    
    // Assignation des valeurs aux propriétés de l'agent
    $agent->setNom($nom);
    $agent->setPrenom($prenom);
    $agent->setEmail($email);
    $agent->setHrUser($hrUser);
    $agent->setTitre($titres[array_rand($titres)]);
    $agent->setCategorie($categories[array_rand($categories)]);
    $agent->setEtablissement('Hôpital Fictif');
    
    // Dates aléatoires
    $dateVenue = new \DateTime();
    $dateVenue->modify('-' . rand(1, 10) . ' years');
    $agent->setDateVenue($dateVenue);
    
    // 20% de chance d'avoir une date de départ
    if (rand(1, 5) === 1) {
        $dateDepart = clone $dateVenue;
        $dateDepart->modify('+' . rand(1, 9) . ' years');
        $agent->setDateDepart($dateDepart);
    }
    
    $dateMaj = new \DateTime();
    $dateMaj->modify('-' . rand(1, 30) . ' days');
    $agent->setDateMaj($dateMaj);
    
    // Autres propriétés
    $agent->setCreateurFiche('Script de population');
    $agent->setIsAdmin(rand(1, 10) === 1); // 10% de chance d'être admin
    $agent->setIsAnesthesiste(rand(1, 5) === 1); // 20% de chance d'être anesthésiste
    
    // Relation avec l'entité juridique
    $agent->setHopital($entiteJuridique);
    
    // Génération d'un mot de passe (non utilisé en production)
    $agent->setPassword('$2y$13$' . bin2hex(random_bytes(20)));
    
    // Persistance de l'agent
    $entityManager->persist($agent);
    
    // Affichage du progrès
    if ($i % 10 === 0) {
        $output->writeln("$i agents générés...");
        // Flush tous les 10 agents pour éviter de surcharger la mémoire
        $entityManager->flush();
    }
}

// Flush final pour s'assurer que tous les agents sont enregistrés
$entityManager->flush();

$output->writeln('100 agents ont été générés avec succès !');

/**
 * Génère un nom de famille aléatoire
 */
function generateRandomLastName(): string {
    $lastNames = [
        'Martin', 'Bernard', 'Thomas', 'Petit', 'Robert', 'Richard', 'Durand', 'Dubois', 'Moreau', 'Laurent',
        'Simon', 'Michel', 'Lefebvre', 'Leroy', 'Roux', 'David', 'Bertrand', 'Morel', 'Fournier', 'Girard',
        'Bonnet', 'Dupont', 'Lambert', 'Fontaine', 'Rousseau', 'Vincent', 'Muller', 'Lefevre', 'Faure', 'Andre',
        'Mercier', 'Blanc', 'Guerin', 'Boyer', 'Garnier', 'Chevalier', 'Francois', 'Legrand', 'Gauthier', 'Garcia',
        'Perrin', 'Robin', 'Clement', 'Morin', 'Nicolas', 'Henry', 'Roussel', 'Mathieu', 'Gautier', 'Masson'
    ];
    
    return $lastNames[array_rand($lastNames)];
}

/**
 * Génère un prénom aléatoire
 */
function generateRandomFirstName(): string {
    $firstNames = [
        'Jean', 'Pierre', 'Michel', 'André', 'Philippe', 'René', 'Louis', 'Alain', 'Jacques', 'Bernard',
        'Marcel', 'Daniel', 'Roger', 'Robert', 'Claude', 'Paul', 'Christian', 'Henri', 'Georges', 'Nicolas',
        'Marie', 'Jeanne', 'Françoise', 'Monique', 'Catherine', 'Nathalie', 'Isabelle', 'Sylvie', 'Anne', 'Jacqueline',
        'Nicole', 'Sophie', 'Martine', 'Christiane', 'Madeleine', 'Christine', 'Suzanne', 'Denise', 'Yvonne', 'Claire',
        'Camille', 'Dominique', 'Claude', 'Stéphane', 'Alex', 'Charlie', 'Maxime', 'Sacha', 'Morgan', 'Lou'
    ];
    
    return $firstNames[array_rand($firstNames)];
}

/**
 * Convertit une chaîne avec accents en ASCII simple
 */
function transliterateToAscii(string $string): string {
    $string = transliterator_transliterate('Any-Latin; Latin-ASCII', $string);
    return preg_replace('/[^a-zA-Z0-9]/', '', $string);
}