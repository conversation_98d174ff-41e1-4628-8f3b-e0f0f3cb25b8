<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Erreur SUPRA détectée</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #dc3545; color: white; padding: 20px; border-radius: 5px; text-align: center; }
        .content { background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-top: 20px; }
        .error-box { background-color: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .context-box { background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0; border-left: 4px solid #007bff; }
        .data-box { background-color: white; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .footer { text-align: center; margin-top: 20px; font-size: 12px; color: #666; }
        .label { font-weight: bold; color: #495057; }
        .value { margin-left: 10px; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚨 Erreur SUPRA détectée</h1>
        <p>Entité Juridique: {{ entiteJuridique.code }}</p>
    </div>

    <div class="content">
        <h2>Détails de l'erreur</h2>
        <p>Une erreur s'est produite dans le système SUPRA le {{ timestamp|date('d/m/Y à H:i:s') }}.</p>

        <div class="context-box">
            <h4 style="color: #0056b3; margin-top: 0;">📍 Contexte</h4>
            <p><span class="label">Module :</span><span class="value">{{ context }}</span></p>
            <p><span class="label">Entité Juridique :</span><span class="value">{{ entiteJuridique.code }}</span></p>
            <p><span class="label">Horodatage :</span><span class="value">{{ timestamp|date('d/m/Y H:i:s') }}</span></p>
        </div>

        <div class="error-box">
            <h4 style="color: #721c24; margin-top: 0;">❌ Message d'erreur</h4>
            <p style="color: #721c24; font-family: monospace; background-color: white; padding: 10px; border-radius: 3px;">{{ errorMessage }}</p>
        </div>

        {% if additionalData is not empty %}
        <div class="data-box">
            <h4 style="margin-top: 0;">📋 Informations supplémentaires</h4>

            {% if additionalData.type is defined %}
            <p><span class="label">Type d'opération :</span><span class="value">{{ additionalData.type }}</span></p>
            {% endif %}

            {% if additionalData.endpoint is defined %}
            <p><span class="label">Point d'entrée :</span><span class="value">{{ additionalData.endpoint }}</span></p>
            {% endif %}

            {% if additionalData.userInfo is defined and additionalData.userInfo is not empty %}
            <p><span class="label">Utilisateur :</span><span class="value">{{ additionalData.userInfo }}</span></p>
            {% endif %}

            {% if additionalData.requestData is defined and additionalData.requestData is not empty %}
            <h5>Données de la requête :</h5>
            <pre>{{ additionalData.requestData|json_encode(constant('JSON_PRETTY_PRINT')) }}</pre>
            {% endif %}
        </div>
        {% endif %}

        <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h4 style="color: #856404; margin-top: 0;">⚠️ Actions recommandées</h4>
            <ul style="color: #856404;">
                <li>Vérifiez les logs système pour plus de détails</li>
                <li>Contrôlez la configuration de l'entité juridique</li>
                <li>Vérifiez la connectivité réseau et les services externes</li>
                <li>Si le problème persiste, contactez l'équipe technique</li>
            </ul>
        </div>

        <div style="margin-top: 20px;">
            <p><strong>Configuration notifications :</strong></p>
            <ul>
                <li>Admin Email : {{ entiteJuridique.adminEmail }}</li>
                <li>Notifications erreurs activées : {{ entiteJuridique.notifierAdminDesErreurProduite ? 'Oui' : 'Non' }}</li>
            </ul>
        </div>
    </div>

    <div class="footer">
        <p>Cette notification a été générée automatiquement par le système SUPRA.<br>
        Entité Juridique: {{ entiteJuridique.code }}<br>
        Pour désactiver ces notifications, modifiez le paramètre "NotifierAdminDesErreurProduite" dans l'administration.</p>
    </div>
</body>
</html>
