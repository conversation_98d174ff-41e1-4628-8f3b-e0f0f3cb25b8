<?php

namespace App\Domain\Service\Security;

use App\Domain\Exception\Security\TooManyLoginAttemptsException;
use App\Entity\Security\LoginAttempt;
use App\Repository\Security\LoginAttemptRepository;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\RequestStack;

/**
 * Service to track and manage login attempts for rate limiting and security monitoring
 */
class LoginAttemptService
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private LoginAttemptRepository $loginAttemptRepository,
        private RequestStack $requestStack,
        private LoggerInterface $securityLogger
    ) {
    }

    /**
     * Record a login attempt
     */
    public function recordLoginAttempt(string $email, bool $success): void
    {
        $request = $this->requestStack->getCurrentRequest();
        
        if (!$request) {
            return;
        }
        
        $ipAddress = $this->getClientIp($request);
        $userAgent = $request->headers->get('User-Agent');
        
        $loginAttempt = new LoginAttempt($ipAddress, $email, $userAgent);
        $loginAttempt->setSuccess($success);
        
        $this->entityManager->persist($loginAttempt);
        $this->entityManager->flush();
        
        // Log the login attempt
        if ($success) {
            $this->securityLogger->info('Successful login', [
                'email' => $email,
                'ip' => $ipAddress,
                'userAgent' => $userAgent
            ]);
        } else {
            $this->securityLogger->warning('Failed login attempt', [
                'email' => $email,
                'ip' => $ipAddress,
                'userAgent' => $userAgent
            ]);
        }
    }

    /**
     * Check if login is allowed based on IP address and email
     * OPTIMISATION: Vérification rapide avec cache
     *
     * @throws TooManyLoginAttemptsException if login is not allowed
     */
    public function checkLoginAllowed(string $email): void
    {
        $request = $this->requestStack->getCurrentRequest();

        if (!$request) {
            return;
        }

        $ipAddress = $this->getClientIp($request);

        // OPTIMISATION: Vérification rapide avec une seule requête
        $rateLimitInfo = $this->loginAttemptRepository->getRateLimitInfo($ipAddress, $email);

        if (!$rateLimitInfo['allowed']) {
            $this->securityLogger->warning('Login attempt blocked due to rate limiting', [
                'email' => $email,
                'ip' => $ipAddress,
                'timeUntilAllowed' => $rateLimitInfo['timeUntilAllowed']
            ]);

            throw new TooManyLoginAttemptsException($rateLimitInfo['timeUntilAllowed']);
        }
    }

    /**
     * Clean up old login attempts
     * 
     * @return int Number of deleted records
     */
    public function cleanupOldAttempts(): int
    {
        $deletedCount = $this->loginAttemptRepository->cleanupOldAttempts();
        
        $this->securityLogger->info('Cleaned up old login attempts', [
            'deletedCount' => $deletedCount
        ]);
        
        return $deletedCount;
    }

    /**
     * Get the client IP address from the request
     */
    private function getClientIp($request): string
    {
        // Try to get the real client IP if behind a proxy
        $ipAddress = $request->getClientIp();
        
        // Sanitize and validate IP address
        if (filter_var($ipAddress, FILTER_VALIDATE_IP) === false) {
            // If invalid IP, use a placeholder
            $ipAddress = '0.0.0.0';
        }
        
        return $ipAddress;
    }
}