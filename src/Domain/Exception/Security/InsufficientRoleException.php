<?php

namespace App\Domain\Exception\Security;

use Symfony\Component\Security\Core\Exception\AuthenticationException;

/**
 * Exception thrown when a user has valid credentials but insufficient roles
 */
class InsufficientRoleException extends AuthenticationException
{
    private array $requiredRoles;
    private array $userRoles;

    public function __construct(
        array $requiredRoles = ['ROLE_ADMIN'], 
        array $userRoles = [], 
        string $message = 'Vous n\'avez pas les droits suffisants pour accéder à l\'espace d\'administration.', 
        array $data = []
    ) {
        parent::__construct($message, 0, null, $data);
        $this->requiredRoles = $requiredRoles;
        $this->userRoles = $userRoles;
    }

    /**
     * Get the required roles
     */
    public function getRequiredRoles(): array
    {
        return $this->requiredRoles;
    }

    /**
     * Get the user's roles
     */
    public function getUserRoles(): array
    {
        return $this->userRoles;
    }

    /**
     * Get the message key for translation
     */
    public function getMessageKey(): string
    {
        return 'Vous n\'avez pas les droits suffisants pour accéder à l\'espace d\'administration.';
    }
}