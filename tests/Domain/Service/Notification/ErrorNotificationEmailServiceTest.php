<?php

namespace App\Tests\Domain\Service\Notification;

use App\Domain\Service\Notification\ErrorNotificationEmailService;
use App\Entity\Structure\EntiteJuridique;
use Psr\Log\LoggerInterface;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Email;
use Twig\Environment;

it('can be instantiated', function () {
    $mailer = $this->createMock(MailerInterface::class);
    $twig = $this->createMock(Environment::class);
    $logger = $this->createMock(LoggerInterface::class);
    
    $service = new ErrorNotificationEmailService($mailer, $twig, $logger);
    
    expect($service)->toBeInstanceOf(ErrorNotificationEmailService::class);
});

it('does not send notification when notifications are disabled', function () {
    // Create mocks
    $mailer = $this->createMock(MailerInterface::class);
    $twig = $this->createMock(Environment::class);
    $logger = $this->createMock(LoggerInterface::class);
    $entiteJuridique = $this->createMock(EntiteJuridique::class);
    
    // Configure mocks
    $entiteJuridique->method('isNotifierAdminDesErreurProduite')
        ->willReturn(false);
    
    $entiteJuridique->method('getCode')
        ->willReturn('EJ001');
    
    // Expect logger to be called
    $logger->expects($this->once())
        ->method('info')
        ->with(
            'Notifications d\'erreur admin désactivées, aucun email envoyé',
            $this->callback(function ($context) {
                return isset($context['entiteJuridique']) && $context['entiteJuridique'] === 'EJ001';
            })
        );
    
    // Expect mailer not to be called
    $mailer->expects($this->never())
        ->method('send');
    
    // Create service and call method
    $service = new ErrorNotificationEmailService($mailer, $twig, $logger);
    $service->sendErrorNotificationToAdmin($entiteJuridique, 'Test Context', 'Test Error');
});

it('does not send notification when admin email is invalid', function () {
    // Create mocks
    $mailer = $this->createMock(MailerInterface::class);
    $twig = $this->createMock(Environment::class);
    $logger = $this->createMock(LoggerInterface::class);
    $entiteJuridique = $this->createMock(EntiteJuridique::class);
    
    // Configure mocks
    $entiteJuridique->method('isNotifierAdminDesErreurProduite')
        ->willReturn(true);
    
    $entiteJuridique->method('getCode')
        ->willReturn('EJ001');
    
    $entiteJuridique->method('getAdminEmail')
        ->willReturn('invalid-email');
    
    // Expect logger to be called
    $logger->expects($this->once())
        ->method('warning')
        ->with(
            'Email admin invalide, impossible d\'envoyer la notification d\'erreur',
            $this->callback(function ($context) {
                return isset($context['entiteJuridique']) && $context['entiteJuridique'] === 'EJ001';
            })
        );
    
    // Expect mailer not to be called
    $mailer->expects($this->never())
        ->method('send');
    
    // Create service and call method
    $service = new ErrorNotificationEmailService($mailer, $twig, $logger);
    $service->sendErrorNotificationToAdmin($entiteJuridique, 'Test Context', 'Test Error');
});

it('sends notification when notifications are enabled and email is valid', function () {
    // Create mocks
    $mailer = $this->createMock(MailerInterface::class);
    $twig = $this->createMock(Environment::class);
    $logger = $this->createMock(LoggerInterface::class);
    $entiteJuridique = $this->createMock(EntiteJuridique::class);
    
    // Configure mocks
    $entiteJuridique->method('isNotifierAdminDesErreurProduite')
        ->willReturn(true);
    
    $entiteJuridique->method('getCode')
        ->willReturn('EJ001');
    
    $entiteJuridique->method('getAdminEmail')
        ->willReturn('<EMAIL>');
    
    $twig->method('render')
        ->willReturn('<html>Email content</html>');
    
    // Expect mailer to be called
    $mailer->expects($this->once())
        ->method('send')
        ->with($this->callback(function ($email) {
            return $email instanceof Email &&
                   $email->getSubject() === '[EJ001] ERREUR SUPRA - Test Context';
        }));
    
    // Expect logger to be called for success
    $logger->expects($this->once())
        ->method('info')
        ->with(
            'Email de notification d\'erreur envoyé avec succès',
            $this->callback(function ($context) {
                return isset($context['subject']) && 
                       $context['subject'] === '[EJ001] ERREUR SUPRA - Test Context';
            })
        );
    
    // Create service and call method
    $service = new ErrorNotificationEmailService($mailer, $twig, $logger);
    $service->sendErrorNotificationToAdmin($entiteJuridique, 'Test Context', 'Test Error');
});

it('handles mailer exceptions gracefully', function () {
    // Create mocks
    $mailer = $this->createMock(MailerInterface::class);
    $twig = $this->createMock(Environment::class);
    $logger = $this->createMock(LoggerInterface::class);
    $entiteJuridique = $this->createMock(EntiteJuridique::class);
    
    // Configure mocks
    $entiteJuridique->method('isNotifierAdminDesErreurProduite')
        ->willReturn(true);
    
    $entiteJuridique->method('getCode')
        ->willReturn('EJ001');
    
    $entiteJuridique->method('getAdminEmail')
        ->willReturn('<EMAIL>');
    
    $twig->method('render')
        ->willReturn('<html>Email content</html>');
    
    // Mailer throws exception
    $mailer->method('send')
        ->willThrowException(new \Exception('Mailer error'));
    
    // Expect logger to be called for error
    $logger->expects($this->once())
        ->method('error')
        ->with(
            'Erreur lors de l\'envoi d\'email de notification d\'erreur',
            $this->callback(function ($context) {
                return isset($context['error']) && 
                       $context['error'] === 'Mailer error';
            })
        );
    
    // Create service and call method
    $service = new ErrorNotificationEmailService($mailer, $twig, $logger);
    
    // This should not throw an exception
    $service->sendErrorNotificationToAdmin($entiteJuridique, 'Test Context', 'Test Error');
});

it('sends affectation import error notification', function () {
    // Create mocks
    $mailer = $this->createMock(MailerInterface::class);
    $twig = $this->createMock(Environment::class);
    $logger = $this->createMock(LoggerInterface::class);
    $entiteJuridique = $this->createMock(EntiteJuridique::class);
    
    // Configure mocks
    $entiteJuridique->method('isNotifierAdminDesErreurProduite')
        ->willReturn(true);
    
    $entiteJuridique->method('getCode')
        ->willReturn('EJ001');
    
    $entiteJuridique->method('getAdminEmail')
        ->willReturn('<EMAIL>');
    
    $twig->method('render')
        ->willReturn('<html>Email content</html>');
    
    // Expect mailer to be called
    $mailer->expects($this->once())
        ->method('send')
        ->with($this->callback(function ($email) {
            return $email instanceof Email &&
                   $email->getSubject() === '[EJ001] ERREUR SUPRA - Importation d\'affectations';
        }));
    
    // Create service and call method
    $service = new ErrorNotificationEmailService($mailer, $twig, $logger);
    $service->sendAffectationImportErrorNotification(
        $entiteJuridique,
        'Test Error',
        ['param1' => 'value1'],
        'User Info'
    );
});