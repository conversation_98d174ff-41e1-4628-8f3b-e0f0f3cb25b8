<?php

namespace App\Factory\Activite;

use App\Domain\Enum\PeriodeType;
use App\Entity\Activite\Actes;
use App\Factory\Praticien\AgentFactory;
use App\Factory\Structure\UfsFactory;
use Zenstruck\Foundry\Persistence\PersistentProxyObjectFactory;

/**
 * @extends PersistentProxyObjectFactory<Actes>
 */
final class ActesFactory extends PersistentProxyObjectFactory
{
    public function __construct()
    {
    }

    public static function class(): string
    {
        return Actes::class;
    }

    protected function defaults(): array|callable
    {
        // Années et mois pour les dates (janvier à juin pour 2023, 2024, 2025)
        $years = [2023, 2024, 2025];
        $months = [1, 2, 3, 4, 5, 6]; // Janvier à Juin
        
        // Choisir une année et un mois aléatoires
        $year = $years[array_rand($years)];
        $month = $months[array_rand($months)];
        
        // Créer la date de réalisation
        $day = random_int(1, 28); // Éviter les problèmes avec février
        $dateRealisation = new \DateTime("$year-$month-$day");
        
        // Extraire l'année et le mois de la date de réalisation
        $annee = (int)$dateRealisation->format('Y');
        $mois = (int)$dateRealisation->format('m');
        
        // Dates de validité
        $validFrom = clone $dateRealisation;
        $validFrom->modify('-1 day'); // Valide à partir de la veille
        
        $validTo = null;
        if (self::faker()->boolean(70)) {
            $validTo = clone $dateRealisation;
            $validTo->modify('+1 month'); // Valide jusqu'à un mois après
        }

        return [
            'internum' => self::faker()->regexify('[0-9]{8}'),
            'code' => self::faker()->regexify('[A-Z0-9]{10}'),
            'description' => self::faker()->sentence(),
            'typeActe' => self::faker()->randomElement(['CCAM', 'NGAP', 'LABO']),
            'annee' => $annee,
            'mois' => $mois,
            'date_realisation' => $dateRealisation,
            'semaineIso' => (int)$dateRealisation->format('W'), // Numéro de semaine ISO
            'nombre_de_realisation' => self::faker()->numberBetween(1, 10),
            'typeVenue' => self::faker()->numberBetween(1, 5),
            'libTypeVenue' => self::faker()->randomElement(['Consultation', 'Hospitalisation', 'Urgence', 'Ambulatoire', 'Externe']),
            'agent' => AgentFactory::random(),
            'ufPrincipal' => UfsFactory::random(),
            'ufDemande' => self::faker()->boolean(70) ? UfsFactory::random() : null,
            'ufIntervention' => self::faker()->boolean(70) ? UfsFactory::random() : null,
            'icrA' => self::faker()->numberBetween(10, 100),
            'coefficient' => self::faker()->randomFloat(2, 0, 5),
            'lettreCoef' => self::faker()->randomElement(['', 'A', 'B', 'C', 'D']),
            'regroupement' => self::faker()->optional()->word(),
            'activite' => self::faker()->optional()->randomElement(['1', '2', '3', '4']),
            'activiteLib' => self::faker()->optional()->randomElement(['MCO', 'SSR', 'PSY', 'HAD']),
            'validFrom' => $validFrom,
            'validTo' => $validTo,
            'periodeType' => self::faker()->randomElement(PeriodeType::VALUES),
            'source' => self::faker()->optional()->randomElement(['CNAM', 'RPPS', 'MANUAL']),
        ];
    }

    protected function initialize(): static
    {
        return $this;
    }
}
