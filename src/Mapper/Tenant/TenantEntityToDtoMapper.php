<?php

namespace App\Mapper\Tenant;

use App\ApiResource\Structure\TenantDto;
use App\Entity\Structure\Tenant;
use Symfonycasts\MicroMapper\AsMapper;
use Symfonycasts\MicroMapper\MapperInterface;

#[AsMapper(from: Tenant::class, to: TenantDto::class)]
class TenantEntityToDtoMapper implements MapperInterface
{
    public function load(object $from, string $toClass, array $context): object
    {
        $tenant = $from;
        assert($tenant instanceof Tenant);

        $dto = new TenantDto();
        $dto->id = $tenant->getId(); // On ne charge que l'ID ici

        return $dto;
    }

    public function populate(object $from, object $to, array $context): object
    {
        $tenant = $from;
        $dto = $to;
        assert($tenant instanceof Tenant);
        assert($dto instanceof TenantDto);

        $dto->nom = $tenant->getNom();
        $dto->description = $tenant->getDescription();
        $dto->dateCreation = $tenant->getDateCreation();

        return $dto;
    }
}
