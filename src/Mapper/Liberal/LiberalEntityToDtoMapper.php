<?php

namespace App\Mapper\Liberal;

use App\ApiResource\Activite\LiberalDto;
use App\Entity\Activite\Liberal;
use App\Repository\AgentUfsRepository;
use Symfonycasts\MicroMapper\AsMapper;
use Symfonycasts\MicroMapper\MapperInterface;

#[AsMapper(from: Liberal::class, to: LiberalDto::class)]
class LiberalEntityToDtoMapper implements MapperInterface
{
    public function __construct(
        private AgentUfsRepository $agentUfsRepository
    ) {}

    public function load(object $from, string $toClass, array $context): object
    {
        $liberal = $from;
        assert($liberal instanceof Liberal);

        $dto = new LiberalDto();
        $dto->id = $liberal->getId(); // On charge uniquement l'ID ici

        return $dto;
    }

    public function populate(object $from, object $to, array $context): object
    {
        $liberal = $from;
        $dto = $to;
        assert($liberal instanceof Liberal);
        assert($dto instanceof LiberalDto);

        $dto->nomActe = $liberal->getNomActe();
        $dto->dateRealisation = $liberal->getDateRealisation();
        $dto->dateDebut = $liberal->getDateDebut();
        $dto->dateFin = $liberal->getDateFin();
        $dto->tarif = $liberal->getTarif();
        $dto->codeActe = $liberal->getCodeActe();
        $dto->typeActe = $liberal->getTypeActe();

        // Champs du trait HorodatageTrait
        $dto->validFrom = $liberal->getValidFrom();
        $dto->validTo = $liberal->getValidTo();
        $dto->periodeType = $liberal->getPeriodeType();
        $dto->source = $liberal->getSource();

        $agent = $liberal->getAgent();
        $dto->agentHrU = $agent?->getHrUser();
        
        // Récupération de la dernière affectation de l'agent
        if ($dto->agentHrU) {
            $lastAffectation = $this->agentUfsRepository->findLastAffectationByHrUser($dto->agentHrU);
            if ($lastAffectation && $lastAffectation->getUfs()) {
                $ufs = $lastAffectation->getUfs();
                $ufCode = $ufs->getUfcode();
                $ufLibelle = $ufs->getLibelle();
                
                if ($ufCode && $ufLibelle) {
                    $dto->derniereAffectionDeAgent = $ufCode . ' - ' . $ufLibelle;
                } elseif ($ufCode) {
                    $dto->derniereAffectionDeAgent = $ufCode;
                } elseif ($ufLibelle) {
                    $dto->derniereAffectionDeAgent = $ufLibelle;
                }
            }
        }
        
        // Récupération du nom, prénom et titre de l'agent
        if ($agent) {
            $titre = $agent->getTitre();
            $prenom = $agent->getPrenom();
            $nom = $agent->getNom();
            
            $nomPrenomTitre = '';
            if ($titre) {
                $nomPrenomTitre .= $titre . ' ';
            }
            if ($prenom) {
                $nomPrenomTitre .= $prenom . ' ';
            }
            if ($nom) {
                $nomPrenomTitre .= $nom;
            }
            
            $dto->agentNomPrenomTitre = trim($nomPrenomTitre) ?: null;
        }

        return $dto;
    }
}
