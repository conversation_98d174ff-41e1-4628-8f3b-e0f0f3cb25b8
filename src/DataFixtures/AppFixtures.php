<?php

namespace App\DataFixtures;



use App\Factory\Activite\ActesFactory;
use App\Factory\Activite\GardesAstreintesFactory;
use App\Factory\Activite\LiberalFactory;
use App\Factory\Activite\SigapsFactory;
use App\Factory\Importation\ImportationFactory;
use App\Factory\Praticien\AgentFactory;
use App\Factory\Praticien\AgentHopitalRoleFactory;
use App\Factory\Praticien\AgentUfsFactory;
use App\Factory\Structure\EntiteJuridiqueFactory;
use App\Factory\Structure\PoleFactory;
use App\Factory\Structure\CrFactory;
use App\Factory\Structure\ServiceFactory;
use App\Factory\Structure\TenantFactory;
use App\Factory\Structure\UfsFactory;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;

/**
 * Fixtures pour peupler la base de données avec des données de test.
 *
 * Cette classe utilise le pattern Factory pour générer des données de test cohérentes
 * pour toutes les entités de l'application (structures, agents, activités, importations).
 *
 * UTILISATION:
 * Pour charger ces fixtures et peupler la base de données:
 *
 * ```
 * php bin/console doctrine:fixtures:load
 * ```
 *
 * Options utiles:
 * --append : Ajoute les données sans purger la base
 * --purge-with-truncate : Vide les tables plus rapidement
 *
 * Exemple en environnement de développement:
 * ```
 * php bin/console doctrine:fixtures:load --env=dev --purge-with-truncate
 * ```
 *
 * Une fois les fixtures chargées, vous pouvez accéder à l'interface d'administration ({api_uri}/manager/dtnib/admin)
 * Mais avant il faut créer un utilisateur administrateur via la commande suivante:
 *
 * php bin/console app:create-admin <EMAIL> <EMAIL> EJ-001 --nom="Administrateur" --prenom="iSMA-SUPRA" --hr-user="ISMA_ADMIN_CHU"
 * * cette commande echouera mais vous affichera la liste des entités juridiques disponibles pour l'adapter afin de la re-send.
 *
 */
class AppFixtures extends Fixture
{
    private const TYPES_ACTES = ['CCAM', 'LABO', 'NGAP'];
    private const CATEGORIES_PRINCIPALES = [1, 2, 3, 4, 5];

    public function load(ObjectManager $manager): void
    {
        // 1️⃣ Création du tenant principal
        $tenant = TenantFactory::createOne();

        // 2️⃣ Création d'une seule entité juridique pour le tenant
        $hopital = EntiteJuridiqueFactory::createOne([
            'tenant' => $tenant
        ]);
        $hopitaux = [$hopital];

        // 3️⃣ Création de la hiérarchie des structures pour l'hôpital
        $allPoles = [];
        $allServices = [];
        $allCrs = [];
        $allUfs = [];

        // Création de 50 pôles pour l'hôpital
        $poles = PoleFactory::createMany(50, [
            'hopital' => $hopital
        ]);
        $allPoles = array_merge($allPoles, $poles);

        // Création de 50 services pour l'hôpital
        $services = ServiceFactory::createMany(50, [
            'hopital' => $hopital
        ]);
        $allServices = array_merge($allServices, $services);

        // Création de CRs pour les pôles
        foreach ($poles as $pole) {
            $crs = CrFactory::createMany(2, [
                'pole' => $pole
            ]);
            $allCrs = array_merge($allCrs, $crs);
        }

        // Création de 200 UFs réparties entre les CRs et Services
        $ufsToCreate = 200;
        $ufsCreated = 0;
        
        // Distribuer les UFs entre les CRs et Services
        while ($ufsCreated < $ufsToCreate) {
            // Alterner entre CRs et Services pour créer les UFs
            if ($ufsCreated % 2 == 0 && !empty($allCrs)) {
                $cr = $allCrs[array_rand($allCrs)];
                $service = $allServices[array_rand($allServices)];
                
                $uf = UfsFactory::createOne([
                    'cr' => $cr,
                    'service' => $service
                ]);
                $allUfs[] = $uf;
            } else {
                $service = $allServices[array_rand($allServices)];
                $cr = !empty($allCrs) ? $allCrs[array_rand($allCrs)] : null;
                
                $uf = UfsFactory::createOne([
                    'service' => $service,
                    'cr' => $cr
                ]);
                $allUfs[] = $uf;
            }
            
            $ufsCreated++;
        }

        //  Création d'un administrateur global avec hôpital assigné
        $adminAgent = AgentFactory::createOne([
            'email' => '<EMAIL>',
            'password' => 'Admin123',
            'roles' => ['ROLE_ADMIN','ROLE_FIFAP'],
            'isAdmin' => true,
            'hopital' => $hopitaux[0] // Assigner explicitement l'hôpital
        ]);

        // Création du rôle admin pour cet agent
        AgentHopitalRoleFactory::createOne([
            'agent' => $adminAgent,
            'hopital' => $hopitaux[0],
            'role' => 'ROLE_FIFAP'
        ]);

        // Création des agents et de leurs données d'activité
        foreach ($hopitaux as $hopital) {
            // Agents réguliers pour cet hôpital avec hôpital assigné explicitement
            $agents = AgentFactory::createMany(6, [
                'hopital' => $hopital // Assigner explicitement l'hôpital
            ]);

            foreach ($agents as $agent) {
                // Rôle de l'agent dans cet hôpital
                AgentHopitalRoleFactory::createOne([
                    'agent' => $agent,
                    'hopital' => $hopital
                ]);

                // Association aux UFs de cet hôpital
                $hopitalUfs = array_filter($allUfs, function($uf) use ($hopital) {
                    // Une UFS appartient à cet hôpital si son service ou son CR appartient à cet hôpital
                    $service = $uf->getService();
                    $cr = $uf->getCr();

                    if ($service && $service->getHopital() === $hopital) {
                        return true;
                    }

                    if ($cr && $cr->getPole() && $cr->getPole()->getHopital() === $hopital) {
                        return true;
                    }

                    return false;
                });

                if (!empty($hopitalUfs)) {
                    AgentUfsFactory::createMany(random_int(2, 3), [
                        'agent' => $agent,
                        'ufs' => $hopitalUfs[array_rand($hopitalUfs)]
                    ]);
                }

                //  Création des activités pour chaque agent

                // Gardes et astreintes
                GardesAstreintesFactory::createMany(random_int(2, 5), [
                    'agent' => $agent
                ]);

                // Activité libérale (optionnelle)
                if (random_int(0, 1)) {
                    LiberalFactory::createMany(random_int(1, 2), [
                        'agent' => $agent
                    ]);
                }

                // Publications SIGAPS
                SigapsFactory::createMany(random_int(1, 3), [
                    'agent' => $agent
                ]);
            }
        }

        // 4️⃣ Création de 2000 actes avec dates de janvier à juin pour 2023, 2024, 2025
        $actesToCreate = 2000;
        $years = [2023, 2024, 2025];
        $months = [1, 2, 3, 4, 5, 6]; // Janvier à Juin
        
        // Répartir les actes entre les années et les mois
        $actesPerYear = intdiv($actesToCreate, count($years));
        $actesRemaining = $actesToCreate % count($years);
        
        $actesCount = 0;
        
        foreach ($years as $year) {
            $actesForThisYear = $actesPerYear + ($actesRemaining > 0 ? 1 : 0);
            $actesRemaining--;
            
            $actesPerMonth = intdiv($actesForThisYear, count($months));
            $actesRemainingForYear = $actesForThisYear % count($months);
            
            foreach ($months as $month) {
                $actesForThisMonth = $actesPerMonth + ($actesRemainingForYear > 0 ? 1 : 0);
                $actesRemainingForYear--;
                
                for ($i = 0; $i < $actesForThisMonth; $i++) {
                    // Choisir un agent et une UF aléatoirement
                    $agent = AgentFactory::random();
                    $uf = $allUfs[array_rand($allUfs)];
                    
                    // Créer une date de réalisation dans le mois et l'année spécifiés
                    $day = random_int(1, 28); // Éviter les problèmes avec février
                    $dateRealisation = new \DateTime("$year-$month-$day");
                    
                    ActesFactory::createOne([
                        'agent' => $agent,
                        'ufPrincipal' => $uf,
                        'typeActe' => self::TYPES_ACTES[array_rand(self::TYPES_ACTES)],
                        'annee' => $year,
                        'mois' => $month,
                        'date_realisation' => $dateRealisation
                    ]);
                    
                    $actesCount++;
                }
            }
        }
        
        // 5️⃣ Création des historiques d'importation
        
        // Ressources possibles pour l'importation
        $ressources = ['Agent', 'Structure', 'Actes'];
        
        // Créer des importations pour chaque entité juridique
        foreach ($hopitaux as $hopital) {
            // Créer 2-5 importations par hôpital
            $nbImportations = random_int(2, 5);
            
            for ($i = 0; $i < $nbImportations; $i++) {
                // Choisir une ressource aléatoire
                $ressource = $ressources[array_rand($ressources)];
                
                // Créer l'importation avec l'entité juridique spécifiée
                ImportationFactory::createOne([
                    'nomRessource' => $ressource,
                    'entiteJuridique' => $hopital,
                    // Les autres propriétés utiliseront les valeurs par défaut du factory
                ]);
            }
        }
        
        // Créer quelques importations sans entité juridique spécifique (niveau global)
        ImportationFactory::createMany(3);

        $manager->flush();
    }
}
