(()=>{function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(e)}function e(t,e){for(var o=0;o<e.length;o++){var r=e[o];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,i(r.key),r)}}function i(e){var i=function(e,i){if("object"!=t(e)||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,i||"default");if("object"!=t(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===i?String:Number)(e)}(e,"string");return"symbol"==t(i)?i:i+""}var o=function(){"use strict";return t=function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.field=e,this.field.addEventListener("input",this.autogrow.bind(this)),this.autogrow()},(i=[{key:"autogrow",value:function(){this.field.style.overflow="hidden",this.field.style.resize="none",this.field.style.boxSizing="border-box",this.field.style.height="auto",this.field.scrollHeight>0&&(this.field.style.height=this.field.scrollHeight+"px")}}])&&e(t.prototype,i),o&&e(t,o),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,i,o}();document.addEventListener("DOMContentLoaded",(function(){document.querySelectorAll("[data-ea-textarea-field]").forEach((function(t){new o(t)}))}))})();