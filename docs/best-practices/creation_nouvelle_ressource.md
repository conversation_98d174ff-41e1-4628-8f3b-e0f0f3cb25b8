## 📌 Documentation: Création d'une Nouvelle Ressource API

* Ce document explique comment ajouter une nouvelle ressource à l'API en utilisant API Platform et SymfonyCast Micro Mapper.

- 1️⃣ Création de l'Entité Doctrine
- Ajouter une nouvelle entité dans src/Entity/ :
```shell
#[ORM\Entity(repositoryClass: NouvelleRessourceRepository::class)]
class NouvelleRessource extends BaseEntity
{
    #[ORM\Column(length: 255)]
    private ?string $nom = null;
}
```
- Ajouter la migration :
```shell
symfony console make:migration
symfony console doctrine:migrations:migrate
```
2️⃣ Création du DTO API
- Créer un fichier src/ApiResource/NouvelleRessourceDto.php :
```shell
#[ApiResource(
    shortName: 'nouvelle_ressource',
    operations: [
        new Get(),
        new GetCollection(),
        new Post(),
    ],
    paginationItemsPerPage: 10,
    provider: EntityToDtoStateProvider::class,
    processor: EntityClassDtoStateProcessor::class,
    stateOptions: new Options(entityClass: NouvelleRessource::class),
)]
class NouvelleRessourceDto
{
    #[ApiProperty(readable: false, writable: false, identifier: true)]
    public ?string $id = null;

    #[ApiProperty(description: "Nom de la ressource", example: "Exemple")]
    public string $nom;
}
```
3️⃣ Création des Mappers
- Créer un fichier src/Mapper/NouvelleRessourceEntityToDtoMapper.php :
```shell
#[AsMapper(from: NouvelleRessource::class, to: NouvelleRessourceDto::class)]
class NouvelleRessourceEntityToDtoMapper implements MapperInterface
{
    public function __construct(private MicroMapperInterface $microMapper) {}

    public function load(object $from, string $toClass, array $context): object
    {
        $entity = $from;
        assert($entity instanceof NouvelleRessource);

        $dto = new NouvelleRessourceDto();
        $dto->id = $entity->getId();
        $dto->nom = $entity->getNom();

        return $dto;
    }

    public function populate(object $from, object $to, array $context): object
    {
        $entity = $from;
        $dto = $to;
        assert($entity instanceof NouvelleRessource);
        assert($dto instanceof NouvelleRessourceDto);

        $dto->nom = $entity->getNom();

        return $dto;
    }
}
```
- Créer un fichier src/Mapper/NouvelleRessourceDtoToEntityMapper.php :
```shell
#[AsMapper(from: NouvelleRessourceDto::class, to: NouvelleRessource::class)]
class NouvelleRessourceDtoToEntityMapper implements MapperInterface
{
    public function __construct(private NouvelleRessourceRepository $repository) {}

    public function load(object $from, string $toClass, array $context): object
    {
        $dto = $from;
        assert($dto instanceof NouvelleRessourceDto);

        $entity = $dto->id ? $this->repository->find($dto->id) : new NouvelleRessource();
        if (!$entity) {
            throw new Exception('Nouvelle ressource non trouvée');
        }

        return $entity;
    }

    public function populate(object $from, object $to, array $context): object
    {
        $dto = $from;
        $entity = $to;
        assert($dto instanceof NouvelleRessourceDto);
        assert($entity instanceof NouvelleRessource);

        $entity->setNom($dto->nom);

        return $entity;
    }
}
```
4️⃣ Tester la Nouvelle Ressource
- Vérifier que la ressource est bien exposée :
```shell
curl -X GET http://localhost:8000/api/nouvelle_ressources
```
- Ajouter une nouvelle ressource :
```shell
curl -X POST http://localhost:8000/api/nouvelle_ressources -H "Content-Type: application/json" -d '{"nom": "Exemple"}'
```
----------------------------------------------------------------------------------
CPAGE (MEME table,contenu different)
- BDD EH 1 (particularite chru chru de nancy *table creer ajouter dans* )
- BDD EH 2
- BDD EH 3
- **Faut** utiliser les table universselle.

* le compte x existe dans l'ad mais pas dans le systeme cpage.
* (nom prenom ,x , adresse mail,)