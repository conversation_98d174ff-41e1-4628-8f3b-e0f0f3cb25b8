<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Erreur d'importation automatique</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #dc3545; color: white; padding: 20px; border-radius: 5px; text-align: center; }
        .content { background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-top: 20px; }
        .error-box { background-color: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .action-required { background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .footer { text-align: center; margin-top: 20px; font-size: 12px; color: #666; }
        ul { margin: 10px 0; padding-left: 20px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>❌ Erreur d'importation automatique</h1>
        <p>Entité Juridique: {{ entiteJuridique.code }}</p>
    </div>

    <div class="content">
        <h2>Détails de l'erreur</h2>
        <p>Une erreur s'est produite lors de l'importation automatique des affectations le {{ timestamp|date('d/m/Y à H:i:s') }}.</p>

        {% if filePath %}
        <p><strong>Fichier concerné :</strong> {{ filePath }}</p>
        {% endif %}

        <div class="error-box">
            <h3 style="color: #721c24; margin-top: 0;">🚨 Erreurs rencontrées</h3>
            <ul>
                {% for error in errors %}
                <li style="color: #721c24;">{{ error }}</li>
                {% endfor %}
            </ul>
        </div>

        <div class="action-required">
            <h4 style="color: #856404; margin-top: 0;">⚠️ Actions requises</h4>
            <ul style="color: #856404;">
                <li>Vérifiez le format du fichier CSV (voir structure attendue ci-dessous)</li>
                <li>Contrôlez les données dans le fichier source</li>
                <li>Vérifiez que le dossier {{ entiteJuridique.affectationAgentFilesPaths }} est accessible</li>
                <li>Consultez les logs système pour plus de détails</li>
            </ul>
        </div>

        <div style="background-color: white; padding: 15px; border-radius: 5px; margin-top: 15px;">
            <h4>📋 Structure CSV attendue</h4>
            <code style="background-color: #f8f9fa; padding: 10px; display: block; border-radius: 3px;">
                uConnexion;matricule;date_debut;date_fin;code_uf;type_grade<br>
                jdupont;12345;2024-01-01;2024-12-31;UF001;Médecin<br>
                mmartin;67890;2024-02-01;;UF002;Infirmier
            </code>
            <p><small><strong>Champs obligatoires :</strong> uConnexion, code_uf</small></p>
            <p><small><strong>Séparateur :</strong> Point-virgule (;)</small></p>
        </div>

        <div style="margin-top: 20px;">
            <p><strong>Dossier surveillé :</strong> {{ entiteJuridique.affectationAgentFilesPaths }}</p>
            <p><small>Les fichiers en erreur ont été déplacés vers le sous-dossier "errors".</small></p>
        </div>
    </div>

    <div class="footer">
        <p>Ce message a été généré automatiquement par le système SUPRA.<br>
        Entité Juridique: {{ entiteJuridique.code }}<br>
        Pour toute assistance, contactez : {{ entiteJuridique.adminEmail }}</p>
    </div>
</body>
</html>
