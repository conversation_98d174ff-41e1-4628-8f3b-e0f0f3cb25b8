<?php

namespace App\ApiResource\Structure;

use ApiPlatform\Doctrine\Orm\Filter\SearchFilter;
use ApiPlatform\Doctrine\Orm\State\Options;
use ApiPlatform\Metadata\ApiFilter;
use ApiPlatform\Metadata\ApiProperty;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\GetCollection;
use ApiPlatform\Metadata\Link;
use App\ApiResource\Base\BaseDto;
use App\ApiResource\Trait\HorodatageDtoTrait;
use App\Entity\Structure\Service;
use App\State\EntityClassDtoStateProcessor;
use App\State\EntityToDtoStateProvider;

#[ApiResource(
    shortName: 'service',
    operations: [
        new Get(),
        new GetCollection(
            uriTemplate: '/services'
        ),
        new GetCollection(
            uriTemplate: '/{ejcode}/services',
            uriVariables: [
                'ejcode' => new Link(
                    description: 'Code de l\'entité juridique',
                    schema: ['type' => 'string'],
                    required: false
                )
            ]
        )
    ],
    paginationItemsPerPage: 10,
    provider: EntityToDtoStateProvider::class,
    processor: EntityClassDtoStateProcessor::class,
    stateOptions: new Options(entityClass: Service::class),
)]
class ServiceDto extends BaseDto
{
    #[ApiProperty(description: "Établissement", example: "CHU")]
    public ?string $etab = null;

    #[ApiProperty(description: "Code du service", example: "S01")]
    #[ApiFilter(SearchFilter::class, strategy: 'partial')]
    public ?string $secode = null;

    #[ApiProperty(description: "Date de début", example: "2023-01-01")]
    public ?\DateTimeInterface $datdeb = null;

    #[ApiProperty(description: "Date de fin", example: "2033-01-01")]
    public ?\DateTimeInterface $datfin = null;

    #[ApiProperty(description: "Libellé du service", example: "Service de cardiologie")]
    #[ApiFilter(SearchFilter::class, strategy: 'partial')]
    public ?string $libelle = null;

    #[ApiProperty(description: "Code CD", example: "123")]
    public ?string $cdcode = null;

    #[ApiProperty(description: "Code TA", example: "01")]
    public ?string $tacode = null;

    #[ApiProperty(description: "Utilisateur PF", example: "USER123")]
    public ?string $pfuser = null;

    #[ApiProperty(description: "Date de création", example: "2023-01-01")]
    public ?\DateTimeInterface $dmdacre = null;

    #[ApiProperty(description: "Date de mise à jour", example: "2023-01-01")]
    public ?\DateTimeInterface $dmdamaj = null;

//    #[ApiProperty(description: "Lien vers le pôle associé", example: "/api/poles/1")]
//    public ?PoleDto $pole = null;

    use HorodatageDtoTrait;
}
