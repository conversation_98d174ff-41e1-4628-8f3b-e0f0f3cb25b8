<?php

namespace App\ApiResource\Structure;

use ApiPlatform\Doctrine\Orm\Filter\SearchFilter;
use ApiPlatform\Doctrine\Orm\State\Options;
use ApiPlatform\Metadata\ApiFilter;
use ApiPlatform\Metadata\ApiProperty;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\GetCollection;
use ApiPlatform\Metadata\Link;
use App\ApiResource\Base\BaseDto;
use App\ApiResource\Trait\HorodatageDtoTrait;
use App\Entity\Structure\Cr;
use App\State\EntityClassDtoStateProcessor;
use App\State\EntityToDtoStateProvider;

#[ApiResource(
    shortName: 'cr',
    operations: [
        new Get(),
        new GetCollection(
            uriTemplate: '/crs',
        ),
        new GetCollection(
            uriTemplate: '/{ejcode}/crs',
            uriVariables: [
                'ejcode' => new Link(
                    description: 'Code de l\'entité juridique',
                    schema: ['type' => 'string'],
                    required: true
                )
            ]
        )
    ],
    paginationItemsPerPage: 10,
    provider: EntityToDtoStateProvider::class,
    processor: EntityClassDtoStateProcessor::class,
    stateOptions: new Options(entityClass: Cr::class),
)]
class CrDto extends BaseDto
{
    #[ApiProperty(description: "Établissement", example: "CHU")]
    #[ApiFilter(SearchFilter::class, strategy: 'partial')]
    public ?string $etab = null;

    #[ApiProperty(description: "Code CR", example: "CR01")]
    #[ApiFilter(SearchFilter::class, strategy: 'partial')]
    public ?string $crcode = null;

    #[ApiProperty(description: "Date de début", example: "2023-01-01")]
    public ?\DateTimeInterface $datdeb = null;

    #[ApiProperty(description: "Date de fin", example: "2033-01-01")]
    public ?\DateTimeInterface $datfin = null;

    #[ApiProperty(description: "Libellé du CR", example: "Centre de Responsabilité 1")]
    #[ApiFilter(SearchFilter::class, strategy: 'partial')]
    public ?string $libelle = null;

    #[ApiProperty(description: "Nom du responsable", example: "Dr. Dupont")]
    public ?string $nomresp = null;

    #[ApiProperty(description: "Code du pôle", example: "P01")]
    public ?string $polecode = null;

    #[ApiProperty(description: "Utilisateur PF", example: "USER123")]
    public ?string $pfuser = null;

    #[ApiProperty(description: "Date de création", example: "2023-01-01")]
    public ?\DateTimeInterface $dmdacre = null;

    #[ApiProperty(description: "Date de mise à jour", example: "2023-01-01")]
    public ?\DateTimeInterface $dmdamaj = null;

    #[ApiProperty(description: "Lien vers le pôle associé", example: "/api/poles/1")]
    public ?PoleDto $pole = null;

    use HorodatageDtoTrait;
}
