###> Environment: Qualification ###
APP_ENV=qualif
APP_DEBUG=false
APP_SECRET=qualif_ultra_secure_secret_key_256_bits_minimum

## For production, generate a secure key with:
## php bin/console secrets:generate-keys
## Consider using Symfony's secrets management for sensitive information:
## https://symfony.com/doc/current/configuration/secrets.html

###> Database Qualification ###
DATABASE_USER=supra_qualif
DATABASE_PASSWORD=qualif_ultra_secure_database_password
DATABASE_HOST=db-qualif.chu-nancy.fr
DATABASE_PORT=5432
DATABASE_NAME=supra_qualif

DATABASE_URL="postgresql://${DATABASE_USER}:${DATABASE_PASSWORD}@${DATABASE_HOST}:${DATABASE_PORT}/${DATABASE_NAME}?serverVersion=15&charset=utf8"

###> JWT Qualification ###
JWT_PASSPHRASE=qualif_jwt_ultra_secure_passphrase_256_bits
# JWT_SECRET should be defined in a local environment file and not committed to version control
JWT_SECRET_KEY=%kernel.project_dir%/config/jwt/private.pem
JWT_PUBLIC_KEY=%kernel.project_dir%/config/jwt/public.pem

###> Cache Qualification ###
CACHE_DSN=redis://redis-qualif.chu-nancy.fr:6379?auth=qualif_redis_password
API_CACHE_TTL=1800

###> Mail Qualification ###
MAILER_DSN=smtp://mail-qualif.chu-nancy.fr:587?encryption=tls&auth_mode=login&username=<EMAIL>&password=qualif_mail_secure_password
MAILER_FROM_EMAIL=<EMAIL>

###> LDAP Qualification ###
LDAP_HOST=ldap://k12adc01-qualif.chu-nancy.fr:389
LDAP_DOMAIN=U001QUALIF
LDAP_BASE_DN=DC=u001qualif,DC=local

###> Services Qualification ###
DATA_INTEGRATOR_BASE_URI=http://data-integrator-qualif.chu-nancy.fr:3000/api

###> CORS Qualification ###
CORS_ALLOW_ORIGIN='^https://qualif\.supra\.chu-nancy\.fr$'

###> Security Qualification ###
HTTPS_ONLY=true
SECURE_COOKIES=true