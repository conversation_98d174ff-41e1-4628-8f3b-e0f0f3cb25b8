<?php

declare(strict_types=1);

namespace App\Controller\Configuration;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;

// ce fichier ne sert a rien (et n'est pas utiliser)
// juste pour s'entraider a utiliser le ldap avec symfony
// je le laisse pour depanner (tester si un ldap avec symfony fonctionne par exemple
class GameWithLdapController extends AbstractController
{
    private const LDAP_HOST = 'ldap://k12adc01.chu-nancy.fr:389';
    private const LDAP_DOMAIN = 'U001PRD';
    private const LDAP_BASE_DN = 'DC=u001prd,DC=local';
//    private const LDAP_BASE_DN = 'DC=chu-nancy,DC=local';

    #[Route('/ldap/test-connection', name: 'ldap_test_connection', methods: ['GET'])]
    public function testLdapConnection(): JsonResponse
    {
        $ldapConn = ldap_connect(self::LDAP_HOST);

        if (!$ldapConn) {
            return new JsonResponse(['success' => false, 'message' => 'Impossible de se connecter au serveur LDAP.']);
        }

        ldap_set_option($ldapConn, LDAP_OPT_PROTOCOL_VERSION, 3);
        ldap_set_option($ldapConn, LDAP_OPT_REFERRALS, 0);

        return new JsonResponse(['success' => true, 'message' => 'Connexion au serveur LDAP réussie.']);
    }

    #[Route('/ldap/authenticate-old', name: 'ldap_authenticate-old', methods: ['POST'])]
    public function authenticateUser_OLD_WORK(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);
        $username = $data['username'] ?? null;
        $password = $data['password'] ?? null;

        if (!$username || !$password) {
            return new JsonResponse(['success' => false, 'message' => 'Nom d\'utilisateur ou mot de passe manquant.']);
        }

        $ldapConn = ldap_connect(self::LDAP_HOST);
        ldap_set_option($ldapConn, LDAP_OPT_PROTOCOL_VERSION, 3);
        ldap_set_option($ldapConn, LDAP_OPT_REFERRALS, 0);

        $userDn = self::LDAP_DOMAIN . '\\' . $username;

        try {
            if (@ldap_bind($ldapConn, $userDn, $password)) {
                return new JsonResponse(['success' => true, 'message' => 'Authentification réussie.']);
            }
        } catch (\Exception $e) {
            return new JsonResponse(['success' => false, 'message' => 'Erreur d\'authentification: ' . $e->getMessage()]);
        }

        return new JsonResponse(['success' => false, 'message' => 'Échec de l\'authentification.']);
    }

    #[Route('/ldap/authenticate', name: 'ldap_authenticate', methods: ['POST'])]
    public function authenticateUser(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);
        $username = $data['username'] ?? null;
        $password = $data['password'] ?? null;

        if (!$username || !$password) {
            return new JsonResponse(['success' => false, 'message' => 'Nom d\'utilisateur ou mot de passe manquant.']);
        }

        $ldapConn = ldap_connect(self::LDAP_HOST);
        ldap_set_option($ldapConn, LDAP_OPT_PROTOCOL_VERSION, 3);
        ldap_set_option($ldapConn, LDAP_OPT_REFERRALS, 0);

        $userDn = self::LDAP_DOMAIN . '\\' . $username;

        try {
            if (@ldap_bind($ldapConn, $userDn, $password)) {
                // Après l'authentification réussie, recherchons les infos
                $filter = "(sAMAccountName=$username)";
                $search = ldap_search($ldapConn, self::LDAP_BASE_DN, $filter, ['cn', 'mail', 'memberOf']);
                $entries = ldap_get_entries($ldapConn, $search);

                return new JsonResponse([
                    'success' => true,
                    'message' => 'Authentification réussie.',
                    'user' => [
                        'username' => $username,
                        'displayName' => $entries[0]['displayname'][0] ?? null,
                        'email' => $entries[0]['mail'][0] ?? null,
                        'department' => $entries[0]['department'][0] ?? null,
                        'title' => $entries[0]['title'][0] ?? null,
                        'groups' => isset($entries[0]['memberof']) ? array_slice($entries[0]['memberof'], 0, $entries[0]['memberof']['count']) : []
                    ]
                ]);
            }
        } catch (\Exception $e) {
            return new JsonResponse(['success' => false, 'message' => 'Erreur d\'authentification: ' . $e->getMessage()]);
        }

        return new JsonResponse(['success' => false, 'message' => 'Échec de l\'authentification.']);
    }

    #[Route('/ldap/search', name: 'ldap_search', methods: ['POST'])]
    public function searchLdap(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);
        $username = $data['username'] ?? '';
        $password = $data['password'] ?? '';

        if (!$username || !$password) {
            return new JsonResponse(['success' => false, 'message' => 'Nom d\'utilisateur et mot de passe requis'], 400);
        }

        $ldapConn = ldap_connect(self::LDAP_HOST);
        ldap_set_option($ldapConn, LDAP_OPT_PROTOCOL_VERSION, 3);
        ldap_set_option($ldapConn, LDAP_OPT_REFERRALS, 0);
        ldap_set_option($ldapConn, LDAP_OPT_NETWORK_TIMEOUT, 10);

        try {
            $userDn = self::LDAP_DOMAIN . '\\' . $username;
            if (!@ldap_bind($ldapConn, $userDn, $password)) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'Échec de l\'authentification',
                    'debug' => ldap_error($ldapConn)
                ], 401);
            }

            $filter = "(&(objectClass=user)(sAMAccountName=$username))";
            $baseDn = "DC=chu-nancy,DC=fr";
            $search = @ldap_search($ldapConn, $baseDn, $filter);

            if ($search === false) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'Erreur de recherche LDAP',
                    'debug' => ['error' => ldap_error($ldapConn)]
                ], 500);
            }

            $entries = ldap_get_entries($ldapConn, $search);

            if ($entries['count'] > 0) {
                return new JsonResponse([
                    'success' => true,
                    'message' => 'Authentification réussie.',
                    'user' => [
                        'username' => $username,
                        'cn' => $entries[0]['cn'][0] ?? null,
                        'sn' => $entries[0]['sn'][0] ?? null,
                        'givenname' => $entries[0]['givenname'][0] ?? null,
                        'displayname' => $entries[0]['displayname'][0] ?? null,
                        'email' => $entries[0]['mail'][0] ?? null,
                        'department' => $entries[0]['department'][0] ?? null,
                        'userPrincipalName' => $entries[0]['userprincipalname'][0] ?? null,
                        'distinguishedName' => $entries[0]['distinguishedname'][0] ?? null,
                        'groups' => isset($entries[0]['memberof']) ? array_slice($entries[0]['memberof'], 0, $entries[0]['memberof']['count']) : []
                    ]
                ]);
            }

            return new JsonResponse([
                'success' => false,
                'message' => 'Utilisateur non trouvé'
            ], 404);

        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Erreur LDAP',
                'error' => $e->getMessage()
            ], 500);
        } finally {
            if (isset($ldapConn)) {
                ldap_close($ldapConn);
            }
        }
    }
}

