<?php

namespace App\Controller\Admin;

use App\Entity\Activite\Liberal;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\BooleanField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ChoiceField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateTimeField;
use EasyCorp\Bundle\EasyAdminBundle\Field\IntegerField;
use EasyCorp\Bundle\EasyAdminBundle\Field\MoneyField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;

class LiberalCrudController extends AbstractCrudController
{
    public static function getEntityFqcn(): string
    {
        return Liberal::class;
    }

    public function configureActions(Actions $actions): Actions
    {
        return $actions
            ->add(Crud::PAGE_INDEX, Action::DETAIL);
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setEntityLabelInSingular('Liberal')
            ->setEntityLabelInPlural('Liberaux')
            ->setPageTitle('index', 'Gestion des actes libéraux')
            ->setPageTitle('detail', 'Détail de l\'acte libéral')
            ->setPageTitle('new', 'Créer un nouvel acte libéral')
            ->setPageTitle('edit', 'Modifier l\'acte libéral')
            ->showEntityActionsInlined();
    }

    public function configureFields(string $pageName): iterable
    {
        if ($pageName === Crud::PAGE_INDEX) {
            return [
                TextField::new('id')->hideOnForm(),
                BooleanField::new('isActif')->setLabel('Actif'),
                DateTimeField::new('dateDebut')->setLabel('Début')->setFormat('dd/MM/yyyy HH:mm'),
                DateTimeField::new('dateFin')->setLabel('Fin')->setFormat('dd/MM/yyyy HH:mm'),
                TextField::new('type_acte')->setLabel('Type'),
                AssociationField::new('agent')->setLabel('Agent'),
            ];
        }
        // Tous les champs pour la vue détail (show), edit et new
        return [
            TextField::new('id')->hideOnForm(),
            BooleanField::new('isActif')->setLabel('Est actif'),
            DateTimeField::new('dateCreation')->setLabel('Date de création')->setDisabled(true),
            DateTimeField::new('dateDebut')->setLabel('Date de début')->setFormat('dd/MM/yyyy HH:mm')->setHelp('Date et heure de début de la période'),
            DateTimeField::new('dateFin')->setLabel('Date de fin')->setFormat('dd/MM/yyyy HH:mm')->setHelp('Date et heure de fin de la période'),
            TextField::new('type_acte')->setLabel('Type d\'acte libéral'),
            TextField::new('tarif')->setLabel('Montant')->setHelp('Montant de l\'acte'),
            AssociationField::new('agent')->setLabel('Agent')->setHelp('Agent effectuant l\'acte')->setRequired(true),
            DateTimeField::new('validFrom')->setLabel('Validité - Début')->hideOnForm(),
            DateTimeField::new('validTo')->setLabel('Validité - Fin')->hideOnForm(),
            ChoiceField::new('periodeType')->setLabel('Période')->setChoices([
                'Hebdomadaire' => 'HEBDOMADAIRE',
                'Mensuel' => 'MENSUEL',
                'Annuel' => 'ANNUEL',
            ])->renderAsBadges([
                'HEBDOMADAIRE' => 'primary',
                'MENSUEL' => 'success',
                'ANNUEL' => 'warning',
            ]),
            TextField::new('source')->setLabel('Source des données')->setHelp('Origine de ces données libérales')->hideOnIndex(),
        ];
    }
    
    /**
     * Custom logic to handle entity creation
     * 
     * Cette méthode génère automatiquement les champs validFrom et validTo
     * à partir des champs dateDebut et dateFin lors de la création d'un acte libéral.
     */
    public function persistEntity(\Doctrine\ORM\EntityManagerInterface $entityManager, $entityInstance): void
    {
        if ($entityInstance instanceof \App\Entity\Activite\Liberal) {
            // Generate validFrom from dateDebut
            if ($entityInstance->getDateDebut() !== null) {
                $entityInstance->setValidFrom($entityInstance->getDateDebut());
            }
            
            // Generate validTo from dateFin
            if ($entityInstance->getDateFin() !== null) {
                $entityInstance->setValidTo($entityInstance->getDateFin());
            }
        }
        
        // Call parent method to persist the entity
        parent::persistEntity($entityManager, $entityInstance);
    }
    
    /**
     * Custom logic to handle entity updates
     * 
     * Cette méthode met à jour les champs validFrom et validTo
     * à partir des champs dateDebut et dateFin lors de la modification d'un acte libéral.
     */
    public function updateEntity(\Doctrine\ORM\EntityManagerInterface $entityManager, $entityInstance): void
    {
        if ($entityInstance instanceof \App\Entity\Activite\Liberal) {
            // Generate validFrom from dateDebut
            if ($entityInstance->getDateDebut() !== null) {
                $entityInstance->setValidFrom($entityInstance->getDateDebut());
            }
            
            // Generate validTo from dateFin
            if ($entityInstance->getDateFin() !== null) {
                $entityInstance->setValidTo($entityInstance->getDateFin());
            }
        }
        
        // Call parent method to update the entity
        parent::updateEntity($entityManager, $entityInstance);
    }
}