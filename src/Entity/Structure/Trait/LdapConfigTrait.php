<?php
namespace App\Entity\Structure\Trait;

use Doctrine\ORM\Mapping as ORM;


/**
 * Trait pour la gestion de la configuration LDAP d'une EntiteJuridique
 *
 * Ce trait fournit des valeurs par défaut pour la configuration LDAP lors de la création.
 * Ces valeurs doivent être personnalisées via l'interface d'administration (/admin)
 * après la création de l'EntiteJuridique.
 *
 * Les paramètres LDAP présents ici sont des exemples de valeurs par défaut (chu-nancy)
 * et ne doivent pas être utilisés en production.
 * La configuration réelle doit être faite dans l'interface d'administration.
 */
trait LdapConfigTrait
{
    #[ORM\Column(type: 'json', nullable: true)]
    private ?array $ldapConfig = [
        'host' => 'ldap://selve.chu-nancy.fr:389',
        'domain' => 'U001PRD',
        'base_dn' => 'DC=chu-nancy,DC=fr',
        'last_check' => null,
        'is_active' => true,
        'service_account_email' => '<EMAIL>',
        'service_account_password' => 'wos54chunancy'
    ];

    public function getLdapConfig(): ?array
    {
        return $this->ldapConfig;
    }

    public function setLdapConfig(array $config): void
    {
        $this->ldapConfig = array_merge($this->ldapConfig ?? [], $config);
    }

    public function getLdapHost(): string
    {
        return $this->ldapConfig['host'] ?? 'ldap://selve.chu-nancy.fr:389';
    }

    public function setLdapHost(string $host): void
    {
        if ($this->ldapConfig === null) {
            $this->ldapConfig = [
                'host' => 'ldap://selve.chu-nancy.fr:389',
                'domain' => 'U001PRD',
                'base_dn' => 'DC=chu-nancy,DC=fr',
                'last_check' => null,
                'is_active' => true,
                'service_account_email' => '<EMAIL>',
                'service_account_password' => 'wos54chunancy'
            ];
        }

        $this->ldapConfig['host'] = $host;
    }

    public function getLdapDomain(): string
    {
        return $this->ldapConfig['domain'] ?? 'U001PRD';
    }

    public function setLdapDomain(string $domain): void
    {
        if ($this->ldapConfig === null) {
            $this->ldapConfig = [
                'host' => 'ldap://selve.chu-nancy.fr:389',
                'domain' => 'U001PRD',
                'base_dn' => 'DC=chu-nancy,DC=fr',
                'last_check' => null,
                'is_active' => true,
                'service_account_email' => '<EMAIL>',
                'service_account_password' => 'wos54chunancy'
            ];
        }

        $this->ldapConfig['domain'] = $domain;
    }

    public function getLdapBaseDn(): string
    {
        return $this->ldapConfig['base_dn'] ?? 'DC=chu-nancy,DC=fr';
    }

    public function setLdapBaseDn(string $baseDn): void
    {
        if ($this->ldapConfig === null) {
            $this->ldapConfig = [
                'host' => 'ldap://selve.chu-nancy.fr:389',
                'domain' => 'U001PRD',
                'base_dn' => 'DC=chu-nancy,DC=fr',
                'last_check' => null,
                'is_active' => true,
                'service_account_email' => '<EMAIL>',
                'service_account_password' => 'wos54chunancy'
            ];
        }

        $this->ldapConfig['base_dn'] = $baseDn;
    }

    public function isLdapActive(): bool
    {
        return $this->ldapConfig['is_active'] ?? true;
    }

    public function setIsLdapActive(bool $isActive): void
    {
        if ($this->ldapConfig === null) {
            $this->ldapConfig = [
                'host' => 'ldap://selve.chu-nancy.fr:389',
                'domain' => 'U001PRD',
                'base_dn' => 'DC=chu-nancy,DC=fr',
                'last_check' => null,
                'is_active' => true,
                'service_account_email' => '<EMAIL>',
                'service_account_password' => 'wos54chunancy'
            ];
        }

        $this->ldapConfig['is_active'] = $isActive;
    }

    public function getLdapLastCheck(): ?\DateTime
    {
        $lastCheck = $this->ldapConfig['last_check'] ?? null;
        return $lastCheck ? new \DateTime($lastCheck) : null;
    }

    public function setLdapLastCheck(?\DateTime $lastCheck): void
    {
        if ($this->ldapConfig === null) {
            $this->ldapConfig = [
                'host' => 'ldap://selve.chu-nancy.fr:389',
                'domain' => 'U001PRD',
                'base_dn' => 'DC=chu-nancy,DC=fr',
                'last_check' => null,
                'is_active' => true,
                'service_account_email' => '<EMAIL>',
                'service_account_password' => 'wos54chunancy'
            ];
        }

        $this->ldapConfig['last_check'] = $lastCheck ? $lastCheck->format('Y-m-d H:i:s') : null;
    }

    public function getLdapServiceAccountEmail(): string
    {
        return $this->ldapConfig['service_account_email'] ?? '<EMAIL>';
    }

    public function setLdapServiceAccountEmail(string $email): void
    {
        if ($this->ldapConfig === null) {
            $this->ldapConfig = [
                'host' => 'ldap://selve.chu-nancy.fr:389',
                'domain' => 'U001PRD',
                'base_dn' => 'DC=chu-nancy,DC=fr',
                'last_check' => null,
                'is_active' => true,
                'service_account_email' => '<EMAIL>',
                'service_account_password' => 'wos54chunancy'
            ];
        }

        $this->ldapConfig['service_account_email'] = $email;
    }

    public function getLdapServiceAccountPassword(): string
    {
        return $this->ldapConfig['service_account_password'] ?? 'wos54chunancy';
    }

    public function setLdapServiceAccountPassword(string $password): void
    {
        if ($this->ldapConfig === null) {
            $this->ldapConfig = [
                'host' => 'ldap://selve.chu-nancy.fr:389',
                'domain' => 'U001PRD',
                'base_dn' => 'DC=chu-nancy,DC=fr',
                'last_check' => null,
                'is_active' => true,
                'service_account_email' => '<EMAIL>',
                'service_account_password' => 'wos54chunancy'
            ];
        }

        $this->ldapConfig['service_account_password'] = $password;
    }
}
