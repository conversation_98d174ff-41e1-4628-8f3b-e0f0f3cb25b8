<?php

namespace App\Domain\Extension\CoreSupraFilter\Cr;

/**
 * Filtre de codes de centres de responsabilité (CR) à exclure du retour de l'API.
 *
 * OBJECTIF : Restreindre l'accès aux listes de CRs en appliquant deux niveaux de filtrage :
 * 1. Exclusion directe par crcode
 * 2. Exclusion en cascade si le pôle parent est exclu
 *
 * FONCTIONNEMENT :
 * - Les codes listés dans EXCLUDED_CRCODES sont filtrés des collections
 * - Les CRs appartenant à des pôles exclus sont également filtrés (cascade)
 * - La comparaison est insensible à la casse (UPPER() en SQL)
 * - Les CRs avec crcode = NULL sont INCLUS par défaut
 * - Le filtrage ne s'applique PAS aux accès individuels (GET /api/crs/{id})
 *
 * CONFIGURATION ACTUELLE :
 * - Exclus : CRs identifiés comme non pertinents + CRs de pôles exclus
 * - Inclus : CRs opérationnels + CRs sans code défini
 *
 * @todo Affiner la liste des codes avec l'équipe métier
 * @todo Vérifier la cohérence avec les exclusions de pôles
 *
 * @see CrCategoryFilterExtension Pour l'implémentation Doctrine
 * @see PoleCategoryExcludeFilter Pour les exclusions de pôles parents
 */
class CrCategoryExcludeFilter
{
    /**
     * Codes de CRs à exclure directement du retour API
     *
     * UTILISATION :
     * - Si vous avez exclu tous les pôles nécessaires dans PoleCategoryExcludeFilter,
     *   laissez ce tableau VIDE []
     * - Si un CR non-médical persiste malgré le filtrage des pôles, c'est probablement
     *   qu'il n'est pas rattaché au bon pôle dans le système
     * - Dans ce cas, ajoutez son crcode ici en attendant la correction des données
     *
     * Exemple : ['CR_ADMIN', 'CR_GHOST', 'CR_ERREUR']
     */
    private const EXCLUDED_CRCODES = [
        // Tableau vide par défaut - le filtrage se fait via les pôles
        // Ajoutez ici uniquement les codes de CRs "orphelins" mal rattachés
    ];

    public static function isExcluded(?string $crcode): bool
    {
        if ($crcode === null) {
            return false;
        }

        return in_array(strtoupper($crcode), array_map('strtoupper', self::EXCLUDED_CRCODES), true);
    }

    public static function getExcludedCrcodes(): array
    {
        return self::EXCLUDED_CRCODES;
    }
}