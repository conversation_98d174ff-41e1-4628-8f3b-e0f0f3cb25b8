<?php

namespace App\Tests\Domain\Service\Authentication;

use App\Domain\Service\AuthLdap;
use App\Domain\Service\JwtTokenService;
use App\Domain\Service\LdapService;
use App\Domain\Service\RequestValidator;
use App\Domain\Service\UserManager;
use App\Entity\Praticien\Agent;
use App\Entity\Structure\EntiteJuridique;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTTokenManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Exception\AuthenticationException;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\UserBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Credentials\CustomCredentials;
use Symfony\Component\Security\Http\Authenticator\Passport\Passport;

it('can be instantiated', function () {
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $jwtTokenManager = $this->createMock(JWTTokenManagerInterface::class);
    $jwtTokenService = $this->createMock(JwtTokenService::class);
    $ldapService = $this->createMock(LdapService::class);
    $userManager = $this->createMock(UserManager::class);
    $requestValidator = $this->createMock(RequestValidator::class);
    
    $authLdap = new AuthLdap(
        $entityManager,
        $jwtTokenManager,
        $jwtTokenService,
        $ldapService,
        $userManager,
        $requestValidator,
        'test'
    );
    
    expect($authLdap)->toBeInstanceOf(AuthLdap::class);
});

it('supports only /api/login-ad POST requests', function () {
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $jwtTokenManager = $this->createMock(JWTTokenManagerInterface::class);
    $jwtTokenService = $this->createMock(JwtTokenService::class);
    $ldapService = $this->createMock(LdapService::class);
    $userManager = $this->createMock(UserManager::class);
    $requestValidator = $this->createMock(RequestValidator::class);
    
    $authLdap = new AuthLdap(
        $entityManager,
        $jwtTokenManager,
        $jwtTokenService,
        $ldapService,
        $userManager,
        $requestValidator,
        'test'
    );
    
    // Create mock requests
    $validRequest = $this->createMock(Request::class);
    $validRequest->method('getPathInfo')->willReturn('/api/login-ad');
    $validRequest->method('isMethod')->with('POST')->willReturn(true);
    
    $invalidPathRequest = $this->createMock(Request::class);
    $invalidPathRequest->method('getPathInfo')->willReturn('/api/other-path');
    $invalidPathRequest->method('isMethod')->with('POST')->willReturn(true);
    
    $invalidMethodRequest = $this->createMock(Request::class);
    $invalidMethodRequest->method('getPathInfo')->willReturn('/api/login-ad');
    $invalidMethodRequest->method('isMethod')->with('POST')->willReturn(false);
    
    // Test support for different requests
    expect($authLdap->supports($validRequest))->toBeTrue();
    expect($authLdap->supports($invalidPathRequest))->toBeFalse();
    expect($authLdap->supports($invalidMethodRequest))->toBeFalse();
});

it('creates passport during authentication', function () {
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $jwtTokenManager = $this->createMock(JWTTokenManagerInterface::class);
    $jwtTokenService = $this->createMock(JwtTokenService::class);
    $ldapService = $this->createMock(LdapService::class);
    $userManager = $this->createMock(UserManager::class);
    $requestValidator = $this->createMock(RequestValidator::class);
    $logger = $this->createMock(LoggerInterface::class);
    $request = $this->createMock(Request::class);
    $repository = $this->createMock(EntityRepository::class);
    $entiteJuridique = $this->createMock(EntiteJuridique::class);
    
    // Configure mocks
    $requestValidator->method('extractAuthCredentials')
        ->with($request)
        ->willReturn([
            'username' => 'test.user',
            'password' => 'password123',
            'hopitalCode' => 'EJ001'
        ]);
    
    $entityManager->method('getRepository')
        ->with(EntiteJuridique::class)
        ->willReturn($repository);
    
    $repository->method('findOneBy')
        ->with(['code' => 'EJ001'])
        ->willReturn($entiteJuridique);
    
    $entiteJuridique->method('isLdapActive')
        ->willReturn(true);
    
    // Create a test subclass that overrides the verifyLdapAndStoreData method
    $authLdap = new class(
        $entityManager,
        $jwtTokenManager,
        $jwtTokenService,
        $ldapService,
        $userManager,
        $requestValidator,
        'test'
    ) extends AuthLdap {
        public function verifyLdapAndStoreData(string $password, string $username, string $hopitalCode): bool {
            return true;
        }
    };
    
    // Call method
    $passport = $authLdap->authenticate($request);
    
    // Assertions
    expect($passport)->toBeInstanceOf(Passport::class);
    
    // Check that the passport has the correct badges
    $badges = $passport->getBadges();
    expect($badges)->toHaveKey(UserBadge::class);
    expect($badges)->toHaveKey(CustomCredentials::class);
});

it('returns successful response with JWT token on authentication success', function () {
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $jwtTokenManager = $this->createMock(JWTTokenManagerInterface::class);
    $jwtTokenService = $this->createMock(JwtTokenService::class);
    $ldapService = $this->createMock(LdapService::class);
    $userManager = $this->createMock(UserManager::class);
    $requestValidator = $this->createMock(RequestValidator::class);
    $logger = $this->createMock(LoggerInterface::class);
    $request = $this->createMock(Request::class);
    $token = $this->createMock(TokenInterface::class);
    $entiteJuridique = $this->createMock(EntiteJuridique::class);
    
    // Create a custom Agent mock that responds to getUsername calls
    $agent = new class extends Agent {
        public function getUsername(): string {
            return 'test.user';
        }
        
        public function getHopital(): ?EntiteJuridique {
            return $this->hopital;
        }
        
        private $hopital;
        
        public function setMockHopital(EntiteJuridique $hopital): void {
            $this->hopital = $hopital;
        }
    };
    $agent->setMockHopital($entiteJuridique);
    
    // Configure mocks
    $userManager->method('findOrCreateUser')
        ->willReturn([
            'agent' => $agent,
            'isNew' => false,
            'needsLdapInit' => false
        ]);
    
    $entiteJuridique->method('getCode')
        ->willReturn('EJ001');
    
    $jwtTokenService->method('generate')
        ->with($agent, 'EJ001')
        ->willReturn('jwt_token_123');
    
    // Create a simple AuthLdap instance and set the ldapData property directly
    $authLdap = new AuthLdap(
        $entityManager,
        $jwtTokenManager,
        $jwtTokenService,
        $ldapService,
        $userManager,
        $requestValidator,
        'test'
    );
    
    // Use reflection to set the private ldapData property
    $reflectionClass = new \ReflectionClass(AuthLdap::class);
    $ldapDataProperty = $reflectionClass->getProperty('ldapData');
    $ldapDataProperty->setAccessible(true);
    $ldapDataProperty->setValue($authLdap, [
        'username' => 'test.user',
        'attributes' => ['sn' => 'User', 'givenname' => 'Test'],
        'password' => 'password123',
        'hopital' => $entiteJuridique
    ]);
    
    // Call method
    $response = $authLdap->onAuthenticationSuccess($request, $token, 'main');
    
    // Assertions
    expect($response)->toBeInstanceOf(JsonResponse::class);
    expect(json_decode($response->getContent(), true))->toHaveKey('token');
    expect(json_decode($response->getContent(), true)['token'])->toBe('jwt_token_123');
    expect(json_decode($response->getContent(), true)['success'])->toBeTrue();
});

it('returns proper response on authentication failure', function () {
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $jwtTokenManager = $this->createMock(JWTTokenManagerInterface::class);
    $jwtTokenService = $this->createMock(JwtTokenService::class);
    $ldapService = $this->createMock(LdapService::class);
    $userManager = $this->createMock(UserManager::class);
    $requestValidator = $this->createMock(RequestValidator::class);
    $request = $this->createMock(Request::class);
    $exception = new AuthenticationException('Authentication failed');
    
    $authLdap = new AuthLdap(
        $entityManager,
        $jwtTokenManager,
        $jwtTokenService,
        $ldapService,
        $userManager,
        $requestValidator,
        'test'
    );
    
    // Call method
    $response = $authLdap->onAuthenticationFailure($request, $exception);
    
    // Assertions
    expect($response)->toBeInstanceOf(JsonResponse::class);
    expect($response->getStatusCode())->toBe(401);
    expect(json_decode($response->getContent(), true)['success'])->toBeFalse();
    expect(json_decode($response->getContent(), true)['message'])->toBe('Échec de l\'authentification LDAP');
    expect(json_decode($response->getContent(), true)['error'])->toBe('Authentication failed');
});

it('finds and validates hospital correctly', function () {
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $jwtTokenManager = $this->createMock(JWTTokenManagerInterface::class);
    $jwtTokenService = $this->createMock(JwtTokenService::class);
    $ldapService = $this->createMock(LdapService::class);
    $userManager = $this->createMock(UserManager::class);
    $requestValidator = $this->createMock(RequestValidator::class);
    $repository = $this->createMock(EntityRepository::class);
    $entiteJuridique = $this->createMock(EntiteJuridique::class);
    
    // Configure mocks
    $entityManager->method('getRepository')
        ->with(EntiteJuridique::class)
        ->willReturn($repository);
    
    $repository->method('findOneBy')
        ->with(['code' => 'EJ001'])
        ->willReturn($entiteJuridique);
    
    $entiteJuridique->method('isLdapActive')
        ->willReturn(true);
    
    $authLdap = new AuthLdap(
        $entityManager,
        $jwtTokenManager,
        $jwtTokenService,
        $ldapService,
        $userManager,
        $requestValidator,
        'test'
    );
    
    // Use reflection to call the protected method
    $reflectionMethod = new \ReflectionMethod(AuthLdap::class, 'findAndValidateHospital');
    $reflectionMethod->setAccessible(true);
    $result = $reflectionMethod->invoke($authLdap, 'EJ001');
    
    // Assertions
    expect($result)->toBe($entiteJuridique);
});

it('throws exception when hospital is not found', function () {
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $jwtTokenManager = $this->createMock(JWTTokenManagerInterface::class);
    $jwtTokenService = $this->createMock(JwtTokenService::class);
    $ldapService = $this->createMock(LdapService::class);
    $userManager = $this->createMock(UserManager::class);
    $requestValidator = $this->createMock(RequestValidator::class);
    $repository = $this->createMock(EntityRepository::class);
    
    // Configure mocks
    $entityManager->method('getRepository')
        ->with(EntiteJuridique::class)
        ->willReturn($repository);
    
    $repository->method('findOneBy')
        ->with(['code' => 'NONEXISTENT'])
        ->willReturn(null);
    
    $authLdap = new AuthLdap(
        $entityManager,
        $jwtTokenManager,
        $jwtTokenService,
        $ldapService,
        $userManager,
        $requestValidator,
        'test'
    );
    
    // Use reflection to call the protected method
    $reflectionMethod = new \ReflectionMethod(AuthLdap::class, 'findAndValidateHospital');
    $reflectionMethod->setAccessible(true);
    
    // This should throw AuthenticationException
    expect(function () use ($reflectionMethod, $authLdap) {
        $reflectionMethod->invoke($authLdap, 'NONEXISTENT');
    })->toThrow(AuthenticationException::class, 'Hôpital (EJ) non trouvé');
});

it('throws exception when LDAP is disabled for hospital', function () {
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $jwtTokenManager = $this->createMock(JWTTokenManagerInterface::class);
    $jwtTokenService = $this->createMock(JwtTokenService::class);
    $ldapService = $this->createMock(LdapService::class);
    $userManager = $this->createMock(UserManager::class);
    $requestValidator = $this->createMock(RequestValidator::class);
    $repository = $this->createMock(EntityRepository::class);
    $entiteJuridique = $this->createMock(EntiteJuridique::class);
    
    // Configure mocks
    $entityManager->method('getRepository')
        ->with(EntiteJuridique::class)
        ->willReturn($repository);
    
    $repository->method('findOneBy')
        ->with(['code' => 'EJ001'])
        ->willReturn($entiteJuridique);
    
    $entiteJuridique->method('isLdapActive')
        ->willReturn(false);
    
    $authLdap = new AuthLdap(
        $entityManager,
        $jwtTokenManager,
        $jwtTokenService,
        $ldapService,
        $userManager,
        $requestValidator,
        'test'
    );
    
    // Use reflection to call the protected method
    $reflectionMethod = new \ReflectionMethod(AuthLdap::class, 'findAndValidateHospital');
    $reflectionMethod->setAccessible(true);
    
    // This should throw AuthenticationException
    expect(function () use ($reflectionMethod, $authLdap) {
        $reflectionMethod->invoke($authLdap, 'EJ001');
    })->toThrow(AuthenticationException::class, 'LDAP désactivé pour cet hôpital');
});