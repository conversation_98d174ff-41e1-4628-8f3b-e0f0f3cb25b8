<?php

namespace App\Domain\Exception\Security;

use Symfony\Component\Security\Core\Exception\AuthenticationException;

/**
 * Exception thrown when a user has made too many failed login attempts
 */
class TooManyLoginAttemptsException extends AuthenticationException
{
    private int $waitTimeInSeconds;

    public function __construct(int $waitTimeInSeconds, string $message = 'Too many failed login attempts. Please try again later.', array $data = [])
    {
        parent::__construct($message, 0, null, $data);
        $this->waitTimeInSeconds = $waitTimeInSeconds;
    }

    /**
     * Get the time in seconds until login is allowed again
     */
    public function getWaitTimeInSeconds(): int
    {
        return $this->waitTimeInSeconds;
    }

    /**
     * Get a human-readable wait time message
     */
    public function getWaitTimeMessage(): string
    {
        if ($this->waitTimeInSeconds < 60) {
            return sprintf('%d seconds', $this->waitTimeInSeconds);
        }
        
        if ($this->waitTimeInSeconds < 3600) {
            $minutes = ceil($this->waitTimeInSeconds / 60);
            return sprintf('%d minute%s', $minutes, $minutes > 1 ? 's' : '');
        }
        
        $hours = ceil($this->waitTimeInSeconds / 3600);
        return sprintf('%d hour%s', $hours, $hours > 1 ? 's' : '');
    }

    /**
     * Get the message key for translation
     */
    public function getMessageKey(): string
    {
        return 'Too many failed login attempts. Please try again in ' . $this->getWaitTimeMessage() . '.';
    }
}