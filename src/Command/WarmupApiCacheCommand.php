<?php

namespace App\Command;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\HttpClient\HttpClient;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Component\Console\Input\InputOption;

#[AsCommand(
    name: 'app:warmup-api-cache',
    description: 'Précharge certains endpoints dans le cache Redis',
)]
class WarmupApiCacheCommand extends Command
{
    private HttpClientInterface $httpClient;
    private string $baseUrl;

    public function __construct(HttpClientInterface $httpClient, string $baseUrl = 'http://localhost:8000')
    {
        $this->httpClient = $httpClient;
        $this->baseUrl = $baseUrl;
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addOption('base-url', null, InputOption::VALUE_OPTIONAL, 'Base URL of the API', 'http://localhost:8000')
            ->addOption('endpoints', null, InputOption::VALUE_OPTIONAL, 'Comma-separated list of endpoints to warm up', 'actes,agents,ufs')
            ->addOption('pages', null, InputOption::VALUE_OPTIONAL, 'Number of pages to warm up for each endpoint', '10')
            ->addOption('items-per-page', null, InputOption::VALUE_OPTIONAL, 'Number of items per page', '10')
            ->addOption('filters', null, InputOption::VALUE_OPTIONAL, 'JSON-encoded filters to apply', '{}');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->title('Cache Warming for API Endpoints');

        // Get options
        $baseUrl = $input->getOption('base-url');
        $endpointsStr = $input->getOption('endpoints');
        $pages = (int) $input->getOption('pages');
        $itemsPerPage = (int) $input->getOption('items-per-page');
        $filtersJson = $input->getOption('filters');

        // Parse endpoints and filters
        $endpoints = explode(',', $endpointsStr);
        $filters = json_decode($filtersJson, true) ?? [];

        if ($baseUrl) {
            $this->baseUrl = $baseUrl;
        }

        $io->section('Starting cache warming process');
        $io->progressStart(count($endpoints) * $pages);

        $totalRequests = 0;
        $successfulRequests = 0;

        foreach ($endpoints as $endpoint) {
            $endpoint = trim($endpoint);
            $io->text("Warming up endpoint: /api/$endpoint");

            // Warm up collection endpoints with pagination
            for ($page = 1; $page <= $pages; $page++) {
                $queryParams = [
                    'page' => $page,
                    'itemsPerPage' => $itemsPerPage,
                ];

                // Add any additional filters
                foreach ($filters as $key => $value) {
                    $queryParams[$key] = $value;
                }

                $url = $this->buildUrl("/api/$endpoint", $queryParams);
                $io->text(" - Warming page $page: $url");

                try {
                    $response = $this->httpClient->request('GET', $url);
                    $statusCode = $response->getStatusCode();
                    
                    $totalRequests++;
                    if ($statusCode >= 200 && $statusCode < 300) {
                        $successfulRequests++;
                        $io->text("   <info>✓</info> Status: $statusCode");
                    } else {
                        $io->text("   <error>✗</error> Status: $statusCode");
                    }
                } catch (\Exception $e) {
                    $io->text("   <error>✗</error> Error: " . $e->getMessage());
                }

                $io->progressAdvance();
            }
        }

        $io->progressFinish();
        $io->success("Cache warming completed: $successfulRequests/$totalRequests requests successful");

        return Command::SUCCESS;
    }

    private function buildUrl(string $path, array $queryParams = []): string
    {
        $url = rtrim($this->baseUrl, '/') . '/' . ltrim($path, '/');
        
        if (!empty($queryParams)) {
            $url .= '?' . http_build_query($queryParams);
        }
        
        return $url;
    }
}