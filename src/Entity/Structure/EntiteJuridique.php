<?php

namespace App\Entity\Structure;

use App\Entity\Base\BaseEntity;
use App\Entity\Praticien\Agent;
use App\Entity\Praticien\AgentHopitalRole;
use App\Entity\Structure\Trait\LdapConfigTrait;
use App\Entity\Structure\Trait\ParametresTrait;
use App\Repository\EntiteJuridiqueRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: EntiteJuridiqueRepository::class)]
class EntiteJuridique extends BaseEntity
{
    use LdapConfigTrait;
    use ParametresTrait;
    //
    #[ORM\Column(length: 50, unique: true, options: ['default' => 'EJ-DEFAULT'])]
    private ?string $code = 'EJ-DEFAULT'; //@todo à remettre à null

    #[ORM\Column(length: 255)]
    private ?string $nom = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $adresse = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $telephone = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $email = null;

    #[ORM\ManyToOne(inversedBy: 'hopitals')]
    #[ORM\JoinColumn(nullable: false)]
    private ?Tenant $tenant = null;

    /**
     * @var Collection<int, Pole>
     */
    #[ORM\OneToMany(targetEntity: Pole::class, mappedBy: 'hopital', orphanRemoval: true)]
    private Collection $poles;

    /**
     * @var Collection<int, Service>
     */
    #[ORM\OneToMany(targetEntity: Service::class, mappedBy: 'hopital', orphanRemoval: true)]
    private Collection $services;

    /**
     * @var Collection<int, Agent>
     */
    #[ORM\OneToMany(targetEntity: Agent::class, mappedBy: 'hopital', orphanRemoval: true)]
    private Collection $users;

    #[ORM\OneToMany(targetEntity: AgentHopitalRole::class, mappedBy: "hopital")]
    private Collection $agentRoles;

    public function __construct()
    {
        parent::__construct();

        $this->agentRoles = new ArrayCollection();
        $this->poles = new ArrayCollection();
        $this->services = new ArrayCollection();
        $this->users = new ArrayCollection();
    }

    public function getCode(): ?string
    {
        return $this->code;
    }

    public function setCode(string $code): static
    {
        $this->code = $code;

        return $this;
    }

    public function getNom(): ?string
    {
        return $this->nom;
    }

    public function setNom(string $nom): static
    {
        $this->nom = $nom;

        return $this;
    }

    public function getAdresse(): ?string
    {
        return $this->adresse;
    }

    public function setAdresse(?string $adresse): static
    {
        $this->adresse = $adresse;

        return $this;
    }

    public function getTelephone(): ?string
    {
        return $this->telephone;
    }

    public function setTelephone(?string $telephone): static
    {
        $this->telephone = $telephone;

        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(?string $email): static
    {
        $this->email = $email;

        return $this;
    }

    public function getTenant(): ?Tenant
    {
        return $this->tenant;
    }

    public function setTenant(?Tenant $tenant): static
    {
        $this->tenant = $tenant;

        return $this;
    }

    public function getAgentRoles(): Collection
    {
        return $this->agentRoles;
    }

    /**
     * @return Collection<int, Pole>
     */
    public function getPoles(): Collection
    {
        return $this->poles;
    }

    public function addPole(Pole $pole): static
    {
        if (!$this->poles->contains($pole)) {
            $this->poles->add($pole);
            $pole->setHopital($this);
        }

        return $this;
    }

    public function removePole(Pole $pole): static
    {
        if ($this->poles->removeElement($pole)) {
            // set the owning side to null (unless already changed)
            if ($pole->getHopital() === $this) {
                $pole->setHopital(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Agent>
     */
    public function getUsers(): Collection
    {
        return $this->users;
    }

    public function addUser(Agent $user): static
    {
        if (!$this->users->contains($user)) {
            $this->users->add($user);
            $user->setHopital($this);
        }

        return $this;
    }

    public function removeUser(Agent $user): static
    {
        if ($this->users->removeElement($user)) {
            // set the owning side to null (unless already changed)
            if ($user->getHopital() === $this) {
                $user->setHopital(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Service>
     */
    public function getServices(): Collection
    {
        return $this->services;
    }

    public function addService(Service $service): static
    {
        if (!$this->services->contains($service)) {
            $this->services->add($service);
            $service->setHopital($this);
        }

        return $this;
    }

    public function removeService(Service $service): static
    {
        if ($this->services->removeElement($service)) {
            // set the owning side to null (unless already changed)
            if ($service->getHopital() === $this) {
                $service->setHopital(null);
            }
        }

        return $this;
    }

    /**
     * Get the count of poles associated with this entity
     * 
     * @return int
     */
    public function getPolesCount(): int
    {
        return $this->poles->count();
    }

    /**
     * Get the count of users associated with this entity
     * 
     * @return int
     */
    public function getUsersCount(): int
    {
        return $this->users->count();
    }

    public function __toString(): string
    {
        return sprintf(
            '%s (%s)',
            $this->nom,
            strlen($this->adresse) > 7 ? substr($this->adresse, 0, 5) . '...' : $this->adresse
        );
    }
}
