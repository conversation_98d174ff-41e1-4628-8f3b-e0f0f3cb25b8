<?php
namespace App\Domain\Service;

/**
 * Service contenant les valeurs LDAP par défaut
 *
 * IMPORTANT: Ce service est maintenu uniquement comme solution de secours (fallback).
 * Les configurations LDAP doivent et sont récupérées depuis l'EntiteJuridique en priorité.
 *
 * Utilisation [process] :
 * 1. Essaye d'abord d'utiliser les configs depuis EntiteJuridique
 * 2. Si non disponible, utiliser ces valeurs par défaut
 *
 *
 * En resumé, ce service est un fallback pour les configurations LDAP.
 * Lors de toute connexion via ldap, les informations seront recuperer depuis l'entite juridique
 * et non pas depuis ce service.
 *
 * @see \App\Domain\Service\LdapService::bindWithServiceAccount()
 * @deprecated prochaine version (2.0.0) de l'application, ce service sera supprimé.
 */
class CoreService
{
//    private const LDAP_HOST = 'ldap://k12adc01.chu-nancy.fr:389';
    private const LDAP_HOST = 'ldap://selve.chu-nancy.fr:389';
    private const LDAP_DOMAIN = 'U001PRD';
    private const LDAP_BASE_DN = 'DC=chu-nancy,DC=fr';

    // Données de connexion pour le compte de service LDAP
    private const LDAP_SERVICE_ACCOUNT_EMAIL = '<EMAIL>';
    private const LDAP_SERVICE_ACCOUNT_PASSWORD = 'wos54chunancy';

    /**
     * Get the LDAP host URL
     * 
     * @return string
     */
    public function getLdapHost(): string
    {
        return self::LDAP_HOST;
    }

    /**
     * Get the LDAP domain
     * 
     * @return string
     */
    public function getLdapDomain(): string
    {
        return self::LDAP_DOMAIN;
    }

    /**
     * Get the LDAP base DN
     * 
     * @return string
     */
    public function getLdapBaseDn(): string
    {
        return self::LDAP_BASE_DN;
    }

    /**
     * Get the LDAP service account email
     * 
     * @return string
     */
    public function getLdapServiceAccountEmail(): string
    {
        return self::LDAP_SERVICE_ACCOUNT_EMAIL;
    }

    /**
     * Get the LDAP service account password
     * 
     * @return string
     */
    public function getLdapServiceAccountPassword(): string
    {
        return self::LDAP_SERVICE_ACCOUNT_PASSWORD;
    }
}
