<?php

namespace App\ApiResource\Structure;

use ApiPlatform\Doctrine\Orm\State\Options;
use ApiPlatform\Metadata\ApiProperty;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\GetCollection;
use App\ApiResource\Base\BaseDto;
use App\Entity\Structure\Tenant;
use App\State\EntityClassDtoStateProcessor;
use App\State\EntityToDtoStateProvider;

#[ApiResource(
    shortName: 'tenant',
    operations: [
        new Get(),
        new GetCollection(),
    ],
    paginationItemsPerPage: 10,
    provider: EntityToDtoStateProvider::class,
    processor: EntityClassDtoStateProcessor::class,
    stateOptions: new Options(entityClass: Tenant::class),
)]
class TenantDto extends BaseDto
{
    #[ApiProperty(description: "Nom du tenant", example: "CHU Nancy")]
    public string $nom;

    #[ApiProperty(description: "Description du tenant", example: "Groupe hospitalier du Grand Est")]
    public string $description;

    #[ApiProperty(description: "Date de création", example: "2024-01-01")]
    public ?\DateTimeInterface $dateCreation = null;
}
