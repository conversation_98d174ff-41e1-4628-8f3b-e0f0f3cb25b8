<?php

namespace App\Command\Security;

use App\Domain\Service\Security\LoginAttemptService;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

/**
 * Command to clean up old login attempts
 * 
 * This command deletes login attempts that are older than the retention period (30 days).
 * It should be run periodically by a cron job to prevent the login_attempt table from growing too large.
 * 
 * Example cron job (daily at 2 AM):
 * 0 2 * * * /usr/bin/php /path/to/bin/console app:security:cleanup-login-attempts
 */
#[AsCommand(
    name: 'app:security:cleanup-login-attempts',
    description: 'Clean up old login attempts',
)]
class CleanupLoginAttemptsCommand extends Command
{
    public function __construct(
        private LoginAttemptService $loginAttemptService
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this->setHelp('This command deletes login attempts that are older than the retention period (30 days).');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->title('Cleaning up old login attempts');

        try {
            $deletedCount = $this->loginAttemptService->cleanupOldAttempts();
            
            $io->success(sprintf('Successfully deleted %d old login attempts.', $deletedCount));
            
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $io->error(sprintf('Error cleaning up old login attempts: %s', $e->getMessage()));
            
            return Command::FAILURE;
        }
    }
}