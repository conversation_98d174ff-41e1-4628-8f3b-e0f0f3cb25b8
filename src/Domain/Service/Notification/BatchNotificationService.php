<?php

namespace App\Domain\Service\Notification;

use App\Entity\Structure\EntiteJuridique;
use Psr\Log\LoggerInterface;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Email;
use Twig\Environment;

/**
 * Service optimisé pour les notifications d'import batch
 * 
 * Permet :
 * - La désactivation conditionnelle des emails pendant les gros imports
 * - L'envoi d'emails de synthèse à la fin des imports
 * - La gestion des erreurs critiques uniquement
 */
class BatchNotificationService
{
    private array $batchErrors = [];
    private array $batchStats = [];
    private bool $emailsDisabled = false;
    private string $currentBatchId;

    public function __construct(
        private readonly MailerInterface $mailer,
        private readonly Environment $twig,
        private readonly LoggerInterface $logger,
        private readonly ErrorNotificationEmailService $errorNotificationService
    ) {
        $this->currentBatchId = uniqid('batch_', true);
    }

    /**
     * Démarre un nouveau batch avec configuration des notifications
     */
    public function startBatch(string $resourceType, int $totalRecords, bool $disableEmails = false): string
    {
        $this->currentBatchId = uniqid('batch_', true);
        $this->emailsDisabled = $disableEmails;
        $this->batchErrors = [];
        $this->batchStats = [
            'resource_type' => $resourceType,
            'total_records' => $totalRecords,
            'processed' => 0,
            'created' => 0,
            'updated' => 0,
            'errors' => 0,
            'start_time' => new \DateTime(),
            'emails_disabled' => $disableEmails,
        ];

        $this->logger->info('Batch démarré', [
            'batch_id' => $this->currentBatchId,
            'resource_type' => $resourceType,
            'total_records' => $totalRecords,
            'emails_disabled' => $disableEmails,
        ]);

        return $this->currentBatchId;
    }

    /**
     * Ajoute une erreur au batch (sans envoyer d'email immédiatement si désactivé)
     */
    public function addBatchError(
        EntiteJuridique $entiteJuridique,
        \Throwable $exception,
        array $context = []
    ): void {
        $errorData = [
            'entite_juridique' => $entiteJuridique->getCode(),
            'message' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'context' => $context,
            'timestamp' => new \DateTime(),
        ];

        $this->batchErrors[] = $errorData;
        $this->batchStats['errors']++;

        // Si les emails sont désactivés, on stocke seulement
        if ($this->emailsDisabled) {
            $this->logger->warning('Erreur batch stockée (emails désactivés)', [
                'batch_id' => $this->currentBatchId,
                'error' => $errorData,
            ]);
            return;
        }

        // Sinon, envoyer immédiatement pour les erreurs critiques
        if ($this->isCriticalError($exception)) {
            $this->sendImmediateCriticalError($entiteJuridique, $exception, $context);
        }
    }

    /**
     * Met à jour les statistiques du batch
     */
    public function updateBatchStats(int $processed = 0, int $created = 0, int $updated = 0): void
    {
        $this->batchStats['processed'] += $processed;
        $this->batchStats['created'] += $created;
        $this->batchStats['updated'] += $updated;
    }

    /**
     * Termine le batch et envoie l'email de synthèse si nécessaire
     */
    public function finalizeBatch(EntiteJuridique $entiteJuridique): array
    {
        $this->batchStats['end_time'] = new \DateTime();
        $this->batchStats['duration'] = $this->batchStats['end_time']->diff($this->batchStats['start_time']);

        // Envoyer l'email de synthèse si des erreurs ou si c'était un gros batch
        if ($this->shouldSendSummaryEmail()) {
            $this->sendBatchSummaryEmail($entiteJuridique);
        }

        $finalStats = $this->batchStats;
        $finalStats['total_errors'] = count($this->batchErrors);

        // Convertir les objets DateTime en strings pour éviter les erreurs de sérialisation
        if (isset($finalStats['start_time']) && $finalStats['start_time'] instanceof \DateTime) {
            $finalStats['start_time'] = $finalStats['start_time']->format('Y-m-d H:i:s');
        }
        if (isset($finalStats['end_time']) && $finalStats['end_time'] instanceof \DateTime) {
            $finalStats['end_time'] = $finalStats['end_time']->format('Y-m-d H:i:s');
        }
        if (isset($finalStats['duration']) && $finalStats['duration'] instanceof \DateInterval) {
            $finalStats['duration'] = $finalStats['duration']->format('%H:%I:%S');
        }

        $this->logger->info('Batch terminé', [
            'batch_id' => $this->currentBatchId,
            'stats' => $finalStats,
        ]);

        // Reset pour le prochain batch
        $this->resetBatch();

        return $finalStats;
    }

    /**
     * Détermine si une erreur est critique et doit être envoyée immédiatement
     */
    private function isCriticalError(\Throwable $exception): bool
    {
        $criticalPatterns = [
            'database connection',
            'memory limit',
            'fatal error',
            'out of memory',
            'connection timeout',
        ];

        $message = strtolower($exception->getMessage());
        
        foreach ($criticalPatterns as $pattern) {
            if (strpos($message, $pattern) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Envoie immédiatement une notification pour les erreurs critiques
     */
    private function sendImmediateCriticalError(
        EntiteJuridique $entiteJuridique,
        \Throwable $exception,
        array $context = []
    ): void {
        try {
            $this->errorNotificationService->sendErrorNotificationToAdmin(
                $entiteJuridique,
                'ERREUR CRITIQUE - Import Batch',
                $exception->getMessage(),
                array_merge($context, [
                    'batch_id' => $this->currentBatchId,
                    'is_critical' => true,
                    'resource_type' => $this->batchStats['resource_type'] ?? 'Unknown',
                ])
            );
        } catch (\Exception $e) {
            $this->logger->error('Erreur envoi notification critique', [
                'batch_id' => $this->currentBatchId,
                'original_error' => $exception->getMessage(),
                'notification_error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Détermine si un email de synthèse doit être envoyé
     */
    private function shouldSendSummaryEmail(): bool
    {
        // Toujours envoyer si il y a des erreurs
        if (!empty($this->batchErrors)) {
            return true;
        }

        // Envoyer pour les gros batches même sans erreur
        if ($this->batchStats['total_records'] >= 1000) {
            return true;
        }

        // Envoyer si les emails étaient désactivés (pour informer de la fin)
        if ($this->emailsDisabled) {
            return true;
        }

        return false;
    }

    /**
     * Envoie l'email de synthèse du batch
     */
    private function sendBatchSummaryEmail(EntiteJuridique $entiteJuridique): void
    {
        try {
            $hasErrors = !empty($this->batchErrors);
            $subject = sprintf(
                '[%s] %s - Import Batch %s (%d enregistrements)',
                $entiteJuridique->getCode(),
                $hasErrors ? 'ERREURS' : 'SUCCÈS',
                $this->batchStats['resource_type'],
                $this->batchStats['total_records']
            );

            $context = [
                'entiteJuridique' => $entiteJuridique,
                'batchStats' => $this->batchStats,
                'errors' => $this->batchErrors,
                'hasErrors' => $hasErrors,
                'batchId' => $this->currentBatchId,
            ];

            $template = $hasErrors 
                ? 'emails/batch/batch_summary_with_errors.html.twig'
                : 'emails/batch/batch_summary_success.html.twig';

            $this->sendBatchEmail($entiteJuridique, $subject, $template, $context);

        } catch (\Exception $e) {
            $this->logger->error('Erreur envoi email synthèse batch', [
                'batch_id' => $this->currentBatchId,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Envoie un email de batch aux destinataires configurés
     */
    private function sendBatchEmail(
        EntiteJuridique $entiteJuridique,
        string $subject,
        string $template,
        array $context
    ): void {
        $recipients = [];

        // Ajouter les destinataires selon la configuration
        if ($entiteJuridique->isNotifierAdminDesErreurProduite()) {
            $recipients[] = $entiteJuridique->getAdminEmail();
        }

        if ($entiteJuridique->isNotifierErreurAuAdminPourAffectation()) {
            $recipients[] = $entiteJuridique->getAdminEmail();
        }

        if ($entiteJuridique->isNotifierErreurAuResponssableDamAffectation()) {
            $recipients[] = $entiteJuridique->getDamResponssableDataAffectionEmail();
        }

        // Filtrer les emails valides et uniques
        $validRecipients = array_unique(array_filter($recipients, function($email) {
            return !empty($email) && filter_var($email, FILTER_VALIDATE_EMAIL);
        }));

        if (empty($validRecipients)) {
            $this->logger->info('Aucun destinataire valide pour l\'email de synthèse batch', [
                'batch_id' => $this->currentBatchId,
            ]);
            return;
        }

        $htmlBody = $this->twig->render($template, $context);

        $email = (new Email())
            ->subject($subject)
            ->html($htmlBody);

        foreach ($validRecipients as $recipient) {
            $email->addTo($recipient);
        }

        $this->mailer->send($email);

        $this->logger->info('Email de synthèse batch envoyé', [
            'batch_id' => $this->currentBatchId,
            'recipients' => $validRecipients,
            'subject' => $subject,
        ]);
    }

    /**
     * Reset les données du batch
     */
    private function resetBatch(): void
    {
        $this->batchErrors = [];
        $this->batchStats = [];
        $this->emailsDisabled = false;
        $this->currentBatchId = uniqid('batch_', true);
    }

    /**
     * Retourne les statistiques actuelles du batch
     */
    public function getCurrentBatchStats(): array
    {
        return $this->batchStats;
    }

    /**
     * Retourne l'ID du batch actuel
     */
    public function getCurrentBatchId(): string
    {
        return $this->currentBatchId;
    }
}
