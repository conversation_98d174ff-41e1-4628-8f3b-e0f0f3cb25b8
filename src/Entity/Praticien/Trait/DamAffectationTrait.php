<?php
namespace App\Entity\Praticien\Trait;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

trait DamAffectationTrait
{
    #[ORM\Column(length: 20, nullable: true)]
    private ?string $rgt = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    private ?int $tauxAffectation = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 5, scale: 2, nullable: true)]
    private ?string $etp = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 10, scale: 9, nullable: true)]
    private ?string $sommeEtp = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 5, scale: 2, nullable: true)]
    private ?string $etpStatutaire = null;

    #[ORM\Column(length: 1, nullable: true)]
    private ?string $affectationPrincipale = null;

    #[ORM\Column(length: 1, nullable: true)]
    private ?string $typeGrade = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $libelleGrade = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    private ?int $absences = null;

    public function getRgt(): ?string
    {
        return $this->rgt;
    }

    public function setRgt(?string $rgt): static
    {
        $this->rgt = $rgt;
        return $this;
    }

    public function getEtpStatutaire(): ?float
    {
        return $this->etpStatutaire;
    }

    public function setEtpStatutaire(?float $etpStatutaire): static
    {
        $this->etpStatutaire = $etpStatutaire;
        return $this;
    }

    public function getSommeEtp(): ?float
    {
        return $this->sommeEtp;
    }
    public function setSommeEtp(?float $sommeEtp): void
    {
        $this->sommeEtp = $sommeEtp;
    }

    public function getTauxAffectation(): ?int
    {
        return $this->tauxAffectation;
    }

    public function setTauxAffectation(?int $tauxAffectation): static
    {
        $this->tauxAffectation = $tauxAffectation;
        return $this;
    }

    public function getEtp(): ?float
    {
        return $this->etp;
    }

    public function setEtp(?float $etp): static
    {
        $this->etp = $etp;
        return $this;
    }

    public function getAffectationPrincipale(): ?string
    {
        return $this->affectationPrincipale;
    }

    public function setAffectationPrincipale(?string $affectationPrincipale): static
    {
        $this->affectationPrincipale = $affectationPrincipale;
        return $this;
    }

    public function getTypeGrade(): ?string
    {
        return $this->typeGrade;
    }

    public function setTypeGrade(?string $typeGrade): static
    {
        $this->typeGrade = $typeGrade;
        return $this;
    }

    public function getLibelleGrade(): ?string
    {
        return $this->libelleGrade;
    }

    public function setLibelleGrade(?string $libelleGrade): static
    {
        $this->libelleGrade = $libelleGrade;
        return $this;
    }

    public function getAbsences(): ?int
    {
        return $this->absences;
    }

    public function setAbsences(?int $absences): static
    {
        $this->absences = $absences;
        return $this;
    }
}