<?php

namespace App\Command\Cron;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\DependencyInjection\Attribute\Autowire;

/**
 * Commande pour la gestion des tâches cron SUPRA.
 *
 * Cette commande permet de :
 * - Installer les tâches cron SUPRA sur le système
 * - Afficher la configuration cron actuelle
 * - Vérifier l'état des tâches cron
 * - Générer le fichier cron avec les bons chemins
 *
 * Le fichier cron contient toutes les tâches d'importation automatiques :
 * - Import mensuel de la structure (5 du mois à 3h00)
 * - Import mensuel des agents (5 du mois à 4h00)
 * - Import automatique des affectations ssi activé dans le EJ (5 du mois à 6h00)
 * - Import quotidien des actes (7h00)
 *
 * Tâches de maintenance et surveillance
 *
 * Usage :
 * - Installer les crons : php bin/console app:cron:manage --install
 * - Afficher la config : php bin/console app:cron:manage --show
 * - Vérifier l'état : php bin/console app:cron:manage --status
 * - Générer le fichier : php bin/console app:cron:manage --generate
 * - Menu interactif : php bin/console app:cron:manage
 *
 * Prérequis : Droits administrateur sur le système Linux
 */
#[AsCommand(
    name: 'app:cron:manage',
    description: 'Gère les tâches cron SUPRA (installation, affichage, vérification)',
)]
class CronManageCommand extends Command
{
    private string $projectDir;
    private string $cronFilePath;

    public function __construct(#[Autowire('%kernel.project_dir%')] string $projectDir)
    {
        parent::__construct();
        $this->projectDir = $projectDir;
        $this->cronFilePath = $projectDir . '/src/Command/Cron/supra-cron.txt';
    }

    protected function configure(): void
    {
        $this
            ->addOption(
                'install',
                'i',
                InputOption::VALUE_NONE,
                'Installe les tâches cron SUPRA'
            )
            ->addOption(
                'show',
                's',
                InputOption::VALUE_NONE,
                'Affiche la configuration cron SUPRA'
            )
            ->addOption(
                'status',
                't',
                InputOption::VALUE_NONE,
                'Vérifie l\'état des tâches cron actives'
            )
            ->addOption(
                'generate',
                'g',
                InputOption::VALUE_NONE,
                'Génère le fichier cron avec les bons chemins'
            )
            ->setHelp('Cette commande gère les tâches cron pour l\'automatisation SUPRA');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        if ($input->getOption('install')) {
            return $this->installCron($io);
        }

        if ($input->getOption('show')) {
            return $this->showCronConfig($io);
        }

        if ($input->getOption('status')) {
            return $this->showCronStatus($io);
        }

        if ($input->getOption('generate')) {
            return $this->generateCronFile($io);
        }

        // Affichage par défaut - menu interactif
        return $this->showInteractiveMenu($io);
    }

    private function installCron(SymfonyStyle $io): int
    {
        $io->title('Installation des tâches cron SUPRA');

        // Vérifier que le fichier cron existe
        if (!file_exists($this->cronFilePath)) {
            $io->error("Fichier cron non trouvé : {$this->cronFilePath}");
            return Command::FAILURE;
        }

        // Générer le fichier avec les bons chemins
        $this->generateCronFile($io);

        $io->section('Installation en cours...');

        // Commande pour installer le cron
        $command = "crontab < {$this->cronFilePath}";

        $io->text("Exécution : {$command}");

        exec($command . ' 2>&1', $output, $returnCode);

        if ($returnCode === 0) {
            $io->success('Tâches cron SUPRA installées avec succès !');
            $io->text('Pour vérifier : crontab -l');
            return Command::SUCCESS;
        } else {
            $io->error(' Erreur lors de l\'installation des tâches cron');
            $io->text('Sortie : ' . implode("\n", $output));
            $io->text(' Assurez-vous d\'avoir les droits administrateur');
            return Command::FAILURE;
        }
    }

    private function showCronConfig(SymfonyStyle $io): int
    {
        $io->title('Configuration cron SUPRA');

        if (!file_exists($this->cronFilePath)) {
            $io->error("Fichier cron non trouvé : {$this->cronFilePath}");
            return Command::FAILURE;
        }

        $content = file_get_contents($this->cronFilePath);
        $io->text($content);

        return Command::SUCCESS;
    }

    private function showCronStatus(SymfonyStyle $io): int
    {
        $io->title('État des tâches cron SUPRA');

        // Récupérer les tâches cron actives
        exec('crontab -l 2>&1', $output, $returnCode);

        if ($returnCode !== 0) {
            $io->warning('Aucune tâche cron trouvée ou erreur d\'accès');
            $io->text('Sortie : ' . implode("\n", $output));
            return Command::SUCCESS;
        }

        // Filtrer les tâches SUPRA
        $supraTasks = array_filter($output, function($line) {
            return strpos($line, 'supra') !== false || strpos($line, 'app:import') !== false;
        });

        if (empty($supraTasks)) {
            $io->warning(' Aucune tâche cron SUPRA trouvée');
            $io->text('Pour installer : php bin/console app:cron:manage --install');
        } else {
            $io->success(' Tâches cron SUPRA actives :');
            foreach ($supraTasks as $task) {
                $io->text('  ' . $task);
            }
        }

        return Command::SUCCESS;
    }

    private function generateCronFile(SymfonyStyle $io): int
    {
        $io->section('Génération du fichier cron avec les chemins corrects');

        // Lire le template
        $template = file_get_contents($this->cronFilePath);

        // Remplacer les placeholders par les vrais chemins
        $consolePath = $this->projectDir . '/bin/console';
        $phpPath = $this->findPhpPath();

        $template = str_replace('/path/to/supra/bin/console', $consolePath, $template);
        $template = str_replace('/usr/bin/php', $phpPath, $template);

        // Écrire le fichier mis à jour
        file_put_contents($this->cronFilePath, $template);

        $io->success(" Fichier cron généré avec :");
        $io->text("  PHP : {$phpPath}");
        $io->text("  Console : {$consolePath}");

        return Command::SUCCESS;
    }

    private function showInteractiveMenu(SymfonyStyle $io): int
    {
        $io->title('Gestionnaire de tâches cron SUPRA');

        $choice = $io->choice(
            'Que souhaitez-vous faire ?',
            [
                'install' => 'Installer les tâches cron SUPRA',
                'show' => 'Afficher la configuration cron',
                'status' => 'Vérifier l\'état des tâches actives',
                'generate' => 'Générer le fichier cron avec les bons chemins'
            ]
        );

        switch ($choice) {
            case 'install':
                return $this->installCron($io);
            case 'show':
                return $this->showCronConfig($io);
            case 'status':
                return $this->showCronStatus($io);
            case 'generate':
                return $this->generateCronFile($io);
        }

        return Command::SUCCESS;
    }

    private function findPhpPath(): string
    {
        // Essayer de trouver le chemin PHP
        $possiblePaths = ['/usr/bin/php', '/usr/local/bin/php', '/opt/php/bin/php'];

        foreach ($possiblePaths as $path) {
            if (file_exists($path)) {
                return $path;
            }
        }

        // Par défaut, utiliser which php
        $output = shell_exec('which php');
        return $output ? trim($output) : '/usr/bin/php';
    }
}
