<?php

namespace App\Entity\Structure;

use ApiPlatform\Doctrine\Orm\Filter\SearchFilter;
use ApiPlatform\Metadata\ApiFilter;
use App\Entity\Base\BaseEntity;
use App\Entity\Trait\HorodatageTrait;
use App\Repository\PoleRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: PoleRepository::class)]
#[ApiFilter(SearchFilter::class, properties: [
    'polecode' => 'partial',
    'libelle' => 'partial'
])]
class Pole extends BaseEntity
{
    use HorodatageTrait;

    #[ORM\Column(length: 5, nullable: true)]
    private ?string $etab = null;

    #[ORM\Column(length: 5, nullable: true)]
    private ?string $polecode = null;

    #[ORM\Column(type: 'date', nullable: true)]
    private ?\DateTimeInterface $datdeb = null;

    #[ORM\Column(type: 'date', nullable: true)]
    private ?\DateTimeInterface $datfin = null;

//    #[ORM\Column(length: 100, nullable: true)]
    #[ORM\Column(length: 100, nullable: true, options: ['collation' => 'fr-FR-x-icu'])]
    private ?string $libelle = null;

    #[ORM\Column(length: 20, nullable: true)]
    private ?string $pfuser = null;

    #[ORM\Column(type: 'date', nullable: true)]
    private ?\DateTimeInterface $dmdacre = null;

    #[ORM\Column(type: 'date', nullable: true)]
    private ?\DateTimeInterface $dmdamaj = null;

    #[ORM\ManyToOne(inversedBy: 'poles')]
    #[ORM\JoinColumn(nullable: false)]
    private ?EntiteJuridique $hopital = null;

    /**
     * @var Collection<int, Cr>
     */
    #[ORM\OneToMany(targetEntity: Cr::class, mappedBy: 'pole')]
    private Collection $crs;

    public function __construct()
    {
        parent::__construct();
        $this->crs = new ArrayCollection();
    }

    public function getLibelle(): ?string
    {
        return $this->libelle;
    }

    public function setLibelle(string $libelle): static
    {
        $this->libelle = $libelle;
        return $this;
    }

    public function getPolecode(): ?string
    {
        return $this->polecode;
    }

    public function setPolecode(string $polecode): static
    {
        $this->polecode = $polecode;
        return $this;
    }

    public function getEtab(): ?string
    {
        return $this->etab;
    }

    public function setEtab(string $etab): static
    {
        $this->etab = $etab;
        return $this;
    }

    public function getDatdeb(): ?\DateTimeInterface
    {
        return $this->datdeb;
    }

    public function setDatdeb(\DateTimeInterface $datdeb): static
    {
        $this->datdeb = $datdeb;
        return $this;
    }

    public function getDatfin(): ?\DateTimeInterface
    {
        return $this->datfin;
    }

    public function setDatfin(?\DateTimeInterface $datfin): static
    {
        $this->datfin = $datfin;
        return $this;
    }

    public function getPfuser(): ?string
    {
        return $this->pfuser;
    }

    public function setPfuser(string $pfuser): static
    {
        $this->pfuser = $pfuser;
        return $this;
    }

    public function getDmdacre(): ?\DateTimeInterface
    {
        return $this->dmdacre;
    }

    public function setDmdacre(\DateTimeInterface $dmdacre): static
    {
        $this->dmdacre = $dmdacre;
        return $this;
    }

    public function getDmdamaj(): ?\DateTimeInterface
    {
        return $this->dmdamaj;
    }

    public function setDmdamaj(?\DateTimeInterface $dmdamaj): static
    {
        $this->dmdamaj = $dmdamaj;
        return $this;
    }

    public function getHopital(): ?EntiteJuridique
    {
        return $this->hopital;
    }

    public function setHopital(?EntiteJuridique $hopital): static
    {
        $this->hopital = $hopital;

        return $this;
    }

    /**
     * @return Collection<int, Cr>
     */
    public function getCrs(): Collection
    {
        return $this->crs;
    }

    public function addCr(Cr $cr): static
    {
        if (!$this->crs->contains($cr)) {
            $this->crs->add($cr);
            $cr->setPole($this);
        }

        return $this;
    }

    public function removeCr(Cr $cr): static
    {
        if ($this->crs->removeElement($cr)) {
            // set the owning side to null (unless already changed)
            if ($cr->getPole() === $this) {
                $cr->setPole(null);
            }
        }

        return $this;
    }

    public function __toString(): string
    {
        return sprintf(
            '%s (%s)',
            $this->libelle,
            $this->polecode)
        ;
    }
}
