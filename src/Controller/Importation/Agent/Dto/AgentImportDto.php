<?php

namespace App\Controller\Importation\Agent\Dto;


/**
 * Data Transfer Object pour l'importation des agents depuis le collecteur.
 *
 * Il contient les propriétés fournies par le collecteur pour chaque agent.
 */
class AgentImportDto
{
    public ?string $hrUser = null;
    public ?string $nom = null;
    public ?string $prenom = null;
    public ?string $titre = null;
    public ?string $categorie = null;
    public ?string $etablissement = null;
    public ?string $dateDepart = null;
    public ?string $dateVenue = null;
    public ?string $dateMaj = null;

    public ?string $email = null;
    public ?string $createurFiche = null;
    public ?bool $isAdmin = null;
    public ?bool $isAnesthesiste = null;
}