## Apres une connection reussi, quelle clef pourait m'intéresser
```json
array:2 [
"count" => 1
0 => array:118 [
"objectclass" => array:5 [
"count" => 4
0 => "top"
1 => "person"
2 => "organizationalPerson"
3 => "user"
]
0 => "objectclass"
"cn" => array:2 [
"count" => 1
0 => "U074647"
]
1 => "cn"
"sn" => array:2 [
"count" => 1
0 => "BALDE"
]
2 => "sn"
"title" => array:2 [
"count" => 1
0 => "M"
]
3 => "title"
"givenname" => array:2 [
"count" => 1
0 => "Ismaila"
]
4 => "givenname"
"distinguishedname" => array:2 [
"count" => 1
0 => "CN=U074647,OU=DEPARTEMENT TERRITORIAL TRANSFORMATION NUMERIQUE ET DE L'INGE...,OU=_chu-nancy,DC=chu-nancy,DC=fr"
]
5 => "distinguishedname"
"instancetype" => array:2 [
"count" => 1
0 => "4"
]
6 => "instancetype"
"whencreated" => array:2 [
"count" => 1
0 => "20231024015216.0Z"
]
7 => "whencreated"
"whenchanged" => array:2 [
"count" => 1
0 => "20250509074813.0Z"
]
8 => "whenchanged"
"displayname" => array:2 [
"count" => 1
0 => "BALDE Ismaila"
]
9 => "displayname"
"usncreated" => array:2 [
"count" => 1
0 => "416941581"
]
10 => "usncreated"
"memberof" => array:19 [
"count" => 18
0 => "CN=DL.DSI.GRH,OU=_Ressources,DC=chu-nancy,DC=fr"
1 => "CN=Mail-Office,OU=GP_SCRIPT_MESSAGERIE,OU=_Groupe_Communs,DC=chu-nancy,DC=fr"
2 => "CN=Users_Microsoft_ME3_O365,OU=_Groupe_Communs,DC=chu-nancy,DC=fr"
3 => "CN=DL.PERSONNELS.CHRU,OU=_Groupes_PRIAM,OU=_chu-nancy,DC=chu-nancy,DC=fr"
4 => "CN=Users_Microsoft_ME3,OU=_Groupe_Communs,DC=chu-nancy,DC=fr"
5 => "CN=politique_mot_passe,OU=_Groupe_Communs,DC=chu-nancy,DC=fr"
6 => "CN=ADMINGRH,OU=GRH,DC=chu-nancy,DC=fr"
7 => "CN=CPage_Consult,OU=GRH,DC=chu-nancy,DC=fr"
8 => "CN=priamListe_DL.DSI,OU=_Groupes_PRIAM,OU=_chu-nancy,DC=chu-nancy,DC=fr"
9 => "CN=priamListe_DL.DSI.CAMAP,OU=_Groupes_PRIAM,OU=_chu-nancy,DC=chu-nancy,DC=fr"
10 => "CN=adconnect-user,OU=_Groupe_Communs,DC=chu-nancy,DC=fr"
11 => "CN=priamApplidisHtml5,OU=_Groupes_PRIAM,OU=_chu-nancy,DC=chu-nancy,DC=fr"
12 => "CN=priamGCS,OU=_Groupes_PRIAM,OU=_chu-nancy,DC=chu-nancy,DC=fr"
13 => "CN=priamMessagerie_Securisee,OU=_Groupes_PRIAM,OU=_chu-nancy,DC=chu-nancy,DC=fr"
14 => "CN=priamWEB-PJPB,OU=_Groupes_PRIAM,OU=_chu-nancy,DC=chu-nancy,DC=fr"
15 => "CN=priamDSI,OU=_Groupes_PRIAM,OU=_chu-nancy,DC=chu-nancy,DC=fr"
16 => "CN=WIFI612,OU=_WIFI,OU=_Groupe_Communs,DC=chu-nancy,DC=fr"
17 => "CN=priamWEB,OU=_Groupes_PRIAM,OU=_chu-nancy,DC=chu-nancy,DC=fr"
]
11 => "memberof"
"usnchanged" => array:2 [
"count" => 1
0 => "891567552"
]
12 => "usnchanged"
"department" => array:2 [
"count" => 1
0 => "DEPARTEMENT TERRITORIAL TRANSFORMATION NUMERIQUE ET DE L'INGENI"
]
13 => "department"
"proxyaddresses" => array:7 [
"count" => 6
0 => "X500:/o=CHU NANCY ORG/ou=Exchange Administrative Group (FYDIBOHF23SPDLT)/cn=Recipients/cn=51f6a58e947041c8941e096b7a6c23dd-U074647"
1 => "x500:/o=ExchangeLabs/ou=Exchange Administrative Group (FYDIBOHF23SPDLT)/cn=Recipients/cn=30412c9b4aa641f990c89d456550f808-0d4fb866-8a"
2 => "smtp:<EMAIL>"
3 => "smtp:<EMAIL>"
4 => "X400:C=us;A= ;P=CHU NANCY ORG;O=Exchange;S=BALDE;G=Ismaila;"
5 => "SMTP:<EMAIL>"
]
14 => "proxyaddresses"
"targetaddress" => array:2 [
"count" => 1
0 => "SMTP:<EMAIL>"
]
15 => "targetaddress"
"mailnickname" => array:2 [
"count" => 1
0 => "U074647"
]
16 => "mailnickname"
"name" => array:2 [
"count" => 1
0 => "U074647"
]
17 => "name"
"objectguid" => array:2 [
"count" => 1
0 => b"""
j¶»╔[ç┼Füúú
\x15mÙÂ
"""
]
18 => "objectguid"
"useraccountcontrol" => array:2 [
"count" => 1
0 => "512"
]
19 => "useraccountcontrol"
"badpwdcount" => array:2 [
"count" => 1
0 => "0"
]
20 => "badpwdcount"
"codepage" => array:2 [
"count" => 1
0 => "0"
]
21 => "codepage"
"countrycode" => array:2 [
"count" => 1
0 => "0"
]
22 => "countrycode"
"employeeid" => array:2 [
"count" => 1
0 => "U074647"
]
23 => "employeeid"
"badpasswordtime" => array:2 [
"count" => 1
0 => "133915284841027044"
]
24 => "badpasswordtime"
"lastlogoff" => array:2 [
"count" => 1
0 => "0"
]
25 => "lastlogoff"
"lastlogon" => array:2 [
"count" => 1
0 => "133915284913853096"
]
26 => "lastlogon"
"pwdlastset" => array:2 [
"count" => 1
0 => "133864168530523666"
]
27 => "pwdlastset"
"primarygroupid" => array:2 [
"count" => 1
0 => "513"
]
28 => "primarygroupid"
"objectsid" => array:2 [
"count" => 1
0 => b"\x01\x05\x00\x00\x00\x00\x00\x05\x15\x00\x00\x00¥:Ø\x7F£ZhO¢\x07w=Éú\x01\x00"
]
29 => "objectsid"
"accountexpires" => array:2 [
"count" => 1
0 => "9223372036854775807"
]
30 => "accountexpires"
"logoncount" => array:2 [
"count" => 1
0 => "2349"
]
31 => "logoncount"
"samaccountname" => array:2 [
"count" => 1
0 => "U074647"
]
32 => "samaccountname"
"samaccounttype" => array:2 [
"count" => 1
0 => "*********"
]
33 => "samaccounttype"
"showinaddressbook" => array:2 [
"count" => 1
0 => "CN=Liste d'adresses globale par défaut,CN=All Global Address Lists,CN=Address Lists Container,CN=CHU NANCY ORG,CN=Microsoft Exchange,CN=Services,CN=Configuration,DC=chu-nancy,DC=fr"
]
34 => "showinaddressbook"
"legacyexchangedn" => array:2 [
"count" => 1
0 => "/o=CHU NANCY ORG/ou=External (FYDIBOHF25SPDLT)/cn=Recipients/cn=89214c72358e4302b5b87efc9067a774"
]
35 => "legacyexchangedn"
"userprincipalname" => array:2 [
"count" => 1
0 => "<EMAIL>"
]
36 => "userprincipalname"
"lockouttime" => array:2 [
"count" => 1
0 => "0"
]
37 => "lockouttime"
"objectcategory" => array:2 [
"count" => 1
0 => "CN=Person,CN=Schema,CN=Configuration,DC=chu-nancy,DC=fr"
]
38 => "objectcategory"
"dscorepropagationdata" => array:6 [
"count" => 5
0 => "**************.0Z"
1 => "**************.0Z"
2 => "**************.0Z"
3 => "**************.0Z"
4 => "**************.0Z"
]
39 => "dscorepropagationdata"
"lastlogontimestamp" => array:2 [
"count" => 1
0 => "133912504813283428"
]
40 => "lastlogontimestamp"
"msds-revealeddsas" => array:11 [
"count" => 10
0 => "CN=PORT-LOUIS,OU=Domain Controllers,DC=chu-nancy,DC=fr"
1 => "CN=PORT-LOUIS,OU=Domain Controllers,DC=chu-nancy,DC=fr"
2 => "CN=PORT-LOUIS,OU=Domain Controllers,DC=chu-nancy,DC=fr"
3 => "CN=PORT-LOUIS,OU=Domain Controllers,DC=chu-nancy,DC=fr"
4 => "CN=PORT-LOUIS,OU=Domain Controllers,DC=chu-nancy,DC=fr"
5 => "CN=PORT-VILA,OU=Domain Controllers,DC=chu-nancy,DC=fr"
6 => "CN=PORT-VILA,OU=Domain Controllers,DC=chu-nancy,DC=fr"
7 => "CN=PORT-VILA,OU=Domain Controllers,DC=chu-nancy,DC=fr"
8 => "CN=PORT-VILA,OU=Domain Controllers,DC=chu-nancy,DC=fr"
9 => "CN=PORT-VILA,OU=Domain Controllers,DC=chu-nancy,DC=fr"
]
41 => "msds-revealeddsas"
"textencodedoraddress" => array:2 [
"count" => 1
0 => "X400:C=us;A= ;P=CHU NANCY ORG;O=Exchange;S=BALDE;G=Ismaila;"
]
42 => "textencodedoraddress"
"mail" => array:2 [
"count" => 1
0 => "<EMAIL>"
]
43 => "mail"
"msexchuseraccountcontrol" => array:2 [
"count" => 1
0 => "0"
]
44 => "msexchuseraccountcontrol"
"msexchmailboxguid" => array:2 [
"count" => 1
0 => b"ÆXqÜ!šÁH¶=€|Æ\x12íà"
]
45 => "msexchmailboxguid"
"msexchpoliciesincluded" => array:3 [
"count" => 2
0 => "d0c0cc55-13ab-446b-9848-ec5a35671f75"
1 => "{26491cfc-9e50-4857-861b-0cb8df22b5d7}"
]
46 => "msexchpoliciesincluded"
"msexchwhenmailboxcreated" => array:2 [
"count" => 1
0 => "**************.0Z"
]
47 => "msexchwhenmailboxcreated"
"msds-externaldirectoryobjectid" => array:2 [
"count" => 1
0 => "User_0d4fb866-8a65-4aee-aaed-d54301d5cf12"
]
48 => "msds-externaldirectoryobjectid"
"msexchsaferecipientshash" => array:2 [
"count" => 1
0 => b"ðü4\x08¡NµUpGÂ•"
]
49 => "msexchsaferecipientshash"
"msexchremoterecipienttype" => array:2 [
"count" => 1
0 => "4"
]
50 => "msexchremoterecipienttype"
"msexchversion" => array:2 [
"count" => 1
0 => "**************"
]
51 => "msexchversion"
"msexchrecipienttypedetails" => array:2 [
"count" => 1
0 => "**********"
]
52 => "msexchrecipienttypedetails"
"msexchsafesendershash" => array:2 [
"count" => 1
0 => b"ðü4\x08¡NµUpGÂ•"
]
53 => "msexchsafesendershash"
"msexchumdtmfmap" => array:4 [
"count" => 3
0 => "emailAddress:422533"
1 => "lastNameFirstName:225334762452"
2 => "firstNameLastName:476245222533"
]
54 => "msexchumdtmfmap"
"msexchtextmessagingstate" => array:3 [
"count" => 2
0 => "302120705"
1 => "16842751"
]
55 => "msexchtextmessagingstate"
"msexchrecipientdisplaytype" => array:2 [
"count" => 1
0 => "-1073741818"
]
56 => "msexchrecipientdisplaytype"
"msexchblockedsendershash" => array:2 [
"count" => 1
0 => b"!\x02qRþÐ'§\x10z‚¬ÂÙ¡µ"
]
57 => "msexchblockedsendershash"
"count" => 58
"dn" => "CN=U074647,OU=DEPARTEMENT TERRITORIAL TRANSFORMATION NUMERIQUE ET DE L'INGE...,OU=_chu-nancy,DC=chu-nancy,DC=fr"
]
]
```

### Exemple de retour JSON minimal

```json
{
  "count": 1,
  "0": {
    "cn":            ["U074647"],
    "sn":            ["BALDE"],
    "givenname":     ["Ismaila"],
    "displayname":   ["BALDE Ismaila"],
    "mail":          ["<EMAIL>"],
    "department":    ["DEPARTEMENT TERRITORIAL TRANSFORMATION …"],
    "userprincipalname": ["<EMAIL>"],
    "distinguishedname": ["CN=U074647,OU=… ,DC=chu-nancy,DC=fr"],
    "memberof":      [
       "CN=DL.DSI.GRH,OU=_Ressources,DC=chu-nancy,DC=fr",
       "CN=ADMINGRH,OU=GRH,DC=chu-nancy,DC=fr",
       …
    ],
    …
  },
  "dn": "CN=U074647,OU=…,DC=chu-nancy,DC=fr"
}
