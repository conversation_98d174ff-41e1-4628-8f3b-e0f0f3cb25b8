<?php

namespace App\State\Partials;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use App\ApiResource\Activite\ActesMetadataDto;
use App\Repository\ActesRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\DependencyInjection\Attribute\Autowire;

/**
 * Provider pour les metadonnees des actes medicaux.
 *  Ce fichier est en @todo a finir et a voir pourquoi j'ai un 404
 * Genere rapidement des statistiques sur le dataset des actes :
 * - Utilise des requetes SQL optimisees avec COUNT et GROUP BY
 * - Support du filtrage par periodes (p1Start/p1End, etc.)
 * - Support du filtrage par entité juridique (ejcode)
 * - Cache Redis pour ameliorer les performances
 * - Temps de generation mesure
 */
class ActesMetadataProvider implements ProviderInterface
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private ActesRepository $actesRepository,
        private RequestStack $requestStack,
        #[Autowire('%env(int:API_CACHE_TTL)%')] private int $apiCacheTtl = 1800
    ) {}

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): ActesMetadataDto
    {
        $startTime = microtime(true);
        $request = $this->requestStack->getCurrentRequest();
        
        $dto = new ActesMetadataDto();
        // Set a unique ID for the DTO (required by API Platform)
        $dto->id = 'metadata_' . uniqid();
        
        // Recuperer les parametres de periodes
        $periods = $this->extractPeriods($request);
        $dto->appliedPeriods = $this->formatAppliedPeriods($periods);
        
        // Recuperer le parametre ejcode (entité juridique)
        $ejCode = $this->extractEjCode($request);
        $dto->appliedEjCode = $ejCode;
        
        // Construire la requete de base avec les periodes
        $qb = $this->actesRepository->createQueryBuilder('a')
            ->where('a.isActif = true');
        
        // Appliquer le filtrage par periodes (meme logique que ActesPeriodesExtension)
        if (!empty($periods)) {
            $this->applyPeriodsFilter($qb, $periods);
        }
        
        // Appliquer le filtrage par entité juridique
        if ($ejCode) {
            $this->applyEjCodeFilter($qb, $ejCode);
        }
        
        // 1. Nombre total d'actes
        $totalQb = clone $qb;
        $dto->totalItems = (int) $totalQb
            ->select('COUNT(a.id)')
            ->getQuery()
            ->getSingleScalarResult();
        
        // 2. Repartition par type
        $typeQb = clone $qb;
        $typeResults = $typeQb
            ->select('a.typeActe, COUNT(a.id) as count')
            ->groupBy('a.typeActe')
            ->getQuery()
            ->getResult();
        
        $dto->totalByType = [];
        foreach ($typeResults as $result) {
            $dto->totalByType[$result['typeActe'] ?? 'UNKNOWN'] = (int) $result['count'];
        }
        
        // 3. Plage de dates
        $dateQb = clone $qb;
        $dateResult = $dateQb
            ->select('MIN(a.date_realisation) as minDate, MAX(a.date_realisation) as maxDate')
            ->getQuery()
            ->getSingleResult();
        
        if ($dateResult['minDate']) {
            if ($dateResult['minDate'] instanceof \DateTimeInterface) {
                $dto->dateRangeMin = $dateResult['minDate']->format('Y-m-d');
            } else {
                $dto->dateRangeMin = (string) $dateResult['minDate'];
            }
        }
        if ($dateResult['maxDate']) {
            if ($dateResult['maxDate'] instanceof \DateTimeInterface) {
                $dto->dateRangeMax = $dateResult['maxDate']->format('Y-m-d');
            } else {
                $dto->dateRangeMax = (string) $dateResult['maxDate'];
            }
        }
        
        // 4. Derniere mise a jour (max dateCreation)
        $updateQb = clone $qb;
        $lastUpdate = $updateQb
            ->select('MAX(a.dateCreation)')
            ->getQuery()
            ->getSingleScalarResult();
        
        if ($lastUpdate) {
            if ($lastUpdate instanceof \DateTimeInterface) {
                $dto->lastUpdate = $lastUpdate;
            } else {
                // If lastUpdate is a string, try to convert it to DateTime
                try {
                    $dto->lastUpdate = new \DateTime($lastUpdate);
                } catch (\Exception $e) {
                    // If conversion fails, set to null
                    $dto->lastUpdate = null;
                }
            }
        }
        
        // 5. Temps de generation
        $endTime = microtime(true);
        $dto->generationTimeMs = (int) round(($endTime - $startTime) * 1000);
        
        return $dto;
    }

    /**
     * Extrait les parametres de periodes de la requete
     */
    private function extractPeriods($request): array
    {
        if (!$request) {
            return [];
        }
        
        $periods = [];
        foreach ($request->query->all() as $key => $value) {
            if (preg_match('/^p(\d+)Start$/', $key, $matches)) {
                $index = $matches[1];
                $start = $value;
                $endKey = "p{$index}End";
                $end = $request->query->get($endKey);
                if ($end) {
                    $periods[] = [$start, $end];
                }
            }
        }
        
        return $periods;
    }

    /**
     * Applique le filtrage par periodes (meme logique que ActesPeriodesExtension)
     */
    private function applyPeriodsFilter($qb, array $periods): void
    {
        if (empty($periods)) {
            return;
        }
        
        $orX = $qb->expr()->orX();
        
        foreach ($periods as $i => [$start, $end]) {
            $paramStart = "period_start_$i";
            $paramEnd = "period_end_$i";
            
            $orX->add(
                $qb->expr()->andX(
                    $qb->expr()->lte('a.validFrom', ":$paramEnd"),
                    $qb->expr()->orX(
                        $qb->expr()->isNull('a.validTo'),
                        $qb->expr()->gte('a.validTo', ":$paramStart")
                    ),
                    $qb->expr()->gte('a.date_realisation', ":$paramStart"),
                    $qb->expr()->lte('a.date_realisation', ":$paramEnd")
                )
            );
            
            $qb->setParameter($paramStart, $start);
            $qb->setParameter($paramEnd, $end);
        }
        
        $qb->andWhere($orX);
    }

    /**
     * Formate les periodes appliquees pour l'affichage
     */
    private function formatAppliedPeriods(array $periods): array
    {
        $formatted = [];
        foreach ($periods as $i => [$start, $end]) {
            $formatted["p" . ($i + 1)] = "$start to $end";
        }
        return $formatted;
    }

    /**
     * Extrait le parametre ejcode (code entité juridique) de la requete
     */
    private function extractEjCode($request): ?string
    {
        if (!$request) {
            return null;
        }
        
        return $request->query->get('ejcode');
    }

    /**
     * Applique le filtrage par entité juridique (ejcode)
     * Relation: acte -> ufIntervention -> cr -> pole -> hopital (EntiteJuridique)
     */
    private function applyEjCodeFilter($qb, ?string $ejCode): void
    {
        if (!$ejCode) {
            return;
        }
        
        // Joindre avec UF intervention, puis CR, puis pôle, puis entité juridique
        // Utiliser innerJoin pour s'assurer que seuls les actes avec une entité juridique correspondante sont inclus
        $qb->innerJoin('a.ufIntervention', 'ufIntervention')
           ->innerJoin('ufIntervention.cr', 'cr')
           ->innerJoin('cr.pole', 'pole')
           ->innerJoin('pole.hopital', 'ej')
           ->andWhere('ej.code = :ejCode')
           ->setParameter('ejCode', $ejCode);
    }
}
