(()=>{var e={865:(e,t,r)=>{!function(e){"use strict";function t(t,n){function i(){t.display.wrapper.offsetHeight?(r(t,n),t.display.lastWrapHeight!=t.display.wrapper.clientHeight&&t.refresh()):n.timeout=setTimeout(i,n.delay)}n.timeout=setTimeout(i,n.delay),n.hurry=function(){clearTimeout(n.timeout),n.timeout=setTimeout(i,50)},e.on(window,"mouseup",n.hurry),e.on(window,"keyup",n.hurry)}function r(t,r){clearTimeout(r.timeout),e.off(window,"mouseup",r.hurry),e.off(window,"keyup",r.hurry)}e.defineOption("autoRefresh",!1,(function(e,n){e.state.autoRefresh&&(r(e,e.state.autoRefresh),e.state.autoRefresh=null),n&&0==e.display.wrapper.offsetHeight&&t(e,e.state.autoRefresh={delay:n.delay||250})}))}(r(237))},340:(e,t,r)=>{!function(e){"use strict";e.multiplexingMode=function(t){var r=Array.prototype.slice.call(arguments,1);function n(e,t,r,n){if("string"==typeof t){var i=e.indexOf(t,r);return n&&i>-1?i+t.length:i}var o=t.exec(r?e.slice(r):e);return o?o.index+r+(n?o[0].length:0):-1}return{startState:function(){return{outer:e.startState(t),innerActive:null,inner:null,startingInner:!1}},copyState:function(r){return{outer:e.copyState(t,r.outer),innerActive:r.innerActive,inner:r.innerActive&&e.copyState(r.innerActive.mode,r.inner),startingInner:r.startingInner}},token:function(i,o){if(o.innerActive){var a=o.innerActive;if(c=i.string,!a.close&&i.sol())return o.innerActive=o.inner=null,this.token(i,o);if((d=a.close&&!o.startingInner?n(c,a.close,i.pos,a.parseDelimiters):-1)==i.pos&&!a.parseDelimiters)return i.match(a.close),o.innerActive=o.inner=null,a.delimStyle&&a.delimStyle+" "+a.delimStyle+"-close";d>-1&&(i.string=c.slice(0,d));var s=a.mode.token(i,o.inner);return d>-1?i.string=c:i.pos>i.start&&(o.startingInner=!1),d==i.pos&&a.parseDelimiters&&(o.innerActive=o.inner=null),a.innerStyle&&(s=s?s+" "+a.innerStyle:a.innerStyle),s}for(var l=1/0,c=i.string,u=0;u<r.length;++u){var d,p=r[u];if((d=n(c,p.open,i.pos))==i.pos){p.parseDelimiters||i.match(p.open),o.startingInner=!!p.parseDelimiters,o.innerActive=p;var f=0;if(t.indent){var m=t.indent(o.outer,"","");m!==e.Pass&&(f=m)}return o.inner=e.startState(p.mode,f),p.delimStyle&&p.delimStyle+" "+p.delimStyle+"-open"}-1!=d&&d<l&&(l=d)}l!=1/0&&(i.string=c.slice(0,l));var h=t.token(i,o.outer);return l!=1/0&&(i.string=c),h},indent:function(r,n,i){var o=r.innerActive?r.innerActive.mode:t;return o.indent?o.indent(r.innerActive?r.inner:r.outer,n,i):e.Pass},blankLine:function(n){var i=n.innerActive?n.innerActive.mode:t;if(i.blankLine&&i.blankLine(n.innerActive?n.inner:n.outer),n.innerActive)"\n"===n.innerActive.close&&(n.innerActive=n.inner=null);else for(var o=0;o<r.length;++o){var a=r[o];"\n"===a.open&&(n.innerActive=a,n.inner=e.startState(a.mode,i.indent?i.indent(n.outer,"",""):0))}},electricChars:t.electricChars,innerMode:function(e){return e.inner?{state:e.inner,mode:e.innerActive.mode}:{state:e.outer,mode:t}}}}}(r(237))},856:(e,t,r)=>{!function(e){"use strict";function t(e,t){if(!e.hasOwnProperty(t))throw new Error("Undefined state "+t+" in simple mode")}function r(e,t){if(!e)return/(?:)/;var r="";return e instanceof RegExp?(e.ignoreCase&&(r="i"),e.unicode&&(r+="u"),e=e.source):e=String(e),new RegExp((!1===t?"":"^")+"(?:"+e+")",r)}function n(e){if(!e)return null;if(e.apply)return e;if("string"==typeof e)return e.replace(/\./g," ");for(var t=[],r=0;r<e.length;r++)t.push(e[r]&&e[r].replace(/\./g," "));return t}function i(e,i){(e.next||e.push)&&t(i,e.next||e.push),this.regex=r(e.regex),this.token=n(e.token),this.data=e}function o(e,t){return function(r,n){if(n.pending){var i=n.pending.shift();return 0==n.pending.length&&(n.pending=null),r.pos+=i.text.length,i.token}if(n.local){if(n.local.end&&r.match(n.local.end)){var o=n.local.endToken||null;return n.local=n.localState=null,o}var a;return o=n.local.mode.token(r,n.localState),n.local.endScan&&(a=n.local.endScan.exec(r.current()))&&(r.pos=r.start+a.index),o}for(var l=e[n.state],c=0;c<l.length;c++){var u=l[c],d=(!u.data.sol||r.sol())&&r.match(u.regex);if(d){u.data.next?n.state=u.data.next:u.data.push?((n.stack||(n.stack=[])).push(n.state),n.state=u.data.push):u.data.pop&&n.stack&&n.stack.length&&(n.state=n.stack.pop()),u.data.mode&&s(t,n,u.data.mode,u.token),u.data.indent&&n.indent.push(r.indentation()+t.indentUnit),u.data.dedent&&n.indent.pop();var p=u.token;if(p&&p.apply&&(p=p(d)),d.length>2&&u.token&&"string"!=typeof u.token){for(var f=2;f<d.length;f++)d[f]&&(n.pending||(n.pending=[])).push({text:d[f],token:u.token[f-1]});return r.backUp(d[0].length-(d[1]?d[1].length:0)),p[0]}return p&&p.join?p[0]:p}}return r.next(),null}}function a(e,t){if(e===t)return!0;if(!e||"object"!=typeof e||!t||"object"!=typeof t)return!1;var r=0;for(var n in e)if(e.hasOwnProperty(n)){if(!t.hasOwnProperty(n)||!a(e[n],t[n]))return!1;r++}for(var n in t)t.hasOwnProperty(n)&&r--;return 0==r}function s(t,n,i,o){var s;if(i.persistent)for(var l=n.persistentStates;l&&!s;l=l.next)(i.spec?a(i.spec,l.spec):i.mode==l.mode)&&(s=l);var c=s?s.mode:i.mode||e.getMode(t,i.spec),u=s?s.state:e.startState(c);i.persistent&&!s&&(n.persistentStates={mode:c,spec:i.spec,state:u,next:n.persistentStates}),n.localState=u,n.local={mode:c,end:i.end&&r(i.end),endScan:i.end&&!1!==i.forceEnd&&r(i.end,!1),endToken:o&&o.join?o[o.length-1]:o}}function l(e,t){for(var r=0;r<t.length;r++)if(t[r]===e)return!0}function c(t,r){return function(n,i,o){if(n.local&&n.local.mode.indent)return n.local.mode.indent(n.localState,i,o);if(null==n.indent||n.local||r.dontIndentStates&&l(n.state,r.dontIndentStates)>-1)return e.Pass;var a=n.indent.length-1,s=t[n.state];e:for(;;){for(var c=0;c<s.length;c++){var u=s[c];if(u.data.dedent&&!1!==u.data.dedentIfLineStart){var d=u.regex.exec(i);if(d&&d[0]){a--,(u.next||u.push)&&(s=t[u.next||u.push]),i=i.slice(d[0].length);continue e}}}break}return a<0?0:n.indent[a]}}e.defineSimpleMode=function(t,r){e.defineMode(t,(function(t){return e.simpleMode(t,r)}))},e.simpleMode=function(r,n){t(n,"start");var a={},s=n.meta||{},l=!1;for(var u in n)if(u!=s&&n.hasOwnProperty(u))for(var d=a[u]=[],p=n[u],f=0;f<p.length;f++){var m=p[f];d.push(new i(m,n)),(m.indent||m.dedent)&&(l=!0)}var h={startState:function(){return{state:"start",pending:null,local:null,localState:null,indent:l?[]:null}},copyState:function(t){var r={state:t.state,pending:t.pending,local:t.local,localState:null,indent:t.indent&&t.indent.slice(0)};t.localState&&(r.localState=e.copyState(t.local.mode,t.localState)),t.stack&&(r.stack=t.stack.slice(0));for(var n=t.persistentStates;n;n=n.next)r.persistentStates={mode:n.mode,spec:n.spec,state:n.state==t.localState?r.localState:e.copyState(n.mode,n.state),next:r.persistentStates};return r},token:o(a,r),innerMode:function(e){return e.local&&{mode:e.local.mode,state:e.localState}},indent:c(a,s)};if(s)for(var g in s)s.hasOwnProperty(g)&&(h[g]=s[g]);return h}}(r(237))},237:function(e){e.exports=function(){"use strict";var e=navigator.userAgent,t=navigator.platform,r=/gecko\/\d/i.test(e),n=/MSIE \d/.test(e),i=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(e),o=/Edge\/(\d+)/.exec(e),a=n||i||o,s=a&&(n?document.documentMode||6:+(o||i)[1]),l=!o&&/WebKit\//.test(e),c=l&&/Qt\/\d+\.\d+/.test(e),u=!o&&/Chrome\/(\d+)/.exec(e),d=u&&+u[1],p=/Opera\//.test(e),f=/Apple Computer/.test(navigator.vendor),m=/Mac OS X 1\d\D([8-9]|\d\d)\D/.test(e),h=/PhantomJS/.test(e),g=f&&(/Mobile\/\w+/.test(e)||navigator.maxTouchPoints>2),v=/Android/.test(e),y=g||v||/webOS|BlackBerry|Opera Mini|Opera Mobi|IEMobile/i.test(e),_=g||/Mac/.test(t),b=/\bCrOS\b/.test(e),x=/win/i.test(t),k=p&&e.match(/Version\/(\d*\.\d*)/);k&&(k=Number(k[1])),k&&k>=15&&(p=!1,l=!0);var w=_&&(c||p&&(null==k||k<12.11)),S=r||a&&s>=9;function C(e){return new RegExp("(^|\\s)"+e+"(?:$|\\s)\\s*")}var M,T=function(e,t){var r=e.className,n=C(t).exec(r);if(n){var i=r.slice(n.index+n[0].length);e.className=r.slice(0,n.index)+(i?n[1]+i:"")}};function L(e){for(var t=e.childNodes.length;t>0;--t)e.removeChild(e.firstChild);return e}function z(e,t){return L(e).appendChild(t)}function D(e,t,r,n){var i=document.createElement(e);if(r&&(i.className=r),n&&(i.style.cssText=n),"string"==typeof t)i.appendChild(document.createTextNode(t));else if(t)for(var o=0;o<t.length;++o)i.appendChild(t[o]);return i}function A(e,t,r,n){var i=D(e,t,r,n);return i.setAttribute("role","presentation"),i}function q(e,t){if(3==t.nodeType&&(t=t.parentNode),e.contains)return e.contains(t);do{if(11==t.nodeType&&(t=t.host),t==e)return!0}while(t=t.parentNode)}function N(e){var t,r=e.ownerDocument||e;try{t=e.activeElement}catch(e){t=r.body||null}for(;t&&t.shadowRoot&&t.shadowRoot.activeElement;)t=t.shadowRoot.activeElement;return t}function F(e,t){var r=e.className;C(t).test(r)||(e.className+=(r?" ":"")+t)}function E(e,t){for(var r=e.split(" "),n=0;n<r.length;n++)r[n]&&!C(r[n]).test(t)&&(t+=" "+r[n]);return t}M=document.createRange?function(e,t,r,n){var i=document.createRange();return i.setEnd(n||e,r),i.setStart(e,t),i}:function(e,t,r){var n=document.body.createTextRange();try{n.moveToElementText(e.parentNode)}catch(e){return n}return n.collapse(!0),n.moveEnd("character",r),n.moveStart("character",t),n};var O=function(e){e.select()};function I(e){return e.display.wrapper.ownerDocument}function P(e){return j(e.display.wrapper)}function j(e){return e.getRootNode?e.getRootNode():e.ownerDocument}function W(e){return I(e).defaultView}function B(e){var t=Array.prototype.slice.call(arguments,1);return function(){return e.apply(null,t)}}function H(e,t,r){for(var n in t||(t={}),e)!e.hasOwnProperty(n)||!1===r&&t.hasOwnProperty(n)||(t[n]=e[n]);return t}function R(e,t,r,n,i){null==t&&-1==(t=e.search(/[^\s\u00a0]/))&&(t=e.length);for(var o=n||0,a=i||0;;){var s=e.indexOf("\t",o);if(s<0||s>=t)return a+(t-o);a+=s-o,a+=r-a%r,o=s+1}}g?O=function(e){e.selectionStart=0,e.selectionEnd=e.value.length}:a&&(O=function(e){try{e.select()}catch(e){}});var $=function(){this.id=null,this.f=null,this.time=0,this.handler=B(this.onTimeout,this)};function U(e,t){for(var r=0;r<e.length;++r)if(e[r]==t)return r;return-1}$.prototype.onTimeout=function(e){e.id=0,e.time<=+new Date?e.f():setTimeout(e.handler,e.time-+new Date)},$.prototype.set=function(e,t){this.f=t;var r=+new Date+e;(!this.id||r<this.time)&&(clearTimeout(this.id),this.id=setTimeout(this.handler,e),this.time=r)};var V=50,K={toString:function(){return"CodeMirror.Pass"}},G={scroll:!1},X={origin:"*mouse"},Q={origin:"+move"};function Y(e,t,r){for(var n=0,i=0;;){var o=e.indexOf("\t",n);-1==o&&(o=e.length);var a=o-n;if(o==e.length||i+a>=t)return n+Math.min(a,t-i);if(i+=o-n,n=o+1,(i+=r-i%r)>=t)return n}}var Z=[""];function J(e){for(;Z.length<=e;)Z.push(ee(Z)+" ");return Z[e]}function ee(e){return e[e.length-1]}function te(e,t){for(var r=[],n=0;n<e.length;n++)r[n]=t(e[n],n);return r}function re(e,t,r){for(var n=0,i=r(t);n<e.length&&r(e[n])<=i;)n++;e.splice(n,0,t)}function ne(){}function ie(e,t){var r;return Object.create?r=Object.create(e):(ne.prototype=e,r=new ne),t&&H(t,r),r}var oe=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/;function ae(e){return/\w/.test(e)||e>""&&(e.toUpperCase()!=e.toLowerCase()||oe.test(e))}function se(e,t){return t?!!(t.source.indexOf("\\w")>-1&&ae(e))||t.test(e):ae(e)}function le(e){for(var t in e)if(e.hasOwnProperty(t)&&e[t])return!1;return!0}var ce=/[\u0300-\u036f\u0483-\u0489\u0591-\u05bd\u05bf\u05c1\u05c2\u05c4\u05c5\u05c7\u0610-\u061a\u064b-\u065e\u0670\u06d6-\u06dc\u06de-\u06e4\u06e7\u06e8\u06ea-\u06ed\u0711\u0730-\u074a\u07a6-\u07b0\u07eb-\u07f3\u0816-\u0819\u081b-\u0823\u0825-\u0827\u0829-\u082d\u0900-\u0902\u093c\u0941-\u0948\u094d\u0951-\u0955\u0962\u0963\u0981\u09bc\u09be\u09c1-\u09c4\u09cd\u09d7\u09e2\u09e3\u0a01\u0a02\u0a3c\u0a41\u0a42\u0a47\u0a48\u0a4b-\u0a4d\u0a51\u0a70\u0a71\u0a75\u0a81\u0a82\u0abc\u0ac1-\u0ac5\u0ac7\u0ac8\u0acd\u0ae2\u0ae3\u0b01\u0b3c\u0b3e\u0b3f\u0b41-\u0b44\u0b4d\u0b56\u0b57\u0b62\u0b63\u0b82\u0bbe\u0bc0\u0bcd\u0bd7\u0c3e-\u0c40\u0c46-\u0c48\u0c4a-\u0c4d\u0c55\u0c56\u0c62\u0c63\u0cbc\u0cbf\u0cc2\u0cc6\u0ccc\u0ccd\u0cd5\u0cd6\u0ce2\u0ce3\u0d3e\u0d41-\u0d44\u0d4d\u0d57\u0d62\u0d63\u0dca\u0dcf\u0dd2-\u0dd4\u0dd6\u0ddf\u0e31\u0e34-\u0e3a\u0e47-\u0e4e\u0eb1\u0eb4-\u0eb9\u0ebb\u0ebc\u0ec8-\u0ecd\u0f18\u0f19\u0f35\u0f37\u0f39\u0f71-\u0f7e\u0f80-\u0f84\u0f86\u0f87\u0f90-\u0f97\u0f99-\u0fbc\u0fc6\u102d-\u1030\u1032-\u1037\u1039\u103a\u103d\u103e\u1058\u1059\u105e-\u1060\u1071-\u1074\u1082\u1085\u1086\u108d\u109d\u135f\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17b7-\u17bd\u17c6\u17c9-\u17d3\u17dd\u180b-\u180d\u18a9\u1920-\u1922\u1927\u1928\u1932\u1939-\u193b\u1a17\u1a18\u1a56\u1a58-\u1a5e\u1a60\u1a62\u1a65-\u1a6c\u1a73-\u1a7c\u1a7f\u1b00-\u1b03\u1b34\u1b36-\u1b3a\u1b3c\u1b42\u1b6b-\u1b73\u1b80\u1b81\u1ba2-\u1ba5\u1ba8\u1ba9\u1c2c-\u1c33\u1c36\u1c37\u1cd0-\u1cd2\u1cd4-\u1ce0\u1ce2-\u1ce8\u1ced\u1dc0-\u1de6\u1dfd-\u1dff\u200c\u200d\u20d0-\u20f0\u2cef-\u2cf1\u2de0-\u2dff\u302a-\u302f\u3099\u309a\ua66f-\ua672\ua67c\ua67d\ua6f0\ua6f1\ua802\ua806\ua80b\ua825\ua826\ua8c4\ua8e0-\ua8f1\ua926-\ua92d\ua947-\ua951\ua980-\ua982\ua9b3\ua9b6-\ua9b9\ua9bc\uaa29-\uaa2e\uaa31\uaa32\uaa35\uaa36\uaa43\uaa4c\uaab0\uaab2-\uaab4\uaab7\uaab8\uaabe\uaabf\uaac1\uabe5\uabe8\uabed\udc00-\udfff\ufb1e\ufe00-\ufe0f\ufe20-\ufe26\uff9e\uff9f]/;function ue(e){return e.charCodeAt(0)>=768&&ce.test(e)}function de(e,t,r){for(;(r<0?t>0:t<e.length)&&ue(e.charAt(t));)t+=r;return t}function pe(e,t,r){for(var n=t>r?-1:1;;){if(t==r)return t;var i=(t+r)/2,o=n<0?Math.ceil(i):Math.floor(i);if(o==t)return e(o)?t:r;e(o)?r=o:t=o+n}}function fe(e,t,r,n){if(!e)return n(t,r,"ltr",0);for(var i=!1,o=0;o<e.length;++o){var a=e[o];(a.from<r&&a.to>t||t==r&&a.to==t)&&(n(Math.max(a.from,t),Math.min(a.to,r),1==a.level?"rtl":"ltr",o),i=!0)}i||n(t,r,"ltr")}var me=null;function he(e,t,r){var n;me=null;for(var i=0;i<e.length;++i){var o=e[i];if(o.from<t&&o.to>t)return i;o.to==t&&(o.from!=o.to&&"before"==r?n=i:me=i),o.from==t&&(o.from!=o.to&&"before"!=r?n=i:me=i)}return null!=n?n:me}var ge=function(){var e="bbbbbbbbbtstwsbbbbbbbbbbbbbbssstwNN%%%NNNNNN,N,N1111111111NNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNbbbbbbsbbbbbbbbbbbbbbbbbbbbbbbbbb,N%%%%NNNNLNNNNN%%11NLNNN1LNNNNNLLLLLLLLLLLLLLLLLLLLLLLNLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLN",t="nnnnnnNNr%%r,rNNmmmmmmmmmmmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmmmmmmmmmmmmmmmnnnnnnnnnn%nnrrrmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmnNmmmmmmrrmmNmmmmrr1111111111";function r(r){return r<=247?e.charAt(r):1424<=r&&r<=1524?"R":1536<=r&&r<=1785?t.charAt(r-1536):1774<=r&&r<=2220?"r":8192<=r&&r<=8203?"w":8204==r?"b":"L"}var n=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/,i=/[stwN]/,o=/[LRr]/,a=/[Lb1n]/,s=/[1n]/;function l(e,t,r){this.level=e,this.from=t,this.to=r}return function(e,t){var c="ltr"==t?"L":"R";if(0==e.length||"ltr"==t&&!n.test(e))return!1;for(var u=e.length,d=[],p=0;p<u;++p)d.push(r(e.charCodeAt(p)));for(var f=0,m=c;f<u;++f){var h=d[f];"m"==h?d[f]=m:m=h}for(var g=0,v=c;g<u;++g){var y=d[g];"1"==y&&"r"==v?d[g]="n":o.test(y)&&(v=y,"r"==y&&(d[g]="R"))}for(var _=1,b=d[0];_<u-1;++_){var x=d[_];"+"==x&&"1"==b&&"1"==d[_+1]?d[_]="1":","!=x||b!=d[_+1]||"1"!=b&&"n"!=b||(d[_]=b),b=x}for(var k=0;k<u;++k){var w=d[k];if(","==w)d[k]="N";else if("%"==w){var S=void 0;for(S=k+1;S<u&&"%"==d[S];++S);for(var C=k&&"!"==d[k-1]||S<u&&"1"==d[S]?"1":"N",M=k;M<S;++M)d[M]=C;k=S-1}}for(var T=0,L=c;T<u;++T){var z=d[T];"L"==L&&"1"==z?d[T]="L":o.test(z)&&(L=z)}for(var D=0;D<u;++D)if(i.test(d[D])){var A=void 0;for(A=D+1;A<u&&i.test(d[A]);++A);for(var q="L"==(D?d[D-1]:c),N=q==("L"==(A<u?d[A]:c))?q?"L":"R":c,F=D;F<A;++F)d[F]=N;D=A-1}for(var E,O=[],I=0;I<u;)if(a.test(d[I])){var P=I;for(++I;I<u&&a.test(d[I]);++I);O.push(new l(0,P,I))}else{var j=I,W=O.length,B="rtl"==t?1:0;for(++I;I<u&&"L"!=d[I];++I);for(var H=j;H<I;)if(s.test(d[H])){j<H&&(O.splice(W,0,new l(1,j,H)),W+=B);var R=H;for(++H;H<I&&s.test(d[H]);++H);O.splice(W,0,new l(2,R,H)),W+=B,j=H}else++H;j<I&&O.splice(W,0,new l(1,j,I))}return"ltr"==t&&(1==O[0].level&&(E=e.match(/^\s+/))&&(O[0].from=E[0].length,O.unshift(new l(0,0,E[0].length))),1==ee(O).level&&(E=e.match(/\s+$/))&&(ee(O).to-=E[0].length,O.push(new l(0,u-E[0].length,u)))),"rtl"==t?O.reverse():O}}();function ve(e,t){var r=e.order;return null==r&&(r=e.order=ge(e.text,t)),r}var ye=[],_e=function(e,t,r){if(e.addEventListener)e.addEventListener(t,r,!1);else if(e.attachEvent)e.attachEvent("on"+t,r);else{var n=e._handlers||(e._handlers={});n[t]=(n[t]||ye).concat(r)}};function be(e,t){return e._handlers&&e._handlers[t]||ye}function xe(e,t,r){if(e.removeEventListener)e.removeEventListener(t,r,!1);else if(e.detachEvent)e.detachEvent("on"+t,r);else{var n=e._handlers,i=n&&n[t];if(i){var o=U(i,r);o>-1&&(n[t]=i.slice(0,o).concat(i.slice(o+1)))}}}function ke(e,t){var r=be(e,t);if(r.length)for(var n=Array.prototype.slice.call(arguments,2),i=0;i<r.length;++i)r[i].apply(null,n)}function we(e,t,r){return"string"==typeof t&&(t={type:t,preventDefault:function(){this.defaultPrevented=!0}}),ke(e,r||t.type,e,t),ze(t)||t.codemirrorIgnore}function Se(e){var t=e._handlers&&e._handlers.cursorActivity;if(t)for(var r=e.curOp.cursorActivityHandlers||(e.curOp.cursorActivityHandlers=[]),n=0;n<t.length;++n)-1==U(r,t[n])&&r.push(t[n])}function Ce(e,t){return be(e,t).length>0}function Me(e){e.prototype.on=function(e,t){_e(this,e,t)},e.prototype.off=function(e,t){xe(this,e,t)}}function Te(e){e.preventDefault?e.preventDefault():e.returnValue=!1}function Le(e){e.stopPropagation?e.stopPropagation():e.cancelBubble=!0}function ze(e){return null!=e.defaultPrevented?e.defaultPrevented:0==e.returnValue}function De(e){Te(e),Le(e)}function Ae(e){return e.target||e.srcElement}function qe(e){var t=e.which;return null==t&&(1&e.button?t=1:2&e.button?t=3:4&e.button&&(t=2)),_&&e.ctrlKey&&1==t&&(t=3),t}var Ne,Fe,Ee=function(){if(a&&s<9)return!1;var e=D("div");return"draggable"in e||"dragDrop"in e}();function Oe(e){if(null==Ne){var t=D("span","​");z(e,D("span",[t,document.createTextNode("x")])),0!=e.firstChild.offsetHeight&&(Ne=t.offsetWidth<=1&&t.offsetHeight>2&&!(a&&s<8))}var r=Ne?D("span","​"):D("span"," ",null,"display: inline-block; width: 1px; margin-right: -1px");return r.setAttribute("cm-text",""),r}function Ie(e){if(null!=Fe)return Fe;var t=z(e,document.createTextNode("AخA")),r=M(t,0,1).getBoundingClientRect(),n=M(t,1,2).getBoundingClientRect();return L(e),!(!r||r.left==r.right)&&(Fe=n.right-r.right<3)}var Pe,je=3!="\n\nb".split(/\n/).length?function(e){for(var t=0,r=[],n=e.length;t<=n;){var i=e.indexOf("\n",t);-1==i&&(i=e.length);var o=e.slice(t,"\r"==e.charAt(i-1)?i-1:i),a=o.indexOf("\r");-1!=a?(r.push(o.slice(0,a)),t+=a+1):(r.push(o),t=i+1)}return r}:function(e){return e.split(/\r\n?|\n/)},We=window.getSelection?function(e){try{return e.selectionStart!=e.selectionEnd}catch(e){return!1}}:function(e){var t;try{t=e.ownerDocument.selection.createRange()}catch(e){}return!(!t||t.parentElement()!=e)&&0!=t.compareEndPoints("StartToEnd",t)},Be="oncopy"in(Pe=D("div"))||(Pe.setAttribute("oncopy","return;"),"function"==typeof Pe.oncopy),He=null;function Re(e){if(null!=He)return He;var t=z(e,D("span","x")),r=t.getBoundingClientRect(),n=M(t,0,1).getBoundingClientRect();return He=Math.abs(r.left-n.left)>1}var $e={},Ue={};function Ve(e,t){arguments.length>2&&(t.dependencies=Array.prototype.slice.call(arguments,2)),$e[e]=t}function Ke(e,t){Ue[e]=t}function Ge(e){if("string"==typeof e&&Ue.hasOwnProperty(e))e=Ue[e];else if(e&&"string"==typeof e.name&&Ue.hasOwnProperty(e.name)){var t=Ue[e.name];"string"==typeof t&&(t={name:t}),(e=ie(t,e)).name=t.name}else{if("string"==typeof e&&/^[\w\-]+\/[\w\-]+\+xml$/.test(e))return Ge("application/xml");if("string"==typeof e&&/^[\w\-]+\/[\w\-]+\+json$/.test(e))return Ge("application/json")}return"string"==typeof e?{name:e}:e||{name:"null"}}function Xe(e,t){t=Ge(t);var r=$e[t.name];if(!r)return Xe(e,"text/plain");var n=r(e,t);if(Qe.hasOwnProperty(t.name)){var i=Qe[t.name];for(var o in i)i.hasOwnProperty(o)&&(n.hasOwnProperty(o)&&(n["_"+o]=n[o]),n[o]=i[o])}if(n.name=t.name,t.helperType&&(n.helperType=t.helperType),t.modeProps)for(var a in t.modeProps)n[a]=t.modeProps[a];return n}var Qe={};function Ye(e,t){H(t,Qe.hasOwnProperty(e)?Qe[e]:Qe[e]={})}function Ze(e,t){if(!0===t)return t;if(e.copyState)return e.copyState(t);var r={};for(var n in t){var i=t[n];i instanceof Array&&(i=i.concat([])),r[n]=i}return r}function Je(e,t){for(var r;e.innerMode&&(r=e.innerMode(t))&&r.mode!=e;)t=r.state,e=r.mode;return r||{mode:e,state:t}}function et(e,t,r){return!e.startState||e.startState(t,r)}var tt=function(e,t,r){this.pos=this.start=0,this.string=e,this.tabSize=t||8,this.lastColumnPos=this.lastColumnValue=0,this.lineStart=0,this.lineOracle=r};function rt(e,t){if((t-=e.first)<0||t>=e.size)throw new Error("There is no line "+(t+e.first)+" in the document.");for(var r=e;!r.lines;)for(var n=0;;++n){var i=r.children[n],o=i.chunkSize();if(t<o){r=i;break}t-=o}return r.lines[t]}function nt(e,t,r){var n=[],i=t.line;return e.iter(t.line,r.line+1,(function(e){var o=e.text;i==r.line&&(o=o.slice(0,r.ch)),i==t.line&&(o=o.slice(t.ch)),n.push(o),++i})),n}function it(e,t,r){var n=[];return e.iter(t,r,(function(e){n.push(e.text)})),n}function ot(e,t){var r=t-e.height;if(r)for(var n=e;n;n=n.parent)n.height+=r}function at(e){if(null==e.parent)return null;for(var t=e.parent,r=U(t.lines,e),n=t.parent;n;t=n,n=n.parent)for(var i=0;n.children[i]!=t;++i)r+=n.children[i].chunkSize();return r+t.first}function st(e,t){var r=e.first;e:do{for(var n=0;n<e.children.length;++n){var i=e.children[n],o=i.height;if(t<o){e=i;continue e}t-=o,r+=i.chunkSize()}return r}while(!e.lines);for(var a=0;a<e.lines.length;++a){var s=e.lines[a].height;if(t<s)break;t-=s}return r+a}function lt(e,t){return t>=e.first&&t<e.first+e.size}function ct(e,t){return String(e.lineNumberFormatter(t+e.firstLineNumber))}function ut(e,t,r){if(void 0===r&&(r=null),!(this instanceof ut))return new ut(e,t,r);this.line=e,this.ch=t,this.sticky=r}function dt(e,t){return e.line-t.line||e.ch-t.ch}function pt(e,t){return e.sticky==t.sticky&&0==dt(e,t)}function ft(e){return ut(e.line,e.ch)}function mt(e,t){return dt(e,t)<0?t:e}function ht(e,t){return dt(e,t)<0?e:t}function gt(e,t){return Math.max(e.first,Math.min(t,e.first+e.size-1))}function vt(e,t){if(t.line<e.first)return ut(e.first,0);var r=e.first+e.size-1;return t.line>r?ut(r,rt(e,r).text.length):yt(t,rt(e,t.line).text.length)}function yt(e,t){var r=e.ch;return null==r||r>t?ut(e.line,t):r<0?ut(e.line,0):e}function _t(e,t){for(var r=[],n=0;n<t.length;n++)r[n]=vt(e,t[n]);return r}tt.prototype.eol=function(){return this.pos>=this.string.length},tt.prototype.sol=function(){return this.pos==this.lineStart},tt.prototype.peek=function(){return this.string.charAt(this.pos)||void 0},tt.prototype.next=function(){if(this.pos<this.string.length)return this.string.charAt(this.pos++)},tt.prototype.eat=function(e){var t=this.string.charAt(this.pos);if("string"==typeof e?t==e:t&&(e.test?e.test(t):e(t)))return++this.pos,t},tt.prototype.eatWhile=function(e){for(var t=this.pos;this.eat(e););return this.pos>t},tt.prototype.eatSpace=function(){for(var e=this.pos;/[\s\u00a0]/.test(this.string.charAt(this.pos));)++this.pos;return this.pos>e},tt.prototype.skipToEnd=function(){this.pos=this.string.length},tt.prototype.skipTo=function(e){var t=this.string.indexOf(e,this.pos);if(t>-1)return this.pos=t,!0},tt.prototype.backUp=function(e){this.pos-=e},tt.prototype.column=function(){return this.lastColumnPos<this.start&&(this.lastColumnValue=R(this.string,this.start,this.tabSize,this.lastColumnPos,this.lastColumnValue),this.lastColumnPos=this.start),this.lastColumnValue-(this.lineStart?R(this.string,this.lineStart,this.tabSize):0)},tt.prototype.indentation=function(){return R(this.string,null,this.tabSize)-(this.lineStart?R(this.string,this.lineStart,this.tabSize):0)},tt.prototype.match=function(e,t,r){if("string"!=typeof e){var n=this.string.slice(this.pos).match(e);return n&&n.index>0?null:(n&&!1!==t&&(this.pos+=n[0].length),n)}var i=function(e){return r?e.toLowerCase():e};if(i(this.string.substr(this.pos,e.length))==i(e))return!1!==t&&(this.pos+=e.length),!0},tt.prototype.current=function(){return this.string.slice(this.start,this.pos)},tt.prototype.hideFirstChars=function(e,t){this.lineStart+=e;try{return t()}finally{this.lineStart-=e}},tt.prototype.lookAhead=function(e){var t=this.lineOracle;return t&&t.lookAhead(e)},tt.prototype.baseToken=function(){var e=this.lineOracle;return e&&e.baseToken(this.pos)};var bt=function(e,t){this.state=e,this.lookAhead=t},xt=function(e,t,r,n){this.state=t,this.doc=e,this.line=r,this.maxLookAhead=n||0,this.baseTokens=null,this.baseTokenPos=1};function kt(e,t,r,n){var i=[e.state.modeGen],o={};At(e,t.text,e.doc.mode,r,(function(e,t){return i.push(e,t)}),o,n);for(var a=r.state,s=function(n){r.baseTokens=i;var s=e.state.overlays[n],l=1,c=0;r.state=!0,At(e,t.text,s.mode,r,(function(e,t){for(var r=l;c<e;){var n=i[l];n>e&&i.splice(l,1,e,i[l+1],n),l+=2,c=Math.min(e,n)}if(t)if(s.opaque)i.splice(r,l-r,e,"overlay "+t),l=r+2;else for(;r<l;r+=2){var o=i[r+1];i[r+1]=(o?o+" ":"")+"overlay "+t}}),o),r.state=a,r.baseTokens=null,r.baseTokenPos=1},l=0;l<e.state.overlays.length;++l)s(l);return{styles:i,classes:o.bgClass||o.textClass?o:null}}function wt(e,t,r){if(!t.styles||t.styles[0]!=e.state.modeGen){var n=St(e,at(t)),i=t.text.length>e.options.maxHighlightLength&&Ze(e.doc.mode,n.state),o=kt(e,t,n);i&&(n.state=i),t.stateAfter=n.save(!i),t.styles=o.styles,o.classes?t.styleClasses=o.classes:t.styleClasses&&(t.styleClasses=null),r===e.doc.highlightFrontier&&(e.doc.modeFrontier=Math.max(e.doc.modeFrontier,++e.doc.highlightFrontier))}return t.styles}function St(e,t,r){var n=e.doc,i=e.display;if(!n.mode.startState)return new xt(n,!0,t);var o=qt(e,t,r),a=o>n.first&&rt(n,o-1).stateAfter,s=a?xt.fromSaved(n,a,o):new xt(n,et(n.mode),o);return n.iter(o,t,(function(r){Ct(e,r.text,s);var n=s.line;r.stateAfter=n==t-1||n%5==0||n>=i.viewFrom&&n<i.viewTo?s.save():null,s.nextLine()})),r&&(n.modeFrontier=s.line),s}function Ct(e,t,r,n){var i=e.doc.mode,o=new tt(t,e.options.tabSize,r);for(o.start=o.pos=n||0,""==t&&Mt(i,r.state);!o.eol();)Tt(i,o,r.state),o.start=o.pos}function Mt(e,t){if(e.blankLine)return e.blankLine(t);if(e.innerMode){var r=Je(e,t);return r.mode.blankLine?r.mode.blankLine(r.state):void 0}}function Tt(e,t,r,n){for(var i=0;i<10;i++){n&&(n[0]=Je(e,r).mode);var o=e.token(t,r);if(t.pos>t.start)return o}throw new Error("Mode "+e.name+" failed to advance stream.")}xt.prototype.lookAhead=function(e){var t=this.doc.getLine(this.line+e);return null!=t&&e>this.maxLookAhead&&(this.maxLookAhead=e),t},xt.prototype.baseToken=function(e){if(!this.baseTokens)return null;for(;this.baseTokens[this.baseTokenPos]<=e;)this.baseTokenPos+=2;var t=this.baseTokens[this.baseTokenPos+1];return{type:t&&t.replace(/( |^)overlay .*/,""),size:this.baseTokens[this.baseTokenPos]-e}},xt.prototype.nextLine=function(){this.line++,this.maxLookAhead>0&&this.maxLookAhead--},xt.fromSaved=function(e,t,r){return t instanceof bt?new xt(e,Ze(e.mode,t.state),r,t.lookAhead):new xt(e,Ze(e.mode,t),r)},xt.prototype.save=function(e){var t=!1!==e?Ze(this.doc.mode,this.state):this.state;return this.maxLookAhead>0?new bt(t,this.maxLookAhead):t};var Lt=function(e,t,r){this.start=e.start,this.end=e.pos,this.string=e.current(),this.type=t||null,this.state=r};function zt(e,t,r,n){var i,o,a=e.doc,s=a.mode,l=rt(a,(t=vt(a,t)).line),c=St(e,t.line,r),u=new tt(l.text,e.options.tabSize,c);for(n&&(o=[]);(n||u.pos<t.ch)&&!u.eol();)u.start=u.pos,i=Tt(s,u,c.state),n&&o.push(new Lt(u,i,Ze(a.mode,c.state)));return n?o:new Lt(u,i,c.state)}function Dt(e,t){if(e)for(;;){var r=e.match(/(?:^|\s+)line-(background-)?(\S+)/);if(!r)break;e=e.slice(0,r.index)+e.slice(r.index+r[0].length);var n=r[1]?"bgClass":"textClass";null==t[n]?t[n]=r[2]:new RegExp("(?:^|\\s)"+r[2]+"(?:$|\\s)").test(t[n])||(t[n]+=" "+r[2])}return e}function At(e,t,r,n,i,o,a){var s=r.flattenSpans;null==s&&(s=e.options.flattenSpans);var l,c=0,u=null,d=new tt(t,e.options.tabSize,n),p=e.options.addModeClass&&[null];for(""==t&&Dt(Mt(r,n.state),o);!d.eol();){if(d.pos>e.options.maxHighlightLength?(s=!1,a&&Ct(e,t,n,d.pos),d.pos=t.length,l=null):l=Dt(Tt(r,d,n.state,p),o),p){var f=p[0].name;f&&(l="m-"+(l?f+" "+l:f))}if(!s||u!=l){for(;c<d.start;)i(c=Math.min(d.start,c+5e3),u);u=l}d.start=d.pos}for(;c<d.pos;){var m=Math.min(d.pos,c+5e3);i(m,u),c=m}}function qt(e,t,r){for(var n,i,o=e.doc,a=r?-1:t-(e.doc.mode.innerMode?1e3:100),s=t;s>a;--s){if(s<=o.first)return o.first;var l=rt(o,s-1),c=l.stateAfter;if(c&&(!r||s+(c instanceof bt?c.lookAhead:0)<=o.modeFrontier))return s;var u=R(l.text,null,e.options.tabSize);(null==i||n>u)&&(i=s-1,n=u)}return i}function Nt(e,t){if(e.modeFrontier=Math.min(e.modeFrontier,t),!(e.highlightFrontier<t-10)){for(var r=e.first,n=t-1;n>r;n--){var i=rt(e,n).stateAfter;if(i&&(!(i instanceof bt)||n+i.lookAhead<t)){r=n+1;break}}e.highlightFrontier=Math.min(e.highlightFrontier,r)}}var Ft=!1,Et=!1;function Ot(){Ft=!0}function It(){Et=!0}function Pt(e,t,r){this.marker=e,this.from=t,this.to=r}function jt(e,t){if(e)for(var r=0;r<e.length;++r){var n=e[r];if(n.marker==t)return n}}function Wt(e,t){for(var r,n=0;n<e.length;++n)e[n]!=t&&(r||(r=[])).push(e[n]);return r}function Bt(e,t,r){var n=r&&window.WeakSet&&(r.markedSpans||(r.markedSpans=new WeakSet));n&&e.markedSpans&&n.has(e.markedSpans)?e.markedSpans.push(t):(e.markedSpans=e.markedSpans?e.markedSpans.concat([t]):[t],n&&n.add(e.markedSpans)),t.marker.attachLine(e)}function Ht(e,t,r){var n;if(e)for(var i=0;i<e.length;++i){var o=e[i],a=o.marker;if(null==o.from||(a.inclusiveLeft?o.from<=t:o.from<t)||o.from==t&&"bookmark"==a.type&&(!r||!o.marker.insertLeft)){var s=null==o.to||(a.inclusiveRight?o.to>=t:o.to>t);(n||(n=[])).push(new Pt(a,o.from,s?null:o.to))}}return n}function Rt(e,t,r){var n;if(e)for(var i=0;i<e.length;++i){var o=e[i],a=o.marker;if(null==o.to||(a.inclusiveRight?o.to>=t:o.to>t)||o.from==t&&"bookmark"==a.type&&(!r||o.marker.insertLeft)){var s=null==o.from||(a.inclusiveLeft?o.from<=t:o.from<t);(n||(n=[])).push(new Pt(a,s?null:o.from-t,null==o.to?null:o.to-t))}}return n}function $t(e,t){if(t.full)return null;var r=lt(e,t.from.line)&&rt(e,t.from.line).markedSpans,n=lt(e,t.to.line)&&rt(e,t.to.line).markedSpans;if(!r&&!n)return null;var i=t.from.ch,o=t.to.ch,a=0==dt(t.from,t.to),s=Ht(r,i,a),l=Rt(n,o,a),c=1==t.text.length,u=ee(t.text).length+(c?i:0);if(s)for(var d=0;d<s.length;++d){var p=s[d];if(null==p.to){var f=jt(l,p.marker);f?c&&(p.to=null==f.to?null:f.to+u):p.to=i}}if(l)for(var m=0;m<l.length;++m){var h=l[m];null!=h.to&&(h.to+=u),null==h.from?jt(s,h.marker)||(h.from=u,c&&(s||(s=[])).push(h)):(h.from+=u,c&&(s||(s=[])).push(h))}s&&(s=Ut(s)),l&&l!=s&&(l=Ut(l));var g=[s];if(!c){var v,y=t.text.length-2;if(y>0&&s)for(var _=0;_<s.length;++_)null==s[_].to&&(v||(v=[])).push(new Pt(s[_].marker,null,null));for(var b=0;b<y;++b)g.push(v);g.push(l)}return g}function Ut(e){for(var t=0;t<e.length;++t){var r=e[t];null!=r.from&&r.from==r.to&&!1!==r.marker.clearWhenEmpty&&e.splice(t--,1)}return e.length?e:null}function Vt(e,t,r){var n=null;if(e.iter(t.line,r.line+1,(function(e){if(e.markedSpans)for(var t=0;t<e.markedSpans.length;++t){var r=e.markedSpans[t].marker;!r.readOnly||n&&-1!=U(n,r)||(n||(n=[])).push(r)}})),!n)return null;for(var i=[{from:t,to:r}],o=0;o<n.length;++o)for(var a=n[o],s=a.find(0),l=0;l<i.length;++l){var c=i[l];if(!(dt(c.to,s.from)<0||dt(c.from,s.to)>0)){var u=[l,1],d=dt(c.from,s.from),p=dt(c.to,s.to);(d<0||!a.inclusiveLeft&&!d)&&u.push({from:c.from,to:s.from}),(p>0||!a.inclusiveRight&&!p)&&u.push({from:s.to,to:c.to}),i.splice.apply(i,u),l+=u.length-3}}return i}function Kt(e){var t=e.markedSpans;if(t){for(var r=0;r<t.length;++r)t[r].marker.detachLine(e);e.markedSpans=null}}function Gt(e,t){if(t){for(var r=0;r<t.length;++r)t[r].marker.attachLine(e);e.markedSpans=t}}function Xt(e){return e.inclusiveLeft?-1:0}function Qt(e){return e.inclusiveRight?1:0}function Yt(e,t){var r=e.lines.length-t.lines.length;if(0!=r)return r;var n=e.find(),i=t.find(),o=dt(n.from,i.from)||Xt(e)-Xt(t);if(o)return-o;var a=dt(n.to,i.to)||Qt(e)-Qt(t);return a||t.id-e.id}function Zt(e,t){var r,n=Et&&e.markedSpans;if(n)for(var i=void 0,o=0;o<n.length;++o)(i=n[o]).marker.collapsed&&null==(t?i.from:i.to)&&(!r||Yt(r,i.marker)<0)&&(r=i.marker);return r}function Jt(e){return Zt(e,!0)}function er(e){return Zt(e,!1)}function tr(e,t){var r,n=Et&&e.markedSpans;if(n)for(var i=0;i<n.length;++i){var o=n[i];o.marker.collapsed&&(null==o.from||o.from<t)&&(null==o.to||o.to>t)&&(!r||Yt(r,o.marker)<0)&&(r=o.marker)}return r}function rr(e,t,r,n,i){var o=rt(e,t),a=Et&&o.markedSpans;if(a)for(var s=0;s<a.length;++s){var l=a[s];if(l.marker.collapsed){var c=l.marker.find(0),u=dt(c.from,r)||Xt(l.marker)-Xt(i),d=dt(c.to,n)||Qt(l.marker)-Qt(i);if(!(u>=0&&d<=0||u<=0&&d>=0)&&(u<=0&&(l.marker.inclusiveRight&&i.inclusiveLeft?dt(c.to,r)>=0:dt(c.to,r)>0)||u>=0&&(l.marker.inclusiveRight&&i.inclusiveLeft?dt(c.from,n)<=0:dt(c.from,n)<0)))return!0}}}function nr(e){for(var t;t=Jt(e);)e=t.find(-1,!0).line;return e}function ir(e){for(var t;t=er(e);)e=t.find(1,!0).line;return e}function or(e){for(var t,r;t=er(e);)e=t.find(1,!0).line,(r||(r=[])).push(e);return r}function ar(e,t){var r=rt(e,t),n=nr(r);return r==n?t:at(n)}function sr(e,t){if(t>e.lastLine())return t;var r,n=rt(e,t);if(!lr(e,n))return t;for(;r=er(n);)n=r.find(1,!0).line;return at(n)+1}function lr(e,t){var r=Et&&t.markedSpans;if(r)for(var n=void 0,i=0;i<r.length;++i)if((n=r[i]).marker.collapsed){if(null==n.from)return!0;if(!n.marker.widgetNode&&0==n.from&&n.marker.inclusiveLeft&&cr(e,t,n))return!0}}function cr(e,t,r){if(null==r.to){var n=r.marker.find(1,!0);return cr(e,n.line,jt(n.line.markedSpans,r.marker))}if(r.marker.inclusiveRight&&r.to==t.text.length)return!0;for(var i=void 0,o=0;o<t.markedSpans.length;++o)if((i=t.markedSpans[o]).marker.collapsed&&!i.marker.widgetNode&&i.from==r.to&&(null==i.to||i.to!=r.from)&&(i.marker.inclusiveLeft||r.marker.inclusiveRight)&&cr(e,t,i))return!0}function ur(e){for(var t=0,r=(e=nr(e)).parent,n=0;n<r.lines.length;++n){var i=r.lines[n];if(i==e)break;t+=i.height}for(var o=r.parent;o;o=(r=o).parent)for(var a=0;a<o.children.length;++a){var s=o.children[a];if(s==r)break;t+=s.height}return t}function dr(e){if(0==e.height)return 0;for(var t,r=e.text.length,n=e;t=Jt(n);){var i=t.find(0,!0);n=i.from.line,r+=i.from.ch-i.to.ch}for(n=e;t=er(n);){var o=t.find(0,!0);r-=n.text.length-o.from.ch,r+=(n=o.to.line).text.length-o.to.ch}return r}function pr(e){var t=e.display,r=e.doc;t.maxLine=rt(r,r.first),t.maxLineLength=dr(t.maxLine),t.maxLineChanged=!0,r.iter((function(e){var r=dr(e);r>t.maxLineLength&&(t.maxLineLength=r,t.maxLine=e)}))}var fr=function(e,t,r){this.text=e,Gt(this,t),this.height=r?r(this):1};function mr(e,t,r,n){e.text=t,e.stateAfter&&(e.stateAfter=null),e.styles&&(e.styles=null),null!=e.order&&(e.order=null),Kt(e),Gt(e,r);var i=n?n(e):1;i!=e.height&&ot(e,i)}function hr(e){e.parent=null,Kt(e)}fr.prototype.lineNo=function(){return at(this)},Me(fr);var gr={},vr={};function yr(e,t){if(!e||/^\s*$/.test(e))return null;var r=t.addModeClass?vr:gr;return r[e]||(r[e]=e.replace(/\S+/g,"cm-$&"))}function _r(e,t){var r=A("span",null,null,l?"padding-right: .1px":null),n={pre:A("pre",[r],"CodeMirror-line"),content:r,col:0,pos:0,cm:e,trailingSpace:!1,splitSpaces:e.getOption("lineWrapping")};t.measure={};for(var i=0;i<=(t.rest?t.rest.length:0);i++){var o=i?t.rest[i-1]:t.line,a=void 0;n.pos=0,n.addToken=xr,Ie(e.display.measure)&&(a=ve(o,e.doc.direction))&&(n.addToken=wr(n.addToken,a)),n.map=[],Cr(o,n,wt(e,o,t!=e.display.externalMeasured&&at(o))),o.styleClasses&&(o.styleClasses.bgClass&&(n.bgClass=E(o.styleClasses.bgClass,n.bgClass||"")),o.styleClasses.textClass&&(n.textClass=E(o.styleClasses.textClass,n.textClass||""))),0==n.map.length&&n.map.push(0,0,n.content.appendChild(Oe(e.display.measure))),0==i?(t.measure.map=n.map,t.measure.cache={}):((t.measure.maps||(t.measure.maps=[])).push(n.map),(t.measure.caches||(t.measure.caches=[])).push({}))}if(l){var s=n.content.lastChild;(/\bcm-tab\b/.test(s.className)||s.querySelector&&s.querySelector(".cm-tab"))&&(n.content.className="cm-tab-wrap-hack")}return ke(e,"renderLine",e,t.line,n.pre),n.pre.className&&(n.textClass=E(n.pre.className,n.textClass||"")),n}function br(e){var t=D("span","•","cm-invalidchar");return t.title="\\u"+e.charCodeAt(0).toString(16),t.setAttribute("aria-label",t.title),t}function xr(e,t,r,n,i,o,l){if(t){var c,u=e.splitSpaces?kr(t,e.trailingSpace):t,d=e.cm.state.specialChars,p=!1;if(d.test(t)){c=document.createDocumentFragment();for(var f=0;;){d.lastIndex=f;var m=d.exec(t),h=m?m.index-f:t.length-f;if(h){var g=document.createTextNode(u.slice(f,f+h));a&&s<9?c.appendChild(D("span",[g])):c.appendChild(g),e.map.push(e.pos,e.pos+h,g),e.col+=h,e.pos+=h}if(!m)break;f+=h+1;var v=void 0;if("\t"==m[0]){var y=e.cm.options.tabSize,_=y-e.col%y;(v=c.appendChild(D("span",J(_),"cm-tab"))).setAttribute("role","presentation"),v.setAttribute("cm-text","\t"),e.col+=_}else"\r"==m[0]||"\n"==m[0]?((v=c.appendChild(D("span","\r"==m[0]?"␍":"␤","cm-invalidchar"))).setAttribute("cm-text",m[0]),e.col+=1):((v=e.cm.options.specialCharPlaceholder(m[0])).setAttribute("cm-text",m[0]),a&&s<9?c.appendChild(D("span",[v])):c.appendChild(v),e.col+=1);e.map.push(e.pos,e.pos+1,v),e.pos++}}else e.col+=t.length,c=document.createTextNode(u),e.map.push(e.pos,e.pos+t.length,c),a&&s<9&&(p=!0),e.pos+=t.length;if(e.trailingSpace=32==u.charCodeAt(t.length-1),r||n||i||p||o||l){var b=r||"";n&&(b+=n),i&&(b+=i);var x=D("span",[c],b,o);if(l)for(var k in l)l.hasOwnProperty(k)&&"style"!=k&&"class"!=k&&x.setAttribute(k,l[k]);return e.content.appendChild(x)}e.content.appendChild(c)}}function kr(e,t){if(e.length>1&&!/  /.test(e))return e;for(var r=t,n="",i=0;i<e.length;i++){var o=e.charAt(i);" "!=o||!r||i!=e.length-1&&32!=e.charCodeAt(i+1)||(o=" "),n+=o,r=" "==o}return n}function wr(e,t){return function(r,n,i,o,a,s,l){i=i?i+" cm-force-border":"cm-force-border";for(var c=r.pos,u=c+n.length;;){for(var d=void 0,p=0;p<t.length&&!((d=t[p]).to>c&&d.from<=c);p++);if(d.to>=u)return e(r,n,i,o,a,s,l);e(r,n.slice(0,d.to-c),i,o,null,s,l),o=null,n=n.slice(d.to-c),c=d.to}}}function Sr(e,t,r,n){var i=!n&&r.widgetNode;i&&e.map.push(e.pos,e.pos+t,i),!n&&e.cm.display.input.needsContentAttribute&&(i||(i=e.content.appendChild(document.createElement("span"))),i.setAttribute("cm-marker",r.id)),i&&(e.cm.display.input.setUneditable(i),e.content.appendChild(i)),e.pos+=t,e.trailingSpace=!1}function Cr(e,t,r){var n=e.markedSpans,i=e.text,o=0;if(n)for(var a,s,l,c,u,d,p,f=i.length,m=0,h=1,g="",v=0;;){if(v==m){l=c=u=s="",p=null,d=null,v=1/0;for(var y=[],_=void 0,b=0;b<n.length;++b){var x=n[b],k=x.marker;if("bookmark"==k.type&&x.from==m&&k.widgetNode)y.push(k);else if(x.from<=m&&(null==x.to||x.to>m||k.collapsed&&x.to==m&&x.from==m)){if(null!=x.to&&x.to!=m&&v>x.to&&(v=x.to,c=""),k.className&&(l+=" "+k.className),k.css&&(s=(s?s+";":"")+k.css),k.startStyle&&x.from==m&&(u+=" "+k.startStyle),k.endStyle&&x.to==v&&(_||(_=[])).push(k.endStyle,x.to),k.title&&((p||(p={})).title=k.title),k.attributes)for(var w in k.attributes)(p||(p={}))[w]=k.attributes[w];k.collapsed&&(!d||Yt(d.marker,k)<0)&&(d=x)}else x.from>m&&v>x.from&&(v=x.from)}if(_)for(var S=0;S<_.length;S+=2)_[S+1]==v&&(c+=" "+_[S]);if(!d||d.from==m)for(var C=0;C<y.length;++C)Sr(t,0,y[C]);if(d&&(d.from||0)==m){if(Sr(t,(null==d.to?f+1:d.to)-m,d.marker,null==d.from),null==d.to)return;d.to==m&&(d=!1)}}if(m>=f)break;for(var M=Math.min(f,v);;){if(g){var T=m+g.length;if(!d){var L=T>M?g.slice(0,M-m):g;t.addToken(t,L,a?a+l:l,u,m+L.length==v?c:"",s,p)}if(T>=M){g=g.slice(M-m),m=M;break}m=T,u=""}g=i.slice(o,o=r[h++]),a=yr(r[h++],t.cm.options)}}else for(var z=1;z<r.length;z+=2)t.addToken(t,i.slice(o,o=r[z]),yr(r[z+1],t.cm.options))}function Mr(e,t,r){this.line=t,this.rest=or(t),this.size=this.rest?at(ee(this.rest))-r+1:1,this.node=this.text=null,this.hidden=lr(e,t)}function Tr(e,t,r){for(var n,i=[],o=t;o<r;o=n){var a=new Mr(e.doc,rt(e.doc,o),o);n=o+a.size,i.push(a)}return i}var Lr=null;function zr(e){Lr?Lr.ops.push(e):e.ownsGroup=Lr={ops:[e],delayedCallbacks:[]}}function Dr(e){var t=e.delayedCallbacks,r=0;do{for(;r<t.length;r++)t[r].call(null);for(var n=0;n<e.ops.length;n++){var i=e.ops[n];if(i.cursorActivityHandlers)for(;i.cursorActivityCalled<i.cursorActivityHandlers.length;)i.cursorActivityHandlers[i.cursorActivityCalled++].call(null,i.cm)}}while(r<t.length)}function Ar(e,t){var r=e.ownsGroup;if(r)try{Dr(r)}finally{Lr=null,t(r)}}var qr=null;function Nr(e,t){var r=be(e,t);if(r.length){var n,i=Array.prototype.slice.call(arguments,2);Lr?n=Lr.delayedCallbacks:qr?n=qr:(n=qr=[],setTimeout(Fr,0));for(var o=function(e){n.push((function(){return r[e].apply(null,i)}))},a=0;a<r.length;++a)o(a)}}function Fr(){var e=qr;qr=null;for(var t=0;t<e.length;++t)e[t]()}function Er(e,t,r,n){for(var i=0;i<t.changes.length;i++){var o=t.changes[i];"text"==o?jr(e,t):"gutter"==o?Br(e,t,r,n):"class"==o?Wr(e,t):"widget"==o&&Hr(e,t,n)}t.changes=null}function Or(e){return e.node==e.text&&(e.node=D("div",null,null,"position: relative"),e.text.parentNode&&e.text.parentNode.replaceChild(e.node,e.text),e.node.appendChild(e.text),a&&s<8&&(e.node.style.zIndex=2)),e.node}function Ir(e,t){var r=t.bgClass?t.bgClass+" "+(t.line.bgClass||""):t.line.bgClass;if(r&&(r+=" CodeMirror-linebackground"),t.background)r?t.background.className=r:(t.background.parentNode.removeChild(t.background),t.background=null);else if(r){var n=Or(t);t.background=n.insertBefore(D("div",null,r),n.firstChild),e.display.input.setUneditable(t.background)}}function Pr(e,t){var r=e.display.externalMeasured;return r&&r.line==t.line?(e.display.externalMeasured=null,t.measure=r.measure,r.built):_r(e,t)}function jr(e,t){var r=t.text.className,n=Pr(e,t);t.text==t.node&&(t.node=n.pre),t.text.parentNode.replaceChild(n.pre,t.text),t.text=n.pre,n.bgClass!=t.bgClass||n.textClass!=t.textClass?(t.bgClass=n.bgClass,t.textClass=n.textClass,Wr(e,t)):r&&(t.text.className=r)}function Wr(e,t){Ir(e,t),t.line.wrapClass?Or(t).className=t.line.wrapClass:t.node!=t.text&&(t.node.className="");var r=t.textClass?t.textClass+" "+(t.line.textClass||""):t.line.textClass;t.text.className=r||""}function Br(e,t,r,n){if(t.gutter&&(t.node.removeChild(t.gutter),t.gutter=null),t.gutterBackground&&(t.node.removeChild(t.gutterBackground),t.gutterBackground=null),t.line.gutterClass){var i=Or(t);t.gutterBackground=D("div",null,"CodeMirror-gutter-background "+t.line.gutterClass,"left: "+(e.options.fixedGutter?n.fixedPos:-n.gutterTotalWidth)+"px; width: "+n.gutterTotalWidth+"px"),e.display.input.setUneditable(t.gutterBackground),i.insertBefore(t.gutterBackground,t.text)}var o=t.line.gutterMarkers;if(e.options.lineNumbers||o){var a=Or(t),s=t.gutter=D("div",null,"CodeMirror-gutter-wrapper","left: "+(e.options.fixedGutter?n.fixedPos:-n.gutterTotalWidth)+"px");if(s.setAttribute("aria-hidden","true"),e.display.input.setUneditable(s),a.insertBefore(s,t.text),t.line.gutterClass&&(s.className+=" "+t.line.gutterClass),!e.options.lineNumbers||o&&o["CodeMirror-linenumbers"]||(t.lineNumber=s.appendChild(D("div",ct(e.options,r),"CodeMirror-linenumber CodeMirror-gutter-elt","left: "+n.gutterLeft["CodeMirror-linenumbers"]+"px; width: "+e.display.lineNumInnerWidth+"px"))),o)for(var l=0;l<e.display.gutterSpecs.length;++l){var c=e.display.gutterSpecs[l].className,u=o.hasOwnProperty(c)&&o[c];u&&s.appendChild(D("div",[u],"CodeMirror-gutter-elt","left: "+n.gutterLeft[c]+"px; width: "+n.gutterWidth[c]+"px"))}}}function Hr(e,t,r){t.alignable&&(t.alignable=null);for(var n=C("CodeMirror-linewidget"),i=t.node.firstChild,o=void 0;i;i=o)o=i.nextSibling,n.test(i.className)&&t.node.removeChild(i);$r(e,t,r)}function Rr(e,t,r,n){var i=Pr(e,t);return t.text=t.node=i.pre,i.bgClass&&(t.bgClass=i.bgClass),i.textClass&&(t.textClass=i.textClass),Wr(e,t),Br(e,t,r,n),$r(e,t,n),t.node}function $r(e,t,r){if(Ur(e,t.line,t,r,!0),t.rest)for(var n=0;n<t.rest.length;n++)Ur(e,t.rest[n],t,r,!1)}function Ur(e,t,r,n,i){if(t.widgets)for(var o=Or(r),a=0,s=t.widgets;a<s.length;++a){var l=s[a],c=D("div",[l.node],"CodeMirror-linewidget"+(l.className?" "+l.className:""));l.handleMouseEvents||c.setAttribute("cm-ignore-events","true"),Vr(l,c,r,n),e.display.input.setUneditable(c),i&&l.above?o.insertBefore(c,r.gutter||r.text):o.appendChild(c),Nr(l,"redraw")}}function Vr(e,t,r,n){if(e.noHScroll){(r.alignable||(r.alignable=[])).push(t);var i=n.wrapperWidth;t.style.left=n.fixedPos+"px",e.coverGutter||(i-=n.gutterTotalWidth,t.style.paddingLeft=n.gutterTotalWidth+"px"),t.style.width=i+"px"}e.coverGutter&&(t.style.zIndex=5,t.style.position="relative",e.noHScroll||(t.style.marginLeft=-n.gutterTotalWidth+"px"))}function Kr(e){if(null!=e.height)return e.height;var t=e.doc.cm;if(!t)return 0;if(!q(document.body,e.node)){var r="position: relative;";e.coverGutter&&(r+="margin-left: -"+t.display.gutters.offsetWidth+"px;"),e.noHScroll&&(r+="width: "+t.display.wrapper.clientWidth+"px;"),z(t.display.measure,D("div",[e.node],null,r))}return e.height=e.node.parentNode.offsetHeight}function Gr(e,t){for(var r=Ae(t);r!=e.wrapper;r=r.parentNode)if(!r||1==r.nodeType&&"true"==r.getAttribute("cm-ignore-events")||r.parentNode==e.sizer&&r!=e.mover)return!0}function Xr(e){return e.lineSpace.offsetTop}function Qr(e){return e.mover.offsetHeight-e.lineSpace.offsetHeight}function Yr(e){if(e.cachedPaddingH)return e.cachedPaddingH;var t=z(e.measure,D("pre","x","CodeMirror-line-like")),r=window.getComputedStyle?window.getComputedStyle(t):t.currentStyle,n={left:parseInt(r.paddingLeft),right:parseInt(r.paddingRight)};return isNaN(n.left)||isNaN(n.right)||(e.cachedPaddingH=n),n}function Zr(e){return V-e.display.nativeBarWidth}function Jr(e){return e.display.scroller.clientWidth-Zr(e)-e.display.barWidth}function en(e){return e.display.scroller.clientHeight-Zr(e)-e.display.barHeight}function tn(e,t,r){var n=e.options.lineWrapping,i=n&&Jr(e);if(!t.measure.heights||n&&t.measure.width!=i){var o=t.measure.heights=[];if(n){t.measure.width=i;for(var a=t.text.firstChild.getClientRects(),s=0;s<a.length-1;s++){var l=a[s],c=a[s+1];Math.abs(l.bottom-c.bottom)>2&&o.push((l.bottom+c.top)/2-r.top)}}o.push(r.bottom-r.top)}}function rn(e,t,r){if(e.line==t)return{map:e.measure.map,cache:e.measure.cache};if(e.rest){for(var n=0;n<e.rest.length;n++)if(e.rest[n]==t)return{map:e.measure.maps[n],cache:e.measure.caches[n]};for(var i=0;i<e.rest.length;i++)if(at(e.rest[i])>r)return{map:e.measure.maps[i],cache:e.measure.caches[i],before:!0}}}function nn(e,t){var r=at(t=nr(t)),n=e.display.externalMeasured=new Mr(e.doc,t,r);n.lineN=r;var i=n.built=_r(e,n);return n.text=i.pre,z(e.display.lineMeasure,i.pre),n}function on(e,t,r,n){return ln(e,sn(e,t),r,n)}function an(e,t){if(t>=e.display.viewFrom&&t<e.display.viewTo)return e.display.view[Bn(e,t)];var r=e.display.externalMeasured;return r&&t>=r.lineN&&t<r.lineN+r.size?r:void 0}function sn(e,t){var r=at(t),n=an(e,r);n&&!n.text?n=null:n&&n.changes&&(Er(e,n,r,On(e)),e.curOp.forceUpdate=!0),n||(n=nn(e,t));var i=rn(n,t,r);return{line:t,view:n,rect:null,map:i.map,cache:i.cache,before:i.before,hasHeights:!1}}function ln(e,t,r,n,i){t.before&&(r=-1);var o,a=r+(n||"");return t.cache.hasOwnProperty(a)?o=t.cache[a]:(t.rect||(t.rect=t.view.text.getBoundingClientRect()),t.hasHeights||(tn(e,t.view,t.rect),t.hasHeights=!0),(o=fn(e,t,r,n)).bogus||(t.cache[a]=o)),{left:o.left,right:o.right,top:i?o.rtop:o.top,bottom:i?o.rbottom:o.bottom}}var cn,un={left:0,right:0,top:0,bottom:0};function dn(e,t,r){for(var n,i,o,a,s,l,c=0;c<e.length;c+=3)if(s=e[c],l=e[c+1],t<s?(i=0,o=1,a="left"):t<l?o=1+(i=t-s):(c==e.length-3||t==l&&e[c+3]>t)&&(i=(o=l-s)-1,t>=l&&(a="right")),null!=i){if(n=e[c+2],s==l&&r==(n.insertLeft?"left":"right")&&(a=r),"left"==r&&0==i)for(;c&&e[c-2]==e[c-3]&&e[c-1].insertLeft;)n=e[2+(c-=3)],a="left";if("right"==r&&i==l-s)for(;c<e.length-3&&e[c+3]==e[c+4]&&!e[c+5].insertLeft;)n=e[(c+=3)+2],a="right";break}return{node:n,start:i,end:o,collapse:a,coverStart:s,coverEnd:l}}function pn(e,t){var r=un;if("left"==t)for(var n=0;n<e.length&&(r=e[n]).left==r.right;n++);else for(var i=e.length-1;i>=0&&(r=e[i]).left==r.right;i--);return r}function fn(e,t,r,n){var i,o=dn(t.map,r,n),l=o.node,c=o.start,u=o.end,d=o.collapse;if(3==l.nodeType){for(var p=0;p<4;p++){for(;c&&ue(t.line.text.charAt(o.coverStart+c));)--c;for(;o.coverStart+u<o.coverEnd&&ue(t.line.text.charAt(o.coverStart+u));)++u;if((i=a&&s<9&&0==c&&u==o.coverEnd-o.coverStart?l.parentNode.getBoundingClientRect():pn(M(l,c,u).getClientRects(),n)).left||i.right||0==c)break;u=c,c-=1,d="right"}a&&s<11&&(i=mn(e.display.measure,i))}else{var f;c>0&&(d=n="right"),i=e.options.lineWrapping&&(f=l.getClientRects()).length>1?f["right"==n?f.length-1:0]:l.getBoundingClientRect()}if(a&&s<9&&!c&&(!i||!i.left&&!i.right)){var m=l.parentNode.getClientRects()[0];i=m?{left:m.left,right:m.left+En(e.display),top:m.top,bottom:m.bottom}:un}for(var h=i.top-t.rect.top,g=i.bottom-t.rect.top,v=(h+g)/2,y=t.view.measure.heights,_=0;_<y.length-1&&!(v<y[_]);_++);var b=_?y[_-1]:0,x=y[_],k={left:("right"==d?i.right:i.left)-t.rect.left,right:("left"==d?i.left:i.right)-t.rect.left,top:b,bottom:x};return i.left||i.right||(k.bogus=!0),e.options.singleCursorHeightPerLine||(k.rtop=h,k.rbottom=g),k}function mn(e,t){if(!window.screen||null==screen.logicalXDPI||screen.logicalXDPI==screen.deviceXDPI||!Re(e))return t;var r=screen.logicalXDPI/screen.deviceXDPI,n=screen.logicalYDPI/screen.deviceYDPI;return{left:t.left*r,right:t.right*r,top:t.top*n,bottom:t.bottom*n}}function hn(e){if(e.measure&&(e.measure.cache={},e.measure.heights=null,e.rest))for(var t=0;t<e.rest.length;t++)e.measure.caches[t]={}}function gn(e){e.display.externalMeasure=null,L(e.display.lineMeasure);for(var t=0;t<e.display.view.length;t++)hn(e.display.view[t])}function vn(e){gn(e),e.display.cachedCharWidth=e.display.cachedTextHeight=e.display.cachedPaddingH=null,e.options.lineWrapping||(e.display.maxLineChanged=!0),e.display.lineNumChars=null}function yn(e){return u&&v?-(e.body.getBoundingClientRect().left-parseInt(getComputedStyle(e.body).marginLeft)):e.defaultView.pageXOffset||(e.documentElement||e.body).scrollLeft}function _n(e){return u&&v?-(e.body.getBoundingClientRect().top-parseInt(getComputedStyle(e.body).marginTop)):e.defaultView.pageYOffset||(e.documentElement||e.body).scrollTop}function bn(e){var t=nr(e).widgets,r=0;if(t)for(var n=0;n<t.length;++n)t[n].above&&(r+=Kr(t[n]));return r}function xn(e,t,r,n,i){if(!i){var o=bn(t);r.top+=o,r.bottom+=o}if("line"==n)return r;n||(n="local");var a=ur(t);if("local"==n?a+=Xr(e.display):a-=e.display.viewOffset,"page"==n||"window"==n){var s=e.display.lineSpace.getBoundingClientRect();a+=s.top+("window"==n?0:_n(I(e)));var l=s.left+("window"==n?0:yn(I(e)));r.left+=l,r.right+=l}return r.top+=a,r.bottom+=a,r}function kn(e,t,r){if("div"==r)return t;var n=t.left,i=t.top;if("page"==r)n-=yn(I(e)),i-=_n(I(e));else if("local"==r||!r){var o=e.display.sizer.getBoundingClientRect();n+=o.left,i+=o.top}var a=e.display.lineSpace.getBoundingClientRect();return{left:n-a.left,top:i-a.top}}function wn(e,t,r,n,i){return n||(n=rt(e.doc,t.line)),xn(e,n,on(e,n,t.ch,i),r)}function Sn(e,t,r,n,i,o){function a(t,a){var s=ln(e,i,t,a?"right":"left",o);return a?s.left=s.right:s.right=s.left,xn(e,n,s,r)}n=n||rt(e.doc,t.line),i||(i=sn(e,n));var s=ve(n,e.doc.direction),l=t.ch,c=t.sticky;if(l>=n.text.length?(l=n.text.length,c="before"):l<=0&&(l=0,c="after"),!s)return a("before"==c?l-1:l,"before"==c);function u(e,t,r){return a(r?e-1:e,1==s[t].level!=r)}var d=he(s,l,c),p=me,f=u(l,d,"before"==c);return null!=p&&(f.other=u(l,p,"before"!=c)),f}function Cn(e,t){var r=0;t=vt(e.doc,t),e.options.lineWrapping||(r=En(e.display)*t.ch);var n=rt(e.doc,t.line),i=ur(n)+Xr(e.display);return{left:r,right:r,top:i,bottom:i+n.height}}function Mn(e,t,r,n,i){var o=ut(e,t,r);return o.xRel=i,n&&(o.outside=n),o}function Tn(e,t,r){var n=e.doc;if((r+=e.display.viewOffset)<0)return Mn(n.first,0,null,-1,-1);var i=st(n,r),o=n.first+n.size-1;if(i>o)return Mn(n.first+n.size-1,rt(n,o).text.length,null,1,1);t<0&&(t=0);for(var a=rt(n,i);;){var s=An(e,a,i,t,r),l=tr(a,s.ch+(s.xRel>0||s.outside>0?1:0));if(!l)return s;var c=l.find(1);if(c.line==i)return c;a=rt(n,i=c.line)}}function Ln(e,t,r,n){n-=bn(t);var i=t.text.length,o=pe((function(t){return ln(e,r,t-1).bottom<=n}),i,0);return{begin:o,end:i=pe((function(t){return ln(e,r,t).top>n}),o,i)}}function zn(e,t,r,n){return r||(r=sn(e,t)),Ln(e,t,r,xn(e,t,ln(e,r,n),"line").top)}function Dn(e,t,r,n){return!(e.bottom<=r)&&(e.top>r||(n?e.left:e.right)>t)}function An(e,t,r,n,i){i-=ur(t);var o=sn(e,t),a=bn(t),s=0,l=t.text.length,c=!0,u=ve(t,e.doc.direction);if(u){var d=(e.options.lineWrapping?Nn:qn)(e,t,r,o,u,n,i);s=(c=1!=d.level)?d.from:d.to-1,l=c?d.to:d.from-1}var p,f,m=null,h=null,g=pe((function(t){var r=ln(e,o,t);return r.top+=a,r.bottom+=a,!!Dn(r,n,i,!1)&&(r.top<=i&&r.left<=n&&(m=t,h=r),!0)}),s,l),v=!1;if(h){var y=n-h.left<h.right-n,_=y==c;g=m+(_?0:1),f=_?"after":"before",p=y?h.left:h.right}else{c||g!=l&&g!=s||g++,f=0==g?"after":g==t.text.length?"before":ln(e,o,g-(c?1:0)).bottom+a<=i==c?"after":"before";var b=Sn(e,ut(r,g,f),"line",t,o);p=b.left,v=i<b.top?-1:i>=b.bottom?1:0}return Mn(r,g=de(t.text,g,1),f,v,n-p)}function qn(e,t,r,n,i,o,a){var s=pe((function(s){var l=i[s],c=1!=l.level;return Dn(Sn(e,ut(r,c?l.to:l.from,c?"before":"after"),"line",t,n),o,a,!0)}),0,i.length-1),l=i[s];if(s>0){var c=1!=l.level,u=Sn(e,ut(r,c?l.from:l.to,c?"after":"before"),"line",t,n);Dn(u,o,a,!0)&&u.top>a&&(l=i[s-1])}return l}function Nn(e,t,r,n,i,o,a){var s=Ln(e,t,n,a),l=s.begin,c=s.end;/\s/.test(t.text.charAt(c-1))&&c--;for(var u=null,d=null,p=0;p<i.length;p++){var f=i[p];if(!(f.from>=c||f.to<=l)){var m=ln(e,n,1!=f.level?Math.min(c,f.to)-1:Math.max(l,f.from)).right,h=m<o?o-m+1e9:m-o;(!u||d>h)&&(u=f,d=h)}}return u||(u=i[i.length-1]),u.from<l&&(u={from:l,to:u.to,level:u.level}),u.to>c&&(u={from:u.from,to:c,level:u.level}),u}function Fn(e){if(null!=e.cachedTextHeight)return e.cachedTextHeight;if(null==cn){cn=D("pre",null,"CodeMirror-line-like");for(var t=0;t<49;++t)cn.appendChild(document.createTextNode("x")),cn.appendChild(D("br"));cn.appendChild(document.createTextNode("x"))}z(e.measure,cn);var r=cn.offsetHeight/50;return r>3&&(e.cachedTextHeight=r),L(e.measure),r||1}function En(e){if(null!=e.cachedCharWidth)return e.cachedCharWidth;var t=D("span","xxxxxxxxxx"),r=D("pre",[t],"CodeMirror-line-like");z(e.measure,r);var n=t.getBoundingClientRect(),i=(n.right-n.left)/10;return i>2&&(e.cachedCharWidth=i),i||10}function On(e){for(var t=e.display,r={},n={},i=t.gutters.clientLeft,o=t.gutters.firstChild,a=0;o;o=o.nextSibling,++a){var s=e.display.gutterSpecs[a].className;r[s]=o.offsetLeft+o.clientLeft+i,n[s]=o.clientWidth}return{fixedPos:In(t),gutterTotalWidth:t.gutters.offsetWidth,gutterLeft:r,gutterWidth:n,wrapperWidth:t.wrapper.clientWidth}}function In(e){return e.scroller.getBoundingClientRect().left-e.sizer.getBoundingClientRect().left}function Pn(e){var t=Fn(e.display),r=e.options.lineWrapping,n=r&&Math.max(5,e.display.scroller.clientWidth/En(e.display)-3);return function(i){if(lr(e.doc,i))return 0;var o=0;if(i.widgets)for(var a=0;a<i.widgets.length;a++)i.widgets[a].height&&(o+=i.widgets[a].height);return r?o+(Math.ceil(i.text.length/n)||1)*t:o+t}}function jn(e){var t=e.doc,r=Pn(e);t.iter((function(e){var t=r(e);t!=e.height&&ot(e,t)}))}function Wn(e,t,r,n){var i=e.display;if(!r&&"true"==Ae(t).getAttribute("cm-not-content"))return null;var o,a,s=i.lineSpace.getBoundingClientRect();try{o=t.clientX-s.left,a=t.clientY-s.top}catch(e){return null}var l,c=Tn(e,o,a);if(n&&c.xRel>0&&(l=rt(e.doc,c.line).text).length==c.ch){var u=R(l,l.length,e.options.tabSize)-l.length;c=ut(c.line,Math.max(0,Math.round((o-Yr(e.display).left)/En(e.display))-u))}return c}function Bn(e,t){if(t>=e.display.viewTo)return null;if((t-=e.display.viewFrom)<0)return null;for(var r=e.display.view,n=0;n<r.length;n++)if((t-=r[n].size)<0)return n}function Hn(e,t,r,n){null==t&&(t=e.doc.first),null==r&&(r=e.doc.first+e.doc.size),n||(n=0);var i=e.display;if(n&&r<i.viewTo&&(null==i.updateLineNumbers||i.updateLineNumbers>t)&&(i.updateLineNumbers=t),e.curOp.viewChanged=!0,t>=i.viewTo)Et&&ar(e.doc,t)<i.viewTo&&$n(e);else if(r<=i.viewFrom)Et&&sr(e.doc,r+n)>i.viewFrom?$n(e):(i.viewFrom+=n,i.viewTo+=n);else if(t<=i.viewFrom&&r>=i.viewTo)$n(e);else if(t<=i.viewFrom){var o=Un(e,r,r+n,1);o?(i.view=i.view.slice(o.index),i.viewFrom=o.lineN,i.viewTo+=n):$n(e)}else if(r>=i.viewTo){var a=Un(e,t,t,-1);a?(i.view=i.view.slice(0,a.index),i.viewTo=a.lineN):$n(e)}else{var s=Un(e,t,t,-1),l=Un(e,r,r+n,1);s&&l?(i.view=i.view.slice(0,s.index).concat(Tr(e,s.lineN,l.lineN)).concat(i.view.slice(l.index)),i.viewTo+=n):$n(e)}var c=i.externalMeasured;c&&(r<c.lineN?c.lineN+=n:t<c.lineN+c.size&&(i.externalMeasured=null))}function Rn(e,t,r){e.curOp.viewChanged=!0;var n=e.display,i=e.display.externalMeasured;if(i&&t>=i.lineN&&t<i.lineN+i.size&&(n.externalMeasured=null),!(t<n.viewFrom||t>=n.viewTo)){var o=n.view[Bn(e,t)];if(null!=o.node){var a=o.changes||(o.changes=[]);-1==U(a,r)&&a.push(r)}}}function $n(e){e.display.viewFrom=e.display.viewTo=e.doc.first,e.display.view=[],e.display.viewOffset=0}function Un(e,t,r,n){var i,o=Bn(e,t),a=e.display.view;if(!Et||r==e.doc.first+e.doc.size)return{index:o,lineN:r};for(var s=e.display.viewFrom,l=0;l<o;l++)s+=a[l].size;if(s!=t){if(n>0){if(o==a.length-1)return null;i=s+a[o].size-t,o++}else i=s-t;t+=i,r+=i}for(;ar(e.doc,r)!=r;){if(o==(n<0?0:a.length-1))return null;r+=n*a[o-(n<0?1:0)].size,o+=n}return{index:o,lineN:r}}function Vn(e,t,r){var n=e.display;0==n.view.length||t>=n.viewTo||r<=n.viewFrom?(n.view=Tr(e,t,r),n.viewFrom=t):(n.viewFrom>t?n.view=Tr(e,t,n.viewFrom).concat(n.view):n.viewFrom<t&&(n.view=n.view.slice(Bn(e,t))),n.viewFrom=t,n.viewTo<r?n.view=n.view.concat(Tr(e,n.viewTo,r)):n.viewTo>r&&(n.view=n.view.slice(0,Bn(e,r)))),n.viewTo=r}function Kn(e){for(var t=e.display.view,r=0,n=0;n<t.length;n++){var i=t[n];i.hidden||i.node&&!i.changes||++r}return r}function Gn(e){e.display.input.showSelection(e.display.input.prepareSelection())}function Xn(e,t){void 0===t&&(t=!0);var r=e.doc,n={},i=n.cursors=document.createDocumentFragment(),o=n.selection=document.createDocumentFragment(),a=e.options.$customCursor;a&&(t=!0);for(var s=0;s<r.sel.ranges.length;s++)if(t||s!=r.sel.primIndex){var l=r.sel.ranges[s];if(!(l.from().line>=e.display.viewTo||l.to().line<e.display.viewFrom)){var c=l.empty();if(a){var u=a(e,l);u&&Qn(e,u,i)}else(c||e.options.showCursorWhenSelecting)&&Qn(e,l.head,i);c||Zn(e,l,o)}}return n}function Qn(e,t,r){var n=Sn(e,t,"div",null,null,!e.options.singleCursorHeightPerLine),i=r.appendChild(D("div"," ","CodeMirror-cursor"));if(i.style.left=n.left+"px",i.style.top=n.top+"px",i.style.height=Math.max(0,n.bottom-n.top)*e.options.cursorHeight+"px",/\bcm-fat-cursor\b/.test(e.getWrapperElement().className)){var o=wn(e,t,"div",null,null),a=o.right-o.left;i.style.width=(a>0?a:e.defaultCharWidth())+"px"}if(n.other){var s=r.appendChild(D("div"," ","CodeMirror-cursor CodeMirror-secondarycursor"));s.style.display="",s.style.left=n.other.left+"px",s.style.top=n.other.top+"px",s.style.height=.85*(n.other.bottom-n.other.top)+"px"}}function Yn(e,t){return e.top-t.top||e.left-t.left}function Zn(e,t,r){var n=e.display,i=e.doc,o=document.createDocumentFragment(),a=Yr(e.display),s=a.left,l=Math.max(n.sizerWidth,Jr(e)-n.sizer.offsetLeft)-a.right,c="ltr"==i.direction;function u(e,t,r,n){t<0&&(t=0),t=Math.round(t),n=Math.round(n),o.appendChild(D("div",null,"CodeMirror-selected","position: absolute; left: "+e+"px;\n                             top: "+t+"px; width: "+(null==r?l-e:r)+"px;\n                             height: "+(n-t)+"px"))}function d(t,r,n){var o,a,d=rt(i,t),p=d.text.length;function f(r,n){return wn(e,ut(t,r),"div",d,n)}function m(t,r,n){var i=zn(e,d,null,t),o="ltr"==r==("after"==n)?"left":"right";return f("after"==n?i.begin:i.end-(/\s/.test(d.text.charAt(i.end-1))?2:1),o)[o]}var h=ve(d,i.direction);return fe(h,r||0,null==n?p:n,(function(e,t,i,d){var g="ltr"==i,v=f(e,g?"left":"right"),y=f(t-1,g?"right":"left"),_=null==r&&0==e,b=null==n&&t==p,x=0==d,k=!h||d==h.length-1;if(y.top-v.top<=3){var w=(c?b:_)&&k,S=(c?_:b)&&x?s:(g?v:y).left,C=w?l:(g?y:v).right;u(S,v.top,C-S,v.bottom)}else{var M,T,L,z;g?(M=c&&_&&x?s:v.left,T=c?l:m(e,i,"before"),L=c?s:m(t,i,"after"),z=c&&b&&k?l:y.right):(M=c?m(e,i,"before"):s,T=!c&&_&&x?l:v.right,L=!c&&b&&k?s:y.left,z=c?m(t,i,"after"):l),u(M,v.top,T-M,v.bottom),v.bottom<y.top&&u(s,v.bottom,null,y.top),u(L,y.top,z-L,y.bottom)}(!o||Yn(v,o)<0)&&(o=v),Yn(y,o)<0&&(o=y),(!a||Yn(v,a)<0)&&(a=v),Yn(y,a)<0&&(a=y)})),{start:o,end:a}}var p=t.from(),f=t.to();if(p.line==f.line)d(p.line,p.ch,f.ch);else{var m=rt(i,p.line),h=rt(i,f.line),g=nr(m)==nr(h),v=d(p.line,p.ch,g?m.text.length+1:null).end,y=d(f.line,g?0:null,f.ch).start;g&&(v.top<y.top-2?(u(v.right,v.top,null,v.bottom),u(s,y.top,y.left,y.bottom)):u(v.right,v.top,y.left-v.right,v.bottom)),v.bottom<y.top&&u(s,v.bottom,null,y.top)}r.appendChild(o)}function Jn(e){if(e.state.focused){var t=e.display;clearInterval(t.blinker);var r=!0;t.cursorDiv.style.visibility="",e.options.cursorBlinkRate>0?t.blinker=setInterval((function(){e.hasFocus()||ni(e),t.cursorDiv.style.visibility=(r=!r)?"":"hidden"}),e.options.cursorBlinkRate):e.options.cursorBlinkRate<0&&(t.cursorDiv.style.visibility="hidden")}}function ei(e){e.hasFocus()||(e.display.input.focus(),e.state.focused||ri(e))}function ti(e){e.state.delayingBlurEvent=!0,setTimeout((function(){e.state.delayingBlurEvent&&(e.state.delayingBlurEvent=!1,e.state.focused&&ni(e))}),100)}function ri(e,t){e.state.delayingBlurEvent&&!e.state.draggingText&&(e.state.delayingBlurEvent=!1),"nocursor"!=e.options.readOnly&&(e.state.focused||(ke(e,"focus",e,t),e.state.focused=!0,F(e.display.wrapper,"CodeMirror-focused"),e.curOp||e.display.selForContextMenu==e.doc.sel||(e.display.input.reset(),l&&setTimeout((function(){return e.display.input.reset(!0)}),20)),e.display.input.receivedFocus()),Jn(e))}function ni(e,t){e.state.delayingBlurEvent||(e.state.focused&&(ke(e,"blur",e,t),e.state.focused=!1,T(e.display.wrapper,"CodeMirror-focused")),clearInterval(e.display.blinker),setTimeout((function(){e.state.focused||(e.display.shift=!1)}),150))}function ii(e){for(var t=e.display,r=t.lineDiv.offsetTop,n=Math.max(0,t.scroller.getBoundingClientRect().top),i=t.lineDiv.getBoundingClientRect().top,o=0,l=0;l<t.view.length;l++){var c=t.view[l],u=e.options.lineWrapping,d=void 0,p=0;if(!c.hidden){if(i+=c.line.height,a&&s<8){var f=c.node.offsetTop+c.node.offsetHeight;d=f-r,r=f}else{var m=c.node.getBoundingClientRect();d=m.bottom-m.top,!u&&c.text.firstChild&&(p=c.text.firstChild.getBoundingClientRect().right-m.left-1)}var h=c.line.height-d;if((h>.005||h<-.005)&&(i<n&&(o-=h),ot(c.line,d),oi(c.line),c.rest))for(var g=0;g<c.rest.length;g++)oi(c.rest[g]);if(p>e.display.sizerWidth){var v=Math.ceil(p/En(e.display));v>e.display.maxLineLength&&(e.display.maxLineLength=v,e.display.maxLine=c.line,e.display.maxLineChanged=!0)}}}Math.abs(o)>2&&(t.scroller.scrollTop+=o)}function oi(e){if(e.widgets)for(var t=0;t<e.widgets.length;++t){var r=e.widgets[t],n=r.node.parentNode;n&&(r.height=n.offsetHeight)}}function ai(e,t,r){var n=r&&null!=r.top?Math.max(0,r.top):e.scroller.scrollTop;n=Math.floor(n-Xr(e));var i=r&&null!=r.bottom?r.bottom:n+e.wrapper.clientHeight,o=st(t,n),a=st(t,i);if(r&&r.ensure){var s=r.ensure.from.line,l=r.ensure.to.line;s<o?(o=s,a=st(t,ur(rt(t,s))+e.wrapper.clientHeight)):Math.min(l,t.lastLine())>=a&&(o=st(t,ur(rt(t,l))-e.wrapper.clientHeight),a=l)}return{from:o,to:Math.max(a,o+1)}}function si(e,t){if(!we(e,"scrollCursorIntoView")){var r=e.display,n=r.sizer.getBoundingClientRect(),i=null,o=r.wrapper.ownerDocument;if(t.top+n.top<0?i=!0:t.bottom+n.top>(o.defaultView.innerHeight||o.documentElement.clientHeight)&&(i=!1),null!=i&&!h){var a=D("div","​",null,"position: absolute;\n                         top: "+(t.top-r.viewOffset-Xr(e.display))+"px;\n                         height: "+(t.bottom-t.top+Zr(e)+r.barHeight)+"px;\n                         left: "+t.left+"px; width: "+Math.max(2,t.right-t.left)+"px;");e.display.lineSpace.appendChild(a),a.scrollIntoView(i),e.display.lineSpace.removeChild(a)}}}function li(e,t,r,n){var i;null==n&&(n=0),e.options.lineWrapping||t!=r||(r="before"==t.sticky?ut(t.line,t.ch+1,"before"):t,t=t.ch?ut(t.line,"before"==t.sticky?t.ch-1:t.ch,"after"):t);for(var o=0;o<5;o++){var a=!1,s=Sn(e,t),l=r&&r!=t?Sn(e,r):s,c=ui(e,i={left:Math.min(s.left,l.left),top:Math.min(s.top,l.top)-n,right:Math.max(s.left,l.left),bottom:Math.max(s.bottom,l.bottom)+n}),u=e.doc.scrollTop,d=e.doc.scrollLeft;if(null!=c.scrollTop&&(vi(e,c.scrollTop),Math.abs(e.doc.scrollTop-u)>1&&(a=!0)),null!=c.scrollLeft&&(_i(e,c.scrollLeft),Math.abs(e.doc.scrollLeft-d)>1&&(a=!0)),!a)break}return i}function ci(e,t){var r=ui(e,t);null!=r.scrollTop&&vi(e,r.scrollTop),null!=r.scrollLeft&&_i(e,r.scrollLeft)}function ui(e,t){var r=e.display,n=Fn(e.display);t.top<0&&(t.top=0);var i=e.curOp&&null!=e.curOp.scrollTop?e.curOp.scrollTop:r.scroller.scrollTop,o=en(e),a={};t.bottom-t.top>o&&(t.bottom=t.top+o);var s=e.doc.height+Qr(r),l=t.top<n,c=t.bottom>s-n;if(t.top<i)a.scrollTop=l?0:t.top;else if(t.bottom>i+o){var u=Math.min(t.top,(c?s:t.bottom)-o);u!=i&&(a.scrollTop=u)}var d=e.options.fixedGutter?0:r.gutters.offsetWidth,p=e.curOp&&null!=e.curOp.scrollLeft?e.curOp.scrollLeft:r.scroller.scrollLeft-d,f=Jr(e)-r.gutters.offsetWidth,m=t.right-t.left>f;return m&&(t.right=t.left+f),t.left<10?a.scrollLeft=0:t.left<p?a.scrollLeft=Math.max(0,t.left+d-(m?0:10)):t.right>f+p-3&&(a.scrollLeft=t.right+(m?0:10)-f),a}function di(e,t){null!=t&&(hi(e),e.curOp.scrollTop=(null==e.curOp.scrollTop?e.doc.scrollTop:e.curOp.scrollTop)+t)}function pi(e){hi(e);var t=e.getCursor();e.curOp.scrollToPos={from:t,to:t,margin:e.options.cursorScrollMargin}}function fi(e,t,r){null==t&&null==r||hi(e),null!=t&&(e.curOp.scrollLeft=t),null!=r&&(e.curOp.scrollTop=r)}function mi(e,t){hi(e),e.curOp.scrollToPos=t}function hi(e){var t=e.curOp.scrollToPos;t&&(e.curOp.scrollToPos=null,gi(e,Cn(e,t.from),Cn(e,t.to),t.margin))}function gi(e,t,r,n){var i=ui(e,{left:Math.min(t.left,r.left),top:Math.min(t.top,r.top)-n,right:Math.max(t.right,r.right),bottom:Math.max(t.bottom,r.bottom)+n});fi(e,i.scrollLeft,i.scrollTop)}function vi(e,t){Math.abs(e.doc.scrollTop-t)<2||(r||Gi(e,{top:t}),yi(e,t,!0),r&&Gi(e),Wi(e,100))}function yi(e,t,r){t=Math.max(0,Math.min(e.display.scroller.scrollHeight-e.display.scroller.clientHeight,t)),(e.display.scroller.scrollTop!=t||r)&&(e.doc.scrollTop=t,e.display.scrollbars.setScrollTop(t),e.display.scroller.scrollTop!=t&&(e.display.scroller.scrollTop=t))}function _i(e,t,r,n){t=Math.max(0,Math.min(t,e.display.scroller.scrollWidth-e.display.scroller.clientWidth)),(r?t==e.doc.scrollLeft:Math.abs(e.doc.scrollLeft-t)<2)&&!n||(e.doc.scrollLeft=t,Zi(e),e.display.scroller.scrollLeft!=t&&(e.display.scroller.scrollLeft=t),e.display.scrollbars.setScrollLeft(t))}function bi(e){var t=e.display,r=t.gutters.offsetWidth,n=Math.round(e.doc.height+Qr(e.display));return{clientHeight:t.scroller.clientHeight,viewHeight:t.wrapper.clientHeight,scrollWidth:t.scroller.scrollWidth,clientWidth:t.scroller.clientWidth,viewWidth:t.wrapper.clientWidth,barLeft:e.options.fixedGutter?r:0,docHeight:n,scrollHeight:n+Zr(e)+t.barHeight,nativeBarWidth:t.nativeBarWidth,gutterWidth:r}}var xi=function(e,t,r){this.cm=r;var n=this.vert=D("div",[D("div",null,null,"min-width: 1px")],"CodeMirror-vscrollbar"),i=this.horiz=D("div",[D("div",null,null,"height: 100%; min-height: 1px")],"CodeMirror-hscrollbar");n.tabIndex=i.tabIndex=-1,e(n),e(i),_e(n,"scroll",(function(){n.clientHeight&&t(n.scrollTop,"vertical")})),_e(i,"scroll",(function(){i.clientWidth&&t(i.scrollLeft,"horizontal")})),this.checkedZeroWidth=!1,a&&s<8&&(this.horiz.style.minHeight=this.vert.style.minWidth="18px")};xi.prototype.update=function(e){var t=e.scrollWidth>e.clientWidth+1,r=e.scrollHeight>e.clientHeight+1,n=e.nativeBarWidth;if(r){this.vert.style.display="block",this.vert.style.bottom=t?n+"px":"0";var i=e.viewHeight-(t?n:0);this.vert.firstChild.style.height=Math.max(0,e.scrollHeight-e.clientHeight+i)+"px"}else this.vert.scrollTop=0,this.vert.style.display="",this.vert.firstChild.style.height="0";if(t){this.horiz.style.display="block",this.horiz.style.right=r?n+"px":"0",this.horiz.style.left=e.barLeft+"px";var o=e.viewWidth-e.barLeft-(r?n:0);this.horiz.firstChild.style.width=Math.max(0,e.scrollWidth-e.clientWidth+o)+"px"}else this.horiz.style.display="",this.horiz.firstChild.style.width="0";return!this.checkedZeroWidth&&e.clientHeight>0&&(0==n&&this.zeroWidthHack(),this.checkedZeroWidth=!0),{right:r?n:0,bottom:t?n:0}},xi.prototype.setScrollLeft=function(e){this.horiz.scrollLeft!=e&&(this.horiz.scrollLeft=e),this.disableHoriz&&this.enableZeroWidthBar(this.horiz,this.disableHoriz,"horiz")},xi.prototype.setScrollTop=function(e){this.vert.scrollTop!=e&&(this.vert.scrollTop=e),this.disableVert&&this.enableZeroWidthBar(this.vert,this.disableVert,"vert")},xi.prototype.zeroWidthHack=function(){var e=_&&!m?"12px":"18px";this.horiz.style.height=this.vert.style.width=e,this.horiz.style.visibility=this.vert.style.visibility="hidden",this.disableHoriz=new $,this.disableVert=new $},xi.prototype.enableZeroWidthBar=function(e,t,r){function n(){var i=e.getBoundingClientRect();("vert"==r?document.elementFromPoint(i.right-1,(i.top+i.bottom)/2):document.elementFromPoint((i.right+i.left)/2,i.bottom-1))!=e?e.style.visibility="hidden":t.set(1e3,n)}e.style.visibility="",t.set(1e3,n)},xi.prototype.clear=function(){var e=this.horiz.parentNode;e.removeChild(this.horiz),e.removeChild(this.vert)};var ki=function(){};function wi(e,t){t||(t=bi(e));var r=e.display.barWidth,n=e.display.barHeight;Si(e,t);for(var i=0;i<4&&r!=e.display.barWidth||n!=e.display.barHeight;i++)r!=e.display.barWidth&&e.options.lineWrapping&&ii(e),Si(e,bi(e)),r=e.display.barWidth,n=e.display.barHeight}function Si(e,t){var r=e.display,n=r.scrollbars.update(t);r.sizer.style.paddingRight=(r.barWidth=n.right)+"px",r.sizer.style.paddingBottom=(r.barHeight=n.bottom)+"px",r.heightForcer.style.borderBottom=n.bottom+"px solid transparent",n.right&&n.bottom?(r.scrollbarFiller.style.display="block",r.scrollbarFiller.style.height=n.bottom+"px",r.scrollbarFiller.style.width=n.right+"px"):r.scrollbarFiller.style.display="",n.bottom&&e.options.coverGutterNextToScrollbar&&e.options.fixedGutter?(r.gutterFiller.style.display="block",r.gutterFiller.style.height=n.bottom+"px",r.gutterFiller.style.width=t.gutterWidth+"px"):r.gutterFiller.style.display=""}ki.prototype.update=function(){return{bottom:0,right:0}},ki.prototype.setScrollLeft=function(){},ki.prototype.setScrollTop=function(){},ki.prototype.clear=function(){};var Ci={native:xi,null:ki};function Mi(e){e.display.scrollbars&&(e.display.scrollbars.clear(),e.display.scrollbars.addClass&&T(e.display.wrapper,e.display.scrollbars.addClass)),e.display.scrollbars=new Ci[e.options.scrollbarStyle]((function(t){e.display.wrapper.insertBefore(t,e.display.scrollbarFiller),_e(t,"mousedown",(function(){e.state.focused&&setTimeout((function(){return e.display.input.focus()}),0)})),t.setAttribute("cm-not-content","true")}),(function(t,r){"horizontal"==r?_i(e,t):vi(e,t)}),e),e.display.scrollbars.addClass&&F(e.display.wrapper,e.display.scrollbars.addClass)}var Ti=0;function Li(e){e.curOp={cm:e,viewChanged:!1,startHeight:e.doc.height,forceUpdate:!1,updateInput:0,typing:!1,changeObjs:null,cursorActivityHandlers:null,cursorActivityCalled:0,selectionChanged:!1,updateMaxLine:!1,scrollLeft:null,scrollTop:null,scrollToPos:null,focus:!1,id:++Ti,markArrays:null},zr(e.curOp)}function zi(e){var t=e.curOp;t&&Ar(t,(function(e){for(var t=0;t<e.ops.length;t++)e.ops[t].cm.curOp=null;Di(e)}))}function Di(e){for(var t=e.ops,r=0;r<t.length;r++)Ai(t[r]);for(var n=0;n<t.length;n++)qi(t[n]);for(var i=0;i<t.length;i++)Ni(t[i]);for(var o=0;o<t.length;o++)Fi(t[o]);for(var a=0;a<t.length;a++)Ei(t[a])}function Ai(e){var t=e.cm,r=t.display;Ri(t),e.updateMaxLine&&pr(t),e.mustUpdate=e.viewChanged||e.forceUpdate||null!=e.scrollTop||e.scrollToPos&&(e.scrollToPos.from.line<r.viewFrom||e.scrollToPos.to.line>=r.viewTo)||r.maxLineChanged&&t.options.lineWrapping,e.update=e.mustUpdate&&new Hi(t,e.mustUpdate&&{top:e.scrollTop,ensure:e.scrollToPos},e.forceUpdate)}function qi(e){e.updatedDisplay=e.mustUpdate&&Vi(e.cm,e.update)}function Ni(e){var t=e.cm,r=t.display;e.updatedDisplay&&ii(t),e.barMeasure=bi(t),r.maxLineChanged&&!t.options.lineWrapping&&(e.adjustWidthTo=on(t,r.maxLine,r.maxLine.text.length).left+3,t.display.sizerWidth=e.adjustWidthTo,e.barMeasure.scrollWidth=Math.max(r.scroller.clientWidth,r.sizer.offsetLeft+e.adjustWidthTo+Zr(t)+t.display.barWidth),e.maxScrollLeft=Math.max(0,r.sizer.offsetLeft+e.adjustWidthTo-Jr(t))),(e.updatedDisplay||e.selectionChanged)&&(e.preparedSelection=r.input.prepareSelection())}function Fi(e){var t=e.cm;null!=e.adjustWidthTo&&(t.display.sizer.style.minWidth=e.adjustWidthTo+"px",e.maxScrollLeft<t.doc.scrollLeft&&_i(t,Math.min(t.display.scroller.scrollLeft,e.maxScrollLeft),!0),t.display.maxLineChanged=!1);var r=e.focus&&e.focus==N(P(t));e.preparedSelection&&t.display.input.showSelection(e.preparedSelection,r),(e.updatedDisplay||e.startHeight!=t.doc.height)&&wi(t,e.barMeasure),e.updatedDisplay&&Yi(t,e.barMeasure),e.selectionChanged&&Jn(t),t.state.focused&&e.updateInput&&t.display.input.reset(e.typing),r&&ei(e.cm)}function Ei(e){var t=e.cm,r=t.display,n=t.doc;e.updatedDisplay&&Ki(t,e.update),null==r.wheelStartX||null==e.scrollTop&&null==e.scrollLeft&&!e.scrollToPos||(r.wheelStartX=r.wheelStartY=null),null!=e.scrollTop&&yi(t,e.scrollTop,e.forceScroll),null!=e.scrollLeft&&_i(t,e.scrollLeft,!0,!0),e.scrollToPos&&si(t,li(t,vt(n,e.scrollToPos.from),vt(n,e.scrollToPos.to),e.scrollToPos.margin));var i=e.maybeHiddenMarkers,o=e.maybeUnhiddenMarkers;if(i)for(var a=0;a<i.length;++a)i[a].lines.length||ke(i[a],"hide");if(o)for(var s=0;s<o.length;++s)o[s].lines.length&&ke(o[s],"unhide");r.wrapper.offsetHeight&&(n.scrollTop=t.display.scroller.scrollTop),e.changeObjs&&ke(t,"changes",t,e.changeObjs),e.update&&e.update.finish()}function Oi(e,t){if(e.curOp)return t();Li(e);try{return t()}finally{zi(e)}}function Ii(e,t){return function(){if(e.curOp)return t.apply(e,arguments);Li(e);try{return t.apply(e,arguments)}finally{zi(e)}}}function Pi(e){return function(){if(this.curOp)return e.apply(this,arguments);Li(this);try{return e.apply(this,arguments)}finally{zi(this)}}}function ji(e){return function(){var t=this.cm;if(!t||t.curOp)return e.apply(this,arguments);Li(t);try{return e.apply(this,arguments)}finally{zi(t)}}}function Wi(e,t){e.doc.highlightFrontier<e.display.viewTo&&e.state.highlight.set(t,B(Bi,e))}function Bi(e){var t=e.doc;if(!(t.highlightFrontier>=e.display.viewTo)){var r=+new Date+e.options.workTime,n=St(e,t.highlightFrontier),i=[];t.iter(n.line,Math.min(t.first+t.size,e.display.viewTo+500),(function(o){if(n.line>=e.display.viewFrom){var a=o.styles,s=o.text.length>e.options.maxHighlightLength?Ze(t.mode,n.state):null,l=kt(e,o,n,!0);s&&(n.state=s),o.styles=l.styles;var c=o.styleClasses,u=l.classes;u?o.styleClasses=u:c&&(o.styleClasses=null);for(var d=!a||a.length!=o.styles.length||c!=u&&(!c||!u||c.bgClass!=u.bgClass||c.textClass!=u.textClass),p=0;!d&&p<a.length;++p)d=a[p]!=o.styles[p];d&&i.push(n.line),o.stateAfter=n.save(),n.nextLine()}else o.text.length<=e.options.maxHighlightLength&&Ct(e,o.text,n),o.stateAfter=n.line%5==0?n.save():null,n.nextLine();if(+new Date>r)return Wi(e,e.options.workDelay),!0})),t.highlightFrontier=n.line,t.modeFrontier=Math.max(t.modeFrontier,n.line),i.length&&Oi(e,(function(){for(var t=0;t<i.length;t++)Rn(e,i[t],"text")}))}}var Hi=function(e,t,r){var n=e.display;this.viewport=t,this.visible=ai(n,e.doc,t),this.editorIsHidden=!n.wrapper.offsetWidth,this.wrapperHeight=n.wrapper.clientHeight,this.wrapperWidth=n.wrapper.clientWidth,this.oldDisplayWidth=Jr(e),this.force=r,this.dims=On(e),this.events=[]};function Ri(e){var t=e.display;!t.scrollbarsClipped&&t.scroller.offsetWidth&&(t.nativeBarWidth=t.scroller.offsetWidth-t.scroller.clientWidth,t.heightForcer.style.height=Zr(e)+"px",t.sizer.style.marginBottom=-t.nativeBarWidth+"px",t.sizer.style.borderRightWidth=Zr(e)+"px",t.scrollbarsClipped=!0)}function $i(e){if(e.hasFocus())return null;var t=N(P(e));if(!t||!q(e.display.lineDiv,t))return null;var r={activeElt:t};if(window.getSelection){var n=W(e).getSelection();n.anchorNode&&n.extend&&q(e.display.lineDiv,n.anchorNode)&&(r.anchorNode=n.anchorNode,r.anchorOffset=n.anchorOffset,r.focusNode=n.focusNode,r.focusOffset=n.focusOffset)}return r}function Ui(e){if(e&&e.activeElt&&e.activeElt!=N(j(e.activeElt))&&(e.activeElt.focus(),!/^(INPUT|TEXTAREA)$/.test(e.activeElt.nodeName)&&e.anchorNode&&q(document.body,e.anchorNode)&&q(document.body,e.focusNode))){var t=e.activeElt.ownerDocument,r=t.defaultView.getSelection(),n=t.createRange();n.setEnd(e.anchorNode,e.anchorOffset),n.collapse(!1),r.removeAllRanges(),r.addRange(n),r.extend(e.focusNode,e.focusOffset)}}function Vi(e,t){var r=e.display,n=e.doc;if(t.editorIsHidden)return $n(e),!1;if(!t.force&&t.visible.from>=r.viewFrom&&t.visible.to<=r.viewTo&&(null==r.updateLineNumbers||r.updateLineNumbers>=r.viewTo)&&r.renderedView==r.view&&0==Kn(e))return!1;Ji(e)&&($n(e),t.dims=On(e));var i=n.first+n.size,o=Math.max(t.visible.from-e.options.viewportMargin,n.first),a=Math.min(i,t.visible.to+e.options.viewportMargin);r.viewFrom<o&&o-r.viewFrom<20&&(o=Math.max(n.first,r.viewFrom)),r.viewTo>a&&r.viewTo-a<20&&(a=Math.min(i,r.viewTo)),Et&&(o=ar(e.doc,o),a=sr(e.doc,a));var s=o!=r.viewFrom||a!=r.viewTo||r.lastWrapHeight!=t.wrapperHeight||r.lastWrapWidth!=t.wrapperWidth;Vn(e,o,a),r.viewOffset=ur(rt(e.doc,r.viewFrom)),e.display.mover.style.top=r.viewOffset+"px";var l=Kn(e);if(!s&&0==l&&!t.force&&r.renderedView==r.view&&(null==r.updateLineNumbers||r.updateLineNumbers>=r.viewTo))return!1;var c=$i(e);return l>4&&(r.lineDiv.style.display="none"),Xi(e,r.updateLineNumbers,t.dims),l>4&&(r.lineDiv.style.display=""),r.renderedView=r.view,Ui(c),L(r.cursorDiv),L(r.selectionDiv),r.gutters.style.height=r.sizer.style.minHeight=0,s&&(r.lastWrapHeight=t.wrapperHeight,r.lastWrapWidth=t.wrapperWidth,Wi(e,400)),r.updateLineNumbers=null,!0}function Ki(e,t){for(var r=t.viewport,n=!0;;n=!1){if(n&&e.options.lineWrapping&&t.oldDisplayWidth!=Jr(e))n&&(t.visible=ai(e.display,e.doc,r));else if(r&&null!=r.top&&(r={top:Math.min(e.doc.height+Qr(e.display)-en(e),r.top)}),t.visible=ai(e.display,e.doc,r),t.visible.from>=e.display.viewFrom&&t.visible.to<=e.display.viewTo)break;if(!Vi(e,t))break;ii(e);var i=bi(e);Gn(e),wi(e,i),Yi(e,i),t.force=!1}t.signal(e,"update",e),e.display.viewFrom==e.display.reportedViewFrom&&e.display.viewTo==e.display.reportedViewTo||(t.signal(e,"viewportChange",e,e.display.viewFrom,e.display.viewTo),e.display.reportedViewFrom=e.display.viewFrom,e.display.reportedViewTo=e.display.viewTo)}function Gi(e,t){var r=new Hi(e,t);if(Vi(e,r)){ii(e),Ki(e,r);var n=bi(e);Gn(e),wi(e,n),Yi(e,n),r.finish()}}function Xi(e,t,r){var n=e.display,i=e.options.lineNumbers,o=n.lineDiv,a=o.firstChild;function s(t){var r=t.nextSibling;return l&&_&&e.display.currentWheelTarget==t?t.style.display="none":t.parentNode.removeChild(t),r}for(var c=n.view,u=n.viewFrom,d=0;d<c.length;d++){var p=c[d];if(p.hidden);else if(p.node&&p.node.parentNode==o){for(;a!=p.node;)a=s(a);var f=i&&null!=t&&t<=u&&p.lineNumber;p.changes&&(U(p.changes,"gutter")>-1&&(f=!1),Er(e,p,u,r)),f&&(L(p.lineNumber),p.lineNumber.appendChild(document.createTextNode(ct(e.options,u)))),a=p.node.nextSibling}else{var m=Rr(e,p,u,r);o.insertBefore(m,a)}u+=p.size}for(;a;)a=s(a)}function Qi(e){var t=e.gutters.offsetWidth;e.sizer.style.marginLeft=t+"px",Nr(e,"gutterChanged",e)}function Yi(e,t){e.display.sizer.style.minHeight=t.docHeight+"px",e.display.heightForcer.style.top=t.docHeight+"px",e.display.gutters.style.height=t.docHeight+e.display.barHeight+Zr(e)+"px"}function Zi(e){var t=e.display,r=t.view;if(t.alignWidgets||t.gutters.firstChild&&e.options.fixedGutter){for(var n=In(t)-t.scroller.scrollLeft+e.doc.scrollLeft,i=t.gutters.offsetWidth,o=n+"px",a=0;a<r.length;a++)if(!r[a].hidden){e.options.fixedGutter&&(r[a].gutter&&(r[a].gutter.style.left=o),r[a].gutterBackground&&(r[a].gutterBackground.style.left=o));var s=r[a].alignable;if(s)for(var l=0;l<s.length;l++)s[l].style.left=o}e.options.fixedGutter&&(t.gutters.style.left=n+i+"px")}}function Ji(e){if(!e.options.lineNumbers)return!1;var t=e.doc,r=ct(e.options,t.first+t.size-1),n=e.display;if(r.length!=n.lineNumChars){var i=n.measure.appendChild(D("div",[D("div",r)],"CodeMirror-linenumber CodeMirror-gutter-elt")),o=i.firstChild.offsetWidth,a=i.offsetWidth-o;return n.lineGutter.style.width="",n.lineNumInnerWidth=Math.max(o,n.lineGutter.offsetWidth-a)+1,n.lineNumWidth=n.lineNumInnerWidth+a,n.lineNumChars=n.lineNumInnerWidth?r.length:-1,n.lineGutter.style.width=n.lineNumWidth+"px",Qi(e.display),!0}return!1}function eo(e,t){for(var r=[],n=!1,i=0;i<e.length;i++){var o=e[i],a=null;if("string"!=typeof o&&(a=o.style,o=o.className),"CodeMirror-linenumbers"==o){if(!t)continue;n=!0}r.push({className:o,style:a})}return t&&!n&&r.push({className:"CodeMirror-linenumbers",style:null}),r}function to(e){var t=e.gutters,r=e.gutterSpecs;L(t),e.lineGutter=null;for(var n=0;n<r.length;++n){var i=r[n],o=i.className,a=i.style,s=t.appendChild(D("div",null,"CodeMirror-gutter "+o));a&&(s.style.cssText=a),"CodeMirror-linenumbers"==o&&(e.lineGutter=s,s.style.width=(e.lineNumWidth||1)+"px")}t.style.display=r.length?"":"none",Qi(e)}function ro(e){to(e.display),Hn(e),Zi(e)}function no(e,t,n,i){var o=this;this.input=n,o.scrollbarFiller=D("div",null,"CodeMirror-scrollbar-filler"),o.scrollbarFiller.setAttribute("cm-not-content","true"),o.gutterFiller=D("div",null,"CodeMirror-gutter-filler"),o.gutterFiller.setAttribute("cm-not-content","true"),o.lineDiv=A("div",null,"CodeMirror-code"),o.selectionDiv=D("div",null,null,"position: relative; z-index: 1"),o.cursorDiv=D("div",null,"CodeMirror-cursors"),o.measure=D("div",null,"CodeMirror-measure"),o.lineMeasure=D("div",null,"CodeMirror-measure"),o.lineSpace=A("div",[o.measure,o.lineMeasure,o.selectionDiv,o.cursorDiv,o.lineDiv],null,"position: relative; outline: none");var c=A("div",[o.lineSpace],"CodeMirror-lines");o.mover=D("div",[c],null,"position: relative"),o.sizer=D("div",[o.mover],"CodeMirror-sizer"),o.sizerWidth=null,o.heightForcer=D("div",null,null,"position: absolute; height: "+V+"px; width: 1px;"),o.gutters=D("div",null,"CodeMirror-gutters"),o.lineGutter=null,o.scroller=D("div",[o.sizer,o.heightForcer,o.gutters],"CodeMirror-scroll"),o.scroller.setAttribute("tabIndex","-1"),o.wrapper=D("div",[o.scrollbarFiller,o.gutterFiller,o.scroller],"CodeMirror"),u&&d>=105&&(o.wrapper.style.clipPath="inset(0px)"),o.wrapper.setAttribute("translate","no"),a&&s<8&&(o.gutters.style.zIndex=-1,o.scroller.style.paddingRight=0),l||r&&y||(o.scroller.draggable=!0),e&&(e.appendChild?e.appendChild(o.wrapper):e(o.wrapper)),o.viewFrom=o.viewTo=t.first,o.reportedViewFrom=o.reportedViewTo=t.first,o.view=[],o.renderedView=null,o.externalMeasured=null,o.viewOffset=0,o.lastWrapHeight=o.lastWrapWidth=0,o.updateLineNumbers=null,o.nativeBarWidth=o.barHeight=o.barWidth=0,o.scrollbarsClipped=!1,o.lineNumWidth=o.lineNumInnerWidth=o.lineNumChars=null,o.alignWidgets=!1,o.cachedCharWidth=o.cachedTextHeight=o.cachedPaddingH=null,o.maxLine=null,o.maxLineLength=0,o.maxLineChanged=!1,o.wheelDX=o.wheelDY=o.wheelStartX=o.wheelStartY=null,o.shift=!1,o.selForContextMenu=null,o.activeTouch=null,o.gutterSpecs=eo(i.gutters,i.lineNumbers),to(o),n.init(o)}Hi.prototype.signal=function(e,t){Ce(e,t)&&this.events.push(arguments)},Hi.prototype.finish=function(){for(var e=0;e<this.events.length;e++)ke.apply(null,this.events[e])};var io=0,oo=null;function ao(e){var t=e.wheelDeltaX,r=e.wheelDeltaY;return null==t&&e.detail&&e.axis==e.HORIZONTAL_AXIS&&(t=e.detail),null==r&&e.detail&&e.axis==e.VERTICAL_AXIS?r=e.detail:null==r&&(r=e.wheelDelta),{x:t,y:r}}function so(e){var t=ao(e);return t.x*=oo,t.y*=oo,t}function lo(e,t){u&&102==d&&(null==e.display.chromeScrollHack?e.display.sizer.style.pointerEvents="none":clearTimeout(e.display.chromeScrollHack),e.display.chromeScrollHack=setTimeout((function(){e.display.chromeScrollHack=null,e.display.sizer.style.pointerEvents=""}),100));var n=ao(t),i=n.x,o=n.y,a=oo;0===t.deltaMode&&(i=t.deltaX,o=t.deltaY,a=1);var s=e.display,c=s.scroller,f=c.scrollWidth>c.clientWidth,m=c.scrollHeight>c.clientHeight;if(i&&f||o&&m){if(o&&_&&l)e:for(var h=t.target,g=s.view;h!=c;h=h.parentNode)for(var v=0;v<g.length;v++)if(g[v].node==h){e.display.currentWheelTarget=h;break e}if(i&&!r&&!p&&null!=a)return o&&m&&vi(e,Math.max(0,c.scrollTop+o*a)),_i(e,Math.max(0,c.scrollLeft+i*a)),(!o||o&&m)&&Te(t),void(s.wheelStartX=null);if(o&&null!=a){var y=o*a,b=e.doc.scrollTop,x=b+s.wrapper.clientHeight;y<0?b=Math.max(0,b+y-50):x=Math.min(e.doc.height,x+y+50),Gi(e,{top:b,bottom:x})}io<20&&0!==t.deltaMode&&(null==s.wheelStartX?(s.wheelStartX=c.scrollLeft,s.wheelStartY=c.scrollTop,s.wheelDX=i,s.wheelDY=o,setTimeout((function(){if(null!=s.wheelStartX){var e=c.scrollLeft-s.wheelStartX,t=c.scrollTop-s.wheelStartY,r=t&&s.wheelDY&&t/s.wheelDY||e&&s.wheelDX&&e/s.wheelDX;s.wheelStartX=s.wheelStartY=null,r&&(oo=(oo*io+r)/(io+1),++io)}}),200)):(s.wheelDX+=i,s.wheelDY+=o))}}a?oo=-.53:r?oo=15:u?oo=-.7:f&&(oo=-1/3);var co=function(e,t){this.ranges=e,this.primIndex=t};co.prototype.primary=function(){return this.ranges[this.primIndex]},co.prototype.equals=function(e){if(e==this)return!0;if(e.primIndex!=this.primIndex||e.ranges.length!=this.ranges.length)return!1;for(var t=0;t<this.ranges.length;t++){var r=this.ranges[t],n=e.ranges[t];if(!pt(r.anchor,n.anchor)||!pt(r.head,n.head))return!1}return!0},co.prototype.deepCopy=function(){for(var e=[],t=0;t<this.ranges.length;t++)e[t]=new uo(ft(this.ranges[t].anchor),ft(this.ranges[t].head));return new co(e,this.primIndex)},co.prototype.somethingSelected=function(){for(var e=0;e<this.ranges.length;e++)if(!this.ranges[e].empty())return!0;return!1},co.prototype.contains=function(e,t){t||(t=e);for(var r=0;r<this.ranges.length;r++){var n=this.ranges[r];if(dt(t,n.from())>=0&&dt(e,n.to())<=0)return r}return-1};var uo=function(e,t){this.anchor=e,this.head=t};function po(e,t,r){var n=e&&e.options.selectionsMayTouch,i=t[r];t.sort((function(e,t){return dt(e.from(),t.from())})),r=U(t,i);for(var o=1;o<t.length;o++){var a=t[o],s=t[o-1],l=dt(s.to(),a.from());if(n&&!a.empty()?l>0:l>=0){var c=ht(s.from(),a.from()),u=mt(s.to(),a.to()),d=s.empty()?a.from()==a.head:s.from()==s.head;o<=r&&--r,t.splice(--o,2,new uo(d?u:c,d?c:u))}}return new co(t,r)}function fo(e,t){return new co([new uo(e,t||e)],0)}function mo(e){return e.text?ut(e.from.line+e.text.length-1,ee(e.text).length+(1==e.text.length?e.from.ch:0)):e.to}function ho(e,t){if(dt(e,t.from)<0)return e;if(dt(e,t.to)<=0)return mo(t);var r=e.line+t.text.length-(t.to.line-t.from.line)-1,n=e.ch;return e.line==t.to.line&&(n+=mo(t).ch-t.to.ch),ut(r,n)}function go(e,t){for(var r=[],n=0;n<e.sel.ranges.length;n++){var i=e.sel.ranges[n];r.push(new uo(ho(i.anchor,t),ho(i.head,t)))}return po(e.cm,r,e.sel.primIndex)}function vo(e,t,r){return e.line==t.line?ut(r.line,e.ch-t.ch+r.ch):ut(r.line+(e.line-t.line),e.ch)}function yo(e,t,r){for(var n=[],i=ut(e.first,0),o=i,a=0;a<t.length;a++){var s=t[a],l=vo(s.from,i,o),c=vo(mo(s),i,o);if(i=s.to,o=c,"around"==r){var u=e.sel.ranges[a],d=dt(u.head,u.anchor)<0;n[a]=new uo(d?c:l,d?l:c)}else n[a]=new uo(l,l)}return new co(n,e.sel.primIndex)}function _o(e){e.doc.mode=Xe(e.options,e.doc.modeOption),bo(e)}function bo(e){e.doc.iter((function(e){e.stateAfter&&(e.stateAfter=null),e.styles&&(e.styles=null)})),e.doc.modeFrontier=e.doc.highlightFrontier=e.doc.first,Wi(e,100),e.state.modeGen++,e.curOp&&Hn(e)}function xo(e,t){return 0==t.from.ch&&0==t.to.ch&&""==ee(t.text)&&(!e.cm||e.cm.options.wholeLineUpdateBefore)}function ko(e,t,r,n){function i(e){return r?r[e]:null}function o(e,r,i){mr(e,r,i,n),Nr(e,"change",e,t)}function a(e,t){for(var r=[],o=e;o<t;++o)r.push(new fr(c[o],i(o),n));return r}var s=t.from,l=t.to,c=t.text,u=rt(e,s.line),d=rt(e,l.line),p=ee(c),f=i(c.length-1),m=l.line-s.line;if(t.full)e.insert(0,a(0,c.length)),e.remove(c.length,e.size-c.length);else if(xo(e,t)){var h=a(0,c.length-1);o(d,d.text,f),m&&e.remove(s.line,m),h.length&&e.insert(s.line,h)}else if(u==d)if(1==c.length)o(u,u.text.slice(0,s.ch)+p+u.text.slice(l.ch),f);else{var g=a(1,c.length-1);g.push(new fr(p+u.text.slice(l.ch),f,n)),o(u,u.text.slice(0,s.ch)+c[0],i(0)),e.insert(s.line+1,g)}else if(1==c.length)o(u,u.text.slice(0,s.ch)+c[0]+d.text.slice(l.ch),i(0)),e.remove(s.line+1,m);else{o(u,u.text.slice(0,s.ch)+c[0],i(0)),o(d,p+d.text.slice(l.ch),f);var v=a(1,c.length-1);m>1&&e.remove(s.line+1,m-1),e.insert(s.line+1,v)}Nr(e,"change",e,t)}function wo(e,t,r){function n(e,i,o){if(e.linked)for(var a=0;a<e.linked.length;++a){var s=e.linked[a];if(s.doc!=i){var l=o&&s.sharedHist;r&&!l||(t(s.doc,l),n(s.doc,e,l))}}}n(e,null,!0)}function So(e,t){if(t.cm)throw new Error("This document is already in use.");e.doc=t,t.cm=e,jn(e),_o(e),Co(e),e.options.direction=t.direction,e.options.lineWrapping||pr(e),e.options.mode=t.modeOption,Hn(e)}function Co(e){("rtl"==e.doc.direction?F:T)(e.display.lineDiv,"CodeMirror-rtl")}function Mo(e){Oi(e,(function(){Co(e),Hn(e)}))}function To(e){this.done=[],this.undone=[],this.undoDepth=e?e.undoDepth:1/0,this.lastModTime=this.lastSelTime=0,this.lastOp=this.lastSelOp=null,this.lastOrigin=this.lastSelOrigin=null,this.generation=this.maxGeneration=e?e.maxGeneration:1}function Lo(e,t){var r={from:ft(t.from),to:mo(t),text:nt(e,t.from,t.to)};return Eo(e,r,t.from.line,t.to.line+1),wo(e,(function(e){return Eo(e,r,t.from.line,t.to.line+1)}),!0),r}function zo(e){for(;e.length&&ee(e).ranges;)e.pop()}function Do(e,t){return t?(zo(e.done),ee(e.done)):e.done.length&&!ee(e.done).ranges?ee(e.done):e.done.length>1&&!e.done[e.done.length-2].ranges?(e.done.pop(),ee(e.done)):void 0}function Ao(e,t,r,n){var i=e.history;i.undone.length=0;var o,a,s=+new Date;if((i.lastOp==n||i.lastOrigin==t.origin&&t.origin&&("+"==t.origin.charAt(0)&&i.lastModTime>s-(e.cm?e.cm.options.historyEventDelay:500)||"*"==t.origin.charAt(0)))&&(o=Do(i,i.lastOp==n)))a=ee(o.changes),0==dt(t.from,t.to)&&0==dt(t.from,a.to)?a.to=mo(t):o.changes.push(Lo(e,t));else{var l=ee(i.done);for(l&&l.ranges||Fo(e.sel,i.done),o={changes:[Lo(e,t)],generation:i.generation},i.done.push(o);i.done.length>i.undoDepth;)i.done.shift(),i.done[0].ranges||i.done.shift()}i.done.push(r),i.generation=++i.maxGeneration,i.lastModTime=i.lastSelTime=s,i.lastOp=i.lastSelOp=n,i.lastOrigin=i.lastSelOrigin=t.origin,a||ke(e,"historyAdded")}function qo(e,t,r,n){var i=t.charAt(0);return"*"==i||"+"==i&&r.ranges.length==n.ranges.length&&r.somethingSelected()==n.somethingSelected()&&new Date-e.history.lastSelTime<=(e.cm?e.cm.options.historyEventDelay:500)}function No(e,t,r,n){var i=e.history,o=n&&n.origin;r==i.lastSelOp||o&&i.lastSelOrigin==o&&(i.lastModTime==i.lastSelTime&&i.lastOrigin==o||qo(e,o,ee(i.done),t))?i.done[i.done.length-1]=t:Fo(t,i.done),i.lastSelTime=+new Date,i.lastSelOrigin=o,i.lastSelOp=r,n&&!1!==n.clearRedo&&zo(i.undone)}function Fo(e,t){var r=ee(t);r&&r.ranges&&r.equals(e)||t.push(e)}function Eo(e,t,r,n){var i=t["spans_"+e.id],o=0;e.iter(Math.max(e.first,r),Math.min(e.first+e.size,n),(function(r){r.markedSpans&&((i||(i=t["spans_"+e.id]={}))[o]=r.markedSpans),++o}))}function Oo(e){if(!e)return null;for(var t,r=0;r<e.length;++r)e[r].marker.explicitlyCleared?t||(t=e.slice(0,r)):t&&t.push(e[r]);return t?t.length?t:null:e}function Io(e,t){var r=t["spans_"+e.id];if(!r)return null;for(var n=[],i=0;i<t.text.length;++i)n.push(Oo(r[i]));return n}function Po(e,t){var r=Io(e,t),n=$t(e,t);if(!r)return n;if(!n)return r;for(var i=0;i<r.length;++i){var o=r[i],a=n[i];if(o&&a)e:for(var s=0;s<a.length;++s){for(var l=a[s],c=0;c<o.length;++c)if(o[c].marker==l.marker)continue e;o.push(l)}else a&&(r[i]=a)}return r}function jo(e,t,r){for(var n=[],i=0;i<e.length;++i){var o=e[i];if(o.ranges)n.push(r?co.prototype.deepCopy.call(o):o);else{var a=o.changes,s=[];n.push({changes:s});for(var l=0;l<a.length;++l){var c=a[l],u=void 0;if(s.push({from:c.from,to:c.to,text:c.text}),t)for(var d in c)(u=d.match(/^spans_(\d+)$/))&&U(t,Number(u[1]))>-1&&(ee(s)[d]=c[d],delete c[d])}}}return n}function Wo(e,t,r,n){if(n){var i=e.anchor;if(r){var o=dt(t,i)<0;o!=dt(r,i)<0?(i=t,t=r):o!=dt(t,r)<0&&(t=r)}return new uo(i,t)}return new uo(r||t,t)}function Bo(e,t,r,n,i){null==i&&(i=e.cm&&(e.cm.display.shift||e.extend)),Ko(e,new co([Wo(e.sel.primary(),t,r,i)],0),n)}function Ho(e,t,r){for(var n=[],i=e.cm&&(e.cm.display.shift||e.extend),o=0;o<e.sel.ranges.length;o++)n[o]=Wo(e.sel.ranges[o],t[o],null,i);Ko(e,po(e.cm,n,e.sel.primIndex),r)}function Ro(e,t,r,n){var i=e.sel.ranges.slice(0);i[t]=r,Ko(e,po(e.cm,i,e.sel.primIndex),n)}function $o(e,t,r,n){Ko(e,fo(t,r),n)}function Uo(e,t,r){var n={ranges:t.ranges,update:function(t){this.ranges=[];for(var r=0;r<t.length;r++)this.ranges[r]=new uo(vt(e,t[r].anchor),vt(e,t[r].head))},origin:r&&r.origin};return ke(e,"beforeSelectionChange",e,n),e.cm&&ke(e.cm,"beforeSelectionChange",e.cm,n),n.ranges!=t.ranges?po(e.cm,n.ranges,n.ranges.length-1):t}function Vo(e,t,r){var n=e.history.done,i=ee(n);i&&i.ranges?(n[n.length-1]=t,Go(e,t,r)):Ko(e,t,r)}function Ko(e,t,r){Go(e,t,r),No(e,e.sel,e.cm?e.cm.curOp.id:NaN,r)}function Go(e,t,r){(Ce(e,"beforeSelectionChange")||e.cm&&Ce(e.cm,"beforeSelectionChange"))&&(t=Uo(e,t,r));var n=r&&r.bias||(dt(t.primary().head,e.sel.primary().head)<0?-1:1);Xo(e,Yo(e,t,n,!0)),r&&!1===r.scroll||!e.cm||"nocursor"==e.cm.getOption("readOnly")||pi(e.cm)}function Xo(e,t){t.equals(e.sel)||(e.sel=t,e.cm&&(e.cm.curOp.updateInput=1,e.cm.curOp.selectionChanged=!0,Se(e.cm)),Nr(e,"cursorActivity",e))}function Qo(e){Xo(e,Yo(e,e.sel,null,!1))}function Yo(e,t,r,n){for(var i,o=0;o<t.ranges.length;o++){var a=t.ranges[o],s=t.ranges.length==e.sel.ranges.length&&e.sel.ranges[o],l=Jo(e,a.anchor,s&&s.anchor,r,n),c=a.head==a.anchor?l:Jo(e,a.head,s&&s.head,r,n);(i||l!=a.anchor||c!=a.head)&&(i||(i=t.ranges.slice(0,o)),i[o]=new uo(l,c))}return i?po(e.cm,i,t.primIndex):t}function Zo(e,t,r,n,i){var o=rt(e,t.line);if(o.markedSpans)for(var a=0;a<o.markedSpans.length;++a){var s=o.markedSpans[a],l=s.marker,c="selectLeft"in l?!l.selectLeft:l.inclusiveLeft,u="selectRight"in l?!l.selectRight:l.inclusiveRight;if((null==s.from||(c?s.from<=t.ch:s.from<t.ch))&&(null==s.to||(u?s.to>=t.ch:s.to>t.ch))){if(i&&(ke(l,"beforeCursorEnter"),l.explicitlyCleared)){if(o.markedSpans){--a;continue}break}if(!l.atomic)continue;if(r){var d=l.find(n<0?1:-1),p=void 0;if((n<0?u:c)&&(d=ea(e,d,-n,d&&d.line==t.line?o:null)),d&&d.line==t.line&&(p=dt(d,r))&&(n<0?p<0:p>0))return Zo(e,d,t,n,i)}var f=l.find(n<0?-1:1);return(n<0?c:u)&&(f=ea(e,f,n,f.line==t.line?o:null)),f?Zo(e,f,t,n,i):null}}return t}function Jo(e,t,r,n,i){var o=n||1,a=Zo(e,t,r,o,i)||!i&&Zo(e,t,r,o,!0)||Zo(e,t,r,-o,i)||!i&&Zo(e,t,r,-o,!0);return a||(e.cantEdit=!0,ut(e.first,0))}function ea(e,t,r,n){return r<0&&0==t.ch?t.line>e.first?vt(e,ut(t.line-1)):null:r>0&&t.ch==(n||rt(e,t.line)).text.length?t.line<e.first+e.size-1?ut(t.line+1,0):null:new ut(t.line,t.ch+r)}function ta(e){e.setSelection(ut(e.firstLine(),0),ut(e.lastLine()),G)}function ra(e,t,r){var n={canceled:!1,from:t.from,to:t.to,text:t.text,origin:t.origin,cancel:function(){return n.canceled=!0}};return r&&(n.update=function(t,r,i,o){t&&(n.from=vt(e,t)),r&&(n.to=vt(e,r)),i&&(n.text=i),void 0!==o&&(n.origin=o)}),ke(e,"beforeChange",e,n),e.cm&&ke(e.cm,"beforeChange",e.cm,n),n.canceled?(e.cm&&(e.cm.curOp.updateInput=2),null):{from:n.from,to:n.to,text:n.text,origin:n.origin}}function na(e,t,r){if(e.cm){if(!e.cm.curOp)return Ii(e.cm,na)(e,t,r);if(e.cm.state.suppressEdits)return}if(!(Ce(e,"beforeChange")||e.cm&&Ce(e.cm,"beforeChange"))||(t=ra(e,t,!0))){var n=Ft&&!r&&Vt(e,t.from,t.to);if(n)for(var i=n.length-1;i>=0;--i)ia(e,{from:n[i].from,to:n[i].to,text:i?[""]:t.text,origin:t.origin});else ia(e,t)}}function ia(e,t){if(1!=t.text.length||""!=t.text[0]||0!=dt(t.from,t.to)){var r=go(e,t);Ao(e,t,r,e.cm?e.cm.curOp.id:NaN),sa(e,t,r,$t(e,t));var n=[];wo(e,(function(e,r){r||-1!=U(n,e.history)||(pa(e.history,t),n.push(e.history)),sa(e,t,null,$t(e,t))}))}}function oa(e,t,r){var n=e.cm&&e.cm.state.suppressEdits;if(!n||r){for(var i,o=e.history,a=e.sel,s="undo"==t?o.done:o.undone,l="undo"==t?o.undone:o.done,c=0;c<s.length&&(i=s[c],r?!i.ranges||i.equals(e.sel):i.ranges);c++);if(c!=s.length){for(o.lastOrigin=o.lastSelOrigin=null;;){if(!(i=s.pop()).ranges){if(n)return void s.push(i);break}if(Fo(i,l),r&&!i.equals(e.sel))return void Ko(e,i,{clearRedo:!1});a=i}var u=[];Fo(a,l),l.push({changes:u,generation:o.generation}),o.generation=i.generation||++o.maxGeneration;for(var d=Ce(e,"beforeChange")||e.cm&&Ce(e.cm,"beforeChange"),p=function(r){var n=i.changes[r];if(n.origin=t,d&&!ra(e,n,!1))return s.length=0,{};u.push(Lo(e,n));var o=r?go(e,n):ee(s);sa(e,n,o,Po(e,n)),!r&&e.cm&&e.cm.scrollIntoView({from:n.from,to:mo(n)});var a=[];wo(e,(function(e,t){t||-1!=U(a,e.history)||(pa(e.history,n),a.push(e.history)),sa(e,n,null,Po(e,n))}))},f=i.changes.length-1;f>=0;--f){var m=p(f);if(m)return m.v}}}}function aa(e,t){if(0!=t&&(e.first+=t,e.sel=new co(te(e.sel.ranges,(function(e){return new uo(ut(e.anchor.line+t,e.anchor.ch),ut(e.head.line+t,e.head.ch))})),e.sel.primIndex),e.cm)){Hn(e.cm,e.first,e.first-t,t);for(var r=e.cm.display,n=r.viewFrom;n<r.viewTo;n++)Rn(e.cm,n,"gutter")}}function sa(e,t,r,n){if(e.cm&&!e.cm.curOp)return Ii(e.cm,sa)(e,t,r,n);if(t.to.line<e.first)aa(e,t.text.length-1-(t.to.line-t.from.line));else if(!(t.from.line>e.lastLine())){if(t.from.line<e.first){var i=t.text.length-1-(e.first-t.from.line);aa(e,i),t={from:ut(e.first,0),to:ut(t.to.line+i,t.to.ch),text:[ee(t.text)],origin:t.origin}}var o=e.lastLine();t.to.line>o&&(t={from:t.from,to:ut(o,rt(e,o).text.length),text:[t.text[0]],origin:t.origin}),t.removed=nt(e,t.from,t.to),r||(r=go(e,t)),e.cm?la(e.cm,t,n):ko(e,t,n),Go(e,r,G),e.cantEdit&&Jo(e,ut(e.firstLine(),0))&&(e.cantEdit=!1)}}function la(e,t,r){var n=e.doc,i=e.display,o=t.from,a=t.to,s=!1,l=o.line;e.options.lineWrapping||(l=at(nr(rt(n,o.line))),n.iter(l,a.line+1,(function(e){if(e==i.maxLine)return s=!0,!0}))),n.sel.contains(t.from,t.to)>-1&&Se(e),ko(n,t,r,Pn(e)),e.options.lineWrapping||(n.iter(l,o.line+t.text.length,(function(e){var t=dr(e);t>i.maxLineLength&&(i.maxLine=e,i.maxLineLength=t,i.maxLineChanged=!0,s=!1)})),s&&(e.curOp.updateMaxLine=!0)),Nt(n,o.line),Wi(e,400);var c=t.text.length-(a.line-o.line)-1;t.full?Hn(e):o.line!=a.line||1!=t.text.length||xo(e.doc,t)?Hn(e,o.line,a.line+1,c):Rn(e,o.line,"text");var u=Ce(e,"changes"),d=Ce(e,"change");if(d||u){var p={from:o,to:a,text:t.text,removed:t.removed,origin:t.origin};d&&Nr(e,"change",e,p),u&&(e.curOp.changeObjs||(e.curOp.changeObjs=[])).push(p)}e.display.selForContextMenu=null}function ca(e,t,r,n,i){var o;n||(n=r),dt(n,r)<0&&(r=(o=[n,r])[0],n=o[1]),"string"==typeof t&&(t=e.splitLines(t)),na(e,{from:r,to:n,text:t,origin:i})}function ua(e,t,r,n){r<e.line?e.line+=n:t<e.line&&(e.line=t,e.ch=0)}function da(e,t,r,n){for(var i=0;i<e.length;++i){var o=e[i],a=!0;if(o.ranges){o.copied||((o=e[i]=o.deepCopy()).copied=!0);for(var s=0;s<o.ranges.length;s++)ua(o.ranges[s].anchor,t,r,n),ua(o.ranges[s].head,t,r,n)}else{for(var l=0;l<o.changes.length;++l){var c=o.changes[l];if(r<c.from.line)c.from=ut(c.from.line+n,c.from.ch),c.to=ut(c.to.line+n,c.to.ch);else if(t<=c.to.line){a=!1;break}}a||(e.splice(0,i+1),i=0)}}}function pa(e,t){var r=t.from.line,n=t.to.line,i=t.text.length-(n-r)-1;da(e.done,r,n,i),da(e.undone,r,n,i)}function fa(e,t,r,n){var i=t,o=t;return"number"==typeof t?o=rt(e,gt(e,t)):i=at(t),null==i?null:(n(o,i)&&e.cm&&Rn(e.cm,i,r),o)}function ma(e){this.lines=e,this.parent=null;for(var t=0,r=0;r<e.length;++r)e[r].parent=this,t+=e[r].height;this.height=t}function ha(e){this.children=e;for(var t=0,r=0,n=0;n<e.length;++n){var i=e[n];t+=i.chunkSize(),r+=i.height,i.parent=this}this.size=t,this.height=r,this.parent=null}uo.prototype.from=function(){return ht(this.anchor,this.head)},uo.prototype.to=function(){return mt(this.anchor,this.head)},uo.prototype.empty=function(){return this.head.line==this.anchor.line&&this.head.ch==this.anchor.ch},ma.prototype={chunkSize:function(){return this.lines.length},removeInner:function(e,t){for(var r=e,n=e+t;r<n;++r){var i=this.lines[r];this.height-=i.height,hr(i),Nr(i,"delete")}this.lines.splice(e,t)},collapse:function(e){e.push.apply(e,this.lines)},insertInner:function(e,t,r){this.height+=r,this.lines=this.lines.slice(0,e).concat(t).concat(this.lines.slice(e));for(var n=0;n<t.length;++n)t[n].parent=this},iterN:function(e,t,r){for(var n=e+t;e<n;++e)if(r(this.lines[e]))return!0}},ha.prototype={chunkSize:function(){return this.size},removeInner:function(e,t){this.size-=t;for(var r=0;r<this.children.length;++r){var n=this.children[r],i=n.chunkSize();if(e<i){var o=Math.min(t,i-e),a=n.height;if(n.removeInner(e,o),this.height-=a-n.height,i==o&&(this.children.splice(r--,1),n.parent=null),0==(t-=o))break;e=0}else e-=i}if(this.size-t<25&&(this.children.length>1||!(this.children[0]instanceof ma))){var s=[];this.collapse(s),this.children=[new ma(s)],this.children[0].parent=this}},collapse:function(e){for(var t=0;t<this.children.length;++t)this.children[t].collapse(e)},insertInner:function(e,t,r){this.size+=t.length,this.height+=r;for(var n=0;n<this.children.length;++n){var i=this.children[n],o=i.chunkSize();if(e<=o){if(i.insertInner(e,t,r),i.lines&&i.lines.length>50){for(var a=i.lines.length%25+25,s=a;s<i.lines.length;){var l=new ma(i.lines.slice(s,s+=25));i.height-=l.height,this.children.splice(++n,0,l),l.parent=this}i.lines=i.lines.slice(0,a),this.maybeSpill()}break}e-=o}},maybeSpill:function(){if(!(this.children.length<=10)){var e=this;do{var t=new ha(e.children.splice(e.children.length-5,5));if(e.parent){e.size-=t.size,e.height-=t.height;var r=U(e.parent.children,e);e.parent.children.splice(r+1,0,t)}else{var n=new ha(e.children);n.parent=e,e.children=[n,t],e=n}t.parent=e.parent}while(e.children.length>10);e.parent.maybeSpill()}},iterN:function(e,t,r){for(var n=0;n<this.children.length;++n){var i=this.children[n],o=i.chunkSize();if(e<o){var a=Math.min(t,o-e);if(i.iterN(e,a,r))return!0;if(0==(t-=a))break;e=0}else e-=o}}};var ga=function(e,t,r){if(r)for(var n in r)r.hasOwnProperty(n)&&(this[n]=r[n]);this.doc=e,this.node=t};function va(e,t,r){ur(t)<(e.curOp&&e.curOp.scrollTop||e.doc.scrollTop)&&di(e,r)}function ya(e,t,r,n){var i=new ga(e,r,n),o=e.cm;return o&&i.noHScroll&&(o.display.alignWidgets=!0),fa(e,t,"widget",(function(t){var r=t.widgets||(t.widgets=[]);if(null==i.insertAt?r.push(i):r.splice(Math.min(r.length,Math.max(0,i.insertAt)),0,i),i.line=t,o&&!lr(e,t)){var n=ur(t)<e.scrollTop;ot(t,t.height+Kr(i)),n&&di(o,i.height),o.curOp.forceUpdate=!0}return!0})),o&&Nr(o,"lineWidgetAdded",o,i,"number"==typeof t?t:at(t)),i}ga.prototype.clear=function(){var e=this.doc.cm,t=this.line.widgets,r=this.line,n=at(r);if(null!=n&&t){for(var i=0;i<t.length;++i)t[i]==this&&t.splice(i--,1);t.length||(r.widgets=null);var o=Kr(this);ot(r,Math.max(0,r.height-o)),e&&(Oi(e,(function(){va(e,r,-o),Rn(e,n,"widget")})),Nr(e,"lineWidgetCleared",e,this,n))}},ga.prototype.changed=function(){var e=this,t=this.height,r=this.doc.cm,n=this.line;this.height=null;var i=Kr(this)-t;i&&(lr(this.doc,n)||ot(n,n.height+i),r&&Oi(r,(function(){r.curOp.forceUpdate=!0,va(r,n,i),Nr(r,"lineWidgetChanged",r,e,at(n))})))},Me(ga);var _a=0,ba=function(e,t){this.lines=[],this.type=t,this.doc=e,this.id=++_a};function xa(e,t,r,n,i){if(n&&n.shared)return wa(e,t,r,n,i);if(e.cm&&!e.cm.curOp)return Ii(e.cm,xa)(e,t,r,n,i);var o=new ba(e,i),a=dt(t,r);if(n&&H(n,o,!1),a>0||0==a&&!1!==o.clearWhenEmpty)return o;if(o.replacedWith&&(o.collapsed=!0,o.widgetNode=A("span",[o.replacedWith],"CodeMirror-widget"),n.handleMouseEvents||o.widgetNode.setAttribute("cm-ignore-events","true"),n.insertLeft&&(o.widgetNode.insertLeft=!0)),o.collapsed){if(rr(e,t.line,t,r,o)||t.line!=r.line&&rr(e,r.line,t,r,o))throw new Error("Inserting collapsed marker partially overlapping an existing one");It()}o.addToHistory&&Ao(e,{from:t,to:r,origin:"markText"},e.sel,NaN);var s,l=t.line,c=e.cm;if(e.iter(l,r.line+1,(function(n){c&&o.collapsed&&!c.options.lineWrapping&&nr(n)==c.display.maxLine&&(s=!0),o.collapsed&&l!=t.line&&ot(n,0),Bt(n,new Pt(o,l==t.line?t.ch:null,l==r.line?r.ch:null),e.cm&&e.cm.curOp),++l})),o.collapsed&&e.iter(t.line,r.line+1,(function(t){lr(e,t)&&ot(t,0)})),o.clearOnEnter&&_e(o,"beforeCursorEnter",(function(){return o.clear()})),o.readOnly&&(Ot(),(e.history.done.length||e.history.undone.length)&&e.clearHistory()),o.collapsed&&(o.id=++_a,o.atomic=!0),c){if(s&&(c.curOp.updateMaxLine=!0),o.collapsed)Hn(c,t.line,r.line+1);else if(o.className||o.startStyle||o.endStyle||o.css||o.attributes||o.title)for(var u=t.line;u<=r.line;u++)Rn(c,u,"text");o.atomic&&Qo(c.doc),Nr(c,"markerAdded",c,o)}return o}ba.prototype.clear=function(){if(!this.explicitlyCleared){var e=this.doc.cm,t=e&&!e.curOp;if(t&&Li(e),Ce(this,"clear")){var r=this.find();r&&Nr(this,"clear",r.from,r.to)}for(var n=null,i=null,o=0;o<this.lines.length;++o){var a=this.lines[o],s=jt(a.markedSpans,this);e&&!this.collapsed?Rn(e,at(a),"text"):e&&(null!=s.to&&(i=at(a)),null!=s.from&&(n=at(a))),a.markedSpans=Wt(a.markedSpans,s),null==s.from&&this.collapsed&&!lr(this.doc,a)&&e&&ot(a,Fn(e.display))}if(e&&this.collapsed&&!e.options.lineWrapping)for(var l=0;l<this.lines.length;++l){var c=nr(this.lines[l]),u=dr(c);u>e.display.maxLineLength&&(e.display.maxLine=c,e.display.maxLineLength=u,e.display.maxLineChanged=!0)}null!=n&&e&&this.collapsed&&Hn(e,n,i+1),this.lines.length=0,this.explicitlyCleared=!0,this.atomic&&this.doc.cantEdit&&(this.doc.cantEdit=!1,e&&Qo(e.doc)),e&&Nr(e,"markerCleared",e,this,n,i),t&&zi(e),this.parent&&this.parent.clear()}},ba.prototype.find=function(e,t){var r,n;null==e&&"bookmark"==this.type&&(e=1);for(var i=0;i<this.lines.length;++i){var o=this.lines[i],a=jt(o.markedSpans,this);if(null!=a.from&&(r=ut(t?o:at(o),a.from),-1==e))return r;if(null!=a.to&&(n=ut(t?o:at(o),a.to),1==e))return n}return r&&{from:r,to:n}},ba.prototype.changed=function(){var e=this,t=this.find(-1,!0),r=this,n=this.doc.cm;t&&n&&Oi(n,(function(){var i=t.line,o=at(t.line),a=an(n,o);if(a&&(hn(a),n.curOp.selectionChanged=n.curOp.forceUpdate=!0),n.curOp.updateMaxLine=!0,!lr(r.doc,i)&&null!=r.height){var s=r.height;r.height=null;var l=Kr(r)-s;l&&ot(i,i.height+l)}Nr(n,"markerChanged",n,e)}))},ba.prototype.attachLine=function(e){if(!this.lines.length&&this.doc.cm){var t=this.doc.cm.curOp;t.maybeHiddenMarkers&&-1!=U(t.maybeHiddenMarkers,this)||(t.maybeUnhiddenMarkers||(t.maybeUnhiddenMarkers=[])).push(this)}this.lines.push(e)},ba.prototype.detachLine=function(e){if(this.lines.splice(U(this.lines,e),1),!this.lines.length&&this.doc.cm){var t=this.doc.cm.curOp;(t.maybeHiddenMarkers||(t.maybeHiddenMarkers=[])).push(this)}},Me(ba);var ka=function(e,t){this.markers=e,this.primary=t;for(var r=0;r<e.length;++r)e[r].parent=this};function wa(e,t,r,n,i){(n=H(n)).shared=!1;var o=[xa(e,t,r,n,i)],a=o[0],s=n.widgetNode;return wo(e,(function(e){s&&(n.widgetNode=s.cloneNode(!0)),o.push(xa(e,vt(e,t),vt(e,r),n,i));for(var l=0;l<e.linked.length;++l)if(e.linked[l].isParent)return;a=ee(o)})),new ka(o,a)}function Sa(e){return e.findMarks(ut(e.first,0),e.clipPos(ut(e.lastLine())),(function(e){return e.parent}))}function Ca(e,t){for(var r=0;r<t.length;r++){var n=t[r],i=n.find(),o=e.clipPos(i.from),a=e.clipPos(i.to);if(dt(o,a)){var s=xa(e,o,a,n.primary,n.primary.type);n.markers.push(s),s.parent=n}}}function Ma(e){for(var t=function(t){var r=e[t],n=[r.primary.doc];wo(r.primary.doc,(function(e){return n.push(e)}));for(var i=0;i<r.markers.length;i++){var o=r.markers[i];-1==U(n,o.doc)&&(o.parent=null,r.markers.splice(i--,1))}},r=0;r<e.length;r++)t(r)}ka.prototype.clear=function(){if(!this.explicitlyCleared){this.explicitlyCleared=!0;for(var e=0;e<this.markers.length;++e)this.markers[e].clear();Nr(this,"clear")}},ka.prototype.find=function(e,t){return this.primary.find(e,t)},Me(ka);var Ta=0,La=function(e,t,r,n,i){if(!(this instanceof La))return new La(e,t,r,n,i);null==r&&(r=0),ha.call(this,[new ma([new fr("",null)])]),this.first=r,this.scrollTop=this.scrollLeft=0,this.cantEdit=!1,this.cleanGeneration=1,this.modeFrontier=this.highlightFrontier=r;var o=ut(r,0);this.sel=fo(o),this.history=new To(null),this.id=++Ta,this.modeOption=t,this.lineSep=n,this.direction="rtl"==i?"rtl":"ltr",this.extend=!1,"string"==typeof e&&(e=this.splitLines(e)),ko(this,{from:o,to:o,text:e}),Ko(this,fo(o),G)};La.prototype=ie(ha.prototype,{constructor:La,iter:function(e,t,r){r?this.iterN(e-this.first,t-e,r):this.iterN(this.first,this.first+this.size,e)},insert:function(e,t){for(var r=0,n=0;n<t.length;++n)r+=t[n].height;this.insertInner(e-this.first,t,r)},remove:function(e,t){this.removeInner(e-this.first,t)},getValue:function(e){var t=it(this,this.first,this.first+this.size);return!1===e?t:t.join(e||this.lineSeparator())},setValue:ji((function(e){var t=ut(this.first,0),r=this.first+this.size-1;na(this,{from:t,to:ut(r,rt(this,r).text.length),text:this.splitLines(e),origin:"setValue",full:!0},!0),this.cm&&fi(this.cm,0,0),Ko(this,fo(t),G)})),replaceRange:function(e,t,r,n){ca(this,e,t=vt(this,t),r=r?vt(this,r):t,n)},getRange:function(e,t,r){var n=nt(this,vt(this,e),vt(this,t));return!1===r?n:""===r?n.join(""):n.join(r||this.lineSeparator())},getLine:function(e){var t=this.getLineHandle(e);return t&&t.text},getLineHandle:function(e){if(lt(this,e))return rt(this,e)},getLineNumber:function(e){return at(e)},getLineHandleVisualStart:function(e){return"number"==typeof e&&(e=rt(this,e)),nr(e)},lineCount:function(){return this.size},firstLine:function(){return this.first},lastLine:function(){return this.first+this.size-1},clipPos:function(e){return vt(this,e)},getCursor:function(e){var t=this.sel.primary();return null==e||"head"==e?t.head:"anchor"==e?t.anchor:"end"==e||"to"==e||!1===e?t.to():t.from()},listSelections:function(){return this.sel.ranges},somethingSelected:function(){return this.sel.somethingSelected()},setCursor:ji((function(e,t,r){$o(this,vt(this,"number"==typeof e?ut(e,t||0):e),null,r)})),setSelection:ji((function(e,t,r){$o(this,vt(this,e),vt(this,t||e),r)})),extendSelection:ji((function(e,t,r){Bo(this,vt(this,e),t&&vt(this,t),r)})),extendSelections:ji((function(e,t){Ho(this,_t(this,e),t)})),extendSelectionsBy:ji((function(e,t){Ho(this,_t(this,te(this.sel.ranges,e)),t)})),setSelections:ji((function(e,t,r){if(e.length){for(var n=[],i=0;i<e.length;i++)n[i]=new uo(vt(this,e[i].anchor),vt(this,e[i].head||e[i].anchor));null==t&&(t=Math.min(e.length-1,this.sel.primIndex)),Ko(this,po(this.cm,n,t),r)}})),addSelection:ji((function(e,t,r){var n=this.sel.ranges.slice(0);n.push(new uo(vt(this,e),vt(this,t||e))),Ko(this,po(this.cm,n,n.length-1),r)})),getSelection:function(e){for(var t,r=this.sel.ranges,n=0;n<r.length;n++){var i=nt(this,r[n].from(),r[n].to());t=t?t.concat(i):i}return!1===e?t:t.join(e||this.lineSeparator())},getSelections:function(e){for(var t=[],r=this.sel.ranges,n=0;n<r.length;n++){var i=nt(this,r[n].from(),r[n].to());!1!==e&&(i=i.join(e||this.lineSeparator())),t[n]=i}return t},replaceSelection:function(e,t,r){for(var n=[],i=0;i<this.sel.ranges.length;i++)n[i]=e;this.replaceSelections(n,t,r||"+input")},replaceSelections:ji((function(e,t,r){for(var n=[],i=this.sel,o=0;o<i.ranges.length;o++){var a=i.ranges[o];n[o]={from:a.from(),to:a.to(),text:this.splitLines(e[o]),origin:r}}for(var s=t&&"end"!=t&&yo(this,n,t),l=n.length-1;l>=0;l--)na(this,n[l]);s?Vo(this,s):this.cm&&pi(this.cm)})),undo:ji((function(){oa(this,"undo")})),redo:ji((function(){oa(this,"redo")})),undoSelection:ji((function(){oa(this,"undo",!0)})),redoSelection:ji((function(){oa(this,"redo",!0)})),setExtending:function(e){this.extend=e},getExtending:function(){return this.extend},historySize:function(){for(var e=this.history,t=0,r=0,n=0;n<e.done.length;n++)e.done[n].ranges||++t;for(var i=0;i<e.undone.length;i++)e.undone[i].ranges||++r;return{undo:t,redo:r}},clearHistory:function(){var e=this;this.history=new To(this.history),wo(this,(function(t){return t.history=e.history}),!0)},markClean:function(){this.cleanGeneration=this.changeGeneration(!0)},changeGeneration:function(e){return e&&(this.history.lastOp=this.history.lastSelOp=this.history.lastOrigin=null),this.history.generation},isClean:function(e){return this.history.generation==(e||this.cleanGeneration)},getHistory:function(){return{done:jo(this.history.done),undone:jo(this.history.undone)}},setHistory:function(e){var t=this.history=new To(this.history);t.done=jo(e.done.slice(0),null,!0),t.undone=jo(e.undone.slice(0),null,!0)},setGutterMarker:ji((function(e,t,r){return fa(this,e,"gutter",(function(e){var n=e.gutterMarkers||(e.gutterMarkers={});return n[t]=r,!r&&le(n)&&(e.gutterMarkers=null),!0}))})),clearGutter:ji((function(e){var t=this;this.iter((function(r){r.gutterMarkers&&r.gutterMarkers[e]&&fa(t,r,"gutter",(function(){return r.gutterMarkers[e]=null,le(r.gutterMarkers)&&(r.gutterMarkers=null),!0}))}))})),lineInfo:function(e){var t;if("number"==typeof e){if(!lt(this,e))return null;if(t=e,!(e=rt(this,e)))return null}else if(null==(t=at(e)))return null;return{line:t,handle:e,text:e.text,gutterMarkers:e.gutterMarkers,textClass:e.textClass,bgClass:e.bgClass,wrapClass:e.wrapClass,widgets:e.widgets}},addLineClass:ji((function(e,t,r){return fa(this,e,"gutter"==t?"gutter":"class",(function(e){var n="text"==t?"textClass":"background"==t?"bgClass":"gutter"==t?"gutterClass":"wrapClass";if(e[n]){if(C(r).test(e[n]))return!1;e[n]+=" "+r}else e[n]=r;return!0}))})),removeLineClass:ji((function(e,t,r){return fa(this,e,"gutter"==t?"gutter":"class",(function(e){var n="text"==t?"textClass":"background"==t?"bgClass":"gutter"==t?"gutterClass":"wrapClass",i=e[n];if(!i)return!1;if(null==r)e[n]=null;else{var o=i.match(C(r));if(!o)return!1;var a=o.index+o[0].length;e[n]=i.slice(0,o.index)+(o.index&&a!=i.length?" ":"")+i.slice(a)||null}return!0}))})),addLineWidget:ji((function(e,t,r){return ya(this,e,t,r)})),removeLineWidget:function(e){e.clear()},markText:function(e,t,r){return xa(this,vt(this,e),vt(this,t),r,r&&r.type||"range")},setBookmark:function(e,t){var r={replacedWith:t&&(null==t.nodeType?t.widget:t),insertLeft:t&&t.insertLeft,clearWhenEmpty:!1,shared:t&&t.shared,handleMouseEvents:t&&t.handleMouseEvents};return xa(this,e=vt(this,e),e,r,"bookmark")},findMarksAt:function(e){var t=[],r=rt(this,(e=vt(this,e)).line).markedSpans;if(r)for(var n=0;n<r.length;++n){var i=r[n];(null==i.from||i.from<=e.ch)&&(null==i.to||i.to>=e.ch)&&t.push(i.marker.parent||i.marker)}return t},findMarks:function(e,t,r){e=vt(this,e),t=vt(this,t);var n=[],i=e.line;return this.iter(e.line,t.line+1,(function(o){var a=o.markedSpans;if(a)for(var s=0;s<a.length;s++){var l=a[s];null!=l.to&&i==e.line&&e.ch>=l.to||null==l.from&&i!=e.line||null!=l.from&&i==t.line&&l.from>=t.ch||r&&!r(l.marker)||n.push(l.marker.parent||l.marker)}++i})),n},getAllMarks:function(){var e=[];return this.iter((function(t){var r=t.markedSpans;if(r)for(var n=0;n<r.length;++n)null!=r[n].from&&e.push(r[n].marker)})),e},posFromIndex:function(e){var t,r=this.first,n=this.lineSeparator().length;return this.iter((function(i){var o=i.text.length+n;if(o>e)return t=e,!0;e-=o,++r})),vt(this,ut(r,t))},indexFromPos:function(e){var t=(e=vt(this,e)).ch;if(e.line<this.first||e.ch<0)return 0;var r=this.lineSeparator().length;return this.iter(this.first,e.line,(function(e){t+=e.text.length+r})),t},copy:function(e){var t=new La(it(this,this.first,this.first+this.size),this.modeOption,this.first,this.lineSep,this.direction);return t.scrollTop=this.scrollTop,t.scrollLeft=this.scrollLeft,t.sel=this.sel,t.extend=!1,e&&(t.history.undoDepth=this.history.undoDepth,t.setHistory(this.getHistory())),t},linkedDoc:function(e){e||(e={});var t=this.first,r=this.first+this.size;null!=e.from&&e.from>t&&(t=e.from),null!=e.to&&e.to<r&&(r=e.to);var n=new La(it(this,t,r),e.mode||this.modeOption,t,this.lineSep,this.direction);return e.sharedHist&&(n.history=this.history),(this.linked||(this.linked=[])).push({doc:n,sharedHist:e.sharedHist}),n.linked=[{doc:this,isParent:!0,sharedHist:e.sharedHist}],Ca(n,Sa(this)),n},unlinkDoc:function(e){if(e instanceof Hs&&(e=e.doc),this.linked)for(var t=0;t<this.linked.length;++t)if(this.linked[t].doc==e){this.linked.splice(t,1),e.unlinkDoc(this),Ma(Sa(this));break}if(e.history==this.history){var r=[e.id];wo(e,(function(e){return r.push(e.id)}),!0),e.history=new To(null),e.history.done=jo(this.history.done,r),e.history.undone=jo(this.history.undone,r)}},iterLinkedDocs:function(e){wo(this,e)},getMode:function(){return this.mode},getEditor:function(){return this.cm},splitLines:function(e){return this.lineSep?e.split(this.lineSep):je(e)},lineSeparator:function(){return this.lineSep||"\n"},setDirection:ji((function(e){"rtl"!=e&&(e="ltr"),e!=this.direction&&(this.direction=e,this.iter((function(e){return e.order=null})),this.cm&&Mo(this.cm))}))}),La.prototype.eachLine=La.prototype.iter;var za=0;function Da(e){var t=this;if(Na(t),!we(t,e)&&!Gr(t.display,e)){Te(e),a&&(za=+new Date);var r=Wn(t,e,!0),n=e.dataTransfer.files;if(r&&!t.isReadOnly())if(n&&n.length&&window.FileReader&&window.File)for(var i=n.length,o=Array(i),s=0,l=function(){++s==i&&Ii(t,(function(){var e={from:r=vt(t.doc,r),to:r,text:t.doc.splitLines(o.filter((function(e){return null!=e})).join(t.doc.lineSeparator())),origin:"paste"};na(t.doc,e),Vo(t.doc,fo(vt(t.doc,r),vt(t.doc,mo(e))))}))()},c=function(e,r){if(t.options.allowDropFileTypes&&-1==U(t.options.allowDropFileTypes,e.type))l();else{var n=new FileReader;n.onerror=function(){return l()},n.onload=function(){var e=n.result;/[\x00-\x08\x0e-\x1f]{2}/.test(e)||(o[r]=e),l()},n.readAsText(e)}},u=0;u<n.length;u++)c(n[u],u);else{if(t.state.draggingText&&t.doc.sel.contains(r)>-1)return t.state.draggingText(e),void setTimeout((function(){return t.display.input.focus()}),20);try{var d=e.dataTransfer.getData("Text");if(d){var p;if(t.state.draggingText&&!t.state.draggingText.copy&&(p=t.listSelections()),Go(t.doc,fo(r,r)),p)for(var f=0;f<p.length;++f)ca(t.doc,"",p[f].anchor,p[f].head,"drag");t.replaceSelection(d,"around","paste"),t.display.input.focus()}}catch(e){}}}}function Aa(e,t){if(a&&(!e.state.draggingText||+new Date-za<100))De(t);else if(!we(e,t)&&!Gr(e.display,t)&&(t.dataTransfer.setData("Text",e.getSelection()),t.dataTransfer.effectAllowed="copyMove",t.dataTransfer.setDragImage&&!f)){var r=D("img",null,null,"position: fixed; left: 0; top: 0;");r.src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==",p&&(r.width=r.height=1,e.display.wrapper.appendChild(r),r._top=r.offsetTop),t.dataTransfer.setDragImage(r,0,0),p&&r.parentNode.removeChild(r)}}function qa(e,t){var r=Wn(e,t);if(r){var n=document.createDocumentFragment();Qn(e,r,n),e.display.dragCursor||(e.display.dragCursor=D("div",null,"CodeMirror-cursors CodeMirror-dragcursors"),e.display.lineSpace.insertBefore(e.display.dragCursor,e.display.cursorDiv)),z(e.display.dragCursor,n)}}function Na(e){e.display.dragCursor&&(e.display.lineSpace.removeChild(e.display.dragCursor),e.display.dragCursor=null)}function Fa(e){if(document.getElementsByClassName){for(var t=document.getElementsByClassName("CodeMirror"),r=[],n=0;n<t.length;n++){var i=t[n].CodeMirror;i&&r.push(i)}r.length&&r[0].operation((function(){for(var t=0;t<r.length;t++)e(r[t])}))}}var Ea=!1;function Oa(){Ea||(Ia(),Ea=!0)}function Ia(){var e;_e(window,"resize",(function(){null==e&&(e=setTimeout((function(){e=null,Fa(Pa)}),100))})),_e(window,"blur",(function(){return Fa(ni)}))}function Pa(e){var t=e.display;t.cachedCharWidth=t.cachedTextHeight=t.cachedPaddingH=null,t.scrollbarsClipped=!1,e.setSize()}for(var ja={3:"Pause",8:"Backspace",9:"Tab",13:"Enter",16:"Shift",17:"Ctrl",18:"Alt",19:"Pause",20:"CapsLock",27:"Esc",32:"Space",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"Left",38:"Up",39:"Right",40:"Down",44:"PrintScrn",45:"Insert",46:"Delete",59:";",61:"=",91:"Mod",92:"Mod",93:"Mod",106:"*",107:"=",109:"-",110:".",111:"/",145:"ScrollLock",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'",224:"Mod",63232:"Up",63233:"Down",63234:"Left",63235:"Right",63272:"Delete",63273:"Home",63275:"End",63276:"PageUp",63277:"PageDown",63302:"Insert"},Wa=0;Wa<10;Wa++)ja[Wa+48]=ja[Wa+96]=String(Wa);for(var Ba=65;Ba<=90;Ba++)ja[Ba]=String.fromCharCode(Ba);for(var Ha=1;Ha<=12;Ha++)ja[Ha+111]=ja[Ha+63235]="F"+Ha;var Ra={};function $a(e){var t,r,n,i,o=e.split(/-(?!$)/);e=o[o.length-1];for(var a=0;a<o.length-1;a++){var s=o[a];if(/^(cmd|meta|m)$/i.test(s))i=!0;else if(/^a(lt)?$/i.test(s))t=!0;else if(/^(c|ctrl|control)$/i.test(s))r=!0;else{if(!/^s(hift)?$/i.test(s))throw new Error("Unrecognized modifier name: "+s);n=!0}}return t&&(e="Alt-"+e),r&&(e="Ctrl-"+e),i&&(e="Cmd-"+e),n&&(e="Shift-"+e),e}function Ua(e){var t={};for(var r in e)if(e.hasOwnProperty(r)){var n=e[r];if(/^(name|fallthrough|(de|at)tach)$/.test(r))continue;if("..."==n){delete e[r];continue}for(var i=te(r.split(" "),$a),o=0;o<i.length;o++){var a=void 0,s=void 0;o==i.length-1?(s=i.join(" "),a=n):(s=i.slice(0,o+1).join(" "),a="...");var l=t[s];if(l){if(l!=a)throw new Error("Inconsistent bindings for "+s)}else t[s]=a}delete e[r]}for(var c in t)e[c]=t[c];return e}function Va(e,t,r,n){var i=(t=Qa(t)).call?t.call(e,n):t[e];if(!1===i)return"nothing";if("..."===i)return"multi";if(null!=i&&r(i))return"handled";if(t.fallthrough){if("[object Array]"!=Object.prototype.toString.call(t.fallthrough))return Va(e,t.fallthrough,r,n);for(var o=0;o<t.fallthrough.length;o++){var a=Va(e,t.fallthrough[o],r,n);if(a)return a}}}function Ka(e){var t="string"==typeof e?e:ja[e.keyCode];return"Ctrl"==t||"Alt"==t||"Shift"==t||"Mod"==t}function Ga(e,t,r){var n=e;return t.altKey&&"Alt"!=n&&(e="Alt-"+e),(w?t.metaKey:t.ctrlKey)&&"Ctrl"!=n&&(e="Ctrl-"+e),(w?t.ctrlKey:t.metaKey)&&"Mod"!=n&&(e="Cmd-"+e),!r&&t.shiftKey&&"Shift"!=n&&(e="Shift-"+e),e}function Xa(e,t){if(p&&34==e.keyCode&&e.char)return!1;var r=ja[e.keyCode];return null!=r&&!e.altGraphKey&&(3==e.keyCode&&e.code&&(r=e.code),Ga(r,e,t))}function Qa(e){return"string"==typeof e?Ra[e]:e}function Ya(e,t){for(var r=e.doc.sel.ranges,n=[],i=0;i<r.length;i++){for(var o=t(r[i]);n.length&&dt(o.from,ee(n).to)<=0;){var a=n.pop();if(dt(a.from,o.from)<0){o.from=a.from;break}}n.push(o)}Oi(e,(function(){for(var t=n.length-1;t>=0;t--)ca(e.doc,"",n[t].from,n[t].to,"+delete");pi(e)}))}function Za(e,t,r){var n=de(e.text,t+r,r);return n<0||n>e.text.length?null:n}function Ja(e,t,r){var n=Za(e,t.ch,r);return null==n?null:new ut(t.line,n,r<0?"after":"before")}function es(e,t,r,n,i){if(e){"rtl"==t.doc.direction&&(i=-i);var o=ve(r,t.doc.direction);if(o){var a,s=i<0?ee(o):o[0],l=i<0==(1==s.level)?"after":"before";if(s.level>0||"rtl"==t.doc.direction){var c=sn(t,r);a=i<0?r.text.length-1:0;var u=ln(t,c,a).top;a=pe((function(e){return ln(t,c,e).top==u}),i<0==(1==s.level)?s.from:s.to-1,a),"before"==l&&(a=Za(r,a,1))}else a=i<0?s.to:s.from;return new ut(n,a,l)}}return new ut(n,i<0?r.text.length:0,i<0?"before":"after")}function ts(e,t,r,n){var i=ve(t,e.doc.direction);if(!i)return Ja(t,r,n);r.ch>=t.text.length?(r.ch=t.text.length,r.sticky="before"):r.ch<=0&&(r.ch=0,r.sticky="after");var o=he(i,r.ch,r.sticky),a=i[o];if("ltr"==e.doc.direction&&a.level%2==0&&(n>0?a.to>r.ch:a.from<r.ch))return Ja(t,r,n);var s,l=function(e,r){return Za(t,e instanceof ut?e.ch:e,r)},c=function(r){return e.options.lineWrapping?(s=s||sn(e,t),zn(e,t,s,r)):{begin:0,end:t.text.length}},u=c("before"==r.sticky?l(r,-1):r.ch);if("rtl"==e.doc.direction||1==a.level){var d=1==a.level==n<0,p=l(r,d?1:-1);if(null!=p&&(d?p<=a.to&&p<=u.end:p>=a.from&&p>=u.begin)){var f=d?"before":"after";return new ut(r.line,p,f)}}var m=function(e,t,n){for(var o=function(e,t){return t?new ut(r.line,l(e,1),"before"):new ut(r.line,e,"after")};e>=0&&e<i.length;e+=t){var a=i[e],s=t>0==(1!=a.level),c=s?n.begin:l(n.end,-1);if(a.from<=c&&c<a.to)return o(c,s);if(c=s?a.from:l(a.to,-1),n.begin<=c&&c<n.end)return o(c,s)}},h=m(o+n,n,u);if(h)return h;var g=n>0?u.end:l(u.begin,-1);return null==g||n>0&&g==t.text.length||!(h=m(n>0?0:i.length-1,n,c(g)))?null:h}Ra.basic={Left:"goCharLeft",Right:"goCharRight",Up:"goLineUp",Down:"goLineDown",End:"goLineEnd",Home:"goLineStartSmart",PageUp:"goPageUp",PageDown:"goPageDown",Delete:"delCharAfter",Backspace:"delCharBefore","Shift-Backspace":"delCharBefore",Tab:"defaultTab","Shift-Tab":"indentAuto",Enter:"newlineAndIndent",Insert:"toggleOverwrite",Esc:"singleSelection"},Ra.pcDefault={"Ctrl-A":"selectAll","Ctrl-D":"deleteLine","Ctrl-Z":"undo","Shift-Ctrl-Z":"redo","Ctrl-Y":"redo","Ctrl-Home":"goDocStart","Ctrl-End":"goDocEnd","Ctrl-Up":"goLineUp","Ctrl-Down":"goLineDown","Ctrl-Left":"goGroupLeft","Ctrl-Right":"goGroupRight","Alt-Left":"goLineStart","Alt-Right":"goLineEnd","Ctrl-Backspace":"delGroupBefore","Ctrl-Delete":"delGroupAfter","Ctrl-S":"save","Ctrl-F":"find","Ctrl-G":"findNext","Shift-Ctrl-G":"findPrev","Shift-Ctrl-F":"replace","Shift-Ctrl-R":"replaceAll","Ctrl-[":"indentLess","Ctrl-]":"indentMore","Ctrl-U":"undoSelection","Shift-Ctrl-U":"redoSelection","Alt-U":"redoSelection",fallthrough:"basic"},Ra.emacsy={"Ctrl-F":"goCharRight","Ctrl-B":"goCharLeft","Ctrl-P":"goLineUp","Ctrl-N":"goLineDown","Ctrl-A":"goLineStart","Ctrl-E":"goLineEnd","Ctrl-V":"goPageDown","Shift-Ctrl-V":"goPageUp","Ctrl-D":"delCharAfter","Ctrl-H":"delCharBefore","Alt-Backspace":"delWordBefore","Ctrl-K":"killLine","Ctrl-T":"transposeChars","Ctrl-O":"openLine"},Ra.macDefault={"Cmd-A":"selectAll","Cmd-D":"deleteLine","Cmd-Z":"undo","Shift-Cmd-Z":"redo","Cmd-Y":"redo","Cmd-Home":"goDocStart","Cmd-Up":"goDocStart","Cmd-End":"goDocEnd","Cmd-Down":"goDocEnd","Alt-Left":"goGroupLeft","Alt-Right":"goGroupRight","Cmd-Left":"goLineLeft","Cmd-Right":"goLineRight","Alt-Backspace":"delGroupBefore","Ctrl-Alt-Backspace":"delGroupAfter","Alt-Delete":"delGroupAfter","Cmd-S":"save","Cmd-F":"find","Cmd-G":"findNext","Shift-Cmd-G":"findPrev","Cmd-Alt-F":"replace","Shift-Cmd-Alt-F":"replaceAll","Cmd-[":"indentLess","Cmd-]":"indentMore","Cmd-Backspace":"delWrappedLineLeft","Cmd-Delete":"delWrappedLineRight","Cmd-U":"undoSelection","Shift-Cmd-U":"redoSelection","Ctrl-Up":"goDocStart","Ctrl-Down":"goDocEnd",fallthrough:["basic","emacsy"]},Ra.default=_?Ra.macDefault:Ra.pcDefault;var rs={selectAll:ta,singleSelection:function(e){return e.setSelection(e.getCursor("anchor"),e.getCursor("head"),G)},killLine:function(e){return Ya(e,(function(t){if(t.empty()){var r=rt(e.doc,t.head.line).text.length;return t.head.ch==r&&t.head.line<e.lastLine()?{from:t.head,to:ut(t.head.line+1,0)}:{from:t.head,to:ut(t.head.line,r)}}return{from:t.from(),to:t.to()}}))},deleteLine:function(e){return Ya(e,(function(t){return{from:ut(t.from().line,0),to:vt(e.doc,ut(t.to().line+1,0))}}))},delLineLeft:function(e){return Ya(e,(function(e){return{from:ut(e.from().line,0),to:e.from()}}))},delWrappedLineLeft:function(e){return Ya(e,(function(t){var r=e.charCoords(t.head,"div").top+5;return{from:e.coordsChar({left:0,top:r},"div"),to:t.from()}}))},delWrappedLineRight:function(e){return Ya(e,(function(t){var r=e.charCoords(t.head,"div").top+5,n=e.coordsChar({left:e.display.lineDiv.offsetWidth+100,top:r},"div");return{from:t.from(),to:n}}))},undo:function(e){return e.undo()},redo:function(e){return e.redo()},undoSelection:function(e){return e.undoSelection()},redoSelection:function(e){return e.redoSelection()},goDocStart:function(e){return e.extendSelection(ut(e.firstLine(),0))},goDocEnd:function(e){return e.extendSelection(ut(e.lastLine()))},goLineStart:function(e){return e.extendSelectionsBy((function(t){return ns(e,t.head.line)}),{origin:"+move",bias:1})},goLineStartSmart:function(e){return e.extendSelectionsBy((function(t){return os(e,t.head)}),{origin:"+move",bias:1})},goLineEnd:function(e){return e.extendSelectionsBy((function(t){return is(e,t.head.line)}),{origin:"+move",bias:-1})},goLineRight:function(e){return e.extendSelectionsBy((function(t){var r=e.cursorCoords(t.head,"div").top+5;return e.coordsChar({left:e.display.lineDiv.offsetWidth+100,top:r},"div")}),Q)},goLineLeft:function(e){return e.extendSelectionsBy((function(t){var r=e.cursorCoords(t.head,"div").top+5;return e.coordsChar({left:0,top:r},"div")}),Q)},goLineLeftSmart:function(e){return e.extendSelectionsBy((function(t){var r=e.cursorCoords(t.head,"div").top+5,n=e.coordsChar({left:0,top:r},"div");return n.ch<e.getLine(n.line).search(/\S/)?os(e,t.head):n}),Q)},goLineUp:function(e){return e.moveV(-1,"line")},goLineDown:function(e){return e.moveV(1,"line")},goPageUp:function(e){return e.moveV(-1,"page")},goPageDown:function(e){return e.moveV(1,"page")},goCharLeft:function(e){return e.moveH(-1,"char")},goCharRight:function(e){return e.moveH(1,"char")},goColumnLeft:function(e){return e.moveH(-1,"column")},goColumnRight:function(e){return e.moveH(1,"column")},goWordLeft:function(e){return e.moveH(-1,"word")},goGroupRight:function(e){return e.moveH(1,"group")},goGroupLeft:function(e){return e.moveH(-1,"group")},goWordRight:function(e){return e.moveH(1,"word")},delCharBefore:function(e){return e.deleteH(-1,"codepoint")},delCharAfter:function(e){return e.deleteH(1,"char")},delWordBefore:function(e){return e.deleteH(-1,"word")},delWordAfter:function(e){return e.deleteH(1,"word")},delGroupBefore:function(e){return e.deleteH(-1,"group")},delGroupAfter:function(e){return e.deleteH(1,"group")},indentAuto:function(e){return e.indentSelection("smart")},indentMore:function(e){return e.indentSelection("add")},indentLess:function(e){return e.indentSelection("subtract")},insertTab:function(e){return e.replaceSelection("\t")},insertSoftTab:function(e){for(var t=[],r=e.listSelections(),n=e.options.tabSize,i=0;i<r.length;i++){var o=r[i].from(),a=R(e.getLine(o.line),o.ch,n);t.push(J(n-a%n))}e.replaceSelections(t)},defaultTab:function(e){e.somethingSelected()?e.indentSelection("add"):e.execCommand("insertTab")},transposeChars:function(e){return Oi(e,(function(){for(var t=e.listSelections(),r=[],n=0;n<t.length;n++)if(t[n].empty()){var i=t[n].head,o=rt(e.doc,i.line).text;if(o)if(i.ch==o.length&&(i=new ut(i.line,i.ch-1)),i.ch>0)i=new ut(i.line,i.ch+1),e.replaceRange(o.charAt(i.ch-1)+o.charAt(i.ch-2),ut(i.line,i.ch-2),i,"+transpose");else if(i.line>e.doc.first){var a=rt(e.doc,i.line-1).text;a&&(i=new ut(i.line,1),e.replaceRange(o.charAt(0)+e.doc.lineSeparator()+a.charAt(a.length-1),ut(i.line-1,a.length-1),i,"+transpose"))}r.push(new uo(i,i))}e.setSelections(r)}))},newlineAndIndent:function(e){return Oi(e,(function(){for(var t=e.listSelections(),r=t.length-1;r>=0;r--)e.replaceRange(e.doc.lineSeparator(),t[r].anchor,t[r].head,"+input");t=e.listSelections();for(var n=0;n<t.length;n++)e.indentLine(t[n].from().line,null,!0);pi(e)}))},openLine:function(e){return e.replaceSelection("\n","start")},toggleOverwrite:function(e){return e.toggleOverwrite()}};function ns(e,t){var r=rt(e.doc,t),n=nr(r);return n!=r&&(t=at(n)),es(!0,e,n,t,1)}function is(e,t){var r=rt(e.doc,t),n=ir(r);return n!=r&&(t=at(n)),es(!0,e,r,t,-1)}function os(e,t){var r=ns(e,t.line),n=rt(e.doc,r.line),i=ve(n,e.doc.direction);if(!i||0==i[0].level){var o=Math.max(r.ch,n.text.search(/\S/)),a=t.line==r.line&&t.ch<=o&&t.ch;return ut(r.line,a?0:o,r.sticky)}return r}function as(e,t,r){if("string"==typeof t&&!(t=rs[t]))return!1;e.display.input.ensurePolled();var n=e.display.shift,i=!1;try{e.isReadOnly()&&(e.state.suppressEdits=!0),r&&(e.display.shift=!1),i=t(e)!=K}finally{e.display.shift=n,e.state.suppressEdits=!1}return i}function ss(e,t,r){for(var n=0;n<e.state.keyMaps.length;n++){var i=Va(t,e.state.keyMaps[n],r,e);if(i)return i}return e.options.extraKeys&&Va(t,e.options.extraKeys,r,e)||Va(t,e.options.keyMap,r,e)}var ls=new $;function cs(e,t,r,n){var i=e.state.keySeq;if(i){if(Ka(t))return"handled";if(/\'$/.test(t)?e.state.keySeq=null:ls.set(50,(function(){e.state.keySeq==i&&(e.state.keySeq=null,e.display.input.reset())})),us(e,i+" "+t,r,n))return!0}return us(e,t,r,n)}function us(e,t,r,n){var i=ss(e,t,n);return"multi"==i&&(e.state.keySeq=t),"handled"==i&&Nr(e,"keyHandled",e,t,r),"handled"!=i&&"multi"!=i||(Te(r),Jn(e)),!!i}function ds(e,t){var r=Xa(t,!0);return!!r&&(t.shiftKey&&!e.state.keySeq?cs(e,"Shift-"+r,t,(function(t){return as(e,t,!0)}))||cs(e,r,t,(function(t){if("string"==typeof t?/^go[A-Z]/.test(t):t.motion)return as(e,t)})):cs(e,r,t,(function(t){return as(e,t)})))}function ps(e,t,r){return cs(e,"'"+r+"'",t,(function(t){return as(e,t,!0)}))}var fs=null;function ms(e){var t=this;if(!(e.target&&e.target!=t.display.input.getField()||(t.curOp.focus=N(P(t)),we(t,e)))){a&&s<11&&27==e.keyCode&&(e.returnValue=!1);var n=e.keyCode;t.display.shift=16==n||e.shiftKey;var i=ds(t,e);p&&(fs=i?n:null,i||88!=n||Be||!(_?e.metaKey:e.ctrlKey)||t.replaceSelection("",null,"cut")),r&&!_&&!i&&46==n&&e.shiftKey&&!e.ctrlKey&&document.execCommand&&document.execCommand("cut"),18!=n||/\bCodeMirror-crosshair\b/.test(t.display.lineDiv.className)||hs(t)}}function hs(e){var t=e.display.lineDiv;function r(e){18!=e.keyCode&&e.altKey||(T(t,"CodeMirror-crosshair"),xe(document,"keyup",r),xe(document,"mouseover",r))}F(t,"CodeMirror-crosshair"),_e(document,"keyup",r),_e(document,"mouseover",r)}function gs(e){16==e.keyCode&&(this.doc.sel.shift=!1),we(this,e)}function vs(e){var t=this;if(!(e.target&&e.target!=t.display.input.getField()||Gr(t.display,e)||we(t,e)||e.ctrlKey&&!e.altKey||_&&e.metaKey)){var r=e.keyCode,n=e.charCode;if(p&&r==fs)return fs=null,void Te(e);if(!p||e.which&&!(e.which<10)||!ds(t,e)){var i=String.fromCharCode(null==n?r:n);"\b"!=i&&(ps(t,e,i)||t.display.input.onKeyPress(e))}}}var ys,_s,bs=400,xs=function(e,t,r){this.time=e,this.pos=t,this.button=r};function ks(e,t){var r=+new Date;return _s&&_s.compare(r,e,t)?(ys=_s=null,"triple"):ys&&ys.compare(r,e,t)?(_s=new xs(r,e,t),ys=null,"double"):(ys=new xs(r,e,t),_s=null,"single")}function ws(e){var t=this,r=t.display;if(!(we(t,e)||r.activeTouch&&r.input.supportsTouch()))if(r.input.ensurePolled(),r.shift=e.shiftKey,Gr(r,e))l||(r.scroller.draggable=!1,setTimeout((function(){return r.scroller.draggable=!0}),100));else if(!qs(t,e)){var n=Wn(t,e),i=qe(e),o=n?ks(n,i):"single";W(t).focus(),1==i&&t.state.selectingText&&t.state.selectingText(e),n&&Ss(t,i,n,o,e)||(1==i?n?Ms(t,n,o,e):Ae(e)==r.scroller&&Te(e):2==i?(n&&Bo(t.doc,n),setTimeout((function(){return r.input.focus()}),20)):3==i&&(S?t.display.input.onContextMenu(e):ti(t)))}}function Ss(e,t,r,n,i){var o="Click";return"double"==n?o="Double"+o:"triple"==n&&(o="Triple"+o),cs(e,Ga(o=(1==t?"Left":2==t?"Middle":"Right")+o,i),i,(function(t){if("string"==typeof t&&(t=rs[t]),!t)return!1;var n=!1;try{e.isReadOnly()&&(e.state.suppressEdits=!0),n=t(e,r)!=K}finally{e.state.suppressEdits=!1}return n}))}function Cs(e,t,r){var n=e.getOption("configureMouse"),i=n?n(e,t,r):{};if(null==i.unit){var o=b?r.shiftKey&&r.metaKey:r.altKey;i.unit=o?"rectangle":"single"==t?"char":"double"==t?"word":"line"}return(null==i.extend||e.doc.extend)&&(i.extend=e.doc.extend||r.shiftKey),null==i.addNew&&(i.addNew=_?r.metaKey:r.ctrlKey),null==i.moveOnDrag&&(i.moveOnDrag=!(_?r.altKey:r.ctrlKey)),i}function Ms(e,t,r,n){a?setTimeout(B(ei,e),0):e.curOp.focus=N(P(e));var i,o=Cs(e,r,n),s=e.doc.sel;e.options.dragDrop&&Ee&&!e.isReadOnly()&&"single"==r&&(i=s.contains(t))>-1&&(dt((i=s.ranges[i]).from(),t)<0||t.xRel>0)&&(dt(i.to(),t)>0||t.xRel<0)?Ts(e,n,t,o):zs(e,n,t,o)}function Ts(e,t,r,n){var i=e.display,o=!1,c=Ii(e,(function(t){l&&(i.scroller.draggable=!1),e.state.draggingText=!1,e.state.delayingBlurEvent&&(e.hasFocus()?e.state.delayingBlurEvent=!1:ti(e)),xe(i.wrapper.ownerDocument,"mouseup",c),xe(i.wrapper.ownerDocument,"mousemove",u),xe(i.scroller,"dragstart",d),xe(i.scroller,"drop",c),o||(Te(t),n.addNew||Bo(e.doc,r,null,null,n.extend),l&&!f||a&&9==s?setTimeout((function(){i.wrapper.ownerDocument.body.focus({preventScroll:!0}),i.input.focus()}),20):i.input.focus())})),u=function(e){o=o||Math.abs(t.clientX-e.clientX)+Math.abs(t.clientY-e.clientY)>=10},d=function(){return o=!0};l&&(i.scroller.draggable=!0),e.state.draggingText=c,c.copy=!n.moveOnDrag,_e(i.wrapper.ownerDocument,"mouseup",c),_e(i.wrapper.ownerDocument,"mousemove",u),_e(i.scroller,"dragstart",d),_e(i.scroller,"drop",c),e.state.delayingBlurEvent=!0,setTimeout((function(){return i.input.focus()}),20),i.scroller.dragDrop&&i.scroller.dragDrop()}function Ls(e,t,r){if("char"==r)return new uo(t,t);if("word"==r)return e.findWordAt(t);if("line"==r)return new uo(ut(t.line,0),vt(e.doc,ut(t.line+1,0)));var n=r(e,t);return new uo(n.from,n.to)}function zs(e,t,r,n){a&&ti(e);var i=e.display,o=e.doc;Te(t);var s,l,c=o.sel,u=c.ranges;if(n.addNew&&!n.extend?(l=o.sel.contains(r),s=l>-1?u[l]:new uo(r,r)):(s=o.sel.primary(),l=o.sel.primIndex),"rectangle"==n.unit)n.addNew||(s=new uo(r,r)),r=Wn(e,t,!0,!0),l=-1;else{var d=Ls(e,r,n.unit);s=n.extend?Wo(s,d.anchor,d.head,n.extend):d}n.addNew?-1==l?(l=u.length,Ko(o,po(e,u.concat([s]),l),{scroll:!1,origin:"*mouse"})):u.length>1&&u[l].empty()&&"char"==n.unit&&!n.extend?(Ko(o,po(e,u.slice(0,l).concat(u.slice(l+1)),0),{scroll:!1,origin:"*mouse"}),c=o.sel):Ro(o,l,s,X):(l=0,Ko(o,new co([s],0),X),c=o.sel);var p=r;function f(t){if(0!=dt(p,t))if(p=t,"rectangle"==n.unit){for(var i=[],a=e.options.tabSize,u=R(rt(o,r.line).text,r.ch,a),d=R(rt(o,t.line).text,t.ch,a),f=Math.min(u,d),m=Math.max(u,d),h=Math.min(r.line,t.line),g=Math.min(e.lastLine(),Math.max(r.line,t.line));h<=g;h++){var v=rt(o,h).text,y=Y(v,f,a);f==m?i.push(new uo(ut(h,y),ut(h,y))):v.length>y&&i.push(new uo(ut(h,y),ut(h,Y(v,m,a))))}i.length||i.push(new uo(r,r)),Ko(o,po(e,c.ranges.slice(0,l).concat(i),l),{origin:"*mouse",scroll:!1}),e.scrollIntoView(t)}else{var _,b=s,x=Ls(e,t,n.unit),k=b.anchor;dt(x.anchor,k)>0?(_=x.head,k=ht(b.from(),x.anchor)):(_=x.anchor,k=mt(b.to(),x.head));var w=c.ranges.slice(0);w[l]=Ds(e,new uo(vt(o,k),_)),Ko(o,po(e,w,l),X)}}var m=i.wrapper.getBoundingClientRect(),h=0;function g(t){var r=++h,a=Wn(e,t,!0,"rectangle"==n.unit);if(a)if(0!=dt(a,p)){e.curOp.focus=N(P(e)),f(a);var s=ai(i,o);(a.line>=s.to||a.line<s.from)&&setTimeout(Ii(e,(function(){h==r&&g(t)})),150)}else{var l=t.clientY<m.top?-20:t.clientY>m.bottom?20:0;l&&setTimeout(Ii(e,(function(){h==r&&(i.scroller.scrollTop+=l,g(t))})),50)}}function v(t){e.state.selectingText=!1,h=1/0,t&&(Te(t),i.input.focus()),xe(i.wrapper.ownerDocument,"mousemove",y),xe(i.wrapper.ownerDocument,"mouseup",_),o.history.lastSelOrigin=null}var y=Ii(e,(function(e){0!==e.buttons&&qe(e)?g(e):v(e)})),_=Ii(e,v);e.state.selectingText=_,_e(i.wrapper.ownerDocument,"mousemove",y),_e(i.wrapper.ownerDocument,"mouseup",_)}function Ds(e,t){var r=t.anchor,n=t.head,i=rt(e.doc,r.line);if(0==dt(r,n)&&r.sticky==n.sticky)return t;var o=ve(i);if(!o)return t;var a=he(o,r.ch,r.sticky),s=o[a];if(s.from!=r.ch&&s.to!=r.ch)return t;var l,c=a+(s.from==r.ch==(1!=s.level)?0:1);if(0==c||c==o.length)return t;if(n.line!=r.line)l=(n.line-r.line)*("ltr"==e.doc.direction?1:-1)>0;else{var u=he(o,n.ch,n.sticky),d=u-a||(n.ch-r.ch)*(1==s.level?-1:1);l=u==c-1||u==c?d<0:d>0}var p=o[c+(l?-1:0)],f=l==(1==p.level),m=f?p.from:p.to,h=f?"after":"before";return r.ch==m&&r.sticky==h?t:new uo(new ut(r.line,m,h),n)}function As(e,t,r,n){var i,o;if(t.touches)i=t.touches[0].clientX,o=t.touches[0].clientY;else try{i=t.clientX,o=t.clientY}catch(e){return!1}if(i>=Math.floor(e.display.gutters.getBoundingClientRect().right))return!1;n&&Te(t);var a=e.display,s=a.lineDiv.getBoundingClientRect();if(o>s.bottom||!Ce(e,r))return ze(t);o-=s.top-a.viewOffset;for(var l=0;l<e.display.gutterSpecs.length;++l){var c=a.gutters.childNodes[l];if(c&&c.getBoundingClientRect().right>=i)return ke(e,r,e,st(e.doc,o),e.display.gutterSpecs[l].className,t),ze(t)}}function qs(e,t){return As(e,t,"gutterClick",!0)}function Ns(e,t){Gr(e.display,t)||Fs(e,t)||we(e,t,"contextmenu")||S||e.display.input.onContextMenu(t)}function Fs(e,t){return!!Ce(e,"gutterContextMenu")&&As(e,t,"gutterContextMenu",!1)}function Es(e){e.display.wrapper.className=e.display.wrapper.className.replace(/\s*cm-s-\S+/g,"")+e.options.theme.replace(/(^|\s)\s*/g," cm-s-"),vn(e)}xs.prototype.compare=function(e,t,r){return this.time+bs>e&&0==dt(t,this.pos)&&r==this.button};var Os={toString:function(){return"CodeMirror.Init"}},Is={},Ps={};function js(e){var t=e.optionHandlers;function r(r,n,i,o){e.defaults[r]=n,i&&(t[r]=o?function(e,t,r){r!=Os&&i(e,t,r)}:i)}e.defineOption=r,e.Init=Os,r("value","",(function(e,t){return e.setValue(t)}),!0),r("mode",null,(function(e,t){e.doc.modeOption=t,_o(e)}),!0),r("indentUnit",2,_o,!0),r("indentWithTabs",!1),r("smartIndent",!0),r("tabSize",4,(function(e){bo(e),vn(e),Hn(e)}),!0),r("lineSeparator",null,(function(e,t){if(e.doc.lineSep=t,t){var r=[],n=e.doc.first;e.doc.iter((function(e){for(var i=0;;){var o=e.text.indexOf(t,i);if(-1==o)break;i=o+t.length,r.push(ut(n,o))}n++}));for(var i=r.length-1;i>=0;i--)ca(e.doc,t,r[i],ut(r[i].line,r[i].ch+t.length))}})),r("specialChars",/[\u0000-\u001f\u007f-\u009f\u00ad\u061c\u200b\u200e\u200f\u2028\u2029\u202d\u202e\u2066\u2067\u2069\ufeff\ufff9-\ufffc]/g,(function(e,t,r){e.state.specialChars=new RegExp(t.source+(t.test("\t")?"":"|\t"),"g"),r!=Os&&e.refresh()})),r("specialCharPlaceholder",br,(function(e){return e.refresh()}),!0),r("electricChars",!0),r("inputStyle",y?"contenteditable":"textarea",(function(){throw new Error("inputStyle can not (yet) be changed in a running editor")}),!0),r("spellcheck",!1,(function(e,t){return e.getInputField().spellcheck=t}),!0),r("autocorrect",!1,(function(e,t){return e.getInputField().autocorrect=t}),!0),r("autocapitalize",!1,(function(e,t){return e.getInputField().autocapitalize=t}),!0),r("rtlMoveVisually",!x),r("wholeLineUpdateBefore",!0),r("theme","default",(function(e){Es(e),ro(e)}),!0),r("keyMap","default",(function(e,t,r){var n=Qa(t),i=r!=Os&&Qa(r);i&&i.detach&&i.detach(e,n),n.attach&&n.attach(e,i||null)})),r("extraKeys",null),r("configureMouse",null),r("lineWrapping",!1,Bs,!0),r("gutters",[],(function(e,t){e.display.gutterSpecs=eo(t,e.options.lineNumbers),ro(e)}),!0),r("fixedGutter",!0,(function(e,t){e.display.gutters.style.left=t?In(e.display)+"px":"0",e.refresh()}),!0),r("coverGutterNextToScrollbar",!1,(function(e){return wi(e)}),!0),r("scrollbarStyle","native",(function(e){Mi(e),wi(e),e.display.scrollbars.setScrollTop(e.doc.scrollTop),e.display.scrollbars.setScrollLeft(e.doc.scrollLeft)}),!0),r("lineNumbers",!1,(function(e,t){e.display.gutterSpecs=eo(e.options.gutters,t),ro(e)}),!0),r("firstLineNumber",1,ro,!0),r("lineNumberFormatter",(function(e){return e}),ro,!0),r("showCursorWhenSelecting",!1,Gn,!0),r("resetSelectionOnContextMenu",!0),r("lineWiseCopyCut",!0),r("pasteLinesPerSelection",!0),r("selectionsMayTouch",!1),r("readOnly",!1,(function(e,t){"nocursor"==t&&(ni(e),e.display.input.blur()),e.display.input.readOnlyChanged(t)})),r("screenReaderLabel",null,(function(e,t){t=""===t?null:t,e.display.input.screenReaderLabelChanged(t)})),r("disableInput",!1,(function(e,t){t||e.display.input.reset()}),!0),r("dragDrop",!0,Ws),r("allowDropFileTypes",null),r("cursorBlinkRate",530),r("cursorScrollMargin",0),r("cursorHeight",1,Gn,!0),r("singleCursorHeightPerLine",!0,Gn,!0),r("workTime",100),r("workDelay",100),r("flattenSpans",!0,bo,!0),r("addModeClass",!1,bo,!0),r("pollInterval",100),r("undoDepth",200,(function(e,t){return e.doc.history.undoDepth=t})),r("historyEventDelay",1250),r("viewportMargin",10,(function(e){return e.refresh()}),!0),r("maxHighlightLength",1e4,bo,!0),r("moveInputWithCursor",!0,(function(e,t){t||e.display.input.resetPosition()})),r("tabindex",null,(function(e,t){return e.display.input.getField().tabIndex=t||""})),r("autofocus",null),r("direction","ltr",(function(e,t){return e.doc.setDirection(t)}),!0),r("phrases",null)}function Ws(e,t,r){if(!t!=!(r&&r!=Os)){var n=e.display.dragFunctions,i=t?_e:xe;i(e.display.scroller,"dragstart",n.start),i(e.display.scroller,"dragenter",n.enter),i(e.display.scroller,"dragover",n.over),i(e.display.scroller,"dragleave",n.leave),i(e.display.scroller,"drop",n.drop)}}function Bs(e){e.options.lineWrapping?(F(e.display.wrapper,"CodeMirror-wrap"),e.display.sizer.style.minWidth="",e.display.sizerWidth=null):(T(e.display.wrapper,"CodeMirror-wrap"),pr(e)),jn(e),Hn(e),vn(e),setTimeout((function(){return wi(e)}),100)}function Hs(e,t){var r=this;if(!(this instanceof Hs))return new Hs(e,t);this.options=t=t?H(t):{},H(Is,t,!1);var n=t.value;"string"==typeof n?n=new La(n,t.mode,null,t.lineSeparator,t.direction):t.mode&&(n.modeOption=t.mode),this.doc=n;var i=new Hs.inputStyles[t.inputStyle](this),o=this.display=new no(e,n,i,t);for(var c in o.wrapper.CodeMirror=this,Es(this),t.lineWrapping&&(this.display.wrapper.className+=" CodeMirror-wrap"),Mi(this),this.state={keyMaps:[],overlays:[],modeGen:0,overwrite:!1,delayingBlurEvent:!1,focused:!1,suppressEdits:!1,pasteIncoming:-1,cutIncoming:-1,selectingText:!1,draggingText:!1,highlight:new $,keySeq:null,specialChars:null},t.autofocus&&!y&&o.input.focus(),a&&s<11&&setTimeout((function(){return r.display.input.reset(!0)}),20),Rs(this),Oa(),Li(this),this.curOp.forceUpdate=!0,So(this,n),t.autofocus&&!y||this.hasFocus()?setTimeout((function(){r.hasFocus()&&!r.state.focused&&ri(r)}),20):ni(this),Ps)Ps.hasOwnProperty(c)&&Ps[c](this,t[c],Os);Ji(this),t.finishInit&&t.finishInit(this);for(var u=0;u<$s.length;++u)$s[u](this);zi(this),l&&t.lineWrapping&&"optimizelegibility"==getComputedStyle(o.lineDiv).textRendering&&(o.lineDiv.style.textRendering="auto")}function Rs(e){var t=e.display;_e(t.scroller,"mousedown",Ii(e,ws)),_e(t.scroller,"dblclick",a&&s<11?Ii(e,(function(t){if(!we(e,t)){var r=Wn(e,t);if(r&&!qs(e,t)&&!Gr(e.display,t)){Te(t);var n=e.findWordAt(r);Bo(e.doc,n.anchor,n.head)}}})):function(t){return we(e,t)||Te(t)}),_e(t.scroller,"contextmenu",(function(t){return Ns(e,t)})),_e(t.input.getField(),"contextmenu",(function(r){t.scroller.contains(r.target)||Ns(e,r)}));var r,n={end:0};function i(){t.activeTouch&&(r=setTimeout((function(){return t.activeTouch=null}),1e3),(n=t.activeTouch).end=+new Date)}function o(e){if(1!=e.touches.length)return!1;var t=e.touches[0];return t.radiusX<=1&&t.radiusY<=1}function l(e,t){if(null==t.left)return!0;var r=t.left-e.left,n=t.top-e.top;return r*r+n*n>400}_e(t.scroller,"touchstart",(function(i){if(!we(e,i)&&!o(i)&&!qs(e,i)){t.input.ensurePolled(),clearTimeout(r);var a=+new Date;t.activeTouch={start:a,moved:!1,prev:a-n.end<=300?n:null},1==i.touches.length&&(t.activeTouch.left=i.touches[0].pageX,t.activeTouch.top=i.touches[0].pageY)}})),_e(t.scroller,"touchmove",(function(){t.activeTouch&&(t.activeTouch.moved=!0)})),_e(t.scroller,"touchend",(function(r){var n=t.activeTouch;if(n&&!Gr(t,r)&&null!=n.left&&!n.moved&&new Date-n.start<300){var o,a=e.coordsChar(t.activeTouch,"page");o=!n.prev||l(n,n.prev)?new uo(a,a):!n.prev.prev||l(n,n.prev.prev)?e.findWordAt(a):new uo(ut(a.line,0),vt(e.doc,ut(a.line+1,0))),e.setSelection(o.anchor,o.head),e.focus(),Te(r)}i()})),_e(t.scroller,"touchcancel",i),_e(t.scroller,"scroll",(function(){t.scroller.clientHeight&&(vi(e,t.scroller.scrollTop),_i(e,t.scroller.scrollLeft,!0),ke(e,"scroll",e))})),_e(t.scroller,"mousewheel",(function(t){return lo(e,t)})),_e(t.scroller,"DOMMouseScroll",(function(t){return lo(e,t)})),_e(t.wrapper,"scroll",(function(){return t.wrapper.scrollTop=t.wrapper.scrollLeft=0})),t.dragFunctions={enter:function(t){we(e,t)||De(t)},over:function(t){we(e,t)||(qa(e,t),De(t))},start:function(t){return Aa(e,t)},drop:Ii(e,Da),leave:function(t){we(e,t)||Na(e)}};var c=t.input.getField();_e(c,"keyup",(function(t){return gs.call(e,t)})),_e(c,"keydown",Ii(e,ms)),_e(c,"keypress",Ii(e,vs)),_e(c,"focus",(function(t){return ri(e,t)})),_e(c,"blur",(function(t){return ni(e,t)}))}Hs.defaults=Is,Hs.optionHandlers=Ps;var $s=[];function Us(e,t,r,n){var i,o=e.doc;null==r&&(r="add"),"smart"==r&&(o.mode.indent?i=St(e,t).state:r="prev");var a=e.options.tabSize,s=rt(o,t),l=R(s.text,null,a);s.stateAfter&&(s.stateAfter=null);var c,u=s.text.match(/^\s*/)[0];if(n||/\S/.test(s.text)){if("smart"==r&&((c=o.mode.indent(i,s.text.slice(u.length),s.text))==K||c>150)){if(!n)return;r="prev"}}else c=0,r="not";"prev"==r?c=t>o.first?R(rt(o,t-1).text,null,a):0:"add"==r?c=l+e.options.indentUnit:"subtract"==r?c=l-e.options.indentUnit:"number"==typeof r&&(c=l+r),c=Math.max(0,c);var d="",p=0;if(e.options.indentWithTabs)for(var f=Math.floor(c/a);f;--f)p+=a,d+="\t";if(p<c&&(d+=J(c-p)),d!=u)return ca(o,d,ut(t,0),ut(t,u.length),"+input"),s.stateAfter=null,!0;for(var m=0;m<o.sel.ranges.length;m++){var h=o.sel.ranges[m];if(h.head.line==t&&h.head.ch<u.length){var g=ut(t,u.length);Ro(o,m,new uo(g,g));break}}}Hs.defineInitHook=function(e){return $s.push(e)};var Vs=null;function Ks(e){Vs=e}function Gs(e,t,r,n,i){var o=e.doc;e.display.shift=!1,n||(n=o.sel);var a=+new Date-200,s="paste"==i||e.state.pasteIncoming>a,l=je(t),c=null;if(s&&n.ranges.length>1)if(Vs&&Vs.text.join("\n")==t){if(n.ranges.length%Vs.text.length==0){c=[];for(var u=0;u<Vs.text.length;u++)c.push(o.splitLines(Vs.text[u]))}}else l.length==n.ranges.length&&e.options.pasteLinesPerSelection&&(c=te(l,(function(e){return[e]})));for(var d=e.curOp.updateInput,p=n.ranges.length-1;p>=0;p--){var f=n.ranges[p],m=f.from(),h=f.to();f.empty()&&(r&&r>0?m=ut(m.line,m.ch-r):e.state.overwrite&&!s?h=ut(h.line,Math.min(rt(o,h.line).text.length,h.ch+ee(l).length)):s&&Vs&&Vs.lineWise&&Vs.text.join("\n")==l.join("\n")&&(m=h=ut(m.line,0)));var g={from:m,to:h,text:c?c[p%c.length]:l,origin:i||(s?"paste":e.state.cutIncoming>a?"cut":"+input")};na(e.doc,g),Nr(e,"inputRead",e,g)}t&&!s&&Qs(e,t),pi(e),e.curOp.updateInput<2&&(e.curOp.updateInput=d),e.curOp.typing=!0,e.state.pasteIncoming=e.state.cutIncoming=-1}function Xs(e,t){var r=e.clipboardData&&e.clipboardData.getData("Text");if(r)return e.preventDefault(),t.isReadOnly()||t.options.disableInput||!t.hasFocus()||Oi(t,(function(){return Gs(t,r,0,null,"paste")})),!0}function Qs(e,t){if(e.options.electricChars&&e.options.smartIndent)for(var r=e.doc.sel,n=r.ranges.length-1;n>=0;n--){var i=r.ranges[n];if(!(i.head.ch>100||n&&r.ranges[n-1].head.line==i.head.line)){var o=e.getModeAt(i.head),a=!1;if(o.electricChars){for(var s=0;s<o.electricChars.length;s++)if(t.indexOf(o.electricChars.charAt(s))>-1){a=Us(e,i.head.line,"smart");break}}else o.electricInput&&o.electricInput.test(rt(e.doc,i.head.line).text.slice(0,i.head.ch))&&(a=Us(e,i.head.line,"smart"));a&&Nr(e,"electricInput",e,i.head.line)}}}function Ys(e){for(var t=[],r=[],n=0;n<e.doc.sel.ranges.length;n++){var i=e.doc.sel.ranges[n].head.line,o={anchor:ut(i,0),head:ut(i+1,0)};r.push(o),t.push(e.getRange(o.anchor,o.head))}return{text:t,ranges:r}}function Zs(e,t,r,n){e.setAttribute("autocorrect",r?"on":"off"),e.setAttribute("autocapitalize",n?"on":"off"),e.setAttribute("spellcheck",!!t)}function Js(){var e=D("textarea",null,null,"position: absolute; bottom: -1em; padding: 0; width: 1px; height: 1em; min-height: 1em; outline: none"),t=D("div",[e],null,"overflow: hidden; position: relative; width: 3px; height: 0px;");return l?e.style.width="1000px":e.setAttribute("wrap","off"),g&&(e.style.border="1px solid black"),t}function el(e){var t=e.optionHandlers,r=e.helpers={};e.prototype={constructor:e,focus:function(){W(this).focus(),this.display.input.focus()},setOption:function(e,r){var n=this.options,i=n[e];n[e]==r&&"mode"!=e||(n[e]=r,t.hasOwnProperty(e)&&Ii(this,t[e])(this,r,i),ke(this,"optionChange",this,e))},getOption:function(e){return this.options[e]},getDoc:function(){return this.doc},addKeyMap:function(e,t){this.state.keyMaps[t?"push":"unshift"](Qa(e))},removeKeyMap:function(e){for(var t=this.state.keyMaps,r=0;r<t.length;++r)if(t[r]==e||t[r].name==e)return t.splice(r,1),!0},addOverlay:Pi((function(t,r){var n=t.token?t:e.getMode(this.options,t);if(n.startState)throw new Error("Overlays may not be stateful.");re(this.state.overlays,{mode:n,modeSpec:t,opaque:r&&r.opaque,priority:r&&r.priority||0},(function(e){return e.priority})),this.state.modeGen++,Hn(this)})),removeOverlay:Pi((function(e){for(var t=this.state.overlays,r=0;r<t.length;++r){var n=t[r].modeSpec;if(n==e||"string"==typeof e&&n.name==e)return t.splice(r,1),this.state.modeGen++,void Hn(this)}})),indentLine:Pi((function(e,t,r){"string"!=typeof t&&"number"!=typeof t&&(t=null==t?this.options.smartIndent?"smart":"prev":t?"add":"subtract"),lt(this.doc,e)&&Us(this,e,t,r)})),indentSelection:Pi((function(e){for(var t=this.doc.sel.ranges,r=-1,n=0;n<t.length;n++){var i=t[n];if(i.empty())i.head.line>r&&(Us(this,i.head.line,e,!0),r=i.head.line,n==this.doc.sel.primIndex&&pi(this));else{var o=i.from(),a=i.to(),s=Math.max(r,o.line);r=Math.min(this.lastLine(),a.line-(a.ch?0:1))+1;for(var l=s;l<r;++l)Us(this,l,e);var c=this.doc.sel.ranges;0==o.ch&&t.length==c.length&&c[n].from().ch>0&&Ro(this.doc,n,new uo(o,c[n].to()),G)}}})),getTokenAt:function(e,t){return zt(this,e,t)},getLineTokens:function(e,t){return zt(this,ut(e),t,!0)},getTokenTypeAt:function(e){e=vt(this.doc,e);var t,r=wt(this,rt(this.doc,e.line)),n=0,i=(r.length-1)/2,o=e.ch;if(0==o)t=r[2];else for(;;){var a=n+i>>1;if((a?r[2*a-1]:0)>=o)i=a;else{if(!(r[2*a+1]<o)){t=r[2*a+2];break}n=a+1}}var s=t?t.indexOf("overlay "):-1;return s<0?t:0==s?null:t.slice(0,s-1)},getModeAt:function(t){var r=this.doc.mode;return r.innerMode?e.innerMode(r,this.getTokenAt(t).state).mode:r},getHelper:function(e,t){return this.getHelpers(e,t)[0]},getHelpers:function(e,t){var n=[];if(!r.hasOwnProperty(t))return n;var i=r[t],o=this.getModeAt(e);if("string"==typeof o[t])i[o[t]]&&n.push(i[o[t]]);else if(o[t])for(var a=0;a<o[t].length;a++){var s=i[o[t][a]];s&&n.push(s)}else o.helperType&&i[o.helperType]?n.push(i[o.helperType]):i[o.name]&&n.push(i[o.name]);for(var l=0;l<i._global.length;l++){var c=i._global[l];c.pred(o,this)&&-1==U(n,c.val)&&n.push(c.val)}return n},getStateAfter:function(e,t){var r=this.doc;return St(this,(e=gt(r,null==e?r.first+r.size-1:e))+1,t).state},cursorCoords:function(e,t){var r=this.doc.sel.primary();return Sn(this,null==e?r.head:"object"==typeof e?vt(this.doc,e):e?r.from():r.to(),t||"page")},charCoords:function(e,t){return wn(this,vt(this.doc,e),t||"page")},coordsChar:function(e,t){return Tn(this,(e=kn(this,e,t||"page")).left,e.top)},lineAtHeight:function(e,t){return e=kn(this,{top:e,left:0},t||"page").top,st(this.doc,e+this.display.viewOffset)},heightAtLine:function(e,t,r){var n,i=!1;if("number"==typeof e){var o=this.doc.first+this.doc.size-1;e<this.doc.first?e=this.doc.first:e>o&&(e=o,i=!0),n=rt(this.doc,e)}else n=e;return xn(this,n,{top:0,left:0},t||"page",r||i).top+(i?this.doc.height-ur(n):0)},defaultTextHeight:function(){return Fn(this.display)},defaultCharWidth:function(){return En(this.display)},getViewport:function(){return{from:this.display.viewFrom,to:this.display.viewTo}},addWidget:function(e,t,r,n,i){var o=this.display,a=(e=Sn(this,vt(this.doc,e))).bottom,s=e.left;if(t.style.position="absolute",t.setAttribute("cm-ignore-events","true"),this.display.input.setUneditable(t),o.sizer.appendChild(t),"over"==n)a=e.top;else if("above"==n||"near"==n){var l=Math.max(o.wrapper.clientHeight,this.doc.height),c=Math.max(o.sizer.clientWidth,o.lineSpace.clientWidth);("above"==n||e.bottom+t.offsetHeight>l)&&e.top>t.offsetHeight?a=e.top-t.offsetHeight:e.bottom+t.offsetHeight<=l&&(a=e.bottom),s+t.offsetWidth>c&&(s=c-t.offsetWidth)}t.style.top=a+"px",t.style.left=t.style.right="","right"==i?(s=o.sizer.clientWidth-t.offsetWidth,t.style.right="0px"):("left"==i?s=0:"middle"==i&&(s=(o.sizer.clientWidth-t.offsetWidth)/2),t.style.left=s+"px"),r&&ci(this,{left:s,top:a,right:s+t.offsetWidth,bottom:a+t.offsetHeight})},triggerOnKeyDown:Pi(ms),triggerOnKeyPress:Pi(vs),triggerOnKeyUp:gs,triggerOnMouseDown:Pi(ws),execCommand:function(e){if(rs.hasOwnProperty(e))return rs[e].call(null,this)},triggerElectric:Pi((function(e){Qs(this,e)})),findPosH:function(e,t,r,n){var i=1;t<0&&(i=-1,t=-t);for(var o=vt(this.doc,e),a=0;a<t&&!(o=tl(this.doc,o,i,r,n)).hitSide;++a);return o},moveH:Pi((function(e,t){var r=this;this.extendSelectionsBy((function(n){return r.display.shift||r.doc.extend||n.empty()?tl(r.doc,n.head,e,t,r.options.rtlMoveVisually):e<0?n.from():n.to()}),Q)})),deleteH:Pi((function(e,t){var r=this.doc.sel,n=this.doc;r.somethingSelected()?n.replaceSelection("",null,"+delete"):Ya(this,(function(r){var i=tl(n,r.head,e,t,!1);return e<0?{from:i,to:r.head}:{from:r.head,to:i}}))})),findPosV:function(e,t,r,n){var i=1,o=n;t<0&&(i=-1,t=-t);for(var a=vt(this.doc,e),s=0;s<t;++s){var l=Sn(this,a,"div");if(null==o?o=l.left:l.left=o,(a=rl(this,l,i,r)).hitSide)break}return a},moveV:Pi((function(e,t){var r=this,n=this.doc,i=[],o=!this.display.shift&&!n.extend&&n.sel.somethingSelected();if(n.extendSelectionsBy((function(a){if(o)return e<0?a.from():a.to();var s=Sn(r,a.head,"div");null!=a.goalColumn&&(s.left=a.goalColumn),i.push(s.left);var l=rl(r,s,e,t);return"page"==t&&a==n.sel.primary()&&di(r,wn(r,l,"div").top-s.top),l}),Q),i.length)for(var a=0;a<n.sel.ranges.length;a++)n.sel.ranges[a].goalColumn=i[a]})),findWordAt:function(e){var t=rt(this.doc,e.line).text,r=e.ch,n=e.ch;if(t){var i=this.getHelper(e,"wordChars");"before"!=e.sticky&&n!=t.length||!r?++n:--r;for(var o=t.charAt(r),a=se(o,i)?function(e){return se(e,i)}:/\s/.test(o)?function(e){return/\s/.test(e)}:function(e){return!/\s/.test(e)&&!se(e)};r>0&&a(t.charAt(r-1));)--r;for(;n<t.length&&a(t.charAt(n));)++n}return new uo(ut(e.line,r),ut(e.line,n))},toggleOverwrite:function(e){null!=e&&e==this.state.overwrite||((this.state.overwrite=!this.state.overwrite)?F(this.display.cursorDiv,"CodeMirror-overwrite"):T(this.display.cursorDiv,"CodeMirror-overwrite"),ke(this,"overwriteToggle",this,this.state.overwrite))},hasFocus:function(){return this.display.input.getField()==N(P(this))},isReadOnly:function(){return!(!this.options.readOnly&&!this.doc.cantEdit)},scrollTo:Pi((function(e,t){fi(this,e,t)})),getScrollInfo:function(){var e=this.display.scroller;return{left:e.scrollLeft,top:e.scrollTop,height:e.scrollHeight-Zr(this)-this.display.barHeight,width:e.scrollWidth-Zr(this)-this.display.barWidth,clientHeight:en(this),clientWidth:Jr(this)}},scrollIntoView:Pi((function(e,t){null==e?(e={from:this.doc.sel.primary().head,to:null},null==t&&(t=this.options.cursorScrollMargin)):"number"==typeof e?e={from:ut(e,0),to:null}:null==e.from&&(e={from:e,to:null}),e.to||(e.to=e.from),e.margin=t||0,null!=e.from.line?mi(this,e):gi(this,e.from,e.to,e.margin)})),setSize:Pi((function(e,t){var r=this,n=function(e){return"number"==typeof e||/^\d+$/.test(String(e))?e+"px":e};null!=e&&(this.display.wrapper.style.width=n(e)),null!=t&&(this.display.wrapper.style.height=n(t)),this.options.lineWrapping&&gn(this);var i=this.display.viewFrom;this.doc.iter(i,this.display.viewTo,(function(e){if(e.widgets)for(var t=0;t<e.widgets.length;t++)if(e.widgets[t].noHScroll){Rn(r,i,"widget");break}++i})),this.curOp.forceUpdate=!0,ke(this,"refresh",this)})),operation:function(e){return Oi(this,e)},startOperation:function(){return Li(this)},endOperation:function(){return zi(this)},refresh:Pi((function(){var e=this.display.cachedTextHeight;Hn(this),this.curOp.forceUpdate=!0,vn(this),fi(this,this.doc.scrollLeft,this.doc.scrollTop),Qi(this.display),(null==e||Math.abs(e-Fn(this.display))>.5||this.options.lineWrapping)&&jn(this),ke(this,"refresh",this)})),swapDoc:Pi((function(e){var t=this.doc;return t.cm=null,this.state.selectingText&&this.state.selectingText(),So(this,e),vn(this),this.display.input.reset(),fi(this,e.scrollLeft,e.scrollTop),this.curOp.forceScroll=!0,Nr(this,"swapDoc",this,t),t})),phrase:function(e){var t=this.options.phrases;return t&&Object.prototype.hasOwnProperty.call(t,e)?t[e]:e},getInputField:function(){return this.display.input.getField()},getWrapperElement:function(){return this.display.wrapper},getScrollerElement:function(){return this.display.scroller},getGutterElement:function(){return this.display.gutters}},Me(e),e.registerHelper=function(t,n,i){r.hasOwnProperty(t)||(r[t]=e[t]={_global:[]}),r[t][n]=i},e.registerGlobalHelper=function(t,n,i,o){e.registerHelper(t,n,o),r[t]._global.push({pred:i,val:o})}}function tl(e,t,r,n,i){var o=t,a=r,s=rt(e,t.line),l=i&&"rtl"==e.direction?-r:r;function c(){var r=t.line+l;return!(r<e.first||r>=e.first+e.size)&&(t=new ut(r,t.ch,t.sticky),s=rt(e,r))}function u(o){var a;if("codepoint"==n){var u=s.text.charCodeAt(t.ch+(r>0?0:-1));if(isNaN(u))a=null;else{var d=r>0?u>=55296&&u<56320:u>=56320&&u<57343;a=new ut(t.line,Math.max(0,Math.min(s.text.length,t.ch+r*(d?2:1))),-r)}}else a=i?ts(e.cm,s,t,r):Ja(s,t,r);if(null==a){if(o||!c())return!1;t=es(i,e.cm,s,t.line,l)}else t=a;return!0}if("char"==n||"codepoint"==n)u();else if("column"==n)u(!0);else if("word"==n||"group"==n)for(var d=null,p="group"==n,f=e.cm&&e.cm.getHelper(t,"wordChars"),m=!0;!(r<0)||u(!m);m=!1){var h=s.text.charAt(t.ch)||"\n",g=se(h,f)?"w":p&&"\n"==h?"n":!p||/\s/.test(h)?null:"p";if(!p||m||g||(g="s"),d&&d!=g){r<0&&(r=1,u(),t.sticky="after");break}if(g&&(d=g),r>0&&!u(!m))break}var v=Jo(e,t,o,a,!0);return pt(o,v)&&(v.hitSide=!0),v}function rl(e,t,r,n){var i,o,a=e.doc,s=t.left;if("page"==n){var l=Math.min(e.display.wrapper.clientHeight,W(e).innerHeight||a(e).documentElement.clientHeight),c=Math.max(l-.5*Fn(e.display),3);i=(r>0?t.bottom:t.top)+r*c}else"line"==n&&(i=r>0?t.bottom+3:t.top-3);for(;(o=Tn(e,s,i)).outside;){if(r<0?i<=0:i>=a.height){o.hitSide=!0;break}i+=5*r}return o}var nl=function(e){this.cm=e,this.lastAnchorNode=this.lastAnchorOffset=this.lastFocusNode=this.lastFocusOffset=null,this.polling=new $,this.composing=null,this.gracePeriod=!1,this.readDOMTimeout=null};function il(e,t){var r=an(e,t.line);if(!r||r.hidden)return null;var n=rt(e.doc,t.line),i=rn(r,n,t.line),o=ve(n,e.doc.direction),a="left";o&&(a=he(o,t.ch)%2?"right":"left");var s=dn(i.map,t.ch,a);return s.offset="right"==s.collapse?s.end:s.start,s}function ol(e){for(var t=e;t;t=t.parentNode)if(/CodeMirror-gutter-wrapper/.test(t.className))return!0;return!1}function al(e,t){return t&&(e.bad=!0),e}function sl(e,t,r,n,i){var o="",a=!1,s=e.doc.lineSeparator(),l=!1;function c(e){return function(t){return t.id==e}}function u(){a&&(o+=s,l&&(o+=s),a=l=!1)}function d(e){e&&(u(),o+=e)}function p(t){if(1==t.nodeType){var r=t.getAttribute("cm-text");if(r)return void d(r);var o,f=t.getAttribute("cm-marker");if(f){var m=e.findMarks(ut(n,0),ut(i+1,0),c(+f));return void(m.length&&(o=m[0].find(0))&&d(nt(e.doc,o.from,o.to).join(s)))}if("false"==t.getAttribute("contenteditable"))return;var h=/^(pre|div|p|li|table|br)$/i.test(t.nodeName);if(!/^br$/i.test(t.nodeName)&&0==t.textContent.length)return;h&&u();for(var g=0;g<t.childNodes.length;g++)p(t.childNodes[g]);/^(pre|p)$/i.test(t.nodeName)&&(l=!0),h&&(a=!0)}else 3==t.nodeType&&d(t.nodeValue.replace(/\u200b/g,"").replace(/\u00a0/g," "))}for(;p(t),t!=r;)t=t.nextSibling,l=!1;return o}function ll(e,t,r){var n;if(t==e.display.lineDiv){if(!(n=e.display.lineDiv.childNodes[r]))return al(e.clipPos(ut(e.display.viewTo-1)),!0);t=null,r=0}else for(n=t;;n=n.parentNode){if(!n||n==e.display.lineDiv)return null;if(n.parentNode&&n.parentNode==e.display.lineDiv)break}for(var i=0;i<e.display.view.length;i++){var o=e.display.view[i];if(o.node==n)return cl(o,t,r)}}function cl(e,t,r){var n=e.text.firstChild,i=!1;if(!t||!q(n,t))return al(ut(at(e.line),0),!0);if(t==n&&(i=!0,t=n.childNodes[r],r=0,!t)){var o=e.rest?ee(e.rest):e.line;return al(ut(at(o),o.text.length),i)}var a=3==t.nodeType?t:null,s=t;for(a||1!=t.childNodes.length||3!=t.firstChild.nodeType||(a=t.firstChild,r&&(r=a.nodeValue.length));s.parentNode!=n;)s=s.parentNode;var l=e.measure,c=l.maps;function u(t,r,n){for(var i=-1;i<(c?c.length:0);i++)for(var o=i<0?l.map:c[i],a=0;a<o.length;a+=3){var s=o[a+2];if(s==t||s==r){var u=at(i<0?e.line:e.rest[i]),d=o[a]+n;return(n<0||s!=t)&&(d=o[a+(n?1:0)]),ut(u,d)}}}var d=u(a,s,r);if(d)return al(d,i);for(var p=s.nextSibling,f=a?a.nodeValue.length-r:0;p;p=p.nextSibling){if(d=u(p,p.firstChild,0))return al(ut(d.line,d.ch-f),i);f+=p.textContent.length}for(var m=s.previousSibling,h=r;m;m=m.previousSibling){if(d=u(m,m.firstChild,-1))return al(ut(d.line,d.ch+h),i);h+=m.textContent.length}}nl.prototype.init=function(e){var t=this,r=this,n=r.cm,i=r.div=e.lineDiv;function o(e){for(var t=e.target;t;t=t.parentNode){if(t==i)return!0;if(/\bCodeMirror-(?:line)?widget\b/.test(t.className))break}return!1}function a(e){if(o(e)&&!we(n,e)){if(n.somethingSelected())Ks({lineWise:!1,text:n.getSelections()}),"cut"==e.type&&n.replaceSelection("",null,"cut");else{if(!n.options.lineWiseCopyCut)return;var t=Ys(n);Ks({lineWise:!0,text:t.text}),"cut"==e.type&&n.operation((function(){n.setSelections(t.ranges,0,G),n.replaceSelection("",null,"cut")}))}if(e.clipboardData){e.clipboardData.clearData();var a=Vs.text.join("\n");if(e.clipboardData.setData("Text",a),e.clipboardData.getData("Text")==a)return void e.preventDefault()}var s=Js(),l=s.firstChild;Zs(l),n.display.lineSpace.insertBefore(s,n.display.lineSpace.firstChild),l.value=Vs.text.join("\n");var c=N(j(i));O(l),setTimeout((function(){n.display.lineSpace.removeChild(s),c.focus(),c==i&&r.showPrimarySelection()}),50)}}i.contentEditable=!0,Zs(i,n.options.spellcheck,n.options.autocorrect,n.options.autocapitalize),_e(i,"paste",(function(e){!o(e)||we(n,e)||Xs(e,n)||s<=11&&setTimeout(Ii(n,(function(){return t.updateFromDOM()})),20)})),_e(i,"compositionstart",(function(e){t.composing={data:e.data,done:!1}})),_e(i,"compositionupdate",(function(e){t.composing||(t.composing={data:e.data,done:!1})})),_e(i,"compositionend",(function(e){t.composing&&(e.data!=t.composing.data&&t.readFromDOMSoon(),t.composing.done=!0)})),_e(i,"touchstart",(function(){return r.forceCompositionEnd()})),_e(i,"input",(function(){t.composing||t.readFromDOMSoon()})),_e(i,"copy",a),_e(i,"cut",a)},nl.prototype.screenReaderLabelChanged=function(e){e?this.div.setAttribute("aria-label",e):this.div.removeAttribute("aria-label")},nl.prototype.prepareSelection=function(){var e=Xn(this.cm,!1);return e.focus=N(j(this.div))==this.div,e},nl.prototype.showSelection=function(e,t){e&&this.cm.display.view.length&&((e.focus||t)&&this.showPrimarySelection(),this.showMultipleSelections(e))},nl.prototype.getSelection=function(){return this.cm.display.wrapper.ownerDocument.getSelection()},nl.prototype.showPrimarySelection=function(){var e=this.getSelection(),t=this.cm,n=t.doc.sel.primary(),i=n.from(),o=n.to();if(t.display.viewTo==t.display.viewFrom||i.line>=t.display.viewTo||o.line<t.display.viewFrom)e.removeAllRanges();else{var a=ll(t,e.anchorNode,e.anchorOffset),s=ll(t,e.focusNode,e.focusOffset);if(!a||a.bad||!s||s.bad||0!=dt(ht(a,s),i)||0!=dt(mt(a,s),o)){var l=t.display.view,c=i.line>=t.display.viewFrom&&il(t,i)||{node:l[0].measure.map[2],offset:0},u=o.line<t.display.viewTo&&il(t,o);if(!u){var d=l[l.length-1].measure,p=d.maps?d.maps[d.maps.length-1]:d.map;u={node:p[p.length-1],offset:p[p.length-2]-p[p.length-3]}}if(c&&u){var f,m=e.rangeCount&&e.getRangeAt(0);try{f=M(c.node,c.offset,u.offset,u.node)}catch(e){}f&&(!r&&t.state.focused?(e.collapse(c.node,c.offset),f.collapsed||(e.removeAllRanges(),e.addRange(f))):(e.removeAllRanges(),e.addRange(f)),m&&null==e.anchorNode?e.addRange(m):r&&this.startGracePeriod()),this.rememberSelection()}else e.removeAllRanges()}}},nl.prototype.startGracePeriod=function(){var e=this;clearTimeout(this.gracePeriod),this.gracePeriod=setTimeout((function(){e.gracePeriod=!1,e.selectionChanged()&&e.cm.operation((function(){return e.cm.curOp.selectionChanged=!0}))}),20)},nl.prototype.showMultipleSelections=function(e){z(this.cm.display.cursorDiv,e.cursors),z(this.cm.display.selectionDiv,e.selection)},nl.prototype.rememberSelection=function(){var e=this.getSelection();this.lastAnchorNode=e.anchorNode,this.lastAnchorOffset=e.anchorOffset,this.lastFocusNode=e.focusNode,this.lastFocusOffset=e.focusOffset},nl.prototype.selectionInEditor=function(){var e=this.getSelection();if(!e.rangeCount)return!1;var t=e.getRangeAt(0).commonAncestorContainer;return q(this.div,t)},nl.prototype.focus=function(){"nocursor"!=this.cm.options.readOnly&&(this.selectionInEditor()&&N(j(this.div))==this.div||this.showSelection(this.prepareSelection(),!0),this.div.focus())},nl.prototype.blur=function(){this.div.blur()},nl.prototype.getField=function(){return this.div},nl.prototype.supportsTouch=function(){return!0},nl.prototype.receivedFocus=function(){var e=this,t=this;function r(){t.cm.state.focused&&(t.pollSelection(),t.polling.set(t.cm.options.pollInterval,r))}this.selectionInEditor()?setTimeout((function(){return e.pollSelection()}),20):Oi(this.cm,(function(){return t.cm.curOp.selectionChanged=!0})),this.polling.set(this.cm.options.pollInterval,r)},nl.prototype.selectionChanged=function(){var e=this.getSelection();return e.anchorNode!=this.lastAnchorNode||e.anchorOffset!=this.lastAnchorOffset||e.focusNode!=this.lastFocusNode||e.focusOffset!=this.lastFocusOffset},nl.prototype.pollSelection=function(){if(null==this.readDOMTimeout&&!this.gracePeriod&&this.selectionChanged()){var e=this.getSelection(),t=this.cm;if(v&&u&&this.cm.display.gutterSpecs.length&&ol(e.anchorNode))return this.cm.triggerOnKeyDown({type:"keydown",keyCode:8,preventDefault:Math.abs}),this.blur(),void this.focus();if(!this.composing){this.rememberSelection();var r=ll(t,e.anchorNode,e.anchorOffset),n=ll(t,e.focusNode,e.focusOffset);r&&n&&Oi(t,(function(){Ko(t.doc,fo(r,n),G),(r.bad||n.bad)&&(t.curOp.selectionChanged=!0)}))}}},nl.prototype.pollContent=function(){null!=this.readDOMTimeout&&(clearTimeout(this.readDOMTimeout),this.readDOMTimeout=null);var e,t,r,n=this.cm,i=n.display,o=n.doc.sel.primary(),a=o.from(),s=o.to();if(0==a.ch&&a.line>n.firstLine()&&(a=ut(a.line-1,rt(n.doc,a.line-1).length)),s.ch==rt(n.doc,s.line).text.length&&s.line<n.lastLine()&&(s=ut(s.line+1,0)),a.line<i.viewFrom||s.line>i.viewTo-1)return!1;a.line==i.viewFrom||0==(e=Bn(n,a.line))?(t=at(i.view[0].line),r=i.view[0].node):(t=at(i.view[e].line),r=i.view[e-1].node.nextSibling);var l,c,u=Bn(n,s.line);if(u==i.view.length-1?(l=i.viewTo-1,c=i.lineDiv.lastChild):(l=at(i.view[u+1].line)-1,c=i.view[u+1].node.previousSibling),!r)return!1;for(var d=n.doc.splitLines(sl(n,r,c,t,l)),p=nt(n.doc,ut(t,0),ut(l,rt(n.doc,l).text.length));d.length>1&&p.length>1;)if(ee(d)==ee(p))d.pop(),p.pop(),l--;else{if(d[0]!=p[0])break;d.shift(),p.shift(),t++}for(var f=0,m=0,h=d[0],g=p[0],v=Math.min(h.length,g.length);f<v&&h.charCodeAt(f)==g.charCodeAt(f);)++f;for(var y=ee(d),_=ee(p),b=Math.min(y.length-(1==d.length?f:0),_.length-(1==p.length?f:0));m<b&&y.charCodeAt(y.length-m-1)==_.charCodeAt(_.length-m-1);)++m;if(1==d.length&&1==p.length&&t==a.line)for(;f&&f>a.ch&&y.charCodeAt(y.length-m-1)==_.charCodeAt(_.length-m-1);)f--,m++;d[d.length-1]=y.slice(0,y.length-m).replace(/^\u200b+/,""),d[0]=d[0].slice(f).replace(/\u200b+$/,"");var x=ut(t,f),k=ut(l,p.length?ee(p).length-m:0);return d.length>1||d[0]||dt(x,k)?(ca(n.doc,d,x,k,"+input"),!0):void 0},nl.prototype.ensurePolled=function(){this.forceCompositionEnd()},nl.prototype.reset=function(){this.forceCompositionEnd()},nl.prototype.forceCompositionEnd=function(){this.composing&&(clearTimeout(this.readDOMTimeout),this.composing=null,this.updateFromDOM(),this.div.blur(),this.div.focus())},nl.prototype.readFromDOMSoon=function(){var e=this;null==this.readDOMTimeout&&(this.readDOMTimeout=setTimeout((function(){if(e.readDOMTimeout=null,e.composing){if(!e.composing.done)return;e.composing=null}e.updateFromDOM()}),80))},nl.prototype.updateFromDOM=function(){var e=this;!this.cm.isReadOnly()&&this.pollContent()||Oi(this.cm,(function(){return Hn(e.cm)}))},nl.prototype.setUneditable=function(e){e.contentEditable="false"},nl.prototype.onKeyPress=function(e){0==e.charCode||this.composing||(e.preventDefault(),this.cm.isReadOnly()||Ii(this.cm,Gs)(this.cm,String.fromCharCode(null==e.charCode?e.keyCode:e.charCode),0))},nl.prototype.readOnlyChanged=function(e){this.div.contentEditable=String("nocursor"!=e)},nl.prototype.onContextMenu=function(){},nl.prototype.resetPosition=function(){},nl.prototype.needsContentAttribute=!0;var ul=function(e){this.cm=e,this.prevInput="",this.pollingFast=!1,this.polling=new $,this.hasSelection=!1,this.composing=null,this.resetting=!1};function dl(e,t){if((t=t?H(t):{}).value=e.value,!t.tabindex&&e.tabIndex&&(t.tabindex=e.tabIndex),!t.placeholder&&e.placeholder&&(t.placeholder=e.placeholder),null==t.autofocus){var r=N(j(e));t.autofocus=r==e||null!=e.getAttribute("autofocus")&&r==document.body}function n(){e.value=s.getValue()}var i;if(e.form&&(_e(e.form,"submit",n),!t.leaveSubmitMethodAlone)){var o=e.form;i=o.submit;try{var a=o.submit=function(){n(),o.submit=i,o.submit(),o.submit=a}}catch(e){}}t.finishInit=function(r){r.save=n,r.getTextArea=function(){return e},r.toTextArea=function(){r.toTextArea=isNaN,n(),e.parentNode.removeChild(r.getWrapperElement()),e.style.display="",e.form&&(xe(e.form,"submit",n),t.leaveSubmitMethodAlone||"function"!=typeof e.form.submit||(e.form.submit=i))}},e.style.display="none";var s=Hs((function(t){return e.parentNode.insertBefore(t,e.nextSibling)}),t);return s}function pl(e){e.off=xe,e.on=_e,e.wheelEventPixels=so,e.Doc=La,e.splitLines=je,e.countColumn=R,e.findColumn=Y,e.isWordChar=ae,e.Pass=K,e.signal=ke,e.Line=fr,e.changeEnd=mo,e.scrollbarModel=Ci,e.Pos=ut,e.cmpPos=dt,e.modes=$e,e.mimeModes=Ue,e.resolveMode=Ge,e.getMode=Xe,e.modeExtensions=Qe,e.extendMode=Ye,e.copyState=Ze,e.startState=et,e.innerMode=Je,e.commands=rs,e.keyMap=Ra,e.keyName=Xa,e.isModifierKey=Ka,e.lookupKey=Va,e.normalizeKeyMap=Ua,e.StringStream=tt,e.SharedTextMarker=ka,e.TextMarker=ba,e.LineWidget=ga,e.e_preventDefault=Te,e.e_stopPropagation=Le,e.e_stop=De,e.addClass=F,e.contains=q,e.rmClass=T,e.keyNames=ja}ul.prototype.init=function(e){var t=this,r=this,n=this.cm;this.createField(e);var i=this.textarea;function o(e){if(!we(n,e)){if(n.somethingSelected())Ks({lineWise:!1,text:n.getSelections()});else{if(!n.options.lineWiseCopyCut)return;var t=Ys(n);Ks({lineWise:!0,text:t.text}),"cut"==e.type?n.setSelections(t.ranges,null,G):(r.prevInput="",i.value=t.text.join("\n"),O(i))}"cut"==e.type&&(n.state.cutIncoming=+new Date)}}e.wrapper.insertBefore(this.wrapper,e.wrapper.firstChild),g&&(i.style.width="0px"),_e(i,"input",(function(){a&&s>=9&&t.hasSelection&&(t.hasSelection=null),r.poll()})),_e(i,"paste",(function(e){we(n,e)||Xs(e,n)||(n.state.pasteIncoming=+new Date,r.fastPoll())})),_e(i,"cut",o),_e(i,"copy",o),_e(e.scroller,"paste",(function(t){if(!Gr(e,t)&&!we(n,t)){if(!i.dispatchEvent)return n.state.pasteIncoming=+new Date,void r.focus();var o=new Event("paste");o.clipboardData=t.clipboardData,i.dispatchEvent(o)}})),_e(e.lineSpace,"selectstart",(function(t){Gr(e,t)||Te(t)})),_e(i,"compositionstart",(function(){var e=n.getCursor("from");r.composing&&r.composing.range.clear(),r.composing={start:e,range:n.markText(e,n.getCursor("to"),{className:"CodeMirror-composing"})}})),_e(i,"compositionend",(function(){r.composing&&(r.poll(),r.composing.range.clear(),r.composing=null)}))},ul.prototype.createField=function(e){this.wrapper=Js(),this.textarea=this.wrapper.firstChild;var t=this.cm.options;Zs(this.textarea,t.spellcheck,t.autocorrect,t.autocapitalize)},ul.prototype.screenReaderLabelChanged=function(e){e?this.textarea.setAttribute("aria-label",e):this.textarea.removeAttribute("aria-label")},ul.prototype.prepareSelection=function(){var e=this.cm,t=e.display,r=e.doc,n=Xn(e);if(e.options.moveInputWithCursor){var i=Sn(e,r.sel.primary().head,"div"),o=t.wrapper.getBoundingClientRect(),a=t.lineDiv.getBoundingClientRect();n.teTop=Math.max(0,Math.min(t.wrapper.clientHeight-10,i.top+a.top-o.top)),n.teLeft=Math.max(0,Math.min(t.wrapper.clientWidth-10,i.left+a.left-o.left))}return n},ul.prototype.showSelection=function(e){var t=this.cm.display;z(t.cursorDiv,e.cursors),z(t.selectionDiv,e.selection),null!=e.teTop&&(this.wrapper.style.top=e.teTop+"px",this.wrapper.style.left=e.teLeft+"px")},ul.prototype.reset=function(e){if(!(this.contextMenuPending||this.composing&&e)){var t=this.cm;if(this.resetting=!0,t.somethingSelected()){this.prevInput="";var r=t.getSelection();this.textarea.value=r,t.state.focused&&O(this.textarea),a&&s>=9&&(this.hasSelection=r)}else e||(this.prevInput=this.textarea.value="",a&&s>=9&&(this.hasSelection=null));this.resetting=!1}},ul.prototype.getField=function(){return this.textarea},ul.prototype.supportsTouch=function(){return!1},ul.prototype.focus=function(){if("nocursor"!=this.cm.options.readOnly&&(!y||N(j(this.textarea))!=this.textarea))try{this.textarea.focus()}catch(e){}},ul.prototype.blur=function(){this.textarea.blur()},ul.prototype.resetPosition=function(){this.wrapper.style.top=this.wrapper.style.left=0},ul.prototype.receivedFocus=function(){this.slowPoll()},ul.prototype.slowPoll=function(){var e=this;this.pollingFast||this.polling.set(this.cm.options.pollInterval,(function(){e.poll(),e.cm.state.focused&&e.slowPoll()}))},ul.prototype.fastPoll=function(){var e=!1,t=this;function r(){t.poll()||e?(t.pollingFast=!1,t.slowPoll()):(e=!0,t.polling.set(60,r))}t.pollingFast=!0,t.polling.set(20,r)},ul.prototype.poll=function(){var e=this,t=this.cm,r=this.textarea,n=this.prevInput;if(this.contextMenuPending||this.resetting||!t.state.focused||We(r)&&!n&&!this.composing||t.isReadOnly()||t.options.disableInput||t.state.keySeq)return!1;var i=r.value;if(i==n&&!t.somethingSelected())return!1;if(a&&s>=9&&this.hasSelection===i||_&&/[\uf700-\uf7ff]/.test(i))return t.display.input.reset(),!1;if(t.doc.sel==t.display.selForContextMenu){var o=i.charCodeAt(0);if(8203!=o||n||(n="​"),8666==o)return this.reset(),this.cm.execCommand("undo")}for(var l=0,c=Math.min(n.length,i.length);l<c&&n.charCodeAt(l)==i.charCodeAt(l);)++l;return Oi(t,(function(){Gs(t,i.slice(l),n.length-l,null,e.composing?"*compose":null),i.length>1e3||i.indexOf("\n")>-1?r.value=e.prevInput="":e.prevInput=i,e.composing&&(e.composing.range.clear(),e.composing.range=t.markText(e.composing.start,t.getCursor("to"),{className:"CodeMirror-composing"}))})),!0},ul.prototype.ensurePolled=function(){this.pollingFast&&this.poll()&&(this.pollingFast=!1)},ul.prototype.onKeyPress=function(){a&&s>=9&&(this.hasSelection=null),this.fastPoll()},ul.prototype.onContextMenu=function(e){var t=this,r=t.cm,n=r.display,i=t.textarea;t.contextMenuPending&&t.contextMenuPending();var o=Wn(r,e),c=n.scroller.scrollTop;if(o&&!p){r.options.resetSelectionOnContextMenu&&-1==r.doc.sel.contains(o)&&Ii(r,Ko)(r.doc,fo(o),G);var u,d=i.style.cssText,f=t.wrapper.style.cssText,m=t.wrapper.offsetParent.getBoundingClientRect();if(t.wrapper.style.cssText="position: static",i.style.cssText="position: absolute; width: 30px; height: 30px;\n      top: "+(e.clientY-m.top-5)+"px; left: "+(e.clientX-m.left-5)+"px;\n      z-index: 1000; background: "+(a?"rgba(255, 255, 255, .05)":"transparent")+";\n      outline: none; border-width: 0; outline: none; overflow: hidden; opacity: .05; filter: alpha(opacity=5);",l&&(u=i.ownerDocument.defaultView.scrollY),n.input.focus(),l&&i.ownerDocument.defaultView.scrollTo(null,u),n.input.reset(),r.somethingSelected()||(i.value=t.prevInput=" "),t.contextMenuPending=v,n.selForContextMenu=r.doc.sel,clearTimeout(n.detectingSelectAll),a&&s>=9&&g(),S){De(e);var h=function(){xe(window,"mouseup",h),setTimeout(v,20)};_e(window,"mouseup",h)}else setTimeout(v,50)}function g(){if(null!=i.selectionStart){var e=r.somethingSelected(),o="​"+(e?i.value:"");i.value="⇚",i.value=o,t.prevInput=e?"":"​",i.selectionStart=1,i.selectionEnd=o.length,n.selForContextMenu=r.doc.sel}}function v(){if(t.contextMenuPending==v&&(t.contextMenuPending=!1,t.wrapper.style.cssText=f,i.style.cssText=d,a&&s<9&&n.scrollbars.setScrollTop(n.scroller.scrollTop=c),null!=i.selectionStart)){(!a||a&&s<9)&&g();var e=0,o=function(){n.selForContextMenu==r.doc.sel&&0==i.selectionStart&&i.selectionEnd>0&&"​"==t.prevInput?Ii(r,ta)(r):e++<10?n.detectingSelectAll=setTimeout(o,500):(n.selForContextMenu=null,n.input.reset())};n.detectingSelectAll=setTimeout(o,200)}}},ul.prototype.readOnlyChanged=function(e){e||this.reset(),this.textarea.disabled="nocursor"==e,this.textarea.readOnly=!!e},ul.prototype.setUneditable=function(){},ul.prototype.needsContentAttribute=!1,js(Hs),el(Hs);var fl="iter insert remove copy getEditor constructor".split(" ");for(var ml in La.prototype)La.prototype.hasOwnProperty(ml)&&U(fl,ml)<0&&(Hs.prototype[ml]=function(e){return function(){return e.apply(this.doc,arguments)}}(La.prototype[ml]));return Me(La),Hs.inputStyles={textarea:ul,contenteditable:nl},Hs.defineMode=function(e){Hs.defaults.mode||"null"==e||(Hs.defaults.mode=e),Ve.apply(this,arguments)},Hs.defineMIME=Ke,Hs.defineMode("null",(function(){return{token:function(e){return e.skipToEnd()}}})),Hs.defineMIME("text/plain","null"),Hs.defineExtension=function(e,t){Hs.prototype[e]=t},Hs.defineDocExtension=function(e,t){La.prototype[e]=t},Hs.fromTextArea=dl,pl(Hs),Hs.version="5.65.18",Hs}()},712:(e,t,r)=>{!function(e){"use strict";function t(e,t,r,n,i,o){this.indented=e,this.column=t,this.type=r,this.info=n,this.align=i,this.prev=o}function r(e,r,n,i){var o=e.indented;return e.context&&"statement"==e.context.type&&"statement"!=n&&(o=e.context.indented),e.context=new t(o,r,n,i,null,e.context)}function n(e){var t=e.context.type;return")"!=t&&"]"!=t&&"}"!=t||(e.indented=e.context.indented),e.context=e.context.prev}function i(e,t,r){return"variable"==t.prevToken||"type"==t.prevToken||!!/\S(?:[^- ]>|[*\]])\s*$|\*$/.test(e.string.slice(0,r))||!(!t.typeAtEndOfLine||e.column()!=e.indentation())||void 0}function o(e){for(;;){if(!e||"top"==e.type)return!0;if("}"==e.type&&"namespace"!=e.prev.info)return!1;e=e.prev}}function a(e){for(var t={},r=e.split(" "),n=0;n<r.length;++n)t[r[n]]=!0;return t}function s(e,t){return"function"==typeof e?e(t):e.propertyIsEnumerable(t)}e.defineMode("clike",(function(a,l){var c,u,d=a.indentUnit,p=l.statementIndentUnit||d,f=l.dontAlignCalls,m=l.keywords||{},h=l.types||{},g=l.builtin||{},v=l.blockKeywords||{},y=l.defKeywords||{},_=l.atoms||{},b=l.hooks||{},x=l.multiLineStrings,k=!1!==l.indentStatements,w=!1!==l.indentSwitch,S=l.namespaceSeparator,C=l.isPunctuationChar||/[\[\]{}\(\),;\:\.]/,M=l.numberStart||/[\d\.]/,T=l.number||/^(?:0x[a-f\d]+|0b[01]+|(?:\d+\.?\d*|\.\d+)(?:e[-+]?\d+)?)(u|ll?|l|f)?/i,L=l.isOperatorChar||/[+\-*&%=<>!?|\/]/,z=l.isIdentifierChar||/[\w\$_\xa1-\uffff]/,D=l.isReservedIdentifier||!1;function A(e,t){var r=e.next();if(b[r]){var n=b[r](e,t);if(!1!==n)return n}if('"'==r||"'"==r)return t.tokenize=q(r),t.tokenize(e,t);if(M.test(r)){if(e.backUp(1),e.match(T))return"number";e.next()}if(C.test(r))return c=r,null;if("/"==r){if(e.eat("*"))return t.tokenize=N,N(e,t);if(e.eat("/"))return e.skipToEnd(),"comment"}if(L.test(r)){for(;!e.match(/^\/[\/*]/,!1)&&e.eat(L););return"operator"}if(e.eatWhile(z),S)for(;e.match(S);)e.eatWhile(z);var i=e.current();return s(m,i)?(s(v,i)&&(c="newstatement"),s(y,i)&&(u=!0),"keyword"):s(h,i)?"type":s(g,i)||D&&D(i)?(s(v,i)&&(c="newstatement"),"builtin"):s(_,i)?"atom":"variable"}function q(e){return function(t,r){for(var n,i=!1,o=!1;null!=(n=t.next());){if(n==e&&!i){o=!0;break}i=!i&&"\\"==n}return(o||!i&&!x)&&(r.tokenize=null),"string"}}function N(e,t){for(var r,n=!1;r=e.next();){if("/"==r&&n){t.tokenize=null;break}n="*"==r}return"comment"}function F(e,t){l.typeFirstDefinitions&&e.eol()&&o(t.context)&&(t.typeAtEndOfLine=i(e,t,e.pos))}return{startState:function(e){return{tokenize:null,context:new t((e||0)-d,0,"top",null,!1),indented:0,startOfLine:!0,prevToken:null}},token:function(e,t){var a=t.context;if(e.sol()&&(null==a.align&&(a.align=!1),t.indented=e.indentation(),t.startOfLine=!0),e.eatSpace())return F(e,t),null;c=u=null;var s=(t.tokenize||A)(e,t);if("comment"==s||"meta"==s)return s;if(null==a.align&&(a.align=!0),";"==c||":"==c||","==c&&e.match(/^\s*(?:\/\/.*)?$/,!1))for(;"statement"==t.context.type;)n(t);else if("{"==c)r(t,e.column(),"}");else if("["==c)r(t,e.column(),"]");else if("("==c)r(t,e.column(),")");else if("}"==c){for(;"statement"==a.type;)a=n(t);for("}"==a.type&&(a=n(t));"statement"==a.type;)a=n(t)}else c==a.type?n(t):k&&(("}"==a.type||"top"==a.type)&&";"!=c||"statement"==a.type&&"newstatement"==c)&&r(t,e.column(),"statement",e.current());if("variable"==s&&("def"==t.prevToken||l.typeFirstDefinitions&&i(e,t,e.start)&&o(t.context)&&e.match(/^\s*\(/,!1))&&(s="def"),b.token){var d=b.token(e,t,s);void 0!==d&&(s=d)}return"def"==s&&!1===l.styleDefs&&(s="variable"),t.startOfLine=!1,t.prevToken=u?"def":s||c,F(e,t),s},indent:function(t,r){if(t.tokenize!=A&&null!=t.tokenize||t.typeAtEndOfLine&&o(t.context))return e.Pass;var n=t.context,i=r&&r.charAt(0),a=i==n.type;if("statement"==n.type&&"}"==i&&(n=n.prev),l.dontIndentStatements)for(;"statement"==n.type&&l.dontIndentStatements.test(n.info);)n=n.prev;if(b.indent){var s=b.indent(t,n,r,d);if("number"==typeof s)return s}var c=n.prev&&"switch"==n.prev.info;if(l.allmanIndentation&&/[{(]/.test(i)){for(;"top"!=n.type&&"}"!=n.type;)n=n.prev;return n.indented}return"statement"==n.type?n.indented+("{"==i?0:p):!n.align||f&&")"==n.type?")"!=n.type||a?n.indented+(a?0:d)+(a||!c||/^(?:case|default)\b/.test(r)?0:d):n.indented+p:n.column+(a?0:1)},electricInput:w?/^\s*(?:case .*?:|default:|\{\}?|\})$/:/^\s*[{}]$/,blockCommentStart:"/*",blockCommentEnd:"*/",blockCommentContinue:" * ",lineComment:"//",fold:"brace"}}));var l="auto if break case register continue return default do sizeof static else struct switch extern typedef union for goto while enum const volatile inline restrict asm fortran",c="alignas alignof and and_eq audit axiom bitand bitor catch class compl concept constexpr const_cast decltype delete dynamic_cast explicit export final friend import module mutable namespace new noexcept not not_eq operator or or_eq override private protected public reinterpret_cast requires static_assert static_cast template this thread_local throw try typeid typename using virtual xor xor_eq",u="bycopy byref in inout oneway out self super atomic nonatomic retain copy readwrite readonly strong weak assign typeof nullable nonnull null_resettable _cmd @interface @implementation @end @protocol @encode @property @synthesize @dynamic @class @public @package @private @protected @required @optional @try @catch @finally @import @selector @encode @defs @synchronized @autoreleasepool @compatibility_alias @available",d="FOUNDATION_EXPORT FOUNDATION_EXTERN NS_INLINE NS_FORMAT_FUNCTION  NS_RETURNS_RETAINEDNS_ERROR_ENUM NS_RETURNS_NOT_RETAINED NS_RETURNS_INNER_POINTER NS_DESIGNATED_INITIALIZER NS_ENUM NS_OPTIONS NS_REQUIRES_NIL_TERMINATION NS_ASSUME_NONNULL_BEGIN NS_ASSUME_NONNULL_END NS_SWIFT_NAME NS_REFINED_FOR_SWIFT",p=a("int long char short double float unsigned signed void bool"),f=a("SEL instancetype id Class Protocol BOOL");function m(e){return s(p,e)||/.+_t$/.test(e)}function h(e){return m(e)||s(f,e)}var g="case do else for if switch while struct enum union",v="struct enum union";function y(e,t){if(!t.startOfLine)return!1;for(var r,n=null;r=e.peek();){if("\\"==r&&e.match(/^.$/)){n=y;break}if("/"==r&&e.match(/^\/[\/\*]/,!1))break;e.next()}return t.tokenize=n,"meta"}function _(e,t){return"type"==t.prevToken&&"type"}function b(e){return!(!e||e.length<2||"_"!=e[0]||"_"!=e[1]&&e[1]===e[1].toLowerCase())}function x(e){return e.eatWhile(/[\w\.']/),"number"}function k(e,t){if(e.backUp(1),e.match(/^(?:R|u8R|uR|UR|LR)/)){var r=e.match(/^"([^\s\\()]{0,16})\(/);return!!r&&(t.cpp11RawStringDelim=r[1],t.tokenize=C,C(e,t))}return e.match(/^(?:u8|u|U|L)/)?!!e.match(/^["']/,!1)&&"string":(e.next(),!1)}function w(e){var t=/(\w+)::~?(\w+)$/.exec(e);return t&&t[1]==t[2]}function S(e,t){for(var r;null!=(r=e.next());)if('"'==r&&!e.eat('"')){t.tokenize=null;break}return"string"}function C(e,t){var r=t.cpp11RawStringDelim.replace(/[^\w\s]/g,"\\$&");return e.match(new RegExp(".*?\\)"+r+'"'))?t.tokenize=null:e.skipToEnd(),"string"}function M(t,r){"string"==typeof t&&(t=[t]);var n=[];function i(e){if(e)for(var t in e)e.hasOwnProperty(t)&&n.push(t)}i(r.keywords),i(r.types),i(r.builtin),i(r.atoms),n.length&&(r.helperType=t[0],e.registerHelper("hintWords",t[0],n));for(var o=0;o<t.length;++o)e.defineMIME(t[o],r)}function T(e,t){for(var r=!1;!e.eol();){if(!r&&e.match('"""')){t.tokenize=null;break}r="\\"==e.next()&&!r}return"string"}function L(e){return function(t,r){for(var n;n=t.next();){if("*"==n&&t.eat("/")){if(1==e){r.tokenize=null;break}return r.tokenize=L(e-1),r.tokenize(t,r)}if("/"==n&&t.eat("*"))return r.tokenize=L(e+1),r.tokenize(t,r)}return"comment"}}function z(e){return function(t,r){for(var n,i=!1,o=!1;!t.eol();){if(!e&&!i&&t.match('"')){o=!0;break}if(e&&t.match('"""')){o=!0;break}n=t.next(),!i&&"$"==n&&t.match("{")&&t.skipTo("}"),i=!i&&"\\"==n&&!e}return!o&&e||(r.tokenize=null),"string"}}M(["text/x-csrc","text/x-c","text/x-chdr"],{name:"clike",keywords:a(l),types:m,blockKeywords:a(g),defKeywords:a(v),typeFirstDefinitions:!0,atoms:a("NULL true false"),isReservedIdentifier:b,hooks:{"#":y,"*":_},modeProps:{fold:["brace","include"]}}),M(["text/x-c++src","text/x-c++hdr"],{name:"clike",keywords:a(l+" "+c),types:m,blockKeywords:a(g+" class try catch"),defKeywords:a(v+" class namespace"),typeFirstDefinitions:!0,atoms:a("true false NULL nullptr"),dontIndentStatements:/^template$/,isIdentifierChar:/[\w\$_~\xa1-\uffff]/,isReservedIdentifier:b,hooks:{"#":y,"*":_,u:k,U:k,L:k,R:k,0:x,1:x,2:x,3:x,4:x,5:x,6:x,7:x,8:x,9:x,token:function(e,t,r){if("variable"==r&&"("==e.peek()&&(";"==t.prevToken||null==t.prevToken||"}"==t.prevToken)&&w(e.current()))return"def"}},namespaceSeparator:"::",modeProps:{fold:["brace","include"]}}),M("text/x-java",{name:"clike",keywords:a("abstract assert break case catch class const continue default do else enum extends final finally for goto if implements import instanceof interface native new package private protected public return static strictfp super switch synchronized this throw throws transient try volatile while @interface"),types:a("var byte short int long float double boolean char void Boolean Byte Character Double Float Integer Long Number Object Short String StringBuffer StringBuilder Void"),blockKeywords:a("catch class do else finally for if switch try while"),defKeywords:a("class interface enum @interface"),typeFirstDefinitions:!0,atoms:a("true false null"),number:/^(?:0x[a-f\d_]+|0b[01_]+|(?:[\d_]+\.?\d*|\.\d+)(?:e[-+]?[\d_]+)?)(u|ll?|l|f)?/i,hooks:{"@":function(e){return!e.match("interface",!1)&&(e.eatWhile(/[\w\$_]/),"meta")},'"':function(e,t){return!!e.match(/""$/)&&(t.tokenize=T,t.tokenize(e,t))}},modeProps:{fold:["brace","import"]}}),M("text/x-csharp",{name:"clike",keywords:a("abstract as async await base break case catch checked class const continue default delegate do else enum event explicit extern finally fixed for foreach goto if implicit in init interface internal is lock namespace new operator out override params private protected public readonly record ref required return sealed sizeof stackalloc static struct switch this throw try typeof unchecked unsafe using virtual void volatile while add alias ascending descending dynamic from get global group into join let orderby partial remove select set value var yield"),types:a("Action Boolean Byte Char DateTime DateTimeOffset Decimal Double Func Guid Int16 Int32 Int64 Object SByte Single String Task TimeSpan UInt16 UInt32 UInt64 bool byte char decimal double short int long object sbyte float string ushort uint ulong"),blockKeywords:a("catch class do else finally for foreach if struct switch try while"),defKeywords:a("class interface namespace record struct var"),typeFirstDefinitions:!0,atoms:a("true false null"),hooks:{"@":function(e,t){return e.eat('"')?(t.tokenize=S,S(e,t)):(e.eatWhile(/[\w\$_]/),"meta")}}}),M("text/x-scala",{name:"clike",keywords:a("abstract case catch class def do else extends final finally for forSome if implicit import lazy match new null object override package private protected return sealed super this throw trait try type val var while with yield _ assert assume require print println printf readLine readBoolean readByte readShort readChar readInt readLong readFloat readDouble"),types:a("AnyVal App Application Array BufferedIterator BigDecimal BigInt Char Console Either Enumeration Equiv Error Exception Fractional Function IndexedSeq Int Integral Iterable Iterator List Map Numeric Nil NotNull Option Ordered Ordering PartialFunction PartialOrdering Product Proxy Range Responder Seq Serializable Set Specializable Stream StringBuilder StringContext Symbol Throwable Traversable TraversableOnce Tuple Unit Vector Boolean Byte Character CharSequence Class ClassLoader Cloneable Comparable Compiler Double Exception Float Integer Long Math Number Object Package Pair Process Runtime Runnable SecurityManager Short StackTraceElement StrictMath String StringBuffer System Thread ThreadGroup ThreadLocal Throwable Triple Void"),multiLineStrings:!0,blockKeywords:a("catch class enum do else finally for forSome if match switch try while"),defKeywords:a("class enum def object package trait type val var"),atoms:a("true false null"),indentStatements:!1,indentSwitch:!1,isOperatorChar:/[+\-*&%=<>!?|\/#:@]/,hooks:{"@":function(e){return e.eatWhile(/[\w\$_]/),"meta"},'"':function(e,t){return!!e.match('""')&&(t.tokenize=T,t.tokenize(e,t))},"'":function(e){return e.match(/^(\\[^'\s]+|[^\\'])'/)?"string-2":(e.eatWhile(/[\w\$_\xa1-\uffff]/),"atom")},"=":function(e,r){var n=r.context;return!("}"!=n.type||!n.align||!e.eat(">"))&&(r.context=new t(n.indented,n.column,n.type,n.info,null,n.prev),"operator")},"/":function(e,t){return!!e.eat("*")&&(t.tokenize=L(1),t.tokenize(e,t))}},modeProps:{closeBrackets:{pairs:'()[]{}""',triples:'"'}}}),M("text/x-kotlin",{name:"clike",keywords:a("package as typealias class interface this super val operator var fun for is in This throw return annotation break continue object if else while do try when !in !is as? file import where by get set abstract enum open inner override private public internal protected catch finally out final vararg reified dynamic companion constructor init sealed field property receiver param sparam lateinit data inline noinline tailrec external annotation crossinline const operator infix suspend actual expect setparam value"),types:a("Boolean Byte Character CharSequence Class ClassLoader Cloneable Comparable Compiler Double Exception Float Integer Long Math Number Object Package Pair Process Runtime Runnable SecurityManager Short StackTraceElement StrictMath String StringBuffer System Thread ThreadGroup ThreadLocal Throwable Triple Void Annotation Any BooleanArray ByteArray Char CharArray DeprecationLevel DoubleArray Enum FloatArray Function Int IntArray Lazy LazyThreadSafetyMode LongArray Nothing ShortArray Unit"),intendSwitch:!1,indentStatements:!1,multiLineStrings:!0,number:/^(?:0x[a-f\d_]+|0b[01_]+|(?:[\d_]+(\.\d+)?|\.\d+)(?:e[-+]?[\d_]+)?)(u|ll?|l|f)?/i,blockKeywords:a("catch class do else finally for if where try while enum"),defKeywords:a("class val var object interface fun"),atoms:a("true false null this"),hooks:{"@":function(e){return e.eatWhile(/[\w\$_]/),"meta"},"*":function(e,t){return"."==t.prevToken?"variable":"operator"},'"':function(e,t){return t.tokenize=z(e.match('""')),t.tokenize(e,t)},"/":function(e,t){return!!e.eat("*")&&(t.tokenize=L(1),t.tokenize(e,t))},indent:function(e,t,r,n){var i=r&&r.charAt(0);return"}"!=e.prevToken&&")"!=e.prevToken||""!=r?"operator"==e.prevToken&&"}"!=r&&"}"!=e.context.type||"variable"==e.prevToken&&"."==i||("}"==e.prevToken||")"==e.prevToken)&&"."==i?2*n+t.indented:t.align&&"}"==t.type?t.indented+(e.context.type==(r||"").charAt(0)?0:n):void 0:e.indented}},modeProps:{closeBrackets:{triples:'"'}}}),M(["x-shader/x-vertex","x-shader/x-fragment"],{name:"clike",keywords:a("sampler1D sampler2D sampler3D samplerCube sampler1DShadow sampler2DShadow const attribute uniform varying break continue discard return for while do if else struct in out inout"),types:a("float int bool void vec2 vec3 vec4 ivec2 ivec3 ivec4 bvec2 bvec3 bvec4 mat2 mat3 mat4"),blockKeywords:a("for while do if else struct"),builtin:a("radians degrees sin cos tan asin acos atan pow exp log exp2 sqrt inversesqrt abs sign floor ceil fract mod min max clamp mix step smoothstep length distance dot cross normalize ftransform faceforward reflect refract matrixCompMult lessThan lessThanEqual greaterThan greaterThanEqual equal notEqual any all not texture1D texture1DProj texture1DLod texture1DProjLod texture2D texture2DProj texture2DLod texture2DProjLod texture3D texture3DProj texture3DLod texture3DProjLod textureCube textureCubeLod shadow1D shadow2D shadow1DProj shadow2DProj shadow1DLod shadow2DLod shadow1DProjLod shadow2DProjLod dFdx dFdy fwidth noise1 noise2 noise3 noise4"),atoms:a("true false gl_FragColor gl_SecondaryColor gl_Normal gl_Vertex gl_MultiTexCoord0 gl_MultiTexCoord1 gl_MultiTexCoord2 gl_MultiTexCoord3 gl_MultiTexCoord4 gl_MultiTexCoord5 gl_MultiTexCoord6 gl_MultiTexCoord7 gl_FogCoord gl_PointCoord gl_Position gl_PointSize gl_ClipVertex gl_FrontColor gl_BackColor gl_FrontSecondaryColor gl_BackSecondaryColor gl_TexCoord gl_FogFragCoord gl_FragCoord gl_FrontFacing gl_FragData gl_FragDepth gl_ModelViewMatrix gl_ProjectionMatrix gl_ModelViewProjectionMatrix gl_TextureMatrix gl_NormalMatrix gl_ModelViewMatrixInverse gl_ProjectionMatrixInverse gl_ModelViewProjectionMatrixInverse gl_TextureMatrixTranspose gl_ModelViewMatrixInverseTranspose gl_ProjectionMatrixInverseTranspose gl_ModelViewProjectionMatrixInverseTranspose gl_TextureMatrixInverseTranspose gl_NormalScale gl_DepthRange gl_ClipPlane gl_Point gl_FrontMaterial gl_BackMaterial gl_LightSource gl_LightModel gl_FrontLightModelProduct gl_BackLightModelProduct gl_TextureColor gl_EyePlaneS gl_EyePlaneT gl_EyePlaneR gl_EyePlaneQ gl_FogParameters gl_MaxLights gl_MaxClipPlanes gl_MaxTextureUnits gl_MaxTextureCoords gl_MaxVertexAttribs gl_MaxVertexUniformComponents gl_MaxVaryingFloats gl_MaxVertexTextureImageUnits gl_MaxTextureImageUnits gl_MaxFragmentUniformComponents gl_MaxCombineTextureImageUnits gl_MaxDrawBuffers"),indentSwitch:!1,hooks:{"#":y},modeProps:{fold:["brace","include"]}}),M("text/x-nesc",{name:"clike",keywords:a(l+" as atomic async call command component components configuration event generic implementation includes interface module new norace nx_struct nx_union post provides signal task uses abstract extends"),types:m,blockKeywords:a(g),atoms:a("null true false"),hooks:{"#":y},modeProps:{fold:["brace","include"]}}),M("text/x-objectivec",{name:"clike",keywords:a(l+" "+u),types:h,builtin:a(d),blockKeywords:a(g+" @synthesize @try @catch @finally @autoreleasepool @synchronized"),defKeywords:a(v+" @interface @implementation @protocol @class"),dontIndentStatements:/^@.*$/,typeFirstDefinitions:!0,atoms:a("YES NO NULL Nil nil true false nullptr"),isReservedIdentifier:b,hooks:{"#":y,"*":_},modeProps:{fold:["brace","include"]}}),M("text/x-objectivec++",{name:"clike",keywords:a(l+" "+u+" "+c),types:h,builtin:a(d),blockKeywords:a(g+" @synthesize @try @catch @finally @autoreleasepool @synchronized class try catch"),defKeywords:a(v+" @interface @implementation @protocol @class class namespace"),dontIndentStatements:/^@.*$|^template$/,typeFirstDefinitions:!0,atoms:a("YES NO NULL Nil nil true false nullptr"),isReservedIdentifier:b,hooks:{"#":y,"*":_,u:k,U:k,L:k,R:k,0:x,1:x,2:x,3:x,4:x,5:x,6:x,7:x,8:x,9:x,token:function(e,t,r){if("variable"==r&&"("==e.peek()&&(";"==t.prevToken||null==t.prevToken||"}"==t.prevToken)&&w(e.current()))return"def"}},namespaceSeparator:"::",modeProps:{fold:["brace","include"]}}),M("text/x-squirrel",{name:"clike",keywords:a("base break clone continue const default delete enum extends function in class foreach local resume return this throw typeof yield constructor instanceof static"),types:m,blockKeywords:a("case catch class else for foreach if switch try while"),defKeywords:a("function local class"),typeFirstDefinitions:!0,atoms:a("true false null"),hooks:{"#":y},modeProps:{fold:["brace","include"]}});var D=null;function A(e){return function(t,r){for(var n,i=!1,o=!1;!t.eol();){if(!i&&t.match('"')&&("single"==e||t.match('""'))){o=!0;break}if(!i&&t.match("``")){D=A(e),o=!0;break}n=t.next(),i="single"==e&&!i&&"\\"==n}return o&&(r.tokenize=null),"string"}}M("text/x-ceylon",{name:"clike",keywords:a("abstracts alias assembly assert assign break case catch class continue dynamic else exists extends finally for function given if import in interface is let module new nonempty object of out outer package return satisfies super switch then this throw try value void while"),types:function(e){var t=e.charAt(0);return t===t.toUpperCase()&&t!==t.toLowerCase()},blockKeywords:a("case catch class dynamic else finally for function if interface module new object switch try while"),defKeywords:a("class dynamic function interface module object package value"),builtin:a("abstract actual aliased annotation by default deprecated doc final formal late license native optional sealed see serializable shared suppressWarnings tagged throws variable"),isPunctuationChar:/[\[\]{}\(\),;\:\.`]/,isOperatorChar:/[+\-*&%=<>!?|^~:\/]/,numberStart:/[\d#$]/,number:/^(?:#[\da-fA-F_]+|\$[01_]+|[\d_]+[kMGTPmunpf]?|[\d_]+\.[\d_]+(?:[eE][-+]?\d+|[kMGTPmunpf]|)|)/i,multiLineStrings:!0,typeFirstDefinitions:!0,atoms:a("true false null larger smaller equal empty finished"),indentSwitch:!1,styleDefs:!1,hooks:{"@":function(e){return e.eatWhile(/[\w\$_]/),"meta"},'"':function(e,t){return t.tokenize=A(e.match('""')?"triple":"single"),t.tokenize(e,t)},"`":function(e,t){return!(!D||!e.match("`"))&&(t.tokenize=D,D=null,t.tokenize(e,t))},"'":function(e){return e.eatWhile(/[\w\$_\xa1-\uffff]/),"atom"},token:function(e,t,r){if(("variable"==r||"type"==r)&&"."==t.prevToken)return"variable-2"}},modeProps:{fold:["brace","import"],closeBrackets:{triples:'"'}}})}(r(237))},656:(e,t,r)=>{!function(e){"use strict";function t(e){for(var t={},r=0;r<e.length;++r)t[e[r].toLowerCase()]=!0;return t}e.defineMode("css",(function(t,r){var n=r.inline;r.propertyKeywords||(r=e.resolveMode("text/css"));var i,o,a=t.indentUnit,s=r.tokenHooks,l=r.documentTypes||{},c=r.mediaTypes||{},u=r.mediaFeatures||{},d=r.mediaValueKeywords||{},p=r.propertyKeywords||{},f=r.nonStandardPropertyKeywords||{},m=r.fontProperties||{},h=r.counterDescriptors||{},g=r.colorKeywords||{},v=r.valueKeywords||{},y=r.allowNested,_=r.lineComment,b=!0===r.supportsAtComponent,x=!1!==t.highlightNonStandardPropertyKeywords;function k(e,t){return i=t,e}function w(e,t){var r=e.next();if(s[r]){var n=s[r](e,t);if(!1!==n)return n}return"@"==r?(e.eatWhile(/[\w\\\-]/),k("def",e.current())):"="==r||("~"==r||"|"==r)&&e.eat("=")?k(null,"compare"):'"'==r||"'"==r?(t.tokenize=S(r),t.tokenize(e,t)):"#"==r?(e.eatWhile(/[\w\\\-]/),k("atom","hash")):"!"==r?(e.match(/^\s*\w*/),k("keyword","important")):/\d/.test(r)||"."==r&&e.eat(/\d/)?(e.eatWhile(/[\w.%]/),k("number","unit")):"-"!==r?/[,+>*\/]/.test(r)?k(null,"select-op"):"."==r&&e.match(/^-?[_a-z][_a-z0-9-]*/i)?k("qualifier","qualifier"):/[:;{}\[\]\(\)]/.test(r)?k(null,r):e.match(/^[\w-.]+(?=\()/)?(/^(url(-prefix)?|domain|regexp)$/i.test(e.current())&&(t.tokenize=C),k("variable callee","variable")):/[\w\\\-]/.test(r)?(e.eatWhile(/[\w\\\-]/),k("property","word")):k(null,null):/[\d.]/.test(e.peek())?(e.eatWhile(/[\w.%]/),k("number","unit")):e.match(/^-[\w\\\-]*/)?(e.eatWhile(/[\w\\\-]/),e.match(/^\s*:/,!1)?k("variable-2","variable-definition"):k("variable-2","variable")):e.match(/^\w+-/)?k("meta","meta"):void 0}function S(e){return function(t,r){for(var n,i=!1;null!=(n=t.next());){if(n==e&&!i){")"==e&&t.backUp(1);break}i=!i&&"\\"==n}return(n==e||!i&&")"!=e)&&(r.tokenize=null),k("string","string")}}function C(e,t){return e.next(),e.match(/^\s*[\"\')]/,!1)?t.tokenize=null:t.tokenize=S(")"),k(null,"(")}function M(e,t,r){this.type=e,this.indent=t,this.prev=r}function T(e,t,r,n){return e.context=new M(r,t.indentation()+(!1===n?0:a),e.context),r}function L(e){return e.context.prev&&(e.context=e.context.prev),e.context.type}function z(e,t,r){return q[r.context.type](e,t,r)}function D(e,t,r,n){for(var i=n||1;i>0;i--)r.context=r.context.prev;return z(e,t,r)}function A(e){var t=e.current().toLowerCase();o=v.hasOwnProperty(t)?"atom":g.hasOwnProperty(t)?"keyword":"variable"}var q={top:function(e,t,r){if("{"==e)return T(r,t,"block");if("}"==e&&r.context.prev)return L(r);if(b&&/@component/i.test(e))return T(r,t,"atComponentBlock");if(/^@(-moz-)?document$/i.test(e))return T(r,t,"documentTypes");if(/^@(media|supports|(-moz-)?document|import)$/i.test(e))return T(r,t,"atBlock");if(/^@(font-face|counter-style)/i.test(e))return r.stateArg=e,"restricted_atBlock_before";if(/^@(-(moz|ms|o|webkit)-)?keyframes$/i.test(e))return"keyframes";if(e&&"@"==e.charAt(0))return T(r,t,"at");if("hash"==e)o="builtin";else if("word"==e)o="tag";else{if("variable-definition"==e)return"maybeprop";if("interpolation"==e)return T(r,t,"interpolation");if(":"==e)return"pseudo";if(y&&"("==e)return T(r,t,"parens")}return r.context.type},block:function(e,t,r){if("word"==e){var n=t.current().toLowerCase();return p.hasOwnProperty(n)?(o="property","maybeprop"):f.hasOwnProperty(n)?(o=x?"string-2":"property","maybeprop"):y?(o=t.match(/^\s*:(?:\s|$)/,!1)?"property":"tag","block"):(o+=" error","maybeprop")}return"meta"==e?"block":y||"hash"!=e&&"qualifier"!=e?q.top(e,t,r):(o="error","block")},maybeprop:function(e,t,r){return":"==e?T(r,t,"prop"):z(e,t,r)},prop:function(e,t,r){if(";"==e)return L(r);if("{"==e&&y)return T(r,t,"propBlock");if("}"==e||"{"==e)return D(e,t,r);if("("==e)return T(r,t,"parens");if("hash"!=e||/^#([0-9a-fA-F]{3,4}|[0-9a-fA-F]{6}|[0-9a-fA-F]{8})$/.test(t.current())){if("word"==e)A(t);else if("interpolation"==e)return T(r,t,"interpolation")}else o+=" error";return"prop"},propBlock:function(e,t,r){return"}"==e?L(r):"word"==e?(o="property","maybeprop"):r.context.type},parens:function(e,t,r){return"{"==e||"}"==e?D(e,t,r):")"==e?L(r):"("==e?T(r,t,"parens"):"interpolation"==e?T(r,t,"interpolation"):("word"==e&&A(t),"parens")},pseudo:function(e,t,r){return"meta"==e?"pseudo":"word"==e?(o="variable-3",r.context.type):z(e,t,r)},documentTypes:function(e,t,r){return"word"==e&&l.hasOwnProperty(t.current())?(o="tag",r.context.type):q.atBlock(e,t,r)},atBlock:function(e,t,r){if("("==e)return T(r,t,"atBlock_parens");if("}"==e||";"==e)return D(e,t,r);if("{"==e)return L(r)&&T(r,t,y?"block":"top");if("interpolation"==e)return T(r,t,"interpolation");if("word"==e){var n=t.current().toLowerCase();o="only"==n||"not"==n||"and"==n||"or"==n?"keyword":c.hasOwnProperty(n)?"attribute":u.hasOwnProperty(n)?"property":d.hasOwnProperty(n)?"keyword":p.hasOwnProperty(n)?"property":f.hasOwnProperty(n)?x?"string-2":"property":v.hasOwnProperty(n)?"atom":g.hasOwnProperty(n)?"keyword":"error"}return r.context.type},atComponentBlock:function(e,t,r){return"}"==e?D(e,t,r):"{"==e?L(r)&&T(r,t,y?"block":"top",!1):("word"==e&&(o="error"),r.context.type)},atBlock_parens:function(e,t,r){return")"==e?L(r):"{"==e||"}"==e?D(e,t,r,2):q.atBlock(e,t,r)},restricted_atBlock_before:function(e,t,r){return"{"==e?T(r,t,"restricted_atBlock"):"word"==e&&"@counter-style"==r.stateArg?(o="variable","restricted_atBlock_before"):z(e,t,r)},restricted_atBlock:function(e,t,r){return"}"==e?(r.stateArg=null,L(r)):"word"==e?(o="@font-face"==r.stateArg&&!m.hasOwnProperty(t.current().toLowerCase())||"@counter-style"==r.stateArg&&!h.hasOwnProperty(t.current().toLowerCase())?"error":"property","maybeprop"):"restricted_atBlock"},keyframes:function(e,t,r){return"word"==e?(o="variable","keyframes"):"{"==e?T(r,t,"top"):z(e,t,r)},at:function(e,t,r){return";"==e?L(r):"{"==e||"}"==e?D(e,t,r):("word"==e?o="tag":"hash"==e&&(o="builtin"),"at")},interpolation:function(e,t,r){return"}"==e?L(r):"{"==e||";"==e?D(e,t,r):("word"==e?o="variable":"variable"!=e&&"("!=e&&")"!=e&&(o="error"),"interpolation")}};return{startState:function(e){return{tokenize:null,state:n?"block":"top",stateArg:null,context:new M(n?"block":"top",e||0,null)}},token:function(e,t){if(!t.tokenize&&e.eatSpace())return null;var r=(t.tokenize||w)(e,t);return r&&"object"==typeof r&&(i=r[1],r=r[0]),o=r,"comment"!=i&&(t.state=q[t.state](i,e,t)),o},indent:function(e,t){var r=e.context,n=t&&t.charAt(0),i=r.indent;return"prop"!=r.type||"}"!=n&&")"!=n||(r=r.prev),r.prev&&("}"!=n||"block"!=r.type&&"top"!=r.type&&"interpolation"!=r.type&&"restricted_atBlock"!=r.type?(")"!=n||"parens"!=r.type&&"atBlock_parens"!=r.type)&&("{"!=n||"at"!=r.type&&"atBlock"!=r.type)||(i=Math.max(0,r.indent-a)):i=(r=r.prev).indent),i},electricChars:"}",blockCommentStart:"/*",blockCommentEnd:"*/",blockCommentContinue:" * ",lineComment:_,fold:"brace"}}));var r=["domain","regexp","url","url-prefix"],n=t(r),i=["all","aural","braille","handheld","print","projection","screen","tty","tv","embossed"],o=t(i),a=["width","min-width","max-width","height","min-height","max-height","device-width","min-device-width","max-device-width","device-height","min-device-height","max-device-height","aspect-ratio","min-aspect-ratio","max-aspect-ratio","device-aspect-ratio","min-device-aspect-ratio","max-device-aspect-ratio","color","min-color","max-color","color-index","min-color-index","max-color-index","monochrome","min-monochrome","max-monochrome","resolution","min-resolution","max-resolution","scan","grid","orientation","device-pixel-ratio","min-device-pixel-ratio","max-device-pixel-ratio","pointer","any-pointer","hover","any-hover","prefers-color-scheme","dynamic-range","video-dynamic-range"],s=t(a),l=["landscape","portrait","none","coarse","fine","on-demand","hover","interlace","progressive","dark","light","standard","high"],c=t(l),u=["align-content","align-items","align-self","alignment-adjust","alignment-baseline","all","anchor-point","animation","animation-delay","animation-direction","animation-duration","animation-fill-mode","animation-iteration-count","animation-name","animation-play-state","animation-timing-function","appearance","azimuth","backdrop-filter","backface-visibility","background","background-attachment","background-blend-mode","background-clip","background-color","background-image","background-origin","background-position","background-position-x","background-position-y","background-repeat","background-size","baseline-shift","binding","bleed","block-size","bookmark-label","bookmark-level","bookmark-state","bookmark-target","border","border-bottom","border-bottom-color","border-bottom-left-radius","border-bottom-right-radius","border-bottom-style","border-bottom-width","border-collapse","border-color","border-image","border-image-outset","border-image-repeat","border-image-slice","border-image-source","border-image-width","border-left","border-left-color","border-left-style","border-left-width","border-radius","border-right","border-right-color","border-right-style","border-right-width","border-spacing","border-style","border-top","border-top-color","border-top-left-radius","border-top-right-radius","border-top-style","border-top-width","border-width","bottom","box-decoration-break","box-shadow","box-sizing","break-after","break-before","break-inside","caption-side","caret-color","clear","clip","color","color-profile","column-count","column-fill","column-gap","column-rule","column-rule-color","column-rule-style","column-rule-width","column-span","column-width","columns","contain","content","counter-increment","counter-reset","crop","cue","cue-after","cue-before","cursor","direction","display","dominant-baseline","drop-initial-after-adjust","drop-initial-after-align","drop-initial-before-adjust","drop-initial-before-align","drop-initial-size","drop-initial-value","elevation","empty-cells","fit","fit-content","fit-position","flex","flex-basis","flex-direction","flex-flow","flex-grow","flex-shrink","flex-wrap","float","float-offset","flow-from","flow-into","font","font-family","font-feature-settings","font-kerning","font-language-override","font-optical-sizing","font-size","font-size-adjust","font-stretch","font-style","font-synthesis","font-variant","font-variant-alternates","font-variant-caps","font-variant-east-asian","font-variant-ligatures","font-variant-numeric","font-variant-position","font-variation-settings","font-weight","gap","grid","grid-area","grid-auto-columns","grid-auto-flow","grid-auto-rows","grid-column","grid-column-end","grid-column-gap","grid-column-start","grid-gap","grid-row","grid-row-end","grid-row-gap","grid-row-start","grid-template","grid-template-areas","grid-template-columns","grid-template-rows","hanging-punctuation","height","hyphens","icon","image-orientation","image-rendering","image-resolution","inline-box-align","inset","inset-block","inset-block-end","inset-block-start","inset-inline","inset-inline-end","inset-inline-start","isolation","justify-content","justify-items","justify-self","left","letter-spacing","line-break","line-height","line-height-step","line-stacking","line-stacking-ruby","line-stacking-shift","line-stacking-strategy","list-style","list-style-image","list-style-position","list-style-type","margin","margin-bottom","margin-left","margin-right","margin-top","marks","marquee-direction","marquee-loop","marquee-play-count","marquee-speed","marquee-style","mask-clip","mask-composite","mask-image","mask-mode","mask-origin","mask-position","mask-repeat","mask-size","mask-type","max-block-size","max-height","max-inline-size","max-width","min-block-size","min-height","min-inline-size","min-width","mix-blend-mode","move-to","nav-down","nav-index","nav-left","nav-right","nav-up","object-fit","object-position","offset","offset-anchor","offset-distance","offset-path","offset-position","offset-rotate","opacity","order","orphans","outline","outline-color","outline-offset","outline-style","outline-width","overflow","overflow-style","overflow-wrap","overflow-x","overflow-y","padding","padding-bottom","padding-left","padding-right","padding-top","page","page-break-after","page-break-before","page-break-inside","page-policy","pause","pause-after","pause-before","perspective","perspective-origin","pitch","pitch-range","place-content","place-items","place-self","play-during","position","presentation-level","punctuation-trim","quotes","region-break-after","region-break-before","region-break-inside","region-fragment","rendering-intent","resize","rest","rest-after","rest-before","richness","right","rotate","rotation","rotation-point","row-gap","ruby-align","ruby-overhang","ruby-position","ruby-span","scale","scroll-behavior","scroll-margin","scroll-margin-block","scroll-margin-block-end","scroll-margin-block-start","scroll-margin-bottom","scroll-margin-inline","scroll-margin-inline-end","scroll-margin-inline-start","scroll-margin-left","scroll-margin-right","scroll-margin-top","scroll-padding","scroll-padding-block","scroll-padding-block-end","scroll-padding-block-start","scroll-padding-bottom","scroll-padding-inline","scroll-padding-inline-end","scroll-padding-inline-start","scroll-padding-left","scroll-padding-right","scroll-padding-top","scroll-snap-align","scroll-snap-type","shape-image-threshold","shape-inside","shape-margin","shape-outside","size","speak","speak-as","speak-header","speak-numeral","speak-punctuation","speech-rate","stress","string-set","tab-size","table-layout","target","target-name","target-new","target-position","text-align","text-align-last","text-combine-upright","text-decoration","text-decoration-color","text-decoration-line","text-decoration-skip","text-decoration-skip-ink","text-decoration-style","text-emphasis","text-emphasis-color","text-emphasis-position","text-emphasis-style","text-height","text-indent","text-justify","text-orientation","text-outline","text-overflow","text-rendering","text-shadow","text-size-adjust","text-space-collapse","text-transform","text-underline-position","text-wrap","top","touch-action","transform","transform-origin","transform-style","transition","transition-delay","transition-duration","transition-property","transition-timing-function","translate","unicode-bidi","user-select","vertical-align","visibility","voice-balance","voice-duration","voice-family","voice-pitch","voice-range","voice-rate","voice-stress","voice-volume","volume","white-space","widows","width","will-change","word-break","word-spacing","word-wrap","writing-mode","z-index","clip-path","clip-rule","mask","enable-background","filter","flood-color","flood-opacity","lighting-color","stop-color","stop-opacity","pointer-events","color-interpolation","color-interpolation-filters","color-rendering","fill","fill-opacity","fill-rule","image-rendering","marker","marker-end","marker-mid","marker-start","paint-order","shape-rendering","stroke","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","text-rendering","baseline-shift","dominant-baseline","glyph-orientation-horizontal","glyph-orientation-vertical","text-anchor","writing-mode"],d=t(u),p=["accent-color","aspect-ratio","border-block","border-block-color","border-block-end","border-block-end-color","border-block-end-style","border-block-end-width","border-block-start","border-block-start-color","border-block-start-style","border-block-start-width","border-block-style","border-block-width","border-inline","border-inline-color","border-inline-end","border-inline-end-color","border-inline-end-style","border-inline-end-width","border-inline-start","border-inline-start-color","border-inline-start-style","border-inline-start-width","border-inline-style","border-inline-width","content-visibility","margin-block","margin-block-end","margin-block-start","margin-inline","margin-inline-end","margin-inline-start","overflow-anchor","overscroll-behavior","padding-block","padding-block-end","padding-block-start","padding-inline","padding-inline-end","padding-inline-start","scroll-snap-stop","scrollbar-3d-light-color","scrollbar-arrow-color","scrollbar-base-color","scrollbar-dark-shadow-color","scrollbar-face-color","scrollbar-highlight-color","scrollbar-shadow-color","scrollbar-track-color","searchfield-cancel-button","searchfield-decoration","searchfield-results-button","searchfield-results-decoration","shape-inside","zoom"],f=t(p),m=t(["font-display","font-family","src","unicode-range","font-variant","font-feature-settings","font-stretch","font-weight","font-style"]),h=t(["additive-symbols","fallback","negative","pad","prefix","range","speak-as","suffix","symbols","system"]),g=["aliceblue","antiquewhite","aqua","aquamarine","azure","beige","bisque","black","blanchedalmond","blue","blueviolet","brown","burlywood","cadetblue","chartreuse","chocolate","coral","cornflowerblue","cornsilk","crimson","cyan","darkblue","darkcyan","darkgoldenrod","darkgray","darkgreen","darkgrey","darkkhaki","darkmagenta","darkolivegreen","darkorange","darkorchid","darkred","darksalmon","darkseagreen","darkslateblue","darkslategray","darkslategrey","darkturquoise","darkviolet","deeppink","deepskyblue","dimgray","dimgrey","dodgerblue","firebrick","floralwhite","forestgreen","fuchsia","gainsboro","ghostwhite","gold","goldenrod","gray","grey","green","greenyellow","honeydew","hotpink","indianred","indigo","ivory","khaki","lavender","lavenderblush","lawngreen","lemonchiffon","lightblue","lightcoral","lightcyan","lightgoldenrodyellow","lightgray","lightgreen","lightgrey","lightpink","lightsalmon","lightseagreen","lightskyblue","lightslategray","lightslategrey","lightsteelblue","lightyellow","lime","limegreen","linen","magenta","maroon","mediumaquamarine","mediumblue","mediumorchid","mediumpurple","mediumseagreen","mediumslateblue","mediumspringgreen","mediumturquoise","mediumvioletred","midnightblue","mintcream","mistyrose","moccasin","navajowhite","navy","oldlace","olive","olivedrab","orange","orangered","orchid","palegoldenrod","palegreen","paleturquoise","palevioletred","papayawhip","peachpuff","peru","pink","plum","powderblue","purple","rebeccapurple","red","rosybrown","royalblue","saddlebrown","salmon","sandybrown","seagreen","seashell","sienna","silver","skyblue","slateblue","slategray","slategrey","snow","springgreen","steelblue","tan","teal","thistle","tomato","turquoise","violet","wheat","white","whitesmoke","yellow","yellowgreen"],v=t(g),y=["above","absolute","activeborder","additive","activecaption","afar","after-white-space","ahead","alias","all","all-scroll","alphabetic","alternate","always","amharic","amharic-abegede","antialiased","appworkspace","arabic-indic","armenian","asterisks","attr","auto","auto-flow","avoid","avoid-column","avoid-page","avoid-region","axis-pan","background","backwards","baseline","below","bidi-override","binary","bengali","blink","block","block-axis","blur","bold","bolder","border","border-box","both","bottom","break","break-all","break-word","brightness","bullets","button","buttonface","buttonhighlight","buttonshadow","buttontext","calc","cambodian","capitalize","caps-lock-indicator","caption","captiontext","caret","cell","center","checkbox","circle","cjk-decimal","cjk-earthly-branch","cjk-heavenly-stem","cjk-ideographic","clear","clip","close-quote","col-resize","collapse","color","color-burn","color-dodge","column","column-reverse","compact","condensed","conic-gradient","contain","content","contents","content-box","context-menu","continuous","contrast","copy","counter","counters","cover","crop","cross","crosshair","cubic-bezier","currentcolor","cursive","cyclic","darken","dashed","decimal","decimal-leading-zero","default","default-button","dense","destination-atop","destination-in","destination-out","destination-over","devanagari","difference","disc","discard","disclosure-closed","disclosure-open","document","dot-dash","dot-dot-dash","dotted","double","down","drop-shadow","e-resize","ease","ease-in","ease-in-out","ease-out","element","ellipse","ellipsis","embed","end","ethiopic","ethiopic-abegede","ethiopic-abegede-am-et","ethiopic-abegede-gez","ethiopic-abegede-ti-er","ethiopic-abegede-ti-et","ethiopic-halehame-aa-er","ethiopic-halehame-aa-et","ethiopic-halehame-am-et","ethiopic-halehame-gez","ethiopic-halehame-om-et","ethiopic-halehame-sid-et","ethiopic-halehame-so-et","ethiopic-halehame-ti-er","ethiopic-halehame-ti-et","ethiopic-halehame-tig","ethiopic-numeric","ew-resize","exclusion","expanded","extends","extra-condensed","extra-expanded","fantasy","fast","fill","fill-box","fixed","flat","flex","flex-end","flex-start","footnotes","forwards","from","geometricPrecision","georgian","grayscale","graytext","grid","groove","gujarati","gurmukhi","hand","hangul","hangul-consonant","hard-light","hebrew","help","hidden","hide","higher","highlight","highlighttext","hiragana","hiragana-iroha","horizontal","hsl","hsla","hue","hue-rotate","icon","ignore","inactiveborder","inactivecaption","inactivecaptiontext","infinite","infobackground","infotext","inherit","initial","inline","inline-axis","inline-block","inline-flex","inline-grid","inline-table","inset","inside","intrinsic","invert","italic","japanese-formal","japanese-informal","justify","kannada","katakana","katakana-iroha","keep-all","khmer","korean-hangul-formal","korean-hanja-formal","korean-hanja-informal","landscape","lao","large","larger","left","level","lighter","lighten","line-through","linear","linear-gradient","lines","list-item","listbox","listitem","local","logical","loud","lower","lower-alpha","lower-armenian","lower-greek","lower-hexadecimal","lower-latin","lower-norwegian","lower-roman","lowercase","ltr","luminosity","malayalam","manipulation","match","matrix","matrix3d","media-play-button","media-slider","media-sliderthumb","media-volume-slider","media-volume-sliderthumb","medium","menu","menulist","menulist-button","menutext","message-box","middle","min-intrinsic","mix","mongolian","monospace","move","multiple","multiple_mask_images","multiply","myanmar","n-resize","narrower","ne-resize","nesw-resize","no-close-quote","no-drop","no-open-quote","no-repeat","none","normal","not-allowed","nowrap","ns-resize","numbers","numeric","nw-resize","nwse-resize","oblique","octal","opacity","open-quote","optimizeLegibility","optimizeSpeed","oriya","oromo","outset","outside","outside-shape","overlay","overline","padding","padding-box","painted","page","paused","persian","perspective","pinch-zoom","plus-darker","plus-lighter","pointer","polygon","portrait","pre","pre-line","pre-wrap","preserve-3d","progress","push-button","radial-gradient","radio","read-only","read-write","read-write-plaintext-only","rectangle","region","relative","repeat","repeating-linear-gradient","repeating-radial-gradient","repeating-conic-gradient","repeat-x","repeat-y","reset","reverse","rgb","rgba","ridge","right","rotate","rotate3d","rotateX","rotateY","rotateZ","round","row","row-resize","row-reverse","rtl","run-in","running","s-resize","sans-serif","saturate","saturation","scale","scale3d","scaleX","scaleY","scaleZ","screen","scroll","scrollbar","scroll-position","se-resize","searchfield","searchfield-cancel-button","searchfield-decoration","searchfield-results-button","searchfield-results-decoration","self-start","self-end","semi-condensed","semi-expanded","separate","sepia","serif","show","sidama","simp-chinese-formal","simp-chinese-informal","single","skew","skewX","skewY","skip-white-space","slide","slider-horizontal","slider-vertical","sliderthumb-horizontal","sliderthumb-vertical","slow","small","small-caps","small-caption","smaller","soft-light","solid","somali","source-atop","source-in","source-out","source-over","space","space-around","space-between","space-evenly","spell-out","square","square-button","start","static","status-bar","stretch","stroke","stroke-box","sub","subpixel-antialiased","svg_masks","super","sw-resize","symbolic","symbols","system-ui","table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row","table-row-group","tamil","telugu","text","text-bottom","text-top","textarea","textfield","thai","thick","thin","threeddarkshadow","threedface","threedhighlight","threedlightshadow","threedshadow","tibetan","tigre","tigrinya-er","tigrinya-er-abegede","tigrinya-et","tigrinya-et-abegede","to","top","trad-chinese-formal","trad-chinese-informal","transform","translate","translate3d","translateX","translateY","translateZ","transparent","ultra-condensed","ultra-expanded","underline","unidirectional-pan","unset","up","upper-alpha","upper-armenian","upper-greek","upper-hexadecimal","upper-latin","upper-norwegian","upper-roman","uppercase","urdu","url","var","vertical","vertical-text","view-box","visible","visibleFill","visiblePainted","visibleStroke","visual","w-resize","wait","wave","wider","window","windowframe","windowtext","words","wrap","wrap-reverse","x-large","x-small","xor","xx-large","xx-small"],_=t(y),b=r.concat(i).concat(a).concat(l).concat(u).concat(p).concat(g).concat(y);function x(e,t){for(var r,n=!1;null!=(r=e.next());){if(n&&"/"==r){t.tokenize=null;break}n="*"==r}return["comment","comment"]}e.registerHelper("hintWords","css",b),e.defineMIME("text/css",{documentTypes:n,mediaTypes:o,mediaFeatures:s,mediaValueKeywords:c,propertyKeywords:d,nonStandardPropertyKeywords:f,fontProperties:m,counterDescriptors:h,colorKeywords:v,valueKeywords:_,tokenHooks:{"/":function(e,t){return!!e.eat("*")&&(t.tokenize=x,x(e,t))}},name:"css"}),e.defineMIME("text/x-scss",{mediaTypes:o,mediaFeatures:s,mediaValueKeywords:c,propertyKeywords:d,nonStandardPropertyKeywords:f,colorKeywords:v,valueKeywords:_,fontProperties:m,allowNested:!0,lineComment:"//",tokenHooks:{"/":function(e,t){return e.eat("/")?(e.skipToEnd(),["comment","comment"]):e.eat("*")?(t.tokenize=x,x(e,t)):["operator","operator"]},":":function(e){return!!e.match(/^\s*\{/,!1)&&[null,null]},$:function(e){return e.match(/^[\w-]+/),e.match(/^\s*:/,!1)?["variable-2","variable-definition"]:["variable-2","variable"]},"#":function(e){return!!e.eat("{")&&[null,"interpolation"]}},name:"css",helperType:"scss"}),e.defineMIME("text/x-less",{mediaTypes:o,mediaFeatures:s,mediaValueKeywords:c,propertyKeywords:d,nonStandardPropertyKeywords:f,colorKeywords:v,valueKeywords:_,fontProperties:m,allowNested:!0,lineComment:"//",tokenHooks:{"/":function(e,t){return e.eat("/")?(e.skipToEnd(),["comment","comment"]):e.eat("*")?(t.tokenize=x,x(e,t)):["operator","operator"]},"@":function(e){return e.eat("{")?[null,"interpolation"]:!e.match(/^(charset|document|font-face|import|(-(moz|ms|o|webkit)-)?keyframes|media|namespace|page|supports)\b/i,!1)&&(e.eatWhile(/[\w\\\-]/),e.match(/^\s*:/,!1)?["variable-2","variable-definition"]:["variable-2","variable"])},"&":function(){return["atom","atom"]}},name:"css",helperType:"less"}),e.defineMIME("text/x-gss",{documentTypes:n,mediaTypes:o,mediaFeatures:s,propertyKeywords:d,nonStandardPropertyKeywords:f,fontProperties:m,counterDescriptors:h,colorKeywords:v,valueKeywords:_,supportsAtComponent:!0,tokenHooks:{"/":function(e,t){return!!e.eat("*")&&(t.tokenize=x,x(e,t))}},name:"css",helperType:"gss"})}(r(237))},838:(e,t,r)=>{!function(e){"use strict";var t="from",r=new RegExp("^(\\s*)\\b("+t+")\\b","i"),n=["run","cmd","entrypoint","shell"],i=new RegExp("^(\\s*)("+n.join("|")+")(\\s+\\[)","i"),o="expose",a=new RegExp("^(\\s*)("+o+")(\\s+)","i"),s=["arg","from","maintainer","label","env","add","copy","volume","user","workdir","onbuild","stopsignal","healthcheck","shell"],l="("+[t,o].concat(n).concat(s).join("|")+")",c=new RegExp("^(\\s*)"+l+"(\\s*)(#.*)?$","i"),u=new RegExp("^(\\s*)"+l+"(\\s+)","i");e.defineSimpleMode("dockerfile",{start:[{regex:/^\s*#.*$/,sol:!0,token:"comment"},{regex:r,token:[null,"keyword"],sol:!0,next:"from"},{regex:c,token:[null,"keyword",null,"error"],sol:!0},{regex:i,token:[null,"keyword",null],sol:!0,next:"array"},{regex:a,token:[null,"keyword",null],sol:!0,next:"expose"},{regex:u,token:[null,"keyword",null],sol:!0,next:"arguments"},{regex:/./,token:null}],from:[{regex:/\s*$/,token:null,next:"start"},{regex:/(\s*)(#.*)$/,token:[null,"error"],next:"start"},{regex:/(\s*\S+\s+)(as)/i,token:[null,"keyword"],next:"start"},{token:null,next:"start"}],single:[{regex:/(?:[^\\']|\\.)/,token:"string"},{regex:/'/,token:"string",pop:!0}],double:[{regex:/(?:[^\\"]|\\.)/,token:"string"},{regex:/"/,token:"string",pop:!0}],array:[{regex:/\]/,token:null,next:"start"},{regex:/"(?:[^\\"]|\\.)*"?/,token:"string"}],expose:[{regex:/\d+$/,token:"number",next:"start"},{regex:/[^\d]+$/,token:null,next:"start"},{regex:/\d+/,token:"number"},{regex:/[^\d]+/,token:null},{token:null,next:"start"}],arguments:[{regex:/^\s*#.*$/,sol:!0,token:"comment"},{regex:/"(?:[^\\"]|\\.)*"?$/,token:"string",next:"start"},{regex:/"/,token:"string",push:"double"},{regex:/'(?:[^\\']|\\.)*'?$/,token:"string",next:"start"},{regex:/'/,token:"string",push:"single"},{regex:/[^#"']+[\\`]$/,token:null},{regex:/[^#"']+$/,token:null,next:"start"},{regex:/[^#"']+/,token:null},{token:null,next:"start"}],meta:{lineComment:"#"}}),e.defineMIME("text/x-dockerfile","dockerfile")}(r(237),r(856))},520:(e,t,r)=>{!function(e){"use strict";var t={script:[["lang",/(javascript|babel)/i,"javascript"],["type",/^(?:text|application)\/(?:x-)?(?:java|ecma)script$|^module$|^$/i,"javascript"],["type",/./,"text/plain"],[null,null,"javascript"]],style:[["lang",/^css$/i,"css"],["type",/^(text\/)?(x-)?(stylesheet|css)$/i,"css"],["type",/./,"text/plain"],[null,null,"css"]]};function r(e,t,r){var n=e.current(),i=n.search(t);return i>-1?e.backUp(n.length-i):n.match(/<\/?$/)&&(e.backUp(n.length),e.match(t,!1)||e.match(n)),r}var n={};function i(e){var t=n[e];return t||(n[e]=new RegExp("\\s+"+e+"\\s*=\\s*('|\")?([^'\"]+)('|\")?\\s*"))}function o(e,t){var r=e.match(i(t));return r?/^\s*(.*?)\s*$/.exec(r[2])[1]:""}function a(e,t){return new RegExp((t?"^":"")+"</\\s*"+e+"\\s*>","i")}function s(e,t){for(var r in e)for(var n=t[r]||(t[r]=[]),i=e[r],o=i.length-1;o>=0;o--)n.unshift(i[o])}function l(e,t){for(var r=0;r<e.length;r++){var n=e[r];if(!n[0]||n[1].test(o(t,n[0])))return n[2]}}e.defineMode("htmlmixed",(function(n,i){var o=e.getMode(n,{name:"xml",htmlMode:!0,multilineTagIndentFactor:i.multilineTagIndentFactor,multilineTagIndentPastTag:i.multilineTagIndentPastTag,allowMissingTagName:i.allowMissingTagName}),c={},u=i&&i.tags,d=i&&i.scriptTypes;if(s(t,c),u&&s(u,c),d)for(var p=d.length-1;p>=0;p--)c.script.unshift(["type",d[p].matches,d[p].mode]);function f(t,i){var s,u=o.token(t,i.htmlState),d=/\btag\b/.test(u);if(d&&!/[<>\s\/]/.test(t.current())&&(s=i.htmlState.tagName&&i.htmlState.tagName.toLowerCase())&&c.hasOwnProperty(s))i.inTag=s+" ";else if(i.inTag&&d&&/>$/.test(t.current())){var p=/^([\S]+) (.*)/.exec(i.inTag);i.inTag=null;var m=">"==t.current()&&l(c[p[1]],p[2]),h=e.getMode(n,m),g=a(p[1],!0),v=a(p[1],!1);i.token=function(e,t){return e.match(g,!1)?(t.token=f,t.localState=t.localMode=null,null):r(e,v,t.localMode.token(e,t.localState))},i.localMode=h,i.localState=e.startState(h,o.indent(i.htmlState,"",""))}else i.inTag&&(i.inTag+=t.current(),t.eol()&&(i.inTag+=" "));return u}return{startState:function(){return{token:f,inTag:null,localMode:null,localState:null,htmlState:e.startState(o)}},copyState:function(t){var r;return t.localState&&(r=e.copyState(t.localMode,t.localState)),{token:t.token,inTag:t.inTag,localMode:t.localMode,localState:r,htmlState:e.copyState(o,t.htmlState)}},token:function(e,t){return t.token(e,t)},indent:function(t,r,n){return!t.localMode||/^\s*<\//.test(r)?o.indent(t.htmlState,r,n):t.localMode.indent?t.localMode.indent(t.localState,r,n):e.Pass},innerMode:function(e){return{state:e.localState||e.htmlState,mode:e.localMode||o}}}}),"xml","javascript","css"),e.defineMIME("text/html","htmlmixed")}(r(237),r(576),r(792),r(656))},792:(e,t,r)=>{!function(e){"use strict";e.defineMode("javascript",(function(t,r){var n,i,o=t.indentUnit,a=r.statementIndent,s=r.jsonld,l=r.json||s,c=!1!==r.trackScope,u=r.typescript,d=r.wordCharacters||/[\w$\xa1-\uffff]/,p=function(){function e(e){return{type:e,style:"keyword"}}var t=e("keyword a"),r=e("keyword b"),n=e("keyword c"),i=e("keyword d"),o=e("operator"),a={type:"atom",style:"atom"};return{if:e("if"),while:t,with:t,else:r,do:r,try:r,finally:r,return:i,break:i,continue:i,new:e("new"),delete:n,void:n,throw:n,debugger:e("debugger"),var:e("var"),const:e("var"),let:e("var"),function:e("function"),catch:e("catch"),for:e("for"),switch:e("switch"),case:e("case"),default:e("default"),in:o,typeof:o,instanceof:o,true:a,false:a,null:a,undefined:a,NaN:a,Infinity:a,this:e("this"),class:e("class"),super:e("atom"),yield:n,export:e("export"),import:e("import"),extends:n,await:n}}(),f=/[+\-*&%=<>!?|~^@]/,m=/^@(context|id|value|language|type|container|list|set|reverse|index|base|vocab|graph)"/;function h(e){for(var t,r=!1,n=!1;null!=(t=e.next());){if(!r){if("/"==t&&!n)return;"["==t?n=!0:n&&"]"==t&&(n=!1)}r=!r&&"\\"==t}}function g(e,t,r){return n=e,i=r,t}function v(e,t){var r=e.next();if('"'==r||"'"==r)return t.tokenize=y(r),t.tokenize(e,t);if("."==r&&e.match(/^\d[\d_]*(?:[eE][+\-]?[\d_]+)?/))return g("number","number");if("."==r&&e.match(".."))return g("spread","meta");if(/[\[\]{}\(\),;\:\.]/.test(r))return g(r);if("="==r&&e.eat(">"))return g("=>","operator");if("0"==r&&e.match(/^(?:x[\dA-Fa-f_]+|o[0-7_]+|b[01_]+)n?/))return g("number","number");if(/\d/.test(r))return e.match(/^[\d_]*(?:n|(?:\.[\d_]*)?(?:[eE][+\-]?[\d_]+)?)?/),g("number","number");if("/"==r)return e.eat("*")?(t.tokenize=_,_(e,t)):e.eat("/")?(e.skipToEnd(),g("comment","comment")):it(e,t,1)?(h(e),e.match(/^\b(([gimyus])(?![gimyus]*\2))+\b/),g("regexp","string-2")):(e.eat("="),g("operator","operator",e.current()));if("`"==r)return t.tokenize=b,b(e,t);if("#"==r&&"!"==e.peek())return e.skipToEnd(),g("meta","meta");if("#"==r&&e.eatWhile(d))return g("variable","property");if("<"==r&&e.match("!--")||"-"==r&&e.match("->")&&!/\S/.test(e.string.slice(0,e.start)))return e.skipToEnd(),g("comment","comment");if(f.test(r))return">"==r&&t.lexical&&">"==t.lexical.type||(e.eat("=")?"!"!=r&&"="!=r||e.eat("="):/[<>*+\-|&?]/.test(r)&&(e.eat(r),">"==r&&e.eat(r))),"?"==r&&e.eat(".")?g("."):g("operator","operator",e.current());if(d.test(r)){e.eatWhile(d);var n=e.current();if("."!=t.lastType){if(p.propertyIsEnumerable(n)){var i=p[n];return g(i.type,i.style,n)}if("async"==n&&e.match(/^(\s|\/\*([^*]|\*(?!\/))*?\*\/)*[\[\(\w]/,!1))return g("async","keyword",n)}return g("variable","variable",n)}}function y(e){return function(t,r){var n,i=!1;if(s&&"@"==t.peek()&&t.match(m))return r.tokenize=v,g("jsonld-keyword","meta");for(;null!=(n=t.next())&&(n!=e||i);)i=!i&&"\\"==n;return i||(r.tokenize=v),g("string","string")}}function _(e,t){for(var r,n=!1;r=e.next();){if("/"==r&&n){t.tokenize=v;break}n="*"==r}return g("comment","comment")}function b(e,t){for(var r,n=!1;null!=(r=e.next());){if(!n&&("`"==r||"$"==r&&e.eat("{"))){t.tokenize=v;break}n=!n&&"\\"==r}return g("quasi","string-2",e.current())}var x="([{}])";function k(e,t){t.fatArrowAt&&(t.fatArrowAt=null);var r=e.string.indexOf("=>",e.start);if(!(r<0)){if(u){var n=/:\s*(?:\w+(?:<[^>]*>|\[\])?|\{[^}]*\})\s*$/.exec(e.string.slice(e.start,r));n&&(r=n.index)}for(var i=0,o=!1,a=r-1;a>=0;--a){var s=e.string.charAt(a),l=x.indexOf(s);if(l>=0&&l<3){if(!i){++a;break}if(0==--i){"("==s&&(o=!0);break}}else if(l>=3&&l<6)++i;else if(d.test(s))o=!0;else if(/["'\/`]/.test(s))for(;;--a){if(0==a)return;if(e.string.charAt(a-1)==s&&"\\"!=e.string.charAt(a-2)){a--;break}}else if(o&&!i){++a;break}}o&&!i&&(t.fatArrowAt=a)}}var w={atom:!0,number:!0,variable:!0,string:!0,regexp:!0,this:!0,import:!0,"jsonld-keyword":!0};function S(e,t,r,n,i,o){this.indented=e,this.column=t,this.type=r,this.prev=i,this.info=o,null!=n&&(this.align=n)}function C(e,t){if(!c)return!1;for(var r=e.localVars;r;r=r.next)if(r.name==t)return!0;for(var n=e.context;n;n=n.prev)for(r=n.vars;r;r=r.next)if(r.name==t)return!0}function M(e,t,r,n,i){var o=e.cc;for(T.state=e,T.stream=i,T.marked=null,T.cc=o,T.style=t,e.lexical.hasOwnProperty("align")||(e.lexical.align=!0);;)if((o.length?o.pop():l?U:R)(r,n)){for(;o.length&&o[o.length-1].lex;)o.pop()();return T.marked?T.marked:"variable"==r&&C(e,n)?"variable-2":t}}var T={state:null,column:null,marked:null,cc:null};function L(){for(var e=arguments.length-1;e>=0;e--)T.cc.push(arguments[e])}function z(){return L.apply(null,arguments),!0}function D(e,t){for(var r=t;r;r=r.next)if(r.name==e)return!0;return!1}function A(e){var t=T.state;if(T.marked="def",c){if(t.context)if("var"==t.lexical.info&&t.context&&t.context.block){var n=q(e,t.context);if(null!=n)return void(t.context=n)}else if(!D(e,t.localVars))return void(t.localVars=new E(e,t.localVars));r.globalVars&&!D(e,t.globalVars)&&(t.globalVars=new E(e,t.globalVars))}}function q(e,t){if(t){if(t.block){var r=q(e,t.prev);return r?r==t.prev?t:new F(r,t.vars,!0):null}return D(e,t.vars)?t:new F(t.prev,new E(e,t.vars),!1)}return null}function N(e){return"public"==e||"private"==e||"protected"==e||"abstract"==e||"readonly"==e}function F(e,t,r){this.prev=e,this.vars=t,this.block=r}function E(e,t){this.name=e,this.next=t}var O=new E("this",new E("arguments",null));function I(){T.state.context=new F(T.state.context,T.state.localVars,!1),T.state.localVars=O}function P(){T.state.context=new F(T.state.context,T.state.localVars,!0),T.state.localVars=null}function j(){T.state.localVars=T.state.context.vars,T.state.context=T.state.context.prev}function W(e,t){var r=function(){var r=T.state,n=r.indented;if("stat"==r.lexical.type)n=r.lexical.indented;else for(var i=r.lexical;i&&")"==i.type&&i.align;i=i.prev)n=i.indented;r.lexical=new S(n,T.stream.column(),e,null,r.lexical,t)};return r.lex=!0,r}function B(){var e=T.state;e.lexical.prev&&(")"==e.lexical.type&&(e.indented=e.lexical.indented),e.lexical=e.lexical.prev)}function H(e){function t(r){return r==e?z():";"==e||"}"==r||")"==r||"]"==r?L():z(t)}return t}function R(e,t){return"var"==e?z(W("vardef",t),Le,H(";"),B):"keyword a"==e?z(W("form"),K,R,B):"keyword b"==e?z(W("form"),R,B):"keyword d"==e?T.stream.match(/^\s*$/,!1)?z():z(W("stat"),X,H(";"),B):"debugger"==e?z(H(";")):"{"==e?z(W("}"),P,pe,B,j):";"==e?z():"if"==e?("else"==T.state.lexical.info&&T.state.cc[T.state.cc.length-1]==B&&T.state.cc.pop()(),z(W("form"),K,R,B,Fe)):"function"==e?z(Pe):"for"==e?z(W("form"),P,Ee,R,j,B):"class"==e||u&&"interface"==t?(T.marked="keyword",z(W("form","class"==e?e:t),Re,B)):"variable"==e?u&&"declare"==t?(T.marked="keyword",z(R)):u&&("module"==t||"enum"==t||"type"==t)&&T.stream.match(/^\s*\w/,!1)?(T.marked="keyword","enum"==t?z(tt):"type"==t?z(We,H("operator"),ve,H(";")):z(W("form"),ze,H("{"),W("}"),pe,B,B)):u&&"namespace"==t?(T.marked="keyword",z(W("form"),U,R,B)):u&&"abstract"==t?(T.marked="keyword",z(R)):z(W("stat"),oe):"switch"==e?z(W("form"),K,H("{"),W("}","switch"),P,pe,B,B,j):"case"==e?z(U,H(":")):"default"==e?z(H(":")):"catch"==e?z(W("form"),I,$,R,B,j):"export"==e?z(W("stat"),Ke,B):"import"==e?z(W("stat"),Xe,B):"async"==e?z(R):"@"==t?z(U,R):L(W("stat"),U,H(";"),B)}function $(e){if("("==e)return z(Be,H(")"))}function U(e,t){return G(e,t,!1)}function V(e,t){return G(e,t,!0)}function K(e){return"("!=e?L():z(W(")"),X,H(")"),B)}function G(e,t,r){if(T.state.fatArrowAt==T.stream.start){var n=r?te:ee;if("("==e)return z(I,W(")"),ue(Be,")"),B,H("=>"),n,j);if("variable"==e)return L(I,ze,H("=>"),n,j)}var i=r?Y:Q;return w.hasOwnProperty(e)?z(i):"function"==e?z(Pe,i):"class"==e||u&&"interface"==t?(T.marked="keyword",z(W("form"),He,B)):"keyword c"==e||"async"==e?z(r?V:U):"("==e?z(W(")"),X,H(")"),B,i):"operator"==e||"spread"==e?z(r?V:U):"["==e?z(W("]"),et,B,i):"{"==e?de(se,"}",null,i):"quasi"==e?L(Z,i):"new"==e?z(re(r)):z()}function X(e){return e.match(/[;\}\)\],]/)?L():L(U)}function Q(e,t){return","==e?z(X):Y(e,t,!1)}function Y(e,t,r){var n=0==r?Q:Y,i=0==r?U:V;return"=>"==e?z(I,r?te:ee,j):"operator"==e?/\+\+|--/.test(t)||u&&"!"==t?z(n):u&&"<"==t&&T.stream.match(/^([^<>]|<[^<>]*>)*>\s*\(/,!1)?z(W(">"),ue(ve,">"),B,n):"?"==t?z(U,H(":"),i):z(i):"quasi"==e?L(Z,n):";"!=e?"("==e?de(V,")","call",n):"."==e?z(ae,n):"["==e?z(W("]"),X,H("]"),B,n):u&&"as"==t?(T.marked="keyword",z(ve,n)):"regexp"==e?(T.state.lastType=T.marked="operator",T.stream.backUp(T.stream.pos-T.stream.start-1),z(i)):void 0:void 0}function Z(e,t){return"quasi"!=e?L():"${"!=t.slice(t.length-2)?z(Z):z(X,J)}function J(e){if("}"==e)return T.marked="string-2",T.state.tokenize=b,z(Z)}function ee(e){return k(T.stream,T.state),L("{"==e?R:U)}function te(e){return k(T.stream,T.state),L("{"==e?R:V)}function re(e){return function(t){return"."==t?z(e?ie:ne):"variable"==t&&u?z(Ce,e?Y:Q):L(e?V:U)}}function ne(e,t){if("target"==t)return T.marked="keyword",z(Q)}function ie(e,t){if("target"==t)return T.marked="keyword",z(Y)}function oe(e){return":"==e?z(B,R):L(Q,H(";"),B)}function ae(e){if("variable"==e)return T.marked="property",z()}function se(e,t){return"async"==e?(T.marked="property",z(se)):"variable"==e||"keyword"==T.style?(T.marked="property","get"==t||"set"==t?z(le):(u&&T.state.fatArrowAt==T.stream.start&&(r=T.stream.match(/^\s*:\s*/,!1))&&(T.state.fatArrowAt=T.stream.pos+r[0].length),z(ce))):"number"==e||"string"==e?(T.marked=s?"property":T.style+" property",z(ce)):"jsonld-keyword"==e?z(ce):u&&N(t)?(T.marked="keyword",z(se)):"["==e?z(U,fe,H("]"),ce):"spread"==e?z(V,ce):"*"==t?(T.marked="keyword",z(se)):":"==e?L(ce):void 0;var r}function le(e){return"variable"!=e?L(ce):(T.marked="property",z(Pe))}function ce(e){return":"==e?z(V):"("==e?L(Pe):void 0}function ue(e,t,r){function n(i,o){if(r?r.indexOf(i)>-1:","==i){var a=T.state.lexical;return"call"==a.info&&(a.pos=(a.pos||0)+1),z((function(r,n){return r==t||n==t?L():L(e)}),n)}return i==t||o==t?z():r&&r.indexOf(";")>-1?L(e):z(H(t))}return function(r,i){return r==t||i==t?z():L(e,n)}}function de(e,t,r){for(var n=3;n<arguments.length;n++)T.cc.push(arguments[n]);return z(W(t,r),ue(e,t),B)}function pe(e){return"}"==e?z():L(R,pe)}function fe(e,t){if(u){if(":"==e)return z(ve);if("?"==t)return z(fe)}}function me(e,t){if(u&&(":"==e||"in"==t))return z(ve)}function he(e){if(u&&":"==e)return T.stream.match(/^\s*\w+\s+is\b/,!1)?z(U,ge,ve):z(ve)}function ge(e,t){if("is"==t)return T.marked="keyword",z()}function ve(e,t){return"keyof"==t||"typeof"==t||"infer"==t||"readonly"==t?(T.marked="keyword",z("typeof"==t?V:ve)):"variable"==e||"void"==t?(T.marked="type",z(Se)):"|"==t||"&"==t?z(ve):"string"==e||"number"==e||"atom"==e?z(Se):"["==e?z(W("]"),ue(ve,"]",","),B,Se):"{"==e?z(W("}"),_e,B,Se):"("==e?z(ue(we,")"),ye,Se):"<"==e?z(ue(ve,">"),ve):"quasi"==e?L(xe,Se):void 0}function ye(e){if("=>"==e)return z(ve)}function _e(e){return e.match(/[\}\)\]]/)?z():","==e||";"==e?z(_e):L(be,_e)}function be(e,t){return"variable"==e||"keyword"==T.style?(T.marked="property",z(be)):"?"==t||"number"==e||"string"==e?z(be):":"==e?z(ve):"["==e?z(H("variable"),me,H("]"),be):"("==e?L(je,be):e.match(/[;\}\)\],]/)?void 0:z()}function xe(e,t){return"quasi"!=e?L():"${"!=t.slice(t.length-2)?z(xe):z(ve,ke)}function ke(e){if("}"==e)return T.marked="string-2",T.state.tokenize=b,z(xe)}function we(e,t){return"variable"==e&&T.stream.match(/^\s*[?:]/,!1)||"?"==t?z(we):":"==e?z(ve):"spread"==e?z(we):L(ve)}function Se(e,t){return"<"==t?z(W(">"),ue(ve,">"),B,Se):"|"==t||"."==e||"&"==t?z(ve):"["==e?z(ve,H("]"),Se):"extends"==t||"implements"==t?(T.marked="keyword",z(ve)):"?"==t?z(ve,H(":"),ve):void 0}function Ce(e,t){if("<"==t)return z(W(">"),ue(ve,">"),B,Se)}function Me(){return L(ve,Te)}function Te(e,t){if("="==t)return z(ve)}function Le(e,t){return"enum"==t?(T.marked="keyword",z(tt)):L(ze,fe,qe,Ne)}function ze(e,t){return u&&N(t)?(T.marked="keyword",z(ze)):"variable"==e?(A(t),z()):"spread"==e?z(ze):"["==e?de(Ae,"]"):"{"==e?de(De,"}"):void 0}function De(e,t){return"variable"!=e||T.stream.match(/^\s*:/,!1)?("variable"==e&&(T.marked="property"),"spread"==e?z(ze):"}"==e?L():"["==e?z(U,H("]"),H(":"),De):z(H(":"),ze,qe)):(A(t),z(qe))}function Ae(){return L(ze,qe)}function qe(e,t){if("="==t)return z(V)}function Ne(e){if(","==e)return z(Le)}function Fe(e,t){if("keyword b"==e&&"else"==t)return z(W("form","else"),R,B)}function Ee(e,t){return"await"==t?z(Ee):"("==e?z(W(")"),Oe,B):void 0}function Oe(e){return"var"==e?z(Le,Ie):"variable"==e?z(Ie):L(Ie)}function Ie(e,t){return")"==e?z():";"==e?z(Ie):"in"==t||"of"==t?(T.marked="keyword",z(U,Ie)):L(U,Ie)}function Pe(e,t){return"*"==t?(T.marked="keyword",z(Pe)):"variable"==e?(A(t),z(Pe)):"("==e?z(I,W(")"),ue(Be,")"),B,he,R,j):u&&"<"==t?z(W(">"),ue(Me,">"),B,Pe):void 0}function je(e,t){return"*"==t?(T.marked="keyword",z(je)):"variable"==e?(A(t),z(je)):"("==e?z(I,W(")"),ue(Be,")"),B,he,j):u&&"<"==t?z(W(">"),ue(Me,">"),B,je):void 0}function We(e,t){return"keyword"==e||"variable"==e?(T.marked="type",z(We)):"<"==t?z(W(">"),ue(Me,">"),B):void 0}function Be(e,t){return"@"==t&&z(U,Be),"spread"==e?z(Be):u&&N(t)?(T.marked="keyword",z(Be)):u&&"this"==e?z(fe,qe):L(ze,fe,qe)}function He(e,t){return"variable"==e?Re(e,t):$e(e,t)}function Re(e,t){if("variable"==e)return A(t),z($e)}function $e(e,t){return"<"==t?z(W(">"),ue(Me,">"),B,$e):"extends"==t||"implements"==t||u&&","==e?("implements"==t&&(T.marked="keyword"),z(u?ve:U,$e)):"{"==e?z(W("}"),Ue,B):void 0}function Ue(e,t){return"async"==e||"variable"==e&&("static"==t||"get"==t||"set"==t||u&&N(t))&&T.stream.match(/^\s+#?[\w$\xa1-\uffff]/,!1)?(T.marked="keyword",z(Ue)):"variable"==e||"keyword"==T.style?(T.marked="property",z(Ve,Ue)):"number"==e||"string"==e?z(Ve,Ue):"["==e?z(U,fe,H("]"),Ve,Ue):"*"==t?(T.marked="keyword",z(Ue)):u&&"("==e?L(je,Ue):";"==e||","==e?z(Ue):"}"==e?z():"@"==t?z(U,Ue):void 0}function Ve(e,t){if("!"==t)return z(Ve);if("?"==t)return z(Ve);if(":"==e)return z(ve,qe);if("="==t)return z(V);var r=T.state.lexical.prev;return L(r&&"interface"==r.info?je:Pe)}function Ke(e,t){return"*"==t?(T.marked="keyword",z(Je,H(";"))):"default"==t?(T.marked="keyword",z(U,H(";"))):"{"==e?z(ue(Ge,"}"),Je,H(";")):L(R)}function Ge(e,t){return"as"==t?(T.marked="keyword",z(H("variable"))):"variable"==e?L(V,Ge):void 0}function Xe(e){return"string"==e?z():"("==e?L(U):"."==e?L(Q):L(Qe,Ye,Je)}function Qe(e,t){return"{"==e?de(Qe,"}"):("variable"==e&&A(t),"*"==t&&(T.marked="keyword"),z(Ze))}function Ye(e){if(","==e)return z(Qe,Ye)}function Ze(e,t){if("as"==t)return T.marked="keyword",z(Qe)}function Je(e,t){if("from"==t)return T.marked="keyword",z(U)}function et(e){return"]"==e?z():L(ue(V,"]"))}function tt(){return L(W("form"),ze,H("{"),W("}"),ue(rt,"}"),B,B)}function rt(){return L(ze,qe)}function nt(e,t){return"operator"==e.lastType||","==e.lastType||f.test(t.charAt(0))||/[,.]/.test(t.charAt(0))}function it(e,t,r){return t.tokenize==v&&/^(?:operator|sof|keyword [bcd]|case|new|export|default|spread|[\[{}\(,;:]|=>)$/.test(t.lastType)||"quasi"==t.lastType&&/\{\s*$/.test(e.string.slice(0,e.pos-(r||0)))}return I.lex=P.lex=!0,j.lex=!0,B.lex=!0,{startState:function(e){var t={tokenize:v,lastType:"sof",cc:[],lexical:new S((e||0)-o,0,"block",!1),localVars:r.localVars,context:r.localVars&&new F(null,null,!1),indented:e||0};return r.globalVars&&"object"==typeof r.globalVars&&(t.globalVars=r.globalVars),t},token:function(e,t){if(e.sol()&&(t.lexical.hasOwnProperty("align")||(t.lexical.align=!1),t.indented=e.indentation(),k(e,t)),t.tokenize!=_&&e.eatSpace())return null;var r=t.tokenize(e,t);return"comment"==n?r:(t.lastType="operator"!=n||"++"!=i&&"--"!=i?n:"incdec",M(t,r,n,i,e))},indent:function(t,n){if(t.tokenize==_||t.tokenize==b)return e.Pass;if(t.tokenize!=v)return 0;var i,s=n&&n.charAt(0),l=t.lexical;if(!/^\s*else\b/.test(n))for(var c=t.cc.length-1;c>=0;--c){var u=t.cc[c];if(u==B)l=l.prev;else if(u!=Fe&&u!=j)break}for(;("stat"==l.type||"form"==l.type)&&("}"==s||(i=t.cc[t.cc.length-1])&&(i==Q||i==Y)&&!/^[,\.=+\-*:?[\(]/.test(n));)l=l.prev;a&&")"==l.type&&"stat"==l.prev.type&&(l=l.prev);var d=l.type,p=s==d;return"vardef"==d?l.indented+("operator"==t.lastType||","==t.lastType?l.info.length+1:0):"form"==d&&"{"==s?l.indented:"form"==d?l.indented+o:"stat"==d?l.indented+(nt(t,n)?a||o:0):"switch"!=l.info||p||0==r.doubleIndentSwitch?l.align?l.column+(p?0:1):l.indented+(p?0:o):l.indented+(/^(?:case|default)\b/.test(n)?o:2*o)},electricInput:/^\s*(?:case .*?:|default:|\{|\})$/,blockCommentStart:l?null:"/*",blockCommentEnd:l?null:"*/",blockCommentContinue:l?null:" * ",lineComment:l?null:"//",fold:"brace",closeBrackets:"()[]{}''\"\"``",helperType:l?"json":"javascript",jsonldMode:s,jsonMode:l,expressionAllowed:it,skipExpression:function(t){M(t,"atom","atom","true",new e.StringStream("",2,null))}}})),e.registerHelper("wordChars","javascript",/[\w$]/),e.defineMIME("text/javascript","javascript"),e.defineMIME("text/ecmascript","javascript"),e.defineMIME("application/javascript","javascript"),e.defineMIME("application/x-javascript","javascript"),e.defineMIME("application/ecmascript","javascript"),e.defineMIME("application/json",{name:"javascript",json:!0}),e.defineMIME("application/x-json",{name:"javascript",json:!0}),e.defineMIME("application/manifest+json",{name:"javascript",json:!0}),e.defineMIME("application/ld+json",{name:"javascript",jsonld:!0}),e.defineMIME("text/typescript",{name:"javascript",typescript:!0}),e.defineMIME("application/typescript",{name:"javascript",typescript:!0})}(r(237))},216:(e,t,r)=>{!function(e){"use strict";e.defineMode("markdown",(function(t,r){var n=e.getMode(t,"text/html"),i="null"==n.name;function o(r){if(e.findModeByName){var n=e.findModeByName(r);n&&(r=n.mime||n.mimes[0])}var i=e.getMode(t,r);return"null"==i.name?null:i}void 0===r.highlightFormatting&&(r.highlightFormatting=!1),void 0===r.maxBlockquoteDepth&&(r.maxBlockquoteDepth=0),void 0===r.taskLists&&(r.taskLists=!1),void 0===r.strikethrough&&(r.strikethrough=!1),void 0===r.emoji&&(r.emoji=!1),void 0===r.fencedCodeBlockHighlighting&&(r.fencedCodeBlockHighlighting=!0),void 0===r.fencedCodeBlockDefaultMode&&(r.fencedCodeBlockDefaultMode="text/plain"),void 0===r.xml&&(r.xml=!0),void 0===r.tokenTypeOverrides&&(r.tokenTypeOverrides={});var a={header:"header",code:"comment",quote:"quote",list1:"variable-2",list2:"variable-3",list3:"keyword",hr:"hr",image:"image",imageAltText:"image-alt-text",imageMarker:"image-marker",formatting:"formatting",linkInline:"link",linkEmail:"link",linkText:"link",linkHref:"string",em:"em",strong:"strong",strikethrough:"strikethrough",emoji:"builtin"};for(var s in a)a.hasOwnProperty(s)&&r.tokenTypeOverrides[s]&&(a[s]=r.tokenTypeOverrides[s]);var l=/^([*\-_])(?:\s*\1){2,}\s*$/,c=/^(?:[*\-+]|^[0-9]+([.)]))\s+/,u=/^\[(x| )\](?=\s)/i,d=r.allowAtxHeaderWithoutSpace?/^(#+)/:/^(#+)(?: |$)/,p=/^ {0,3}(?:\={1,}|-{2,})\s*$/,f=/^[^#!\[\]*_\\<>` "'(~:]+/,m=/^(~~~+|```+)[ \t]*([\w\/+#-]*)[^\n`]*$/,h=/^\s*\[[^\]]+?\]:.*$/,g=/[!"#$%&'()*+,\-.\/:;<=>?@\[\\\]^_`{|}~\xA1\xA7\xAB\xB6\xB7\xBB\xBF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061E\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u0AF0\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166D\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E42\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]|\uD800[\uDD00-\uDD02\uDF9F\uDFD0]|\uD801\uDD6F|\uD802[\uDC57\uDD1F\uDD3F\uDE50-\uDE58\uDE7F\uDEF0-\uDEF6\uDF39-\uDF3F\uDF99-\uDF9C]|\uD804[\uDC47-\uDC4D\uDCBB\uDCBC\uDCBE-\uDCC1\uDD40-\uDD43\uDD74\uDD75\uDDC5-\uDDC9\uDDCD\uDDDB\uDDDD-\uDDDF\uDE38-\uDE3D\uDEA9]|\uD805[\uDCC6\uDDC1-\uDDD7\uDE41-\uDE43\uDF3C-\uDF3E]|\uD809[\uDC70-\uDC74]|\uD81A[\uDE6E\uDE6F\uDEF5\uDF37-\uDF3B\uDF44]|\uD82F\uDC9F|\uD836[\uDE87-\uDE8B]/,v="    ";function y(e,t,r){return t.f=t.inline=r,r(e,t)}function _(e,t,r){return t.f=t.block=r,r(e,t)}function b(e){return!e||!/\S/.test(e.string)}function x(t){if(t.linkTitle=!1,t.linkHref=!1,t.linkText=!1,t.em=!1,t.strong=!1,t.strikethrough=!1,t.quote=0,t.indentedCode=!1,t.f==w){var r=i;if(!r){var o=e.innerMode(n,t.htmlState);r="xml"==o.mode.name&&null===o.state.tagStart&&!o.state.context&&o.state.tokenize.isInText}r&&(t.f=T,t.block=k,t.htmlState=null)}return t.trailingSpace=0,t.trailingSpaceNewLine=!1,t.prevLine=t.thisLine,t.thisLine={stream:null},null}function k(t,n){var i=t.column()===n.indentation,s=b(n.prevLine.stream),f=n.indentedCode,g=n.prevLine.hr,v=!1!==n.list,_=(n.listStack[n.listStack.length-1]||0)+3;n.indentedCode=!1;var x=n.indentation;if(null===n.indentationDiff&&(n.indentationDiff=n.indentation,v)){for(n.list=null;x<n.listStack[n.listStack.length-1];)n.listStack.pop(),n.listStack.length?n.indentation=n.listStack[n.listStack.length-1]:n.list=!1;!1!==n.list&&(n.indentationDiff=x-n.listStack[n.listStack.length-1])}var k=!(s||g||n.prevLine.header||v&&f||n.prevLine.fencedCodeEnd),w=(!1===n.list||g||s)&&n.indentation<=_&&t.match(l),M=null;if(n.indentationDiff>=4&&(f||n.prevLine.fencedCodeEnd||n.prevLine.header||s))return t.skipToEnd(),n.indentedCode=!0,a.code;if(t.eatSpace())return null;if(i&&n.indentation<=_&&(M=t.match(d))&&M[1].length<=6)return n.quote=0,n.header=M[1].length,n.thisLine.header=!0,r.highlightFormatting&&(n.formatting="header"),n.f=n.inline,C(n);if(n.indentation<=_&&t.eat(">"))return n.quote=i?1:n.quote+1,r.highlightFormatting&&(n.formatting="quote"),t.eatSpace(),C(n);if(!w&&!n.setext&&i&&n.indentation<=_&&(M=t.match(c))){var T=M[1]?"ol":"ul";return n.indentation=x+t.current().length,n.list=!0,n.quote=0,n.listStack.push(n.indentation),n.em=!1,n.strong=!1,n.code=!1,n.strikethrough=!1,r.taskLists&&t.match(u,!1)&&(n.taskList=!0),n.f=n.inline,r.highlightFormatting&&(n.formatting=["list","list-"+T]),C(n)}return i&&n.indentation<=_&&(M=t.match(m,!0))?(n.quote=0,n.fencedEndRE=new RegExp(M[1]+"+ *$"),n.localMode=r.fencedCodeBlockHighlighting&&o(M[2]||r.fencedCodeBlockDefaultMode),n.localMode&&(n.localState=e.startState(n.localMode)),n.f=n.block=S,r.highlightFormatting&&(n.formatting="code-block"),n.code=-1,C(n)):n.setext||!(k&&v||n.quote||!1!==n.list||n.code||w||h.test(t.string))&&(M=t.lookAhead(1))&&(M=M.match(p))?(n.setext?(n.header=n.setext,n.setext=0,t.skipToEnd(),r.highlightFormatting&&(n.formatting="header")):(n.header="="==M[0].charAt(0)?1:2,n.setext=n.header),n.thisLine.header=!0,n.f=n.inline,C(n)):w?(t.skipToEnd(),n.hr=!0,n.thisLine.hr=!0,a.hr):"["===t.peek()?y(t,n,q):y(t,n,n.inline)}function w(t,r){var o=n.token(t,r.htmlState);if(!i){var a=e.innerMode(n,r.htmlState);("xml"==a.mode.name&&null===a.state.tagStart&&!a.state.context&&a.state.tokenize.isInText||r.md_inside&&t.current().indexOf(">")>-1)&&(r.f=T,r.block=k,r.htmlState=null)}return o}function S(e,t){var n,i=t.listStack[t.listStack.length-1]||0,o=t.indentation<i,s=i+3;return t.fencedEndRE&&t.indentation<=s&&(o||e.match(t.fencedEndRE))?(r.highlightFormatting&&(t.formatting="code-block"),o||(n=C(t)),t.localMode=t.localState=null,t.block=k,t.f=T,t.fencedEndRE=null,t.code=0,t.thisLine.fencedCodeEnd=!0,o?_(e,t,t.block):n):t.localMode?t.localMode.token(e,t.localState):(e.skipToEnd(),a.code)}function C(e){var t=[];if(e.formatting){t.push(a.formatting),"string"==typeof e.formatting&&(e.formatting=[e.formatting]);for(var n=0;n<e.formatting.length;n++)t.push(a.formatting+"-"+e.formatting[n]),"header"===e.formatting[n]&&t.push(a.formatting+"-"+e.formatting[n]+"-"+e.header),"quote"===e.formatting[n]&&(!r.maxBlockquoteDepth||r.maxBlockquoteDepth>=e.quote?t.push(a.formatting+"-"+e.formatting[n]+"-"+e.quote):t.push("error"))}if(e.taskOpen)return t.push("meta"),t.length?t.join(" "):null;if(e.taskClosed)return t.push("property"),t.length?t.join(" "):null;if(e.linkHref?t.push(a.linkHref,"url"):(e.strong&&t.push(a.strong),e.em&&t.push(a.em),e.strikethrough&&t.push(a.strikethrough),e.emoji&&t.push(a.emoji),e.linkText&&t.push(a.linkText),e.code&&t.push(a.code),e.image&&t.push(a.image),e.imageAltText&&t.push(a.imageAltText,"link"),e.imageMarker&&t.push(a.imageMarker)),e.header&&t.push(a.header,a.header+"-"+e.header),e.quote&&(t.push(a.quote),!r.maxBlockquoteDepth||r.maxBlockquoteDepth>=e.quote?t.push(a.quote+"-"+e.quote):t.push(a.quote+"-"+r.maxBlockquoteDepth)),!1!==e.list){var i=(e.listStack.length-1)%3;i?1===i?t.push(a.list2):t.push(a.list3):t.push(a.list1)}return e.trailingSpaceNewLine?t.push("trailing-space-new-line"):e.trailingSpace&&t.push("trailing-space-"+(e.trailingSpace%2?"a":"b")),t.length?t.join(" "):null}function M(e,t){if(e.match(f,!0))return C(t)}function T(t,i){var o=i.text(t,i);if(void 0!==o)return o;if(i.list)return i.list=null,C(i);if(i.taskList)return" "===t.match(u,!0)[1]?i.taskOpen=!0:i.taskClosed=!0,r.highlightFormatting&&(i.formatting="task"),i.taskList=!1,C(i);if(i.taskOpen=!1,i.taskClosed=!1,i.header&&t.match(/^#+$/,!0))return r.highlightFormatting&&(i.formatting="header"),C(i);var s=t.next();if(i.linkTitle){i.linkTitle=!1;var l=s;"("===s&&(l=")");var c="^\\s*(?:[^"+(l=(l+"").replace(/([.?*+^\[\]\\(){}|-])/g,"\\$1"))+"\\\\]+|\\\\\\\\|\\\\.)"+l;if(t.match(new RegExp(c),!0))return a.linkHref}if("`"===s){var d=i.formatting;r.highlightFormatting&&(i.formatting="code"),t.eatWhile("`");var p=t.current().length;if(0!=i.code||i.quote&&1!=p){if(p==i.code){var f=C(i);return i.code=0,f}return i.formatting=d,C(i)}return i.code=p,C(i)}if(i.code)return C(i);if("\\"===s&&(t.next(),r.highlightFormatting)){var m=C(i),h=a.formatting+"-escape";return m?m+" "+h:h}if("!"===s&&t.match(/\[[^\]]*\] ?(?:\(|\[)/,!1))return i.imageMarker=!0,i.image=!0,r.highlightFormatting&&(i.formatting="image"),C(i);if("["===s&&i.imageMarker&&t.match(/[^\]]*\](\(.*?\)| ?\[.*?\])/,!1))return i.imageMarker=!1,i.imageAltText=!0,r.highlightFormatting&&(i.formatting="image"),C(i);if("]"===s&&i.imageAltText){r.highlightFormatting&&(i.formatting="image");var m=C(i);return i.imageAltText=!1,i.image=!1,i.inline=i.f=z,m}if("["===s&&!i.image)return i.linkText&&t.match(/^.*?\]/)||(i.linkText=!0,r.highlightFormatting&&(i.formatting="link")),C(i);if("]"===s&&i.linkText){r.highlightFormatting&&(i.formatting="link");var m=C(i);return i.linkText=!1,i.inline=i.f=t.match(/\(.*?\)| ?\[.*?\]/,!1)?z:T,m}if("<"===s&&t.match(/^(https?|ftps?):\/\/(?:[^\\>]|\\.)+>/,!1))return i.f=i.inline=L,r.highlightFormatting&&(i.formatting="link"),(m=C(i))?m+=" ":m="",m+a.linkInline;if("<"===s&&t.match(/^[^> \\]+@(?:[^\\>]|\\.)+>/,!1))return i.f=i.inline=L,r.highlightFormatting&&(i.formatting="link"),(m=C(i))?m+=" ":m="",m+a.linkEmail;if(r.xml&&"<"===s&&t.match(/^(!--|\?|!\[CDATA\[|[a-z][a-z0-9-]*(?:\s+[a-z_:.\-]+(?:\s*=\s*[^>]+)?)*\s*(?:>|$))/i,!1)){var v=t.string.indexOf(">",t.pos);if(-1!=v){var y=t.string.substring(t.start,v);/markdown\s*=\s*('|"){0,1}1('|"){0,1}/.test(y)&&(i.md_inside=!0)}return t.backUp(1),i.htmlState=e.startState(n),_(t,i,w)}if(r.xml&&"<"===s&&t.match(/^\/\w*?>/))return i.md_inside=!1,"tag";if("*"===s||"_"===s){for(var b=1,x=1==t.pos?" ":t.string.charAt(t.pos-2);b<3&&t.eat(s);)b++;var k=t.peek()||" ",S=!/\s/.test(k)&&(!g.test(k)||/\s/.test(x)||g.test(x)),M=!/\s/.test(x)&&(!g.test(x)||/\s/.test(k)||g.test(k)),D=null,A=null;if(b%2&&(i.em||!S||"*"!==s&&M&&!g.test(x)?i.em!=s||!M||"*"!==s&&S&&!g.test(k)||(D=!1):D=!0),b>1&&(i.strong||!S||"*"!==s&&M&&!g.test(x)?i.strong!=s||!M||"*"!==s&&S&&!g.test(k)||(A=!1):A=!0),null!=A||null!=D)return r.highlightFormatting&&(i.formatting=null==D?"strong":null==A?"em":"strong em"),!0===D&&(i.em=s),!0===A&&(i.strong=s),f=C(i),!1===D&&(i.em=!1),!1===A&&(i.strong=!1),f}else if(" "===s&&(t.eat("*")||t.eat("_"))){if(" "===t.peek())return C(i);t.backUp(1)}if(r.strikethrough)if("~"===s&&t.eatWhile(s)){if(i.strikethrough)return r.highlightFormatting&&(i.formatting="strikethrough"),f=C(i),i.strikethrough=!1,f;if(t.match(/^[^\s]/,!1))return i.strikethrough=!0,r.highlightFormatting&&(i.formatting="strikethrough"),C(i)}else if(" "===s&&t.match("~~",!0)){if(" "===t.peek())return C(i);t.backUp(2)}if(r.emoji&&":"===s&&t.match(/^(?:[a-z_\d+][a-z_\d+-]*|\-[a-z_\d+][a-z_\d+-]*):/)){i.emoji=!0,r.highlightFormatting&&(i.formatting="emoji");var q=C(i);return i.emoji=!1,q}return" "===s&&(t.match(/^ +$/,!1)?i.trailingSpace++:i.trailingSpace&&(i.trailingSpaceNewLine=!0)),C(i)}function L(e,t){if(">"===e.next()){t.f=t.inline=T,r.highlightFormatting&&(t.formatting="link");var n=C(t);return n?n+=" ":n="",n+a.linkInline}return e.match(/^[^>]+/,!0),a.linkInline}function z(e,t){if(e.eatSpace())return null;var n=e.next();return"("===n||"["===n?(t.f=t.inline=A("("===n?")":"]"),r.highlightFormatting&&(t.formatting="link-string"),t.linkHref=!0,C(t)):"error"}var D={")":/^(?:[^\\\(\)]|\\.|\((?:[^\\\(\)]|\\.)*\))*?(?=\))/,"]":/^(?:[^\\\[\]]|\\.|\[(?:[^\\\[\]]|\\.)*\])*?(?=\])/};function A(e){return function(t,n){if(t.next()===e){n.f=n.inline=T,r.highlightFormatting&&(n.formatting="link-string");var i=C(n);return n.linkHref=!1,i}return t.match(D[e]),n.linkHref=!0,C(n)}}function q(e,t){return e.match(/^([^\]\\]|\\.)*\]:/,!1)?(t.f=N,e.next(),r.highlightFormatting&&(t.formatting="link"),t.linkText=!0,C(t)):y(e,t,T)}function N(e,t){if(e.match("]:",!0)){t.f=t.inline=F,r.highlightFormatting&&(t.formatting="link");var n=C(t);return t.linkText=!1,n}return e.match(/^([^\]\\]|\\.)+/,!0),a.linkText}function F(e,t){return e.eatSpace()?null:(e.match(/^[^\s]+/,!0),void 0===e.peek()?t.linkTitle=!0:e.match(/^(?:\s+(?:"(?:[^"\\]|\\.)+"|'(?:[^'\\]|\\.)+'|\((?:[^)\\]|\\.)+\)))?/,!0),t.f=t.inline=T,a.linkHref+" url")}var E={startState:function(){return{f:k,prevLine:{stream:null},thisLine:{stream:null},block:k,htmlState:null,indentation:0,inline:T,text:M,formatting:!1,linkText:!1,linkHref:!1,linkTitle:!1,code:0,em:!1,strong:!1,header:0,setext:0,hr:!1,taskList:!1,list:!1,listStack:[],quote:0,trailingSpace:0,trailingSpaceNewLine:!1,strikethrough:!1,emoji:!1,fencedEndRE:null}},copyState:function(t){return{f:t.f,prevLine:t.prevLine,thisLine:t.thisLine,block:t.block,htmlState:t.htmlState&&e.copyState(n,t.htmlState),indentation:t.indentation,localMode:t.localMode,localState:t.localMode?e.copyState(t.localMode,t.localState):null,inline:t.inline,text:t.text,formatting:!1,linkText:t.linkText,linkTitle:t.linkTitle,linkHref:t.linkHref,code:t.code,em:t.em,strong:t.strong,strikethrough:t.strikethrough,emoji:t.emoji,header:t.header,setext:t.setext,hr:t.hr,taskList:t.taskList,list:t.list,listStack:t.listStack.slice(0),quote:t.quote,indentedCode:t.indentedCode,trailingSpace:t.trailingSpace,trailingSpaceNewLine:t.trailingSpaceNewLine,md_inside:t.md_inside,fencedEndRE:t.fencedEndRE}},token:function(e,t){if(t.formatting=!1,e!=t.thisLine.stream){if(t.header=0,t.hr=!1,e.match(/^\s*$/,!0))return x(t),null;if(t.prevLine=t.thisLine,t.thisLine={stream:e},t.taskList=!1,t.trailingSpace=0,t.trailingSpaceNewLine=!1,!t.localState&&(t.f=t.block,t.f!=w)){var r=e.match(/^\s*/,!0)[0].replace(/\t/g,v).length;if(t.indentation=r,t.indentationDiff=null,r>0)return null}}return t.f(e,t)},innerMode:function(e){return e.block==w?{state:e.htmlState,mode:n}:e.localState?{state:e.localState,mode:e.localMode}:{state:e,mode:E}},indent:function(t,r,i){return t.block==w&&n.indent?n.indent(t.htmlState,r,i):t.localState&&t.localMode.indent?t.localMode.indent(t.localState,r,i):e.Pass},blankLine:x,getType:C,blockCommentStart:"\x3c!--",blockCommentEnd:"--\x3e",closeBrackets:"()[]{}''\"\"``",fold:"markdown"};return E}),"xml"),e.defineMIME("text/markdown","markdown"),e.defineMIME("text/x-markdown","markdown")}(r(237),r(576),r(602))},602:(e,t,r)=>{!function(e){"use strict";e.modeInfo=[{name:"APL",mime:"text/apl",mode:"apl",ext:["dyalog","apl"]},{name:"PGP",mimes:["application/pgp","application/pgp-encrypted","application/pgp-keys","application/pgp-signature"],mode:"asciiarmor",ext:["asc","pgp","sig"]},{name:"ASN.1",mime:"text/x-ttcn-asn",mode:"asn.1",ext:["asn","asn1"]},{name:"Asterisk",mime:"text/x-asterisk",mode:"asterisk",file:/^extensions\.conf$/i},{name:"Brainfuck",mime:"text/x-brainfuck",mode:"brainfuck",ext:["b","bf"]},{name:"C",mime:"text/x-csrc",mode:"clike",ext:["c","h","ino"]},{name:"C++",mime:"text/x-c++src",mode:"clike",ext:["cpp","c++","cc","cxx","hpp","h++","hh","hxx"],alias:["cpp"]},{name:"Cobol",mime:"text/x-cobol",mode:"cobol",ext:["cob","cpy","cbl"]},{name:"C#",mime:"text/x-csharp",mode:"clike",ext:["cs"],alias:["csharp","cs"]},{name:"Clojure",mime:"text/x-clojure",mode:"clojure",ext:["clj","cljc","cljx"]},{name:"ClojureScript",mime:"text/x-clojurescript",mode:"clojure",ext:["cljs"]},{name:"Closure Stylesheets (GSS)",mime:"text/x-gss",mode:"css",ext:["gss"]},{name:"CMake",mime:"text/x-cmake",mode:"cmake",ext:["cmake","cmake.in"],file:/^CMakeLists\.txt$/},{name:"CoffeeScript",mimes:["application/vnd.coffeescript","text/coffeescript","text/x-coffeescript"],mode:"coffeescript",ext:["coffee"],alias:["coffee","coffee-script"]},{name:"Common Lisp",mime:"text/x-common-lisp",mode:"commonlisp",ext:["cl","lisp","el"],alias:["lisp"]},{name:"Cypher",mime:"application/x-cypher-query",mode:"cypher",ext:["cyp","cypher"]},{name:"Cython",mime:"text/x-cython",mode:"python",ext:["pyx","pxd","pxi"]},{name:"Crystal",mime:"text/x-crystal",mode:"crystal",ext:["cr"]},{name:"CSS",mime:"text/css",mode:"css",ext:["css"]},{name:"CQL",mime:"text/x-cassandra",mode:"sql",ext:["cql"]},{name:"D",mime:"text/x-d",mode:"d",ext:["d"]},{name:"Dart",mimes:["application/dart","text/x-dart"],mode:"dart",ext:["dart"]},{name:"diff",mime:"text/x-diff",mode:"diff",ext:["diff","patch"]},{name:"Django",mime:"text/x-django",mode:"django"},{name:"Dockerfile",mime:"text/x-dockerfile",mode:"dockerfile",file:/^Dockerfile$/},{name:"DTD",mime:"application/xml-dtd",mode:"dtd",ext:["dtd"]},{name:"Dylan",mime:"text/x-dylan",mode:"dylan",ext:["dylan","dyl","intr"]},{name:"EBNF",mime:"text/x-ebnf",mode:"ebnf"},{name:"ECL",mime:"text/x-ecl",mode:"ecl",ext:["ecl"]},{name:"edn",mime:"application/edn",mode:"clojure",ext:["edn"]},{name:"Eiffel",mime:"text/x-eiffel",mode:"eiffel",ext:["e"]},{name:"Elm",mime:"text/x-elm",mode:"elm",ext:["elm"]},{name:"Embedded JavaScript",mime:"application/x-ejs",mode:"htmlembedded",ext:["ejs"]},{name:"Embedded Ruby",mime:"application/x-erb",mode:"htmlembedded",ext:["erb"]},{name:"Erlang",mime:"text/x-erlang",mode:"erlang",ext:["erl"]},{name:"Esper",mime:"text/x-esper",mode:"sql"},{name:"Factor",mime:"text/x-factor",mode:"factor",ext:["factor"]},{name:"FCL",mime:"text/x-fcl",mode:"fcl"},{name:"Forth",mime:"text/x-forth",mode:"forth",ext:["forth","fth","4th"]},{name:"Fortran",mime:"text/x-fortran",mode:"fortran",ext:["f","for","f77","f90","f95"]},{name:"F#",mime:"text/x-fsharp",mode:"mllike",ext:["fs"],alias:["fsharp"]},{name:"Gas",mime:"text/x-gas",mode:"gas",ext:["s"]},{name:"Gherkin",mime:"text/x-feature",mode:"gherkin",ext:["feature"]},{name:"GitHub Flavored Markdown",mime:"text/x-gfm",mode:"gfm",file:/^(readme|contributing|history)\.md$/i},{name:"Go",mime:"text/x-go",mode:"go",ext:["go"]},{name:"Groovy",mime:"text/x-groovy",mode:"groovy",ext:["groovy","gradle"],file:/^Jenkinsfile$/},{name:"HAML",mime:"text/x-haml",mode:"haml",ext:["haml"]},{name:"Haskell",mime:"text/x-haskell",mode:"haskell",ext:["hs"]},{name:"Haskell (Literate)",mime:"text/x-literate-haskell",mode:"haskell-literate",ext:["lhs"]},{name:"Haxe",mime:"text/x-haxe",mode:"haxe",ext:["hx"]},{name:"HXML",mime:"text/x-hxml",mode:"haxe",ext:["hxml"]},{name:"ASP.NET",mime:"application/x-aspx",mode:"htmlembedded",ext:["aspx"],alias:["asp","aspx"]},{name:"HTML",mime:"text/html",mode:"htmlmixed",ext:["html","htm","handlebars","hbs"],alias:["xhtml"]},{name:"HTTP",mime:"message/http",mode:"http"},{name:"IDL",mime:"text/x-idl",mode:"idl",ext:["pro"]},{name:"Pug",mime:"text/x-pug",mode:"pug",ext:["jade","pug"],alias:["jade"]},{name:"Java",mime:"text/x-java",mode:"clike",ext:["java"]},{name:"Java Server Pages",mime:"application/x-jsp",mode:"htmlembedded",ext:["jsp"],alias:["jsp"]},{name:"JavaScript",mimes:["text/javascript","text/ecmascript","application/javascript","application/x-javascript","application/ecmascript"],mode:"javascript",ext:["js"],alias:["ecmascript","js","node"]},{name:"JSON",mimes:["application/json","application/x-json"],mode:"javascript",ext:["json","map"],alias:["json5"]},{name:"JSON-LD",mime:"application/ld+json",mode:"javascript",ext:["jsonld"],alias:["jsonld"]},{name:"JSX",mime:"text/jsx",mode:"jsx",ext:["jsx"]},{name:"Jinja2",mime:"text/jinja2",mode:"jinja2",ext:["j2","jinja","jinja2"]},{name:"Julia",mime:"text/x-julia",mode:"julia",ext:["jl"],alias:["jl"]},{name:"Kotlin",mime:"text/x-kotlin",mode:"clike",ext:["kt"]},{name:"LESS",mime:"text/x-less",mode:"css",ext:["less"]},{name:"LiveScript",mime:"text/x-livescript",mode:"livescript",ext:["ls"],alias:["ls"]},{name:"Lua",mime:"text/x-lua",mode:"lua",ext:["lua"]},{name:"Markdown",mime:"text/x-markdown",mode:"markdown",ext:["markdown","md","mkd"]},{name:"mIRC",mime:"text/mirc",mode:"mirc"},{name:"MariaDB SQL",mime:"text/x-mariadb",mode:"sql"},{name:"Mathematica",mime:"text/x-mathematica",mode:"mathematica",ext:["m","nb","wl","wls"]},{name:"Modelica",mime:"text/x-modelica",mode:"modelica",ext:["mo"]},{name:"MUMPS",mime:"text/x-mumps",mode:"mumps",ext:["mps"]},{name:"MS SQL",mime:"text/x-mssql",mode:"sql"},{name:"mbox",mime:"application/mbox",mode:"mbox",ext:["mbox"]},{name:"MySQL",mime:"text/x-mysql",mode:"sql"},{name:"Nginx",mime:"text/x-nginx-conf",mode:"nginx",file:/nginx.*\.conf$/i},{name:"NSIS",mime:"text/x-nsis",mode:"nsis",ext:["nsh","nsi"]},{name:"NTriples",mimes:["application/n-triples","application/n-quads","text/n-triples"],mode:"ntriples",ext:["nt","nq"]},{name:"Objective-C",mime:"text/x-objectivec",mode:"clike",ext:["m"],alias:["objective-c","objc"]},{name:"Objective-C++",mime:"text/x-objectivec++",mode:"clike",ext:["mm"],alias:["objective-c++","objc++"]},{name:"OCaml",mime:"text/x-ocaml",mode:"mllike",ext:["ml","mli","mll","mly"]},{name:"Octave",mime:"text/x-octave",mode:"octave",ext:["m"]},{name:"Oz",mime:"text/x-oz",mode:"oz",ext:["oz"]},{name:"Pascal",mime:"text/x-pascal",mode:"pascal",ext:["p","pas"]},{name:"PEG.js",mime:"null",mode:"pegjs",ext:["jsonld"]},{name:"Perl",mime:"text/x-perl",mode:"perl",ext:["pl","pm"]},{name:"PHP",mimes:["text/x-php","application/x-httpd-php","application/x-httpd-php-open"],mode:"php",ext:["php","php3","php4","php5","php7","phtml"]},{name:"Pig",mime:"text/x-pig",mode:"pig",ext:["pig"]},{name:"Plain Text",mime:"text/plain",mode:"null",ext:["txt","text","conf","def","list","log"]},{name:"PLSQL",mime:"text/x-plsql",mode:"sql",ext:["pls"]},{name:"PostgreSQL",mime:"text/x-pgsql",mode:"sql"},{name:"PowerShell",mime:"application/x-powershell",mode:"powershell",ext:["ps1","psd1","psm1"]},{name:"Properties files",mime:"text/x-properties",mode:"properties",ext:["properties","ini","in"],alias:["ini","properties"]},{name:"ProtoBuf",mime:"text/x-protobuf",mode:"protobuf",ext:["proto"]},{name:"Python",mime:"text/x-python",mode:"python",ext:["BUILD","bzl","py","pyw"],file:/^(BUCK|BUILD)$/},{name:"Puppet",mime:"text/x-puppet",mode:"puppet",ext:["pp"]},{name:"Q",mime:"text/x-q",mode:"q",ext:["q"]},{name:"R",mime:"text/x-rsrc",mode:"r",ext:["r","R"],alias:["rscript"]},{name:"reStructuredText",mime:"text/x-rst",mode:"rst",ext:["rst"],alias:["rst"]},{name:"RPM Changes",mime:"text/x-rpm-changes",mode:"rpm"},{name:"RPM Spec",mime:"text/x-rpm-spec",mode:"rpm",ext:["spec"]},{name:"Ruby",mime:"text/x-ruby",mode:"ruby",ext:["rb"],alias:["jruby","macruby","rake","rb","rbx"]},{name:"Rust",mime:"text/x-rustsrc",mode:"rust",ext:["rs"]},{name:"SAS",mime:"text/x-sas",mode:"sas",ext:["sas"]},{name:"Sass",mime:"text/x-sass",mode:"sass",ext:["sass"]},{name:"Scala",mime:"text/x-scala",mode:"clike",ext:["scala"]},{name:"Scheme",mime:"text/x-scheme",mode:"scheme",ext:["scm","ss"]},{name:"SCSS",mime:"text/x-scss",mode:"css",ext:["scss"]},{name:"Shell",mimes:["text/x-sh","application/x-sh"],mode:"shell",ext:["sh","ksh","bash"],alias:["bash","sh","zsh"],file:/^PKGBUILD$/},{name:"Sieve",mime:"application/sieve",mode:"sieve",ext:["siv","sieve"]},{name:"Slim",mimes:["text/x-slim","application/x-slim"],mode:"slim",ext:["slim"]},{name:"Smalltalk",mime:"text/x-stsrc",mode:"smalltalk",ext:["st"]},{name:"Smarty",mime:"text/x-smarty",mode:"smarty",ext:["tpl"]},{name:"Solr",mime:"text/x-solr",mode:"solr"},{name:"SML",mime:"text/x-sml",mode:"mllike",ext:["sml","sig","fun","smackspec"]},{name:"Soy",mime:"text/x-soy",mode:"soy",ext:["soy"],alias:["closure template"]},{name:"SPARQL",mime:"application/sparql-query",mode:"sparql",ext:["rq","sparql"],alias:["sparul"]},{name:"Spreadsheet",mime:"text/x-spreadsheet",mode:"spreadsheet",alias:["excel","formula"]},{name:"SQL",mime:"text/x-sql",mode:"sql",ext:["sql"]},{name:"SQLite",mime:"text/x-sqlite",mode:"sql"},{name:"Squirrel",mime:"text/x-squirrel",mode:"clike",ext:["nut"]},{name:"Stylus",mime:"text/x-styl",mode:"stylus",ext:["styl"]},{name:"Swift",mime:"text/x-swift",mode:"swift",ext:["swift"]},{name:"sTeX",mime:"text/x-stex",mode:"stex"},{name:"LaTeX",mime:"text/x-latex",mode:"stex",ext:["text","ltx","tex"],alias:["tex"]},{name:"SystemVerilog",mime:"text/x-systemverilog",mode:"verilog",ext:["v","sv","svh"]},{name:"Tcl",mime:"text/x-tcl",mode:"tcl",ext:["tcl"]},{name:"Textile",mime:"text/x-textile",mode:"textile",ext:["textile"]},{name:"TiddlyWiki",mime:"text/x-tiddlywiki",mode:"tiddlywiki"},{name:"Tiki wiki",mime:"text/tiki",mode:"tiki"},{name:"TOML",mime:"text/x-toml",mode:"toml",ext:["toml"]},{name:"Tornado",mime:"text/x-tornado",mode:"tornado"},{name:"troff",mime:"text/troff",mode:"troff",ext:["1","2","3","4","5","6","7","8","9"]},{name:"TTCN",mime:"text/x-ttcn",mode:"ttcn",ext:["ttcn","ttcn3","ttcnpp"]},{name:"TTCN_CFG",mime:"text/x-ttcn-cfg",mode:"ttcn-cfg",ext:["cfg"]},{name:"Turtle",mime:"text/turtle",mode:"turtle",ext:["ttl"]},{name:"TypeScript",mime:"application/typescript",mode:"javascript",ext:["ts"],alias:["ts"]},{name:"TypeScript-JSX",mime:"text/typescript-jsx",mode:"jsx",ext:["tsx"],alias:["tsx"]},{name:"Twig",mime:"text/x-twig",mode:"twig"},{name:"Web IDL",mime:"text/x-webidl",mode:"webidl",ext:["webidl"]},{name:"VB.NET",mime:"text/x-vb",mode:"vb",ext:["vb"]},{name:"VBScript",mime:"text/vbscript",mode:"vbscript",ext:["vbs"]},{name:"Velocity",mime:"text/velocity",mode:"velocity",ext:["vtl"]},{name:"Verilog",mime:"text/x-verilog",mode:"verilog",ext:["v"]},{name:"VHDL",mime:"text/x-vhdl",mode:"vhdl",ext:["vhd","vhdl"]},{name:"Vue.js Component",mimes:["script/x-vue","text/x-vue"],mode:"vue",ext:["vue"]},{name:"XML",mimes:["application/xml","text/xml"],mode:"xml",ext:["xml","xsl","xsd","svg"],alias:["rss","wsdl","xsd"]},{name:"XQuery",mime:"application/xquery",mode:"xquery",ext:["xy","xquery"]},{name:"Yacas",mime:"text/x-yacas",mode:"yacas",ext:["ys"]},{name:"YAML",mimes:["text/x-yaml","text/yaml"],mode:"yaml",ext:["yaml","yml"],alias:["yml"]},{name:"Z80",mime:"text/x-z80",mode:"z80",ext:["z80"]},{name:"mscgen",mime:"text/x-mscgen",mode:"mscgen",ext:["mscgen","mscin","msc"]},{name:"xu",mime:"text/x-xu",mode:"mscgen",ext:["xu"]},{name:"msgenny",mime:"text/x-msgenny",mode:"mscgen",ext:["msgenny"]},{name:"WebAssembly",mime:"text/webassembly",mode:"wast",ext:["wat","wast"]}];for(var t=0;t<e.modeInfo.length;t++){var r=e.modeInfo[t];r.mimes&&(r.mime=r.mimes[0])}e.findModeByMIME=function(t){t=t.toLowerCase();for(var r=0;r<e.modeInfo.length;r++){var n=e.modeInfo[r];if(n.mime==t)return n;if(n.mimes)for(var i=0;i<n.mimes.length;i++)if(n.mimes[i]==t)return n}return/\+xml$/.test(t)?e.findModeByMIME("application/xml"):/\+json$/.test(t)?e.findModeByMIME("application/json"):void 0},e.findModeByExtension=function(t){t=t.toLowerCase();for(var r=0;r<e.modeInfo.length;r++){var n=e.modeInfo[r];if(n.ext)for(var i=0;i<n.ext.length;i++)if(n.ext[i]==t)return n}},e.findModeByFileName=function(t){for(var r=0;r<e.modeInfo.length;r++){var n=e.modeInfo[r];if(n.file&&n.file.test(t))return n}var i=t.lastIndexOf("."),o=i>-1&&t.substring(i+1,t.length);if(o)return e.findModeByExtension(o)},e.findModeByName=function(t){t=t.toLowerCase();for(var r=0;r<e.modeInfo.length;r++){var n=e.modeInfo[r];if(n.name.toLowerCase()==t)return n;if(n.alias)for(var i=0;i<n.alias.length;i++)if(n.alias[i].toLowerCase()==t)return n}}}(r(237))},460:(e,t,r)=>{!function(e){"use strict";e.defineMode("nginx",(function(e){function t(e){for(var t={},r=e.split(" "),n=0;n<r.length;++n)t[r[n]]=!0;return t}var r,n=t("break return rewrite set accept_mutex accept_mutex_delay access_log add_after_body add_before_body add_header addition_types aio alias allow ancient_browser ancient_browser_value auth_basic auth_basic_user_file auth_http auth_http_header auth_http_timeout autoindex autoindex_exact_size autoindex_localtime charset charset_types client_body_buffer_size client_body_in_file_only client_body_in_single_buffer client_body_temp_path client_body_timeout client_header_buffer_size client_header_timeout client_max_body_size connection_pool_size create_full_put_path daemon dav_access dav_methods debug_connection debug_points default_type degradation degrade deny devpoll_changes devpoll_events directio directio_alignment empty_gif env epoll_events error_log eventport_events expires fastcgi_bind fastcgi_buffer_size fastcgi_buffers fastcgi_busy_buffers_size fastcgi_cache fastcgi_cache_key fastcgi_cache_methods fastcgi_cache_min_uses fastcgi_cache_path fastcgi_cache_use_stale fastcgi_cache_valid fastcgi_catch_stderr fastcgi_connect_timeout fastcgi_hide_header fastcgi_ignore_client_abort fastcgi_ignore_headers fastcgi_index fastcgi_intercept_errors fastcgi_max_temp_file_size fastcgi_next_upstream fastcgi_param fastcgi_pass_header fastcgi_pass_request_body fastcgi_pass_request_headers fastcgi_read_timeout fastcgi_send_lowat fastcgi_send_timeout fastcgi_split_path_info fastcgi_store fastcgi_store_access fastcgi_temp_file_write_size fastcgi_temp_path fastcgi_upstream_fail_timeout fastcgi_upstream_max_fails flv geoip_city geoip_country google_perftools_profiles gzip gzip_buffers gzip_comp_level gzip_disable gzip_hash gzip_http_version gzip_min_length gzip_no_buffer gzip_proxied gzip_static gzip_types gzip_vary gzip_window if_modified_since ignore_invalid_headers image_filter image_filter_buffer image_filter_jpeg_quality image_filter_transparency imap_auth imap_capabilities imap_client_buffer index ip_hash keepalive_requests keepalive_timeout kqueue_changes kqueue_events large_client_header_buffers limit_conn limit_conn_log_level limit_rate limit_rate_after limit_req limit_req_log_level limit_req_zone limit_zone lingering_time lingering_timeout lock_file log_format log_not_found log_subrequest map_hash_bucket_size map_hash_max_size master_process memcached_bind memcached_buffer_size memcached_connect_timeout memcached_next_upstream memcached_read_timeout memcached_send_timeout memcached_upstream_fail_timeout memcached_upstream_max_fails merge_slashes min_delete_depth modern_browser modern_browser_value msie_padding msie_refresh multi_accept open_file_cache open_file_cache_errors open_file_cache_events open_file_cache_min_uses open_file_cache_valid open_log_file_cache output_buffers override_charset perl perl_modules perl_require perl_set pid pop3_auth pop3_capabilities port_in_redirect postpone_gzipping postpone_output protocol proxy proxy_bind proxy_buffer proxy_buffer_size proxy_buffering proxy_buffers proxy_busy_buffers_size proxy_cache proxy_cache_key proxy_cache_methods proxy_cache_min_uses proxy_cache_path proxy_cache_use_stale proxy_cache_valid proxy_connect_timeout proxy_headers_hash_bucket_size proxy_headers_hash_max_size proxy_hide_header proxy_ignore_client_abort proxy_ignore_headers proxy_intercept_errors proxy_max_temp_file_size proxy_method proxy_next_upstream proxy_pass_error_message proxy_pass_header proxy_pass_request_body proxy_pass_request_headers proxy_read_timeout proxy_redirect proxy_send_lowat proxy_send_timeout proxy_set_body proxy_set_header proxy_ssl_session_reuse proxy_store proxy_store_access proxy_temp_file_write_size proxy_temp_path proxy_timeout proxy_upstream_fail_timeout proxy_upstream_max_fails random_index read_ahead real_ip_header recursive_error_pages request_pool_size reset_timedout_connection resolver resolver_timeout rewrite_log rtsig_overflow_events rtsig_overflow_test rtsig_overflow_threshold rtsig_signo satisfy secure_link_secret send_lowat send_timeout sendfile sendfile_max_chunk server_name_in_redirect server_names_hash_bucket_size server_names_hash_max_size server_tokens set_real_ip_from smtp_auth smtp_capabilities smtp_client_buffer smtp_greeting_delay so_keepalive source_charset ssi ssi_ignore_recycled_buffers ssi_min_file_chunk ssi_silent_errors ssi_types ssi_value_length ssl ssl_certificate ssl_certificate_key ssl_ciphers ssl_client_certificate ssl_crl ssl_dhparam ssl_engine ssl_prefer_server_ciphers ssl_protocols ssl_session_cache ssl_session_timeout ssl_verify_client ssl_verify_depth starttls stub_status sub_filter sub_filter_once sub_filter_types tcp_nodelay tcp_nopush thread_stack_size timeout timer_resolution types_hash_bucket_size types_hash_max_size underscores_in_headers uninitialized_variable_warn use user userid userid_domain userid_expires userid_mark userid_name userid_p3p userid_path userid_service valid_referers variables_hash_bucket_size variables_hash_max_size worker_connections worker_cpu_affinity worker_priority worker_processes worker_rlimit_core worker_rlimit_nofile worker_rlimit_sigpending worker_threads working_directory xclient xml_entities xslt_stylesheet xslt_typesdrew@li229-23"),i=t("http mail events server types location upstream charset_map limit_except if geo map"),o=t("include root server server_name listen internal proxy_pass memcached_pass fastcgi_pass try_files"),a=e.indentUnit;function s(e,t){return r=t,e}function l(e,t){e.eatWhile(/[\w\$_]/);var r=e.current();if(n.propertyIsEnumerable(r))return"keyword";if(i.propertyIsEnumerable(r))return"variable-2";if(o.propertyIsEnumerable(r))return"string-2";var a=e.next();return"@"==a?(e.eatWhile(/[\w\\\-]/),s("meta",e.current())):"/"==a&&e.eat("*")?(t.tokenize=c,c(e,t)):"<"==a&&e.eat("!")?(t.tokenize=u,u(e,t)):"="!=a?"~"!=a&&"|"!=a||!e.eat("=")?'"'==a||"'"==a?(t.tokenize=d(a),t.tokenize(e,t)):"#"==a?(e.skipToEnd(),s("comment","comment")):"!"==a?(e.match(/^\s*\w*/),s("keyword","important")):/\d/.test(a)?(e.eatWhile(/[\w.%]/),s("number","unit")):/[,.+>*\/]/.test(a)?s(null,"select-op"):/[;{}:\[\]]/.test(a)?s(null,a):(e.eatWhile(/[\w\\\-]/),s("variable","variable")):s(null,"compare"):void s(null,"compare")}function c(e,t){for(var r,n=!1;null!=(r=e.next());){if(n&&"/"==r){t.tokenize=l;break}n="*"==r}return s("comment","comment")}function u(e,t){for(var r,n=0;null!=(r=e.next());){if(n>=2&&">"==r){t.tokenize=l;break}n="-"==r?n+1:0}return s("comment","comment")}function d(e){return function(t,r){for(var n,i=!1;null!=(n=t.next())&&(n!=e||i);)i=!i&&"\\"==n;return i||(r.tokenize=l),s("string","string")}}return{startState:function(e){return{tokenize:l,baseIndent:e||0,stack:[]}},token:function(e,t){if(e.eatSpace())return null;r=null;var n=t.tokenize(e,t),i=t.stack[t.stack.length-1];return"hash"==r&&"rule"==i?n="atom":"variable"==n&&("rule"==i?n="number":i&&"@media{"!=i||(n="tag")),"rule"==i&&/^[\{\};]$/.test(r)&&t.stack.pop(),"{"==r?"@media"==i?t.stack[t.stack.length-1]="@media{":t.stack.push("{"):"}"==r?t.stack.pop():"@media"==r?t.stack.push("@media"):"{"==i&&"comment"!=r&&t.stack.push("rule"),n},indent:function(e,t){var r=e.stack.length;return/^\}/.test(t)&&(r-="rule"==e.stack[e.stack.length-1]?2:1),e.baseIndent+r*a},electricChars:"}"}})),e.defineMIME("text/x-nginx-conf","nginx")}(r(237))},0:(e,t,r)=>{!function(e){"use strict";function t(e){for(var t={},r=e.split(" "),n=0;n<r.length;++n)t[r[n]]=!0;return t}function r(e,t,i){return 0==e.length?n(t):function(o,a){for(var s=e[0],l=0;l<s.length;l++)if(o.match(s[l][0]))return a.tokenize=r(e.slice(1),t),s[l][1];return a.tokenize=n(t,i),"string"}}function n(e,t){return function(r,n){return i(r,n,e,t)}}function i(e,t,n,i){if(!1!==i&&e.match("${",!1)||e.match("{$",!1))return t.tokenize=null,"string";if(!1!==i&&e.match(/^\$[a-zA-Z_][a-zA-Z0-9_]*/))return e.match("[",!1)&&(t.tokenize=r([[["[",null]],[[/\d[\w\.]*/,"number"],[/\$[a-zA-Z_][a-zA-Z0-9_]*/,"variable-2"],[/[\w\$]+/,"variable"]],[["]",null]]],n,i)),e.match(/^->\w/,!1)&&(t.tokenize=r([[["->",null]],[[/[\w]+/,"variable"]]],n,i)),"variable-2";for(var o=!1;!e.eol()&&(o||!1===i||!e.match("{$",!1)&&!e.match(/^(\$[a-zA-Z_][a-zA-Z0-9_]*|\$\{)/,!1));){if(!o&&e.match(n)){t.tokenize=null,t.tokStack.pop(),t.tokStack.pop();break}o="\\"==e.next()&&!o}return"string"}var o="abstract and array as break case catch class clone const continue declare default do else elseif enddeclare endfor endforeach endif endswitch endwhile enum extends final for foreach function global goto if implements interface instanceof namespace new or private protected public static switch throw trait try use var while xor die echo empty exit eval include include_once isset list require require_once return print unset __halt_compiler self static parent yield insteadof finally readonly match",a="true false null TRUE FALSE NULL __CLASS__ __DIR__ __FILE__ __LINE__ __METHOD__ __FUNCTION__ __NAMESPACE__ __TRAIT__",s="func_num_args func_get_arg func_get_args strlen strcmp strncmp strcasecmp strncasecmp each error_reporting define defined trigger_error user_error set_error_handler restore_error_handler get_declared_classes get_loaded_extensions extension_loaded get_extension_funcs debug_backtrace constant bin2hex hex2bin sleep usleep time mktime gmmktime strftime gmstrftime strtotime date gmdate getdate localtime checkdate flush wordwrap htmlspecialchars htmlentities html_entity_decode md5 md5_file crc32 getimagesize image_type_to_mime_type phpinfo phpversion phpcredits strnatcmp strnatcasecmp substr_count strspn strcspn strtok strtoupper strtolower strpos strrpos strrev hebrev hebrevc nl2br basename dirname pathinfo stripslashes stripcslashes strstr stristr strrchr str_shuffle str_word_count strcoll substr substr_replace quotemeta ucfirst ucwords strtr addslashes addcslashes rtrim str_replace str_repeat count_chars chunk_split trim ltrim strip_tags similar_text explode implode setlocale localeconv parse_str str_pad chop strchr sprintf printf vprintf vsprintf sscanf fscanf parse_url urlencode urldecode rawurlencode rawurldecode readlink linkinfo link unlink exec system escapeshellcmd escapeshellarg passthru shell_exec proc_open proc_close rand srand getrandmax mt_rand mt_srand mt_getrandmax base64_decode base64_encode abs ceil floor round is_finite is_nan is_infinite bindec hexdec octdec decbin decoct dechex base_convert number_format fmod ip2long long2ip getenv putenv getopt microtime gettimeofday getrusage uniqid quoted_printable_decode set_time_limit get_cfg_var magic_quotes_runtime set_magic_quotes_runtime get_magic_quotes_gpc get_magic_quotes_runtime import_request_variables error_log serialize unserialize memory_get_usage memory_get_peak_usage var_dump var_export debug_zval_dump print_r highlight_file show_source highlight_string ini_get ini_get_all ini_set ini_alter ini_restore get_include_path set_include_path restore_include_path setcookie header headers_sent connection_aborted connection_status ignore_user_abort parse_ini_file is_uploaded_file move_uploaded_file intval floatval doubleval strval gettype settype is_null is_resource is_bool is_long is_float is_int is_integer is_double is_real is_numeric is_string is_array is_object is_scalar ereg ereg_replace eregi eregi_replace split spliti join sql_regcase dl pclose popen readfile rewind rmdir umask fclose feof fgetc fgets fgetss fread fopen fpassthru ftruncate fstat fseek ftell fflush fwrite fputs mkdir rename copy tempnam tmpfile file file_get_contents file_put_contents stream_select stream_context_create stream_context_set_params stream_context_set_option stream_context_get_options stream_filter_prepend stream_filter_append fgetcsv flock get_meta_tags stream_set_write_buffer set_file_buffer set_socket_blocking stream_set_blocking socket_set_blocking stream_get_meta_data stream_register_wrapper stream_wrapper_register stream_set_timeout socket_set_timeout socket_get_status realpath fnmatch fsockopen pfsockopen pack unpack get_browser crypt opendir closedir chdir getcwd rewinddir readdir dir glob fileatime filectime filegroup fileinode filemtime fileowner fileperms filesize filetype file_exists is_writable is_writeable is_readable is_executable is_file is_dir is_link stat lstat chown touch clearstatcache mail ob_start ob_flush ob_clean ob_end_flush ob_end_clean ob_get_flush ob_get_clean ob_get_length ob_get_level ob_get_status ob_get_contents ob_implicit_flush ob_list_handlers ksort krsort natsort natcasesort asort arsort sort rsort usort uasort uksort shuffle array_walk count end prev next reset current key min max in_array array_search extract compact array_fill range array_multisort array_push array_pop array_shift array_unshift array_splice array_slice array_merge array_merge_recursive array_keys array_values array_count_values array_reverse array_reduce array_pad array_flip array_change_key_case array_rand array_unique array_intersect array_intersect_assoc array_diff array_diff_assoc array_sum array_filter array_map array_chunk array_key_exists array_intersect_key array_combine array_column pos sizeof key_exists assert assert_options version_compare ftok str_rot13 aggregate session_name session_module_name session_save_path session_id session_regenerate_id session_decode session_register session_unregister session_is_registered session_encode session_start session_destroy session_unset session_set_save_handler session_cache_limiter session_cache_expire session_set_cookie_params session_get_cookie_params session_write_close preg_match preg_match_all preg_replace preg_replace_callback preg_split preg_quote preg_grep overload ctype_alnum ctype_alpha ctype_cntrl ctype_digit ctype_lower ctype_graph ctype_print ctype_punct ctype_space ctype_upper ctype_xdigit virtual apache_request_headers apache_note apache_lookup_uri apache_child_terminate apache_setenv apache_response_headers apache_get_version getallheaders mysql_connect mysql_pconnect mysql_close mysql_select_db mysql_create_db mysql_drop_db mysql_query mysql_unbuffered_query mysql_db_query mysql_list_dbs mysql_list_tables mysql_list_fields mysql_list_processes mysql_error mysql_errno mysql_affected_rows mysql_insert_id mysql_result mysql_num_rows mysql_num_fields mysql_fetch_row mysql_fetch_array mysql_fetch_assoc mysql_fetch_object mysql_data_seek mysql_fetch_lengths mysql_fetch_field mysql_field_seek mysql_free_result mysql_field_name mysql_field_table mysql_field_len mysql_field_type mysql_field_flags mysql_escape_string mysql_real_escape_string mysql_stat mysql_thread_id mysql_client_encoding mysql_get_client_info mysql_get_host_info mysql_get_proto_info mysql_get_server_info mysql_info mysql mysql_fieldname mysql_fieldtable mysql_fieldlen mysql_fieldtype mysql_fieldflags mysql_selectdb mysql_createdb mysql_dropdb mysql_freeresult mysql_numfields mysql_numrows mysql_listdbs mysql_listtables mysql_listfields mysql_db_name mysql_dbname mysql_tablename mysql_table_name pg_connect pg_pconnect pg_close pg_connection_status pg_connection_busy pg_connection_reset pg_host pg_dbname pg_port pg_tty pg_options pg_ping pg_query pg_send_query pg_cancel_query pg_fetch_result pg_fetch_row pg_fetch_assoc pg_fetch_array pg_fetch_object pg_fetch_all pg_affected_rows pg_get_result pg_result_seek pg_result_status pg_free_result pg_last_oid pg_num_rows pg_num_fields pg_field_name pg_field_num pg_field_size pg_field_type pg_field_prtlen pg_field_is_null pg_get_notify pg_get_pid pg_result_error pg_last_error pg_last_notice pg_put_line pg_end_copy pg_copy_to pg_copy_from pg_trace pg_untrace pg_lo_create pg_lo_unlink pg_lo_open pg_lo_close pg_lo_read pg_lo_write pg_lo_read_all pg_lo_import pg_lo_export pg_lo_seek pg_lo_tell pg_escape_string pg_escape_bytea pg_unescape_bytea pg_client_encoding pg_set_client_encoding pg_meta_data pg_convert pg_insert pg_update pg_delete pg_select pg_exec pg_getlastoid pg_cmdtuples pg_errormessage pg_numrows pg_numfields pg_fieldname pg_fieldsize pg_fieldtype pg_fieldnum pg_fieldprtlen pg_fieldisnull pg_freeresult pg_result pg_loreadall pg_locreate pg_lounlink pg_loopen pg_loclose pg_loread pg_lowrite pg_loimport pg_loexport http_response_code get_declared_traits getimagesizefromstring socket_import_stream stream_set_chunk_size trait_exists header_register_callback class_uses session_status session_register_shutdown echo print global static exit array empty eval isset unset die include require include_once require_once json_decode json_encode json_last_error json_last_error_msg curl_close curl_copy_handle curl_errno curl_error curl_escape curl_exec curl_file_create curl_getinfo curl_init curl_multi_add_handle curl_multi_close curl_multi_exec curl_multi_getcontent curl_multi_info_read curl_multi_init curl_multi_remove_handle curl_multi_select curl_multi_setopt curl_multi_strerror curl_pause curl_reset curl_setopt_array curl_setopt curl_share_close curl_share_init curl_share_setopt curl_strerror curl_unescape curl_version mysqli_affected_rows mysqli_autocommit mysqli_change_user mysqli_character_set_name mysqli_close mysqli_commit mysqli_connect_errno mysqli_connect_error mysqli_connect mysqli_data_seek mysqli_debug mysqli_dump_debug_info mysqli_errno mysqli_error_list mysqli_error mysqli_fetch_all mysqli_fetch_array mysqli_fetch_assoc mysqli_fetch_field_direct mysqli_fetch_field mysqli_fetch_fields mysqli_fetch_lengths mysqli_fetch_object mysqli_fetch_row mysqli_field_count mysqli_field_seek mysqli_field_tell mysqli_free_result mysqli_get_charset mysqli_get_client_info mysqli_get_client_stats mysqli_get_client_version mysqli_get_connection_stats mysqli_get_host_info mysqli_get_proto_info mysqli_get_server_info mysqli_get_server_version mysqli_info mysqli_init mysqli_insert_id mysqli_kill mysqli_more_results mysqli_multi_query mysqli_next_result mysqli_num_fields mysqli_num_rows mysqli_options mysqli_ping mysqli_prepare mysqli_query mysqli_real_connect mysqli_real_escape_string mysqli_real_query mysqli_reap_async_query mysqli_refresh mysqli_rollback mysqli_select_db mysqli_set_charset mysqli_set_local_infile_default mysqli_set_local_infile_handler mysqli_sqlstate mysqli_ssl_set mysqli_stat mysqli_stmt_init mysqli_store_result mysqli_thread_id mysqli_thread_safe mysqli_use_result mysqli_warning_count";e.registerHelper("hintWords","php",[o,a,s].join(" ").split(" ")),e.registerHelper("wordChars","php",/[\w$]/);var l={name:"clike",helperType:"php",keywords:t(o),blockKeywords:t("catch do else elseif for foreach if switch try while finally"),defKeywords:t("class enum function interface namespace trait"),atoms:t(a),builtin:t(s),multiLineStrings:!0,hooks:{$:function(e){return e.eatWhile(/[\w\$_]/),"variable-2"},"<":function(e,t){var r;if(r=e.match(/^<<\s*/)){var i=e.eat(/['"]/);e.eatWhile(/[\w\.]/);var o=e.current().slice(r[0].length+(i?2:1));if(i&&e.eat(i),o)return(t.tokStack||(t.tokStack=[])).push(o,0),t.tokenize=n(o,"'"!=i),"string"}return!1},"#":function(e){for(;!e.eol()&&!e.match("?>",!1);)e.next();return"comment"},"/":function(e){if(e.eat("/")){for(;!e.eol()&&!e.match("?>",!1);)e.next();return"comment"}return!1},'"':function(e,t){return(t.tokStack||(t.tokStack=[])).push('"',0),t.tokenize=n('"'),"string"},"{":function(e,t){return t.tokStack&&t.tokStack.length&&t.tokStack[t.tokStack.length-1]++,!1},"}":function(e,t){return t.tokStack&&t.tokStack.length>0&&! --t.tokStack[t.tokStack.length-1]&&(t.tokenize=n(t.tokStack[t.tokStack.length-2])),!1}}};e.defineMode("php",(function(t,r){var n=e.getMode(t,r&&r.htmlMode||"text/html"),i=e.getMode(t,l);function o(t,r){var o=r.curMode==i;if(t.sol()&&r.pending&&'"'!=r.pending&&"'"!=r.pending&&(r.pending=null),o)return o&&null==r.php.tokenize&&t.match("?>")?(r.curMode=n,r.curState=r.html,r.php.context.prev||(r.php=null),"meta"):i.token(t,r.curState);if(t.match(/^<\?\w*/))return r.curMode=i,r.php||(r.php=e.startState(i,n.indent(r.html,"",""))),r.curState=r.php,"meta";if('"'==r.pending||"'"==r.pending){for(;!t.eol()&&t.next()!=r.pending;);var a="string"}else r.pending&&t.pos<r.pending.end?(t.pos=r.pending.end,a=r.pending.style):a=n.token(t,r.curState);r.pending&&(r.pending=null);var s,l=t.current(),c=l.search(/<\?/);return-1!=c&&("string"==a&&(s=l.match(/[\'\"]$/))&&!/\?>/.test(l)?r.pending=s[0]:r.pending={end:t.pos,style:a},t.backUp(l.length-c)),a}return{startState:function(){var t=e.startState(n),o=r.startOpen?e.startState(i):null;return{html:t,php:o,curMode:r.startOpen?i:n,curState:r.startOpen?o:t,pending:null}},copyState:function(t){var r,o=t.html,a=e.copyState(n,o),s=t.php,l=s&&e.copyState(i,s);return r=t.curMode==n?a:l,{html:a,php:l,curMode:t.curMode,curState:r,pending:t.pending}},token:o,indent:function(e,t,r){return e.curMode!=i&&/^\s*<\//.test(t)||e.curMode==i&&/^\?>/.test(t)?n.indent(e.html,t,r):e.curMode.indent(e.curState,t,r)},blockCommentStart:"/*",blockCommentEnd:"*/",lineComment:"//",innerMode:function(e){return{state:e.curState,mode:e.curMode}}}}),"htmlmixed","clike"),e.defineMIME("application/x-httpd-php","php"),e.defineMIME("application/x-httpd-php-open",{name:"php",startOpen:!0}),e.defineMIME("text/x-php",l)}(r(237),r(520),r(712))},684:(e,t,r)=>{!function(e){"use strict";e.defineMode("shell",(function(){var t={};function r(e,r){for(var n=0;n<r.length;n++)t[r[n]]=e}var n=["true","false"],i=["if","then","do","else","elif","while","until","for","in","esac","fi","fin","fil","done","exit","set","unset","export","function"],o=["ab","awk","bash","beep","cat","cc","cd","chown","chmod","chroot","clear","cp","curl","cut","diff","echo","find","gawk","gcc","get","git","grep","hg","kill","killall","ln","ls","make","mkdir","openssl","mv","nc","nl","node","npm","ping","ps","restart","rm","rmdir","sed","service","sh","shopt","shred","source","sort","sleep","ssh","start","stop","su","sudo","svn","tee","telnet","top","touch","vi","vim","wall","wc","wget","who","write","yes","zsh"];function a(e,r){if(e.eatSpace())return null;var n=e.sol(),i=e.next();if("\\"===i)return e.next(),null;if("'"===i||'"'===i||"`"===i)return r.tokens.unshift(s(i,"`"===i?"quote":"string")),d(e,r);if("#"===i)return n&&e.eat("!")?(e.skipToEnd(),"meta"):(e.skipToEnd(),"comment");if("$"===i)return r.tokens.unshift(c),d(e,r);if("+"===i||"="===i)return"operator";if("-"===i)return e.eat("-"),e.eatWhile(/\w/),"attribute";if("<"==i){if(e.match("<<"))return"operator";var o=e.match(/^<-?\s*['"]?([^'"]*)['"]?/);if(o)return r.tokens.unshift(u(o[1])),"string-2"}if(/\d/.test(i)&&(e.eatWhile(/\d/),e.eol()||!/\w/.test(e.peek())))return"number";e.eatWhile(/[\w-]/);var a=e.current();return"="===e.peek()&&/\w+/.test(a)?"def":t.hasOwnProperty(a)?t[a]:null}function s(e,t){var r="("==e?")":"{"==e?"}":e;return function(n,i){for(var o,a=!1;null!=(o=n.next());){if(o===r&&!a){i.tokens.shift();break}if("$"===o&&!a&&"'"!==e&&n.peek()!=r){a=!0,n.backUp(1),i.tokens.unshift(c);break}if(!a&&e!==r&&o===e)return i.tokens.unshift(s(e,t)),d(n,i);if(!a&&/['"]/.test(o)&&!/['"]/.test(e)){i.tokens.unshift(l(o,"string")),n.backUp(1);break}a=!a&&"\\"===o}return t}}function l(e,t){return function(r,n){return n.tokens[0]=s(e,t),r.next(),d(r,n)}}e.registerHelper("hintWords","shell",n.concat(i,o)),r("atom",n),r("keyword",i),r("builtin",o);var c=function(e,t){t.tokens.length>1&&e.eat("$");var r=e.next();return/['"({]/.test(r)?(t.tokens[0]=s(r,"("==r?"quote":"{"==r?"def":"string"),d(e,t)):(/\d/.test(r)||e.eatWhile(/\w/),t.tokens.shift(),"def")};function u(e){return function(t,r){return t.sol()&&t.string==e&&r.tokens.shift(),t.skipToEnd(),"string-2"}}function d(e,t){return(t.tokens[0]||a)(e,t)}return{startState:function(){return{tokens:[]}},token:function(e,t){return d(e,t)},closeBrackets:"()[]{}''\"\"``",lineComment:"#",fold:"brace"}})),e.defineMIME("text/x-sh","shell"),e.defineMIME("application/x-sh","shell")}(r(237))},532:(e,t,r)=>{!function(e){"use strict";function t(e){for(var t;null!=(t=e.next());)if("`"==t&&!e.eat("`"))return"variable-2";return e.backUp(e.current().length-1),e.eatWhile(/\w/)?"variable-2":null}function r(e){for(var t;null!=(t=e.next());)if('"'==t&&!e.eat('"'))return"variable-2";return e.backUp(e.current().length-1),e.eatWhile(/\w/)?"variable-2":null}function n(e){return e.eat("@")&&(e.match("session."),e.match("local."),e.match("global.")),e.eat("'")?(e.match(/^.*'/),"variable-2"):e.eat('"')?(e.match(/^.*"/),"variable-2"):e.eat("`")?(e.match(/^.*`/),"variable-2"):e.match(/^[0-9a-zA-Z$\.\_]+/)?"variable-2":null}function i(e){return e.eat("N")?"atom":e.match(/^[a-zA-Z.#!?]/)?"variable-2":null}e.defineMode("sql",(function(t,r){var n=r.client||{},i=r.atoms||{false:!0,true:!0,null:!0},l=r.builtin||a(s),c=r.keywords||a(o),u=r.operatorChars||/^[*+\-%<>!=&|~^\/]/,d=r.support||{},p=r.hooks||{},f=r.dateSQL||{date:!0,time:!0,timestamp:!0},m=!1!==r.backslashStringEscapes,h=r.brackets||/^[\{}\(\)\[\]]/,g=r.punctuation||/^[;.,:]/;function v(e,t){var r=e.next();if(p[r]){var o=p[r](e,t);if(!1!==o)return o}if(d.hexNumber&&("0"==r&&e.match(/^[xX][0-9a-fA-F]+/)||("x"==r||"X"==r)&&e.match(/^'[0-9a-fA-F]*'/)))return"number";if(d.binaryNumber&&(("b"==r||"B"==r)&&e.match(/^'[01]*'/)||"0"==r&&e.match(/^b[01]+/)))return"number";if(r.charCodeAt(0)>47&&r.charCodeAt(0)<58)return e.match(/^[0-9]*(\.[0-9]+)?([eE][-+]?[0-9]+)?/),d.decimallessFloat&&e.match(/^\.(?!\.)/),"number";if("?"==r&&(e.eatSpace()||e.eol()||e.eat(";")))return"variable-3";if("'"==r||'"'==r&&d.doubleQuote)return t.tokenize=y(r),t.tokenize(e,t);if((d.nCharCast&&("n"==r||"N"==r)||d.charsetCast&&"_"==r&&e.match(/[a-z][a-z0-9]*/i))&&("'"==e.peek()||'"'==e.peek()))return"keyword";if(d.escapeConstant&&("e"==r||"E"==r)&&("'"==e.peek()||'"'==e.peek()&&d.doubleQuote))return t.tokenize=function(e,t){return(t.tokenize=y(e.next(),!0))(e,t)},"keyword";if(d.commentSlashSlash&&"/"==r&&e.eat("/"))return e.skipToEnd(),"comment";if(d.commentHash&&"#"==r||"-"==r&&e.eat("-")&&(!d.commentSpaceRequired||e.eat(" ")))return e.skipToEnd(),"comment";if("/"==r&&e.eat("*"))return t.tokenize=_(1),t.tokenize(e,t);if("."!=r){if(u.test(r))return e.eatWhile(u),"operator";if(h.test(r))return"bracket";if(g.test(r))return e.eatWhile(g),"punctuation";if("{"==r&&(e.match(/^( )*(d|D|t|T|ts|TS)( )*'[^']*'( )*}/)||e.match(/^( )*(d|D|t|T|ts|TS)( )*"[^"]*"( )*}/)))return"number";e.eatWhile(/^[_\w\d]/);var a=e.current().toLowerCase();return f.hasOwnProperty(a)&&(e.match(/^( )+'[^']*'/)||e.match(/^( )+"[^"]*"/))?"number":i.hasOwnProperty(a)?"atom":l.hasOwnProperty(a)?"type":c.hasOwnProperty(a)?"keyword":n.hasOwnProperty(a)?"builtin":null}return d.zerolessFloat&&e.match(/^(?:\d+(?:e[+-]?\d+)?)/i)?"number":e.match(/^\.+/)?null:e.match(/^[\w\d_$#]+/)?"variable-2":void 0}function y(e,t){return function(r,n){for(var i,o=!1;null!=(i=r.next());){if(i==e&&!o){n.tokenize=v;break}o=(m||t)&&!o&&"\\"==i}return"string"}}function _(e){return function(t,r){var n=t.match(/^.*?(\/\*|\*\/)/);return n?"/*"==n[1]?r.tokenize=_(e+1):r.tokenize=e>1?_(e-1):v:t.skipToEnd(),"comment"}}function b(e,t,r){t.context={prev:t.context,indent:e.indentation(),col:e.column(),type:r}}function x(e){e.indent=e.context.indent,e.context=e.context.prev}return{startState:function(){return{tokenize:v,context:null}},token:function(e,t){if(e.sol()&&t.context&&null==t.context.align&&(t.context.align=!1),t.tokenize==v&&e.eatSpace())return null;var r=t.tokenize(e,t);if("comment"==r)return r;t.context&&null==t.context.align&&(t.context.align=!0);var n=e.current();return"("==n?b(e,t,")"):"["==n?b(e,t,"]"):t.context&&t.context.type==n&&x(t),r},indent:function(r,n){var i=r.context;if(!i)return e.Pass;var o=n.charAt(0)==i.type;return i.align?i.col+(o?0:1):i.indent+(o?0:t.indentUnit)},blockCommentStart:"/*",blockCommentEnd:"*/",lineComment:d.commentSlashSlash?"//":d.commentHash?"#":"--",closeBrackets:"()[]{}''\"\"``",config:r}}));var o="alter and as asc between by count create delete desc distinct drop from group having in insert into is join like not on or order select set table union update values where limit ";function a(e){for(var t={},r=e.split(" "),n=0;n<r.length;++n)t[r[n]]=!0;return t}var s="bool boolean bit blob enum long longblob longtext medium mediumblob mediumint mediumtext time timestamp tinyblob tinyint tinytext text bigint int int1 int2 int3 int4 int8 integer float float4 float8 double char varbinary varchar varcharacter precision real date datetime year unsigned signed decimal numeric";e.defineMIME("text/x-sql",{name:"sql",keywords:a(o+"begin"),builtin:a(s),atoms:a("false true null unknown"),dateSQL:a("date time timestamp"),support:a("doubleQuote binaryNumber hexNumber")}),e.defineMIME("text/x-mssql",{name:"sql",client:a("$partition binary_checksum checksum connectionproperty context_info current_request_id error_line error_message error_number error_procedure error_severity error_state formatmessage get_filestream_transaction_context getansinull host_id host_name isnull isnumeric min_active_rowversion newid newsequentialid rowcount_big xact_state object_id"),keywords:a(o+"begin trigger proc view index for add constraint key primary foreign collate clustered nonclustered declare exec go if use index holdlock nolock nowait paglock readcommitted readcommittedlock readpast readuncommitted repeatableread rowlock serializable snapshot tablock tablockx updlock with"),builtin:a("bigint numeric bit smallint decimal smallmoney int tinyint money float real char varchar text nchar nvarchar ntext binary varbinary image cursor timestamp hierarchyid uniqueidentifier sql_variant xml table "),atoms:a("is not null like and or in left right between inner outer join all any some cross unpivot pivot exists"),operatorChars:/^[*+\-%<>!=^\&|\/]/,brackets:/^[\{}\(\)]/,punctuation:/^[;.,:/]/,backslashStringEscapes:!1,dateSQL:a("date datetimeoffset datetime2 smalldatetime datetime time"),hooks:{"@":n}}),e.defineMIME("text/x-mysql",{name:"sql",client:a("charset clear connect edit ego exit go help nopager notee nowarning pager print prompt quit rehash source status system tee"),keywords:a(o+"accessible action add after algorithm all analyze asensitive at authors auto_increment autocommit avg avg_row_length before binary binlog both btree cache call cascade cascaded case catalog_name chain change changed character check checkpoint checksum class_origin client_statistics close coalesce code collate collation collations column columns comment commit committed completion concurrent condition connection consistent constraint contains continue contributors convert cross current current_date current_time current_timestamp current_user cursor data database databases day_hour day_microsecond day_minute day_second deallocate dec declare default delay_key_write delayed delimiter des_key_file describe deterministic dev_pop dev_samp deviance diagnostics directory disable discard distinctrow div dual dumpfile each elseif enable enclosed end ends engine engines enum errors escape escaped even event events every execute exists exit explain extended fast fetch field fields first flush for force foreign found_rows full fulltext function general get global grant grants group group_concat handler hash help high_priority hosts hour_microsecond hour_minute hour_second if ignore ignore_server_ids import index index_statistics infile inner innodb inout insensitive insert_method install interval invoker isolation iterate key keys kill language last leading leave left level limit linear lines list load local localtime localtimestamp lock logs low_priority master master_heartbeat_period master_ssl_verify_server_cert masters match max max_rows maxvalue message_text middleint migrate min min_rows minute_microsecond minute_second mod mode modifies modify mutex mysql_errno natural next no no_write_to_binlog offline offset one online open optimize option optionally out outer outfile pack_keys parser partition partitions password phase plugin plugins prepare preserve prev primary privileges procedure processlist profile profiles purge query quick range read read_write reads real rebuild recover references regexp relaylog release remove rename reorganize repair repeatable replace require resignal restrict resume return returns revoke right rlike rollback rollup row row_format rtree savepoint schedule schema schema_name schemas second_microsecond security sensitive separator serializable server session share show signal slave slow smallint snapshot soname spatial specific sql sql_big_result sql_buffer_result sql_cache sql_calc_found_rows sql_no_cache sql_small_result sqlexception sqlstate sqlwarning ssl start starting starts status std stddev stddev_pop stddev_samp storage straight_join subclass_origin sum suspend table_name table_statistics tables tablespace temporary terminated to trailing transaction trigger triggers truncate uncommitted undo uninstall unique unlock upgrade usage use use_frm user user_resources user_statistics using utc_date utc_time utc_timestamp value variables varying view views warnings when while with work write xa xor year_month zerofill begin do then else loop repeat"),builtin:a("bool boolean bit blob decimal double float long longblob longtext medium mediumblob mediumint mediumtext time timestamp tinyblob tinyint tinytext text bigint int int1 int2 int3 int4 int8 integer float float4 float8 double char varbinary varchar varcharacter precision date datetime year unsigned signed numeric"),atoms:a("false true null unknown"),operatorChars:/^[*+\-%<>!=&|^]/,dateSQL:a("date time timestamp"),support:a("decimallessFloat zerolessFloat binaryNumber hexNumber doubleQuote nCharCast charsetCast commentHash commentSpaceRequired"),hooks:{"@":n,"`":t,"\\":i}}),e.defineMIME("text/x-mariadb",{name:"sql",client:a("charset clear connect edit ego exit go help nopager notee nowarning pager print prompt quit rehash source status system tee"),keywords:a(o+"accessible action add after algorithm all always analyze asensitive at authors auto_increment autocommit avg avg_row_length before binary binlog both btree cache call cascade cascaded case catalog_name chain change changed character check checkpoint checksum class_origin client_statistics close coalesce code collate collation collations column columns comment commit committed completion concurrent condition connection consistent constraint contains continue contributors convert cross current current_date current_time current_timestamp current_user cursor data database databases day_hour day_microsecond day_minute day_second deallocate dec declare default delay_key_write delayed delimiter des_key_file describe deterministic dev_pop dev_samp deviance diagnostics directory disable discard distinctrow div dual dumpfile each elseif enable enclosed end ends engine engines enum errors escape escaped even event events every execute exists exit explain extended fast fetch field fields first flush for force foreign found_rows full fulltext function general generated get global grant grants group group_concat handler hard hash help high_priority hosts hour_microsecond hour_minute hour_second if ignore ignore_server_ids import index index_statistics infile inner innodb inout insensitive insert_method install interval invoker isolation iterate key keys kill language last leading leave left level limit linear lines list load local localtime localtimestamp lock logs low_priority master master_heartbeat_period master_ssl_verify_server_cert masters match max max_rows maxvalue message_text middleint migrate min min_rows minute_microsecond minute_second mod mode modifies modify mutex mysql_errno natural next no no_write_to_binlog offline offset one online open optimize option optionally out outer outfile pack_keys parser partition partitions password persistent phase plugin plugins prepare preserve prev primary privileges procedure processlist profile profiles purge query quick range read read_write reads real rebuild recover references regexp relaylog release remove rename reorganize repair repeatable replace require resignal restrict resume return returns revoke right rlike rollback rollup row row_format rtree savepoint schedule schema schema_name schemas second_microsecond security sensitive separator serializable server session share show shutdown signal slave slow smallint snapshot soft soname spatial specific sql sql_big_result sql_buffer_result sql_cache sql_calc_found_rows sql_no_cache sql_small_result sqlexception sqlstate sqlwarning ssl start starting starts status std stddev stddev_pop stddev_samp storage straight_join subclass_origin sum suspend table_name table_statistics tables tablespace temporary terminated to trailing transaction trigger triggers truncate uncommitted undo uninstall unique unlock upgrade usage use use_frm user user_resources user_statistics using utc_date utc_time utc_timestamp value variables varying view views virtual warnings when while with work write xa xor year_month zerofill begin do then else loop repeat"),builtin:a("bool boolean bit blob decimal double float long longblob longtext medium mediumblob mediumint mediumtext time timestamp tinyblob tinyint tinytext text bigint int int1 int2 int3 int4 int8 integer float float4 float8 double char varbinary varchar varcharacter precision date datetime year unsigned signed numeric"),atoms:a("false true null unknown"),operatorChars:/^[*+\-%<>!=&|^]/,dateSQL:a("date time timestamp"),support:a("decimallessFloat zerolessFloat binaryNumber hexNumber doubleQuote nCharCast charsetCast commentHash commentSpaceRequired"),hooks:{"@":n,"`":t,"\\":i}}),e.defineMIME("text/x-sqlite",{name:"sql",client:a("auth backup bail binary changes check clone databases dbinfo dump echo eqp exit explain fullschema headers help import imposter indexes iotrace limit lint load log mode nullvalue once open output print prompt quit read restore save scanstats schema separator session shell show stats system tables testcase timeout timer trace vfsinfo vfslist vfsname width"),keywords:a(o+"abort action add after all analyze attach autoincrement before begin cascade case cast check collate column commit conflict constraint cross current_date current_time current_timestamp database default deferrable deferred detach each else end escape except exclusive exists explain fail for foreign full glob if ignore immediate index indexed initially inner instead intersect isnull key left limit match natural no notnull null of offset outer plan pragma primary query raise recursive references regexp reindex release rename replace restrict right rollback row savepoint temp temporary then to transaction trigger unique using vacuum view virtual when with without"),builtin:a("bool boolean bit blob decimal double float long longblob longtext medium mediumblob mediumint mediumtext time timestamp tinyblob tinyint tinytext text clob bigint int int2 int8 integer float double char varchar date datetime year unsigned signed numeric real"),atoms:a("null current_date current_time current_timestamp"),operatorChars:/^[*+\-%<>!=&|/~]/,dateSQL:a("date time timestamp datetime"),support:a("decimallessFloat zerolessFloat"),identifierQuote:'"',hooks:{"@":n,":":n,"?":n,$:n,'"':r,"`":t}}),e.defineMIME("text/x-cassandra",{name:"sql",client:{},keywords:a("add all allow alter and any apply as asc authorize batch begin by clustering columnfamily compact consistency count create custom delete desc distinct drop each_quorum exists filtering from grant if in index insert into key keyspace keyspaces level limit local_one local_quorum modify nan norecursive nosuperuser not of on one order password permission permissions primary quorum rename revoke schema select set storage superuser table three to token truncate ttl two type unlogged update use user users using values where with writetime"),builtin:a("ascii bigint blob boolean counter decimal double float frozen inet int list map static text timestamp timeuuid tuple uuid varchar varint"),atoms:a("false true infinity NaN"),operatorChars:/^[<>=]/,dateSQL:{},support:a("commentSlashSlash decimallessFloat"),hooks:{}}),e.defineMIME("text/x-plsql",{name:"sql",client:a("appinfo arraysize autocommit autoprint autorecovery autotrace blockterminator break btitle cmdsep colsep compatibility compute concat copycommit copytypecheck define describe echo editfile embedded escape exec execute feedback flagger flush heading headsep instance linesize lno loboffset logsource long longchunksize markup native newpage numformat numwidth pagesize pause pno recsep recsepchar release repfooter repheader serveroutput shiftinout show showmode size spool sqlblanklines sqlcase sqlcode sqlcontinue sqlnumber sqlpluscompatibility sqlprefix sqlprompt sqlterminator suffix tab term termout time timing trimout trimspool ttitle underline verify version wrap"),keywords:a("abort accept access add all alter and any array arraylen as asc assert assign at attributes audit authorization avg base_table begin between binary_integer body boolean by case cast char char_base check close cluster clusters colauth column comment commit compress connect connected constant constraint crash create current currval cursor data_base database date dba deallocate debugoff debugon decimal declare default definition delay delete desc digits dispose distinct do drop else elseif elsif enable end entry escape exception exception_init exchange exclusive exists exit external fast fetch file for force form from function generic goto grant group having identified if immediate in increment index indexes indicator initial initrans insert interface intersect into is key level library like limited local lock log logging long loop master maxextents maxtrans member minextents minus mislabel mode modify multiset new next no noaudit nocompress nologging noparallel not nowait number_base object of off offline on online only open option or order out package parallel partition pctfree pctincrease pctused pls_integer positive positiven pragma primary prior private privileges procedure public raise range raw read rebuild record ref references refresh release rename replace resource restrict return returning returns reverse revoke rollback row rowid rowlabel rownum rows run savepoint schema segment select separate session set share snapshot some space split sql start statement storage subtype successful synonym tabauth table tables tablespace task terminate then to trigger truncate type union unique unlimited unrecoverable unusable update use using validate value values variable view views when whenever where while with work"),builtin:a("abs acos add_months ascii asin atan atan2 average bfile bfilename bigserial bit blob ceil character chartorowid chr clob concat convert cos cosh count dec decode deref dual dump dup_val_on_index empty error exp false float floor found glb greatest hextoraw initcap instr instrb int integer isopen last_day least length lengthb ln lower lpad ltrim lub make_ref max min mlslabel mod months_between natural naturaln nchar nclob new_time next_day nextval nls_charset_decl_len nls_charset_id nls_charset_name nls_initcap nls_lower nls_sort nls_upper nlssort no_data_found notfound null number numeric nvarchar2 nvl others power rawtohex real reftohex round rowcount rowidtochar rowtype rpad rtrim serial sign signtype sin sinh smallint soundex sqlcode sqlerrm sqrt stddev string substr substrb sum sysdate tan tanh to_char text to_date to_label to_multi_byte to_number to_single_byte translate true trunc uid unlogged upper user userenv varchar varchar2 variance varying vsize xml"),operatorChars:/^[*\/+\-%<>!=~]/,dateSQL:a("date time timestamp"),support:a("doubleQuote nCharCast zerolessFloat binaryNumber hexNumber")}),e.defineMIME("text/x-hive",{name:"sql",keywords:a("select alter $elem$ $key$ $value$ add after all analyze and archive as asc before between binary both bucket buckets by cascade case cast change cluster clustered clusterstatus collection column columns comment compute concatenate continue create cross cursor data database databases dbproperties deferred delete delimited desc describe directory disable distinct distribute drop else enable end escaped exclusive exists explain export extended external fetch fields fileformat first format formatted from full function functions grant group having hold_ddltime idxproperties if import in index indexes inpath inputdriver inputformat insert intersect into is items join keys lateral left like limit lines load local location lock locks mapjoin materialized minus msck no_drop nocompress not of offline on option or order out outer outputdriver outputformat overwrite partition partitioned partitions percent plus preserve procedure purge range rcfile read readonly reads rebuild recordreader recordwriter recover reduce regexp rename repair replace restrict revoke right rlike row schema schemas semi sequencefile serde serdeproperties set shared show show_database sort sorted ssl statistics stored streamtable table tables tablesample tblproperties temporary terminated textfile then tmp to touch transform trigger unarchive undo union uniquejoin unlock update use using utc utc_tmestamp view when where while with admin authorization char compact compactions conf cube current current_date current_timestamp day decimal defined dependency directories elem_type exchange file following for grouping hour ignore inner interval jar less logical macro minute month more none noscan over owner partialscan preceding pretty principals protection reload rewrite role roles rollup rows second server sets skewed transactions truncate unbounded unset uri user values window year"),builtin:a("bool boolean long timestamp tinyint smallint bigint int float double date datetime unsigned string array struct map uniontype key_type utctimestamp value_type varchar"),atoms:a("false true null unknown"),operatorChars:/^[*+\-%<>!=]/,dateSQL:a("date timestamp"),support:a("doubleQuote binaryNumber hexNumber")}),e.defineMIME("text/x-pgsql",{name:"sql",client:a("source"),keywords:a(o+"a abort abs absent absolute access according action ada add admin after aggregate alias all allocate also alter always analyse analyze and any are array array_agg array_max_cardinality as asc asensitive assert assertion assignment asymmetric at atomic attach attribute attributes authorization avg backward base64 before begin begin_frame begin_partition bernoulli between bigint binary bit bit_length blob blocked bom boolean both breadth by c cache call called cardinality cascade cascaded case cast catalog catalog_name ceil ceiling chain char char_length character character_length character_set_catalog character_set_name character_set_schema characteristics characters check checkpoint class class_origin clob close cluster coalesce cobol collate collation collation_catalog collation_name collation_schema collect column column_name columns command_function command_function_code comment comments commit committed concurrently condition condition_number configuration conflict connect connection connection_name constant constraint constraint_catalog constraint_name constraint_schema constraints constructor contains content continue control conversion convert copy corr corresponding cost count covar_pop covar_samp create cross csv cube cume_dist current current_catalog current_date current_default_transform_group current_path current_role current_row current_schema current_time current_timestamp current_transform_group_for_type current_user cursor cursor_name cycle data database datalink datatype date datetime_interval_code datetime_interval_precision day db deallocate debug dec decimal declare default defaults deferrable deferred defined definer degree delete delimiter delimiters dense_rank depends depth deref derived desc describe descriptor detach detail deterministic diagnostics dictionary disable discard disconnect dispatch distinct dlnewcopy dlpreviouscopy dlurlcomplete dlurlcompleteonly dlurlcompletewrite dlurlpath dlurlpathonly dlurlpathwrite dlurlscheme dlurlserver dlvalue do document domain double drop dump dynamic dynamic_function dynamic_function_code each element else elseif elsif empty enable encoding encrypted end end_frame end_partition endexec enforced enum equals errcode error escape event every except exception exclude excluding exclusive exec execute exists exit exp explain expression extension external extract false family fetch file filter final first first_value flag float floor following for force foreach foreign fortran forward found frame_row free freeze from fs full function functions fusion g general generated get global go goto grant granted greatest group grouping groups handler having header hex hierarchy hint hold hour id identity if ignore ilike immediate immediately immutable implementation implicit import in include including increment indent index indexes indicator info inherit inherits initially inline inner inout input insensitive insert instance instantiable instead int integer integrity intersect intersection interval into invoker is isnull isolation join k key key_member key_type label lag language large last last_value lateral lead leading leakproof least left length level library like like_regex limit link listen ln load local localtime localtimestamp location locator lock locked log logged loop lower m map mapping match matched materialized max max_cardinality maxvalue member merge message message_length message_octet_length message_text method min minute minvalue mod mode modifies module month more move multiset mumps name names namespace national natural nchar nclob nesting new next nfc nfd nfkc nfkd nil no none normalize normalized not nothing notice notify notnull nowait nth_value ntile null nullable nullif nulls number numeric object occurrences_regex octet_length octets of off offset oids old on only open operator option options or order ordering ordinality others out outer output over overlaps overlay overriding owned owner p pad parallel parameter parameter_mode parameter_name parameter_ordinal_position parameter_specific_catalog parameter_specific_name parameter_specific_schema parser partial partition pascal passing passthrough password path percent percent_rank percentile_cont percentile_disc perform period permission pg_context pg_datatype_name pg_exception_context pg_exception_detail pg_exception_hint placing plans pli policy portion position position_regex power precedes preceding precision prepare prepared preserve primary print_strict_params prior privileges procedural procedure procedures program public publication query quote raise range rank read reads real reassign recheck recovery recursive ref references referencing refresh regr_avgx regr_avgy regr_count regr_intercept regr_r2 regr_slope regr_sxx regr_sxy regr_syy reindex relative release rename repeatable replace replica requiring reset respect restart restore restrict result result_oid return returned_cardinality returned_length returned_octet_length returned_sqlstate returning returns reverse revoke right role rollback rollup routine routine_catalog routine_name routine_schema routines row row_count row_number rows rowtype rule savepoint scale schema schema_name schemas scope scope_catalog scope_name scope_schema scroll search second section security select selective self sensitive sequence sequences serializable server server_name session session_user set setof sets share show similar simple size skip slice smallint snapshot some source space specific specific_name specifictype sql sqlcode sqlerror sqlexception sqlstate sqlwarning sqrt stable stacked standalone start state statement static statistics stddev_pop stddev_samp stdin stdout storage strict strip structure style subclass_origin submultiset subscription substring substring_regex succeeds sum symmetric sysid system system_time system_user t table table_name tables tablesample tablespace temp template temporary text then ties time timestamp timezone_hour timezone_minute to token top_level_count trailing transaction transaction_active transactions_committed transactions_rolled_back transform transforms translate translate_regex translation treat trigger trigger_catalog trigger_name trigger_schema trim trim_array true truncate trusted type types uescape unbounded uncommitted under unencrypted union unique unknown unlink unlisten unlogged unnamed unnest until untyped update upper uri usage use_column use_variable user user_defined_type_catalog user_defined_type_code user_defined_type_name user_defined_type_schema using vacuum valid validate validator value value_of values var_pop var_samp varbinary varchar variable_conflict variadic varying verbose version versioning view views volatile warning when whenever where while whitespace width_bucket window with within without work wrapper write xml xmlagg xmlattributes xmlbinary xmlcast xmlcomment xmlconcat xmldeclaration xmldocument xmlelement xmlexists xmlforest xmliterate xmlnamespaces xmlparse xmlpi xmlquery xmlroot xmlschema xmlserialize xmltable xmltext xmlvalidate year yes zone"),builtin:a("bigint int8 bigserial serial8 bit varying varbit boolean bool box bytea character char varchar cidr circle date double precision float8 inet integer int int4 interval json jsonb line lseg macaddr macaddr8 money numeric decimal path pg_lsn point polygon real float4 smallint int2 smallserial serial2 serial serial4 text time zone timetz timestamp timestamptz tsquery tsvector txid_snapshot uuid xml"),atoms:a("false true null unknown"),operatorChars:/^[*\/+\-%<>!=&|^\/#@?~]/,backslashStringEscapes:!1,dateSQL:a("date time timestamp"),support:a("decimallessFloat zerolessFloat binaryNumber hexNumber nCharCast charsetCast escapeConstant")}),e.defineMIME("text/x-gql",{name:"sql",keywords:a("ancestor and asc by contains desc descendant distinct from group has in is limit offset on order select superset where"),atoms:a("false true"),builtin:a("blob datetime first key __key__ string integer double boolean null"),operatorChars:/^[*+\-%<>!=]/}),e.defineMIME("text/x-gpsql",{name:"sql",client:a("source"),keywords:a("abort absolute access action active add admin after aggregate all also alter always analyse analyze and any array as asc assertion assignment asymmetric at authorization backward before begin between bigint binary bit boolean both by cache called cascade cascaded case cast chain char character characteristics check checkpoint class close cluster coalesce codegen collate column comment commit committed concurrency concurrently configuration connection constraint constraints contains content continue conversion copy cost cpu_rate_limit create createdb createexttable createrole createuser cross csv cube current current_catalog current_date current_role current_schema current_time current_timestamp current_user cursor cycle data database day deallocate dec decimal declare decode default defaults deferrable deferred definer delete delimiter delimiters deny desc dictionary disable discard distinct distributed do document domain double drop dxl each else enable encoding encrypted end enum errors escape every except exchange exclude excluding exclusive execute exists explain extension external extract false family fetch fields filespace fill filter first float following for force foreign format forward freeze from full function global grant granted greatest group group_id grouping handler hash having header hold host hour identity if ignore ilike immediate immutable implicit in including inclusive increment index indexes inherit inherits initially inline inner inout input insensitive insert instead int integer intersect interval into invoker is isnull isolation join key language large last leading least left level like limit list listen load local localtime localtimestamp location lock log login mapping master match maxvalue median merge minute minvalue missing mode modifies modify month move name names national natural nchar new newline next no nocreatedb nocreateexttable nocreaterole nocreateuser noinherit nologin none noovercommit nosuperuser not nothing notify notnull nowait null nullif nulls numeric object of off offset oids old on only operator option options or order ordered others out outer over overcommit overlaps overlay owned owner parser partial partition partitions passing password percent percentile_cont percentile_disc placing plans position preceding precision prepare prepared preserve primary prior privileges procedural procedure protocol queue quote randomly range read readable reads real reassign recheck recursive ref references reindex reject relative release rename repeatable replace replica reset resource restart restrict returning returns revoke right role rollback rollup rootpartition row rows rule savepoint scatter schema scroll search second security segment select sequence serializable session session_user set setof sets share show similar simple smallint some split sql stable standalone start statement statistics stdin stdout storage strict strip subpartition subpartitions substring superuser symmetric sysid system table tablespace temp template temporary text then threshold ties time timestamp to trailing transaction treat trigger trim true truncate trusted type unbounded uncommitted unencrypted union unique unknown unlisten until update user using vacuum valid validation validator value values varchar variadic varying verbose version view volatile web when where whitespace window with within without work writable write xml xmlattributes xmlconcat xmlelement xmlexists xmlforest xmlparse xmlpi xmlroot xmlserialize year yes zone"),builtin:a("bigint int8 bigserial serial8 bit varying varbit boolean bool box bytea character char varchar cidr circle date double precision float float8 inet integer int int4 interval json jsonb line lseg macaddr macaddr8 money numeric decimal path pg_lsn point polygon real float4 smallint int2 smallserial serial2 serial serial4 text time without zone with timetz timestamp timestamptz tsquery tsvector txid_snapshot uuid xml"),atoms:a("false true null unknown"),operatorChars:/^[*+\-%<>!=&|^\/#@?~]/,dateSQL:a("date time timestamp"),support:a("decimallessFloat zerolessFloat binaryNumber hexNumber nCharCast charsetCast")}),e.defineMIME("text/x-sparksql",{name:"sql",keywords:a("add after all alter analyze and anti archive array as asc at between bucket buckets by cache cascade case cast change clear cluster clustered codegen collection column columns comment commit compact compactions compute concatenate cost create cross cube current current_date current_timestamp database databases data dbproperties defined delete delimited deny desc describe dfs directories distinct distribute drop else end escaped except exchange exists explain export extended external false fields fileformat first following for format formatted from full function functions global grant group grouping having if ignore import in index indexes inner inpath inputformat insert intersect interval into is items join keys last lateral lazy left like limit lines list load local location lock locks logical macro map minus msck natural no not null nulls of on optimize option options or order out outer outputformat over overwrite partition partitioned partitions percent preceding principals purge range recordreader recordwriter recover reduce refresh regexp rename repair replace reset restrict revoke right rlike role roles rollback rollup row rows schema schemas select semi separated serde serdeproperties set sets show skewed sort sorted start statistics stored stratify struct table tables tablesample tblproperties temp temporary terminated then to touch transaction transactions transform true truncate unarchive unbounded uncache union unlock unset use using values view when where window with"),builtin:a("abs acos acosh add_months aggregate and any approx_count_distinct approx_percentile array array_contains array_distinct array_except array_intersect array_join array_max array_min array_position array_remove array_repeat array_sort array_union arrays_overlap arrays_zip ascii asin asinh assert_true atan atan2 atanh avg base64 between bigint bin binary bit_and bit_count bit_get bit_length bit_or bit_xor bool_and bool_or boolean bround btrim cardinality case cast cbrt ceil ceiling char char_length character_length chr coalesce collect_list collect_set concat concat_ws conv corr cos cosh cot count count_if count_min_sketch covar_pop covar_samp crc32 cume_dist current_catalog current_database current_date current_timestamp current_timezone current_user date date_add date_format date_from_unix_date date_part date_sub date_trunc datediff day dayofmonth dayofweek dayofyear decimal decode degrees delimited dense_rank div double e element_at elt encode every exists exp explode explode_outer expm1 extract factorial filter find_in_set first first_value flatten float floor forall format_number format_string from_csv from_json from_unixtime from_utc_timestamp get_json_object getbit greatest grouping grouping_id hash hex hour hypot if ifnull in initcap inline inline_outer input_file_block_length input_file_block_start input_file_name inputformat instr int isnan isnotnull isnull java_method json_array_length json_object_keys json_tuple kurtosis lag last last_day last_value lcase lead least left length levenshtein like ln locate log log10 log1p log2 lower lpad ltrim make_date make_dt_interval make_interval make_timestamp make_ym_interval map map_concat map_entries map_filter map_from_arrays map_from_entries map_keys map_values map_zip_with max max_by md5 mean min min_by minute mod monotonically_increasing_id month months_between named_struct nanvl negative next_day not now nth_value ntile nullif nvl nvl2 octet_length or outputformat overlay parse_url percent_rank percentile percentile_approx pi pmod posexplode posexplode_outer position positive pow power printf quarter radians raise_error rand randn random rank rcfile reflect regexp regexp_extract regexp_extract_all regexp_like regexp_replace repeat replace reverse right rint rlike round row_number rpad rtrim schema_of_csv schema_of_json second sentences sequence sequencefile serde session_window sha sha1 sha2 shiftleft shiftright shiftrightunsigned shuffle sign signum sin sinh size skewness slice smallint some sort_array soundex space spark_partition_id split sqrt stack std stddev stddev_pop stddev_samp str_to_map string struct substr substring substring_index sum tan tanh textfile timestamp timestamp_micros timestamp_millis timestamp_seconds tinyint to_csv to_date to_json to_timestamp to_unix_timestamp to_utc_timestamp transform transform_keys transform_values translate trim trunc try_add try_divide typeof ucase unbase64 unhex uniontype unix_date unix_micros unix_millis unix_seconds unix_timestamp upper uuid var_pop var_samp variance version weekday weekofyear when width_bucket window xpath xpath_boolean xpath_double xpath_float xpath_int xpath_long xpath_number xpath_short xpath_string xxhash64 year zip_with"),atoms:a("false true null"),operatorChars:/^[*\/+\-%<>!=~&|^]/,dateSQL:a("date time timestamp"),support:a("doubleQuote zerolessFloat")}),e.defineMIME("text/x-esper",{name:"sql",client:a("source"),keywords:a("alter and as asc between by count create delete desc distinct drop from group having in insert into is join like not on or order select set table union update values where limit after all and as at asc avedev avg between by case cast coalesce count create current_timestamp day days delete define desc distinct else end escape events every exists false first from full group having hour hours in inner insert instanceof into irstream is istream join last lastweekday left limit like max match_recognize matches median measures metadatasql min minute minutes msec millisecond milliseconds not null offset on or order outer output partition pattern prev prior regexp retain-union retain-intersection right rstream sec second seconds select set some snapshot sql stddev sum then true unidirectional until update variable weekday when where window"),builtin:{},atoms:a("false true null"),operatorChars:/^[*+\-%<>!=&|^\/#@?~]/,dateSQL:a("time"),support:a("decimallessFloat zerolessFloat binaryNumber hexNumber")}),e.defineMIME("text/x-trino",{name:"sql",keywords:a("abs absent acos add admin after all all_match alter analyze and any any_match approx_distinct approx_most_frequent approx_percentile approx_set arbitrary array_agg array_distinct array_except array_intersect array_join array_max array_min array_position array_remove array_sort array_union arrays_overlap as asc asin at at_timezone atan atan2 authorization avg bar bernoulli beta_cdf between bing_tile bing_tile_at bing_tile_coordinates bing_tile_polygon bing_tile_quadkey bing_tile_zoom_level bing_tiles_around bit_count bitwise_and bitwise_and_agg bitwise_left_shift bitwise_not bitwise_or bitwise_or_agg bitwise_right_shift bitwise_right_shift_arithmetic bitwise_xor bool_and bool_or both by call cardinality cascade case cast catalogs cbrt ceil ceiling char2hexint checksum chr classify coalesce codepoint column columns combinations comment commit committed concat concat_ws conditional constraint contains contains_sequence convex_hull_agg copartition corr cos cosh cosine_similarity count count_if covar_pop covar_samp crc32 create cross cube cume_dist current current_catalog current_date current_groups current_path current_role current_schema current_time current_timestamp current_timezone current_user data date_add date_diff date_format date_parse date_trunc day day_of_month day_of_week day_of_year deallocate default define definer degrees delete dense_rank deny desc describe descriptor distinct distributed dow doy drop e element_at else empty empty_approx_set encoding end error escape evaluate_classifier_predictions every except excluding execute exists exp explain extract false features fetch filter final first first_value flatten floor following for format format_datetime format_number from from_base from_base32 from_base64 from_base64url from_big_endian_32 from_big_endian_64 from_encoded_polyline from_geojson_geometry from_hex from_ieee754_32 from_ieee754_64 from_iso8601_date from_iso8601_timestamp from_iso8601_timestamp_nanos from_unixtime from_unixtime_nanos from_utf8 full functions geometric_mean geometry_from_hadoop_shape geometry_invalid_reason geometry_nearest_points geometry_to_bing_tiles geometry_union geometry_union_agg grant granted grants graphviz great_circle_distance greatest group grouping groups hamming_distance hash_counts having histogram hmac_md5 hmac_sha1 hmac_sha256 hmac_sha512 hour human_readable_seconds if ignore in including index infinity initial inner input insert intersect intersection_cardinality into inverse_beta_cdf inverse_normal_cdf invoker io is is_finite is_infinite is_json_scalar is_nan isolation jaccard_index join json_array json_array_contains json_array_get json_array_length json_exists json_extract json_extract_scalar json_format json_object json_parse json_query json_size json_value keep key keys kurtosis lag last last_day_of_month last_value lateral lead leading learn_classifier learn_libsvm_classifier learn_libsvm_regressor learn_regressor least left length level levenshtein_distance like limit line_interpolate_point line_interpolate_points line_locate_point listagg ln local localtime localtimestamp log log10 log2 logical lower lpad ltrim luhn_check make_set_digest map_agg map_concat map_entries map_filter map_from_entries map_keys map_union map_values map_zip_with match match_recognize matched matches materialized max max_by md5 measures merge merge_set_digest millisecond min min_by minute mod month multimap_agg multimap_from_entries murmur3 nan natural next nfc nfd nfkc nfkd ngrams no none none_match normal_cdf normalize not now nth_value ntile null nullif nulls numeric_histogram object objectid_timestamp of offset omit on one only option or order ordinality outer output over overflow parse_data_size parse_datetime parse_duration partition partitions passing past path pattern per percent_rank permute pi position pow power preceding prepare privileges properties prune qdigest_agg quarter quotes radians rand random range rank read recursive reduce reduce_agg refresh regexp_count regexp_extract regexp_extract_all regexp_like regexp_position regexp_replace regexp_split regr_intercept regr_slope regress rename render repeat repeatable replace reset respect restrict returning reverse revoke rgb right role roles rollback rollup round row_number rows rpad rtrim running scalar schema schemas second security seek select sequence serializable session set sets sha1 sha256 sha512 show shuffle sign simplify_geometry sin skewness skip slice some soundex spatial_partitioning spatial_partitions split split_part split_to_map split_to_multimap spooky_hash_v2_32 spooky_hash_v2_64 sqrt st_area st_asbinary st_astext st_boundary st_buffer st_centroid st_contains st_convexhull st_coorddim st_crosses st_difference st_dimension st_disjoint st_distance st_endpoint st_envelope st_envelopeaspts st_equals st_exteriorring st_geometries st_geometryfromtext st_geometryn st_geometrytype st_geomfrombinary st_interiorringn st_interiorrings st_intersection st_intersects st_isclosed st_isempty st_isring st_issimple st_isvalid st_length st_linefromtext st_linestring st_multipoint st_numgeometries st_numinteriorring st_numpoints st_overlaps st_point st_pointn st_points st_polygon st_relate st_startpoint st_symdifference st_touches st_union st_within st_x st_xmax st_xmin st_y st_ymax st_ymin start starts_with stats stddev stddev_pop stddev_samp string strpos subset substr substring sum system table tables tablesample tan tanh tdigest_agg text then ties timestamp_objectid timezone_hour timezone_minute to to_base to_base32 to_base64 to_base64url to_big_endian_32 to_big_endian_64 to_char to_date to_encoded_polyline to_geojson_geometry to_geometry to_hex to_ieee754_32 to_ieee754_64 to_iso8601 to_milliseconds to_spherical_geography to_timestamp to_unixtime to_utf8 trailing transaction transform transform_keys transform_values translate trim trim_array true truncate try try_cast type typeof uescape unbounded uncommitted unconditional union unique unknown unmatched unnest update upper url_decode url_encode url_extract_fragment url_extract_host url_extract_parameter url_extract_path url_extract_port url_extract_protocol url_extract_query use user using utf16 utf32 utf8 validate value value_at_quantile values values_at_quantiles var_pop var_samp variance verbose version view week week_of_year when where width_bucket wilson_interval_lower wilson_interval_upper window with with_timezone within without word_stem work wrapper write xxhash64 year year_of_week yow zip zip_with"),builtin:a("array bigint bingtile boolean char codepoints color date decimal double function geometry hyperloglog int integer interval ipaddress joniregexp json json2016 jsonpath kdbtree likepattern map model objectid p4hyperloglog precision qdigest re2jregexp real regressor row setdigest smallint sphericalgeography tdigest time timestamp tinyint uuid varbinary varchar zone"),atoms:a("false true null unknown"),operatorChars:/^[[\]|<>=!\-+*/%]/,dateSQL:a("date time timestamp zone"),support:a("decimallessFloat zerolessFloat hexNumber")})}(r(237))},956:(e,t,r)=>{!function(e){"use strict";e.defineMode("twig:inner",(function(){var e=["and","as","autoescape","endautoescape","block","do","endblock","else","elseif","extends","for","endfor","embed","endembed","filter","endfilter","flush","from","if","endif","in","is","include","import","not","or","set","spaceless","endspaceless","with","endwith","trans","endtrans","blocktrans","endblocktrans","macro","endmacro","use","verbatim","endverbatim"],t=/^[+\-*&%=<>!?|~^]/,r=/^[:\[\(\{]/,n=["true","false","null","empty","defined","divisibleby","divisible by","even","odd","iterable","sameas","same as"],i=/^(\d[+\-\*\/])?\d+(\.\d+)?/;function o(o,a){var s=o.peek();if(a.incomment)return o.skipTo("#}")?(o.eatWhile(/\#|}/),a.incomment=!1):o.skipToEnd(),"comment";if(a.intag){if(a.operator){if(a.operator=!1,o.match(n))return"atom";if(o.match(i))return"number"}if(a.sign){if(a.sign=!1,o.match(n))return"atom";if(o.match(i))return"number"}if(a.instring)return s==a.instring&&(a.instring=!1),o.next(),"string";if("'"==s||'"'==s)return a.instring=s,o.next(),"string";if(o.match(a.intag+"}")||o.eat("-")&&o.match(a.intag+"}"))return a.intag=!1,"tag";if(o.match(t))return a.operator=!0,"operator";if(o.match(r))a.sign=!0;else if(o.eat(" ")||o.sol()){if(o.match(e))return"keyword";if(o.match(n))return"atom";if(o.match(i))return"number";o.sol()&&o.next()}else o.next();return"variable"}if(o.eat("{")){if(o.eat("#"))return a.incomment=!0,o.skipTo("#}")?(o.eatWhile(/\#|}/),a.incomment=!1):o.skipToEnd(),"comment";if(s=o.eat(/\{|%/))return a.intag=s,"{"==s&&(a.intag="}"),o.eat("-"),"tag"}o.next()}return e=new RegExp("(("+e.join(")|(")+"))\\b"),n=new RegExp("(("+n.join(")|(")+"))\\b"),{startState:function(){return{}},token:function(e,t){return o(e,t)}}})),e.defineMode("twig",(function(t,r){var n=e.getMode(t,"twig:inner");return r&&r.base?e.multiplexingMode(e.getMode(t,r.base),{open:/\{[{#%]/,close:/[}#%]\}/,mode:n,parseDelimiters:!0}):n})),e.defineMIME("text/x-twig","twig")}(r(237),r(340))},576:(e,t,r)=>{!function(e){"use strict";var t={autoSelfClosers:{area:!0,base:!0,br:!0,col:!0,command:!0,embed:!0,frame:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0,menuitem:!0},implicitlyClosed:{dd:!0,li:!0,optgroup:!0,option:!0,p:!0,rp:!0,rt:!0,tbody:!0,td:!0,tfoot:!0,th:!0,tr:!0},contextGrabbers:{dd:{dd:!0,dt:!0},dt:{dd:!0,dt:!0},li:{li:!0},option:{option:!0,optgroup:!0},optgroup:{optgroup:!0},p:{address:!0,article:!0,aside:!0,blockquote:!0,dir:!0,div:!0,dl:!0,fieldset:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,menu:!0,nav:!0,ol:!0,p:!0,pre:!0,section:!0,table:!0,ul:!0},rp:{rp:!0,rt:!0},rt:{rp:!0,rt:!0},tbody:{tbody:!0,tfoot:!0},td:{td:!0,th:!0},tfoot:{tbody:!0},th:{td:!0,th:!0},thead:{tbody:!0,tfoot:!0},tr:{tr:!0}},doNotIndent:{pre:!0},allowUnquoted:!0,allowMissing:!0,caseFold:!0},r={autoSelfClosers:{},implicitlyClosed:{},contextGrabbers:{},doNotIndent:{},allowUnquoted:!1,allowMissing:!1,allowMissingTagName:!1,caseFold:!1};e.defineMode("xml",(function(n,i){var o,a,s=n.indentUnit,l={},c=i.htmlMode?t:r;for(var u in c)l[u]=c[u];for(var u in i)l[u]=i[u];function d(e,t){function r(r){return t.tokenize=r,r(e,t)}var n=e.next();return"<"==n?e.eat("!")?e.eat("[")?e.match("CDATA[")?r(m("atom","]]>")):null:e.match("--")?r(m("comment","--\x3e")):e.match("DOCTYPE",!0,!0)?(e.eatWhile(/[\w\._\-]/),r(h(1))):null:e.eat("?")?(e.eatWhile(/[\w\._\-]/),t.tokenize=m("meta","?>"),"meta"):(o=e.eat("/")?"closeTag":"openTag",t.tokenize=p,"tag bracket"):"&"==n?(e.eat("#")?e.eat("x")?e.eatWhile(/[a-fA-F\d]/)&&e.eat(";"):e.eatWhile(/[\d]/)&&e.eat(";"):e.eatWhile(/[\w\.\-:]/)&&e.eat(";"))?"atom":"error":(e.eatWhile(/[^&<]/),null)}function p(e,t){var r=e.next();if(">"==r||"/"==r&&e.eat(">"))return t.tokenize=d,o=">"==r?"endTag":"selfcloseTag","tag bracket";if("="==r)return o="equals",null;if("<"==r){t.tokenize=d,t.state=b,t.tagName=t.tagStart=null;var n=t.tokenize(e,t);return n?n+" tag error":"tag error"}return/[\'\"]/.test(r)?(t.tokenize=f(r),t.stringStartCol=e.column(),t.tokenize(e,t)):(e.match(/^[^\s\u00a0=<>\"\']*[^\s\u00a0=<>\"\'\/]/),"word")}function f(e){var t=function(t,r){for(;!t.eol();)if(t.next()==e){r.tokenize=p;break}return"string"};return t.isInAttribute=!0,t}function m(e,t){return function(r,n){for(;!r.eol();){if(r.match(t)){n.tokenize=d;break}r.next()}return e}}function h(e){return function(t,r){for(var n;null!=(n=t.next());){if("<"==n)return r.tokenize=h(e+1),r.tokenize(t,r);if(">"==n){if(1==e){r.tokenize=d;break}return r.tokenize=h(e-1),r.tokenize(t,r)}}return"meta"}}function g(e){return e&&e.toLowerCase()}function v(e,t,r){this.prev=e.context,this.tagName=t||"",this.indent=e.indented,this.startOfLine=r,(l.doNotIndent.hasOwnProperty(t)||e.context&&e.context.noIndent)&&(this.noIndent=!0)}function y(e){e.context&&(e.context=e.context.prev)}function _(e,t){for(var r;;){if(!e.context)return;if(r=e.context.tagName,!l.contextGrabbers.hasOwnProperty(g(r))||!l.contextGrabbers[g(r)].hasOwnProperty(g(t)))return;y(e)}}function b(e,t,r){return"openTag"==e?(r.tagStart=t.column(),x):"closeTag"==e?k:b}function x(e,t,r){return"word"==e?(r.tagName=t.current(),a="tag",C):l.allowMissingTagName&&"endTag"==e?(a="tag bracket",C(e,t,r)):(a="error",x)}function k(e,t,r){if("word"==e){var n=t.current();return r.context&&r.context.tagName!=n&&l.implicitlyClosed.hasOwnProperty(g(r.context.tagName))&&y(r),r.context&&r.context.tagName==n||!1===l.matchClosing?(a="tag",w):(a="tag error",S)}return l.allowMissingTagName&&"endTag"==e?(a="tag bracket",w(e,t,r)):(a="error",S)}function w(e,t,r){return"endTag"!=e?(a="error",w):(y(r),b)}function S(e,t,r){return a="error",w(e,t,r)}function C(e,t,r){if("word"==e)return a="attribute",M;if("endTag"==e||"selfcloseTag"==e){var n=r.tagName,i=r.tagStart;return r.tagName=r.tagStart=null,"selfcloseTag"==e||l.autoSelfClosers.hasOwnProperty(g(n))?_(r,n):(_(r,n),r.context=new v(r,n,i==r.indented)),b}return a="error",C}function M(e,t,r){return"equals"==e?T:(l.allowMissing||(a="error"),C(e,t,r))}function T(e,t,r){return"string"==e?L:"word"==e&&l.allowUnquoted?(a="string",C):(a="error",C(e,t,r))}function L(e,t,r){return"string"==e?L:C(e,t,r)}return d.isInText=!0,{startState:function(e){var t={tokenize:d,state:b,indented:e||0,tagName:null,tagStart:null,context:null};return null!=e&&(t.baseIndent=e),t},token:function(e,t){if(!t.tagName&&e.sol()&&(t.indented=e.indentation()),e.eatSpace())return null;o=null;var r=t.tokenize(e,t);return(r||o)&&"comment"!=r&&(a=null,t.state=t.state(o||r,e,t),a&&(r="error"==a?r+" error":a)),r},indent:function(t,r,n){var i=t.context;if(t.tokenize.isInAttribute)return t.tagStart==t.indented?t.stringStartCol+1:t.indented+s;if(i&&i.noIndent)return e.Pass;if(t.tokenize!=p&&t.tokenize!=d)return n?n.match(/^(\s*)/)[0].length:0;if(t.tagName)return!1!==l.multilineTagIndentPastTag?t.tagStart+t.tagName.length+2:t.tagStart+s*(l.multilineTagIndentFactor||1);if(l.alignCDATA&&/<!\[CDATA\[/.test(r))return 0;var o=r&&/^<(\/)?([\w_:\.-]*)/.exec(r);if(o&&o[1])for(;i;){if(i.tagName==o[2]){i=i.prev;break}if(!l.implicitlyClosed.hasOwnProperty(g(i.tagName)))break;i=i.prev}else if(o)for(;i;){var a=l.contextGrabbers[g(i.tagName)];if(!a||!a.hasOwnProperty(g(o[2])))break;i=i.prev}for(;i&&i.prev&&!i.startOfLine;)i=i.prev;return i?i.indent+s:t.baseIndent||0},electricInput:/<\/[\s\w:]+>$/,blockCommentStart:"\x3c!--",blockCommentEnd:"--\x3e",configuration:l.htmlMode?"html":"xml",helperType:l.htmlMode?"html":"xml",skipAttribute:function(e){e.state==T&&(e.state=C)},xmlCurrentTag:function(e){return e.tagName?{name:e.tagName,close:"closeTag"==e.type}:null},xmlCurrentContext:function(e){for(var t=[],r=e.context;r;r=r.prev)t.push(r.tagName);return t.reverse()}}})),e.defineMIME("text/xml","xml"),e.defineMIME("application/xml","xml"),e.mimeModes.hasOwnProperty("text/html")||e.defineMIME("text/html",{name:"xml",htmlMode:!0})}(r(237))},82:(e,t,r)=>{var n,i,o,a;n=r(237),r(496),i=0,o=1,a=2,n.defineMode("yaml-frontmatter",(function(e,t){var r=n.getMode(e,"yaml"),s=n.getMode(e,t&&t.base||"gfm");function l(e){return e.state==o?{mode:r,state:e.yaml}:{mode:s,state:e.inner}}return{startState:function(){return{state:i,yaml:null,inner:n.startState(s)}},copyState:function(e){return{state:e.state,yaml:e.yaml&&n.copyState(r,e.yaml),inner:n.copyState(s,e.inner)}},token:function(e,t){if(t.state==i)return e.match("---",!1)?(t.state=o,t.yaml=n.startState(r),r.token(e,t.yaml)):(t.state=a,s.token(e,t.inner));if(t.state==o){var l=e.sol()&&e.match(/(---|\.\.\.)/,!1),c=r.token(e,t.yaml);return l&&(t.state=a,t.yaml=null),c}return s.token(e,t.inner)},innerMode:l,indent:function(e,t,r){var i=l(e);return i.mode.indent?i.mode.indent(i.state,t,r):n.Pass},blankLine:function(e){var t=l(e);if(t.mode.blankLine)return t.mode.blankLine(t.state)}}}))},496:(e,t,r)=>{!function(e){"use strict";e.defineMode("yaml",(function(){var e=new RegExp("\\b(("+["true","false","on","off","yes","no"].join(")|(")+"))$","i");return{token:function(t,r){var n=t.peek(),i=r.escaped;if(r.escaped=!1,"#"==n&&(0==t.pos||/\s/.test(t.string.charAt(t.pos-1))))return t.skipToEnd(),"comment";if(t.match(/^('([^']|\\.)*'?|"([^"]|\\.)*"?)/))return"string";if(r.literal&&t.indentation()>r.keyCol)return t.skipToEnd(),"string";if(r.literal&&(r.literal=!1),t.sol()){if(r.keyCol=0,r.pair=!1,r.pairStart=!1,t.match("---"))return"def";if(t.match("..."))return"def";if(t.match(/\s*-\s+/))return"meta"}if(t.match(/^(\{|\}|\[|\])/))return"{"==n?r.inlinePairs++:"}"==n?r.inlinePairs--:"["==n?r.inlineList++:r.inlineList--,"meta";if(r.inlineList>0&&!i&&","==n)return t.next(),"meta";if(r.inlinePairs>0&&!i&&","==n)return r.keyCol=0,r.pair=!1,r.pairStart=!1,t.next(),"meta";if(r.pairStart){if(t.match(/^\s*(\||\>)\s*/))return r.literal=!0,"meta";if(t.match(/^\s*(\&|\*)[a-z0-9\._-]+\b/i))return"variable-2";if(0==r.inlinePairs&&t.match(/^\s*-?[0-9\.\,]+\s?$/))return"number";if(r.inlinePairs>0&&t.match(/^\s*-?[0-9\.\,]+\s?(?=(,|}))/))return"number";if(t.match(e))return"keyword"}return!r.pair&&t.match(/^\s*(?:[,\[\]{}&*!|>'"%@`][^\s'":]|[^\s,\[\]{}#&*!|>'"%@`])[^#:]*(?=:($|\s))/)?(r.pair=!0,r.keyCol=t.indentation(),"atom"):r.pair&&t.match(/^:\s*/)?(r.pairStart=!0,"meta"):(r.pairStart=!1,r.escaped="\\"==n,t.next(),null)},startState:function(){return{pair:!1,pairStart:!1,keyCol:0,inlinePairs:0,inlineList:0,literal:!1,escaped:!1}},lineComment:"#",fold:"indent"}})),e.defineMIME("text/x-yaml","yaml"),e.defineMIME("text/yaml","yaml")}(r(237))},136:(e,t,r)=>{"use strict";r.r(t)}},t={};function r(n){var i=t[n];if(void 0!==i)return i.exports;var o=t[n]={exports:{}};return e[n].call(o.exports,o,o.exports,r),o.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{"use strict";var e=r(237),t=r.n(e);r(656),r(838),r(792),r(216),r(460),r(0),r(684),r(532),r(956),r(576),r(82),r(496),r(865);r(136),document.addEventListener("DOMContentLoaded",(function(){document.querySelectorAll("[data-ea-code-editor-field]").forEach((function(e){var r=t().fromTextArea(e,{autocapitalize:!1,autocorrect:!1,indentWithTabs:"true"===e.dataset.indentWithTabs,lineNumbers:"true"===e.dataset.showLineNumbers,lineWrapping:!0,mode:e.dataset.language,scrollbarStyle:"native",spellcheck:!1,tabSize:e.dataset.tabSize,theme:"default",autoRefresh:!0,readOnly:e.readOnly});if(e.required&&r.on("change",r.save),""!==e.dataset.numberOfRows){var n=r.getWrapperElement();n.style.setProperty("height","".concat(21*e.dataset.numberOfRows,"px"),"important"),n.style.setProperty("max-block-size","none")}}))}))})()})();