<?php

namespace App\Entity\Activite;

use App\Entity\Base\BaseEntity;
use App\Entity\Praticien\Agent;
use App\Entity\Trait\HorodatageTrait;
use App\Repository\SigapsRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: SigapsRepository::class)]
#[ORM\Table(name: "sigaps", indexes: [
    new ORM\Index(name: "idx_valid_from", columns: ["validFrom"]),
    new ORM\Index(name: "idx_valid_to", columns: ["validTo"]),
    new ORM\Index(name: "idx_periode_type", columns: ["periodeType"])
])]
class Sigaps extends BaseEntity
{
    use HorodatageTrait;
    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $dateDebut = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $dateFin = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 10, scale: 2, nullable: true)]
    private ?string $score = null;

    #[ORM\Column(type: Types::JSON, nullable: true)]
    private ?array $repartition_par_categorie = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $categorie = null;

    #[ORM\Column(nullable: true)]
    private ?int $nombre_publication = null;

    #[ORM\ManyToOne(inversedBy: 'sigaps')]
    private ?Agent $agent = null;
    

    public function getDateDebut(): ?\DateTimeInterface
    {
        return $this->dateDebut;
    }

    public function setDateDebut(?\DateTimeInterface $dateDebut): static
    {
        $this->dateDebut = $dateDebut;
        return $this;
    }

    public function getDateFin(): ?\DateTimeInterface
    {
        return $this->dateFin;
    }

    public function setDateFin(?\DateTimeInterface $dateFin): static
    {
        $this->dateFin = $dateFin;
        return $this;
    }

    public function getScore(): ?string
    {
        return $this->score;
    }

    public function setScore(?string $score): static
    {
        $this->score = $score;
        return $this;
    }

    public function getRepartitionParCategorie(): ?array
    {
        return $this->repartition_par_categorie;
    }

    public function setRepartitionParCategorie(?array $repartition_par_categorie): static
    {
        $this->repartition_par_categorie = $repartition_par_categorie;
        return $this;
    }


    public function getAgent(): ?Agent
    {
        return $this->agent;
    }

    public function setAgent(?Agent $agent): static
    {
        $this->agent = $agent;
        return $this;
    }

    public function getCategorie(): ?string
    {
        return $this->categorie;
    }

    public function setCategorie(?string $categorie): static
    {
        $this->categorie = $categorie;
        return $this;
    }

    public function getNombrePublication(): ?int
    {
        return $this->nombre_publication;
    }

    public function setNombrePublication(?int $nombre_publication): static
    {
        $this->nombre_publication = $nombre_publication;
        return $this;
    }
}
