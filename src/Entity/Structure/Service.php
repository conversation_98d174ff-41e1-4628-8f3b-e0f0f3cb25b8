<?php

namespace App\Entity\Structure;

use ApiPlatform\Doctrine\Orm\Filter\SearchFilter;
use ApiPlatform\Metadata\ApiFilter;
use App\Entity\Base\BaseEntity;
use App\Entity\Trait\HorodatageTrait;
use App\Repository\ServiceRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: ServiceRepository::class)]
#[ApiFilter(SearchFilter::class, properties: [
    'secode' => 'partial',
    'libelle' => 'partial'
])]
class Service extends BaseEntity
{
    use HorodatageTrait;

    #[ORM\Column(length: 5, nullable: true)]
    private ?string $etab = null;

    #[ORM\Column(length: 4, nullable: true)]
    private ?string $secode = null;

    #[ORM\Column(type: 'date', nullable: true)]
    private ?\DateTimeInterface $datdeb = null;

    #[ORM\Column(type: 'date', nullable: true)]
    private ?\DateTimeInterface $datfin = null;


    #[ORM\Column(length: 100, nullable: true)]
    private ?string $libelle = null;

    #[ORM\Column(length: 5, nullable: true)]
    private ?string $cdcode = null;

    #[ORM\Column(length: 2, nullable: true)]
    private ?string $tacode = null;

    #[ORM\Column(length: 20, nullable: true)]
    private ?string $pfuser = null;

    #[ORM\Column(type: 'date', nullable: true)]
    private ?\DateTimeInterface $dmdacre = null;

    #[ORM\Column(type: 'date', nullable: true)]
    private ?\DateTimeInterface $dmdamaj = null;

    #[ORM\ManyToOne(inversedBy: 'services')]
    private ?EntiteJuridique $hopital = null;

    /**
     * @var Collection<int, Ufs>
     */
    #[ORM\OneToMany(targetEntity: Ufs::class, mappedBy: 'service')]
    private Collection $ufs;

    public function __construct()
    {
        parent::__construct();
        $this->ufs = new ArrayCollection();
    }

    public function getLibelle(): ?string
    {
        return $this->libelle;
    }

    public function setLibelle(?string $libelle): static
    {
        $this->libelle = $libelle;
        return $this;
    }

    public function getSecode(): ?string
    {
        return $this->secode;
    }

    public function setSecode(?string $secode): static
    {
        $this->secode = $secode;
        return $this;
    }

    public function getEtab(): ?string
    {
        return $this->etab;
    }

    public function setEtab(?string $etab): static
    {
        $this->etab = $etab;
        return $this;
    }

    public function getDatdeb(): ?\DateTimeInterface
    {
        return $this->datdeb;
    }

    public function setDatdeb(?\DateTimeInterface $datdeb): static
    {
        $this->datdeb = $datdeb;
        return $this;
    }

    public function getDatfin(): ?\DateTimeInterface
    {
        return $this->datfin;
    }

    public function setDatfin(?\DateTimeInterface $datfin): static
    {
        $this->datfin = $datfin;
        return $this;
    }
    public function getCdcode(): ?string
    {
        return $this->cdcode;
    }

    public function setCdcode(?string $cdcode): static
    {
        $this->cdcode = $cdcode;
        return $this;
    }

    public function getTacode(): ?string
    {
        return $this->tacode;
    }

    public function setTacode(?string $tacode): static
    {
        $this->tacode = $tacode;
        return $this;
    }

    public function getPfuser(): ?string
    {
        return $this->pfuser;
    }

    public function setPfuser(?string $pfuser): static
    {
        $this->pfuser = $pfuser;
        return $this;
    }

    public function getDmdacre(): ?\DateTimeInterface
    {
        return $this->dmdacre;
    }

    public function setDmdacre(?\DateTimeInterface $dmdacre): static
    {
        $this->dmdacre = $dmdacre;
        return $this;
    }

    public function getDmdamaj(): ?\DateTimeInterface
    {
        return $this->dmdamaj;
    }

    public function setDmdamaj(?\DateTimeInterface $dmdamaj): static
    {
        $this->dmdamaj = $dmdamaj;
        return $this;
    }

    public function getHopital(): ?EntiteJuridique
    {
        return $this->hopital;
    }

    public function setHopital(?EntiteJuridique $hopital): void
    {
        $this->hopital = $hopital;
    }

    /**
     * @return Collection<int, Ufs>
     */
    public function getUfs(): Collection
    {
        return $this->ufs;
    }

    public function addUf(Ufs $uf): static
    {
        if (!$this->ufs->contains($uf)) {
            $this->ufs->add($uf);
            $uf->setService($this);
        }

        return $this;
    }

    public function removeUf(Ufs $uf): static
    {
        if ($this->ufs->removeElement($uf)) {
            // set the owning side to null (unless already changed)
            if ($uf->getService() === $this) {
                $uf->setService(null);
            }
        }

        return $this;
    }
    public function __toString(): string
    {
        return sprintf(
            '%s (%s)',
            $this->libelle,
            $this->secode)
            ;
    }
}
