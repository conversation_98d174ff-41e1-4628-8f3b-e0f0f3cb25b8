<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="robots" content="noindex, nofollow">
    <meta name="referrer" content="strict-origin-when-cross-origin">
    <title>{% block title %}Connexion - Administration Supra{% endblock %}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 25px 45px rgba(0, 0, 0, 0.15);
            padding: 50px 40px;
            width: 100%;
            max-width: 450px;
            position: relative;
            animation: slideUp 0.8s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .login-header h1 {
            color: #2d3748;
            font-size: 2.2rem;
            font-weight: 700;
            margin-bottom: 8px;
            letter-spacing: -0.5px;
        }

        .login-header p {
            color: #718096;
            font-size: 1rem;
            font-weight: 400;
        }

        .error-message {
            background: linear-gradient(135deg, #fc8181 0%, #f56565 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 25px;
            font-size: 0.9rem;
            text-align: center;
            animation: shake 0.5s ease-in-out;
        }
        
        .error-feedback {
            color: #e53e3e;
            font-size: 0.8rem;
            margin-top: 5px;
            min-height: 1rem;
            font-weight: 500;
        }
        

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .form-group {
            margin-bottom: 25px;
            position: relative;
        }

        .form-group label {
            display: block;
            color: #4a5568;
            font-weight: 600;
            margin-bottom: 8px;
            font-size: 0.95rem;
        }

        .form-input {
            width: 100%;
            padding: 16px 20px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f7fafc;
            outline: none;
        }

        .form-input:focus {
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
        }

        .form-input:hover {
            border-color: #cbd5e0;
            background: white;
        }

        .remember-group {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
        }

        .checkbox-wrapper {
            position: relative;
            display: flex;
            align-items: center;
        }

        .form-checkbox {
            appearance: none;
            width: 20px;
            height: 20px;
            border: 2px solid #e2e8f0;
            border-radius: 6px;
            margin-right: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .form-checkbox:checked {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: #667eea;
        }

        .form-checkbox:checked::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .checkbox-label {
            color: #4a5568;
            font-size: 0.95rem;
            cursor: pointer;
            user-select: none;
        }

        .submit-btn {
            width: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 18px 24px;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .submit-btn:active {
            transform: translateY(0);
        }

        .submit-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .submit-btn:hover::before {
            left: 100%;
        }

        .floating-shapes {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .shape {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }

        .shape1 {
            top: 10%;
            left: 10%;
            width: 80px;
            height: 80px;
            background: white;
            border-radius: 50%;
            animation-delay: 0s;
        }

        .shape2 {
            top: 70%;
            right: 10%;
            width: 120px;
            height: 120px;
            background: white;
            border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
            animation-delay: 2s;
        }

        .shape3 {
            bottom: 20%;
            left: 20%;
            width: 60px;
            height: 60px;
            background: white;
            border-radius: 20%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        @media (max-width: 768px) {
            .login-container {
                margin: 20px;
                padding: 40px 30px;
            }

            .login-header h1 {
                font-size: 1.8rem;
            }
        }
    </style>
</head>
<body>
    <div class="floating-shapes">
        <div class="shape shape1"></div>
        <div class="shape shape2"></div>
        <div class="shape shape3"></div>
    </div>

    <div class="login-container">
        <div class="login-header">
            <h1>Administration</h1>
            <p>Accès sécurisé à l'espace administrateur</p>
        </div>

        {% if error %}
            <div class="error-message">
                {{ error.messageKey|trans(error.messageData, 'security') }}
            </div>
        {% endif %}

        <form method="post" id="loginForm" autocomplete="off" novalidate>
            <input type="hidden" name="_token" value="{{ csrf_token('authenticate') }}">
            
            {# Honeypot field to catch bots - should remain empty #}
            <div style="display:none">
                <input type="text" 
                       name="website" 
                       id="website" 
                       tabindex="-1" 
                       autocomplete="off">
            </div>

            <div class="form-group">
                <label for="email">Adresse email</label>
                <input type="email"
                       id="email"
                       name="email"
                       class="form-input"
                       value="{{ last_username }}"
                       required
                       autocomplete="email"
                       placeholder="<EMAIL>"
                       pattern="[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}"
                       title="Veuillez entrer une adresse email valide">
                <div class="error-feedback" id="email-error"></div>
            </div>

            <div class="form-group">
                <label for="password">Mot de passe</label>
                <input type="password"
                       id="password"
                       name="password"
                       class="form-input"
                       required
                       autocomplete="current-password"
                       placeholder="••••••••"
                       minlength="8"
                       title="Veuillez entrer votre mot de passe">
                <div class="error-feedback" id="password-error"></div>
            </div>

            
            {# Display flash messages #}
            {% for message in app.flashes('error') %}
                <div class="error-message">
                    {{ message }}
                </div>
            {% endfor %}

            <div class="remember-group">
                <div class="checkbox-wrapper">
                    <input type="checkbox"
                           id="remember_me"
                           name="_remember_me"
                           class="form-checkbox">
                    <label for="remember_me" class="checkbox-label">Se souvenir de moi</label>
                </div>
            </div>

            <button type="submit" class="submit-btn" id="submitBtn">
                Se connecter
            </button>
        </form>
    </div>

    <script>
        // Animation d'entrée pour les champs et validation du formulaire
        document.addEventListener('DOMContentLoaded', function() {
            const inputs = document.querySelectorAll('.form-input');
            const submitBtn = document.getElementById('submitBtn');
            const form = document.getElementById('loginForm');
            const emailInput = document.getElementById('email');
            const passwordInput = document.getElementById('password');
            const emailError = document.getElementById('email-error');
            const passwordError = document.getElementById('password-error');

            // Focus automatique sur le premier champ
            emailInput.focus();

            // Fonction de validation de l'email
            function validateEmail(email) {
                const re = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                return re.test(String(email).toLowerCase());
            }

            // Validation du formulaire avant soumission
            form.addEventListener('submit', function(e) {
                let isValid = true;
                
                // Réinitialiser les messages d'erreur
                emailError.textContent = '';
                passwordError.textContent = '';
                
                // Vérifier le champ email
                if (!emailInput.value.trim()) {
                    emailError.textContent = 'L\'adresse email est requise';
                    emailInput.style.borderColor = '#fc8181';
                    isValid = false;
                } else if (!validateEmail(emailInput.value.trim())) {
                    emailError.textContent = 'Veuillez entrer une adresse email valide';
                    emailInput.style.borderColor = '#fc8181';
                    isValid = false;
                }
                
                // Vérifier le champ mot de passe
                if (!passwordInput.value) {
                    passwordError.textContent = 'Le mot de passe est requis';
                    passwordInput.style.borderColor = '#fc8181';
                    isValid = false;
                }
                
                // Vérifier le honeypot (s'il est rempli, c'est probablement un bot)
                const honeypot = document.getElementById('website');
                if (honeypot.value) {
                    console.log('Bot détecté via honeypot');
                    e.preventDefault();
                    return false;
                }
                
                // Si le formulaire est valide, afficher l'animation de chargement
                if (isValid) {
                    submitBtn.innerHTML = `
                        <span style="display: inline-flex; align-items: center;">
                            <svg style="animation: spin 1s linear infinite; width: 20px; height: 20px; margin-right: 8px;" viewBox="0 0 24 24" fill="none">
                                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" opacity="0.25"/>
                                <path fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"/>
                            </svg>
                            Connexion en cours...
                        </span>
                    `;
                    submitBtn.disabled = true;
                } else {
                    e.preventDefault();
                }
            });

            // Validation en temps réel
            inputs.forEach(input => {
                input.addEventListener('blur', function() {
                    if (this.value.trim() === '') {
                        this.style.borderColor = '#fc8181';
                        this.style.boxShadow = '0 0 0 3px rgba(252, 129, 129, 0.1)';
                        
                        // Afficher le message d'erreur approprié
                        if (this.id === 'email') {
                            emailError.textContent = 'L\'adresse email est requise';
                        } else if (this.id === 'password') {
                            passwordError.textContent = 'Le mot de passe est requis';
                        }
                    } else {
                        // Validation spécifique pour l'email
                        if (this.id === 'email' && !validateEmail(this.value.trim())) {
                            this.style.borderColor = '#fc8181';
                            this.style.boxShadow = '0 0 0 3px rgba(252, 129, 129, 0.1)';
                            emailError.textContent = 'Veuillez entrer une adresse email valide';
                        } else {
                            this.style.borderColor = '#48bb78';
                            this.style.boxShadow = '0 0 0 3px rgba(72, 187, 120, 0.1)';
                            
                            // Effacer le message d'erreur
                            if (this.id === 'email') {
                                emailError.textContent = '';
                            } else if (this.id === 'password') {
                                passwordError.textContent = '';
                            }
                        }
                    }
                });

                input.addEventListener('input', function() {
                    if (this.style.borderColor === 'rgb(252, 129, 129)') {
                        this.style.borderColor = '#e2e8f0';
                        this.style.boxShadow = 'none';
                        
                        // Effacer le message d'erreur
                        if (this.id === 'email') {
                            emailError.textContent = '';
                        } else if (this.id === 'password') {
                            passwordError.textContent = '';
                        }
                    }
                });
            });
            
            // Ajouter une protection contre les attaques par force brute côté client
            let loginAttempts = parseInt(localStorage.getItem('loginAttempts') || '0');
            let lastAttemptTime = parseInt(localStorage.getItem('lastAttemptTime') || '0');
            const now = Date.now();
            const cooldownPeriod = 15 * 60 * 1000; // 15 minutes en millisecondes
            
            // Réinitialiser le compteur si la période de cooldown est passée
            if (now - lastAttemptTime > cooldownPeriod) {
                loginAttempts = 0;
                localStorage.setItem('loginAttempts', '0');
            }
            
            // Si trop de tentatives, désactiver le formulaire
            if (loginAttempts >= 5) {
                const remainingTime = Math.ceil((lastAttemptTime + cooldownPeriod - now) / 1000 / 60);
                const errorMessage = document.createElement('div');
                errorMessage.className = 'error-message';
                errorMessage.textContent = `Trop de tentatives de connexion. Veuillez réessayer dans ${remainingTime} minute(s).`;
                form.parentNode.insertBefore(errorMessage, form);
                form.style.opacity = '0.5';
                form.style.pointerEvents = 'none';
            }
            
            // Incrémenter le compteur à chaque tentative
            form.addEventListener('submit', function() {
                if (loginAttempts < 5) {
                    loginAttempts++;
                    localStorage.setItem('loginAttempts', loginAttempts.toString());
                    localStorage.setItem('lastAttemptTime', Date.now().toString());
                }
            });
        });

        // Ajout du style pour l'animation de rotation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
