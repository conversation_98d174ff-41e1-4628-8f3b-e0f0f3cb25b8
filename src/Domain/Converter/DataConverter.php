<?php

namespace App\Domain\Converter;


/**
 * Classe utilitaire pour la conversion de données lors des importations.
 */
class DataConverter
{
    /**
     * Convertit une chaîne en float en gérant les valeurs nulles et vides.
     */
    public static function toFloat(?string $value): ?float
    {
        if ($value === null || trim($value) === '') {
            return null;
        }

        return (float) str_replace(',', '.', trim($value));
    }

    /**
     * Convertit une chaîne en integer en gérant les valeurs nulles et vides.
     */
    public static function toInt(?string $value): ?int
    {
        if ($value === null || trim($value) === '') {
            return null;
        }

        return (int) trim($value);
    }

    /**
     * Convertit une chaîne de date en DateTime.
     */
    public static function toDateTime(?string $value): ?\DateTime
    {
        if ($value === null || trim($value) === '') {
            return null;
        }

        try {
            return new \DateTime(trim($value));
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Convertit une chaîne de date avec un format spécifique en DateTime.
     */
    public static function parseDateFromFormat(?string $dateStr, string $format = 'd/m/Y'): ?\DateTime
    {
        if (empty($dateStr)) {
            return null;
        }

        try {
            $date = \DateTime::createFromFormat($format, trim($dateStr));
            // Vérification que la date a été créée correctement
            return $date !== false ? $date : null;
        } catch (\Exception) {
            return null;
        }
    }

    /**
     * Calcule l'ETP basé sur l'ETP statutaire et le taux d'affectation.
     */
    public static function calculateEtp(?float $etpStatutaire, ?int $tauxAffectation): ?float
    {
        if ($etpStatutaire === null || $tauxAffectation === null) {
            return null;
        }

        return $etpStatutaire * ($tauxAffectation / 100);
    }

    /**
     * Nettoie et normalise une chaîne de caractères.
     */
    public static function cleanString(?string $value): ?string
    {
        if ($value === null) {
            return null;
        }

        $cleaned = trim($value);
        return $cleaned === '' ? null : $cleaned;
    }
}