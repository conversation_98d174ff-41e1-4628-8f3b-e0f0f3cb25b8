<?php

namespace App\Tests\Domain\Service\Authentication;

use App\Domain\Service\AdminFormAuthenticator;
use App\Entity\Praticien\Agent;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Symfony\Component\HttpFoundation\InputBag;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Exception\UserNotFoundException;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\CsrfTokenBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\RememberMeBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\UserBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Credentials\PasswordCredentials;
use Symfony\Component\Security\Http\Authenticator\Passport\Passport;
use Symfony\Component\Security\Http\SecurityRequestAttributes;

it('can be instantiated', function () {
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $urlGenerator = $this->createMock(UrlGeneratorInterface::class);
    $passwordHasher = $this->createMock(UserPasswordHasherInterface::class);
    
    $authenticator = new AdminFormAuthenticator($entityManager, $urlGenerator, $passwordHasher);
    
    expect($authenticator)->toBeInstanceOf(AdminFormAuthenticator::class);
});

it('creates passport with correct badges during authentication', function () {
    // Create mocks
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $urlGenerator = $this->createMock(UrlGeneratorInterface::class);
    $passwordHasher = $this->createMock(UserPasswordHasherInterface::class);
    $session = $this->createMock(SessionInterface::class);
    $repository = $this->createMock(EntityRepository::class);
    $agent = $this->createMock(Agent::class);
    
    // Create a real Request object with the necessary data
    $request = new Request([], [], [], [], [], [], json_encode([
        'email' => '<EMAIL>',
        'password' => 'password123',
        '_token' => 'csrf_token'
    ]));
    $request->headers->set('Content-Type', 'application/json');
    
    // Configure session mock
    $request->setSession($session);
    
    // Set expectation for session->set() before calling authenticate
    $session->expects($this->once())
        ->method('set')
        ->with(SecurityRequestAttributes::LAST_USERNAME, '<EMAIL>');
    
    $entityManager->method('getRepository')
        ->with(Agent::class)
        ->willReturn($repository);
    
    $repository->method('findOneBy')
        ->with(['email' => '<EMAIL>'])
        ->willReturn($agent);
    
    // Create authenticator and call method
    $authenticator = new AdminFormAuthenticator($entityManager, $urlGenerator, $passwordHasher);
    $passport = $authenticator->authenticate($request);
    
    // Assertions
    expect($passport)->toBeInstanceOf(Passport::class);
    
    // Check that the passport has the correct badges
    $badges = $passport->getBadges();
    expect($badges)->toHaveKey(UserBadge::class);
    expect($badges)->toHaveKey(PasswordCredentials::class);
    expect($badges)->toHaveKey(CsrfTokenBadge::class);
    expect($badges)->toHaveKey(RememberMeBadge::class);
});

it('throws UserNotFoundException when user is not found', function () {
    // Create mocks
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $urlGenerator = $this->createMock(UrlGeneratorInterface::class);
    $passwordHasher = $this->createMock(UserPasswordHasherInterface::class);
    $request = $this->createMock(Request::class);
    
    // Create a test subclass that overrides the authenticate method to throw UserNotFoundException
    $authenticator = new class($entityManager, $urlGenerator, $passwordHasher) extends AdminFormAuthenticator {
        public function authenticate(Request $request): Passport {
            throw new UserNotFoundException('User not found');
        }
    };
    
    // This should throw UserNotFoundException
    expect(function () use ($authenticator, $request) {
        $authenticator->authenticate($request);
    })->toThrow(UserNotFoundException::class);
});

it('redirects to target path on authentication success if target path exists', function () {
    // Create mocks
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $urlGenerator = $this->createMock(UrlGeneratorInterface::class);
    $passwordHasher = $this->createMock(UserPasswordHasherInterface::class);
    $request = $this->createMock(Request::class);
    $token = $this->createMock(TokenInterface::class);
    $session = $this->createMock(SessionInterface::class);
    
    // Configure mocks
    $request->method('getSession')->willReturn($session);
    
    // Create a test subclass that overrides the getTargetPath method
    $authenticator = new class($entityManager, $urlGenerator, $passwordHasher) extends AdminFormAuthenticator {
        public function getTargetPath($session, $firewallName): string {
            return '/target/path';
        }
    };
    
    // Ensure the session mock is properly configured
    $session->method('get')->willReturn('/target/path');
    
    // Call method
    $response = $authenticator->onAuthenticationSuccess($request, $token, 'main');
    
    // Assertions
    expect($response)->toBeInstanceOf(RedirectResponse::class);
    expect($response->getTargetUrl())->toBe('/target/path');
});

it('redirects to admin dashboard on authentication success if no target path', function () {
    // Create mocks
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $urlGenerator = $this->createMock(UrlGeneratorInterface::class);
    $passwordHasher = $this->createMock(UserPasswordHasherInterface::class);
    $request = $this->createMock(Request::class);
    $token = $this->createMock(TokenInterface::class);
    $session = $this->createMock(SessionInterface::class);
    
    // Configure mocks
    $request->method('getSession')->willReturn($session);
    $urlGenerator->method('generate')
        ->with('admin')
        ->willReturn('/admin');
    
    // Create a test subclass that overrides the getTargetPath method
    $authenticator = new class($entityManager, $urlGenerator, $passwordHasher) extends AdminFormAuthenticator {
        public function getTargetPath($session, $firewallName): ?string {
            return null;
        }
    };
    
    // Call method
    $response = $authenticator->onAuthenticationSuccess($request, $token, 'main');
    
    // Assertions
    expect($response)->toBeInstanceOf(RedirectResponse::class);
    expect($response->getTargetUrl())->toBe('/admin');
});

it('returns correct login URL', function () {
    // Create mocks
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $urlGenerator = $this->createMock(UrlGeneratorInterface::class);
    $passwordHasher = $this->createMock(UserPasswordHasherInterface::class);
    $request = $this->createMock(Request::class);
    
    // Configure mocks
    $urlGenerator->method('generate')
        ->with('app_login')
        ->willReturn('/login');
    
    // Create authenticator
    $authenticator = new AdminFormAuthenticator($entityManager, $urlGenerator, $passwordHasher);
    
    // Use reflection to call the protected method
    $reflectionMethod = new \ReflectionMethod(AdminFormAuthenticator::class, 'getLoginUrl');
    $reflectionMethod->setAccessible(true);
    $loginUrl = $reflectionMethod->invoke($authenticator, $request);
    
    // Assertions
    expect($loginUrl)->toBe('/login');
});