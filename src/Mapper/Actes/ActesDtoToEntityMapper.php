<?php

namespace App\Mapper\Actes;

use App\ApiResource\Activite\ActesDto;
use App\Entity\Activite\Actes;
use App\Repository\ActesRepository;
use App\Repository\AgentRepository;
use App\Repository\UfsRepository;
use Doctrine\DBAL\Driver\PDO\Exception;
use Symfonycasts\MicroMapper\AsMapper;
use Symfonycasts\MicroMapper\MapperInterface;

/**
 * Mapper pour convertir ActesDto en Actes entity.
 * @see State/Batch/ActesBatchProcessor.php pour le traitement par lot (csv).
 */
#[AsMapper(from: ActesDto::class, to: Actes::class)]
class ActesDtoToEntityMapper implements MapperInterface
{
    public function __construct(
        private ActesRepository     $actesRepository,
        private AgentRepository $agentRepository,
        private UfsRepository       $ufsRepository
    ) {}

    public function load(object $from, string $toClass, array $context): object
    {
        $dto = $from;
        assert($dto instanceof ActesDto);

        // On récupère l'entité existante si un ID est fourni, sinon on en crée une nouvelle
        $acte = $dto->id ? $this->actesRepository->find($dto->id) : new Actes();
        if (!$acte) {
            throw new Exception('Acte non trouvé');
        }

        return $acte;
    }

    public function populate(object $from, object $to, array $context): object
    {
        $dto = $from;
        $acte = $to;
        assert($dto instanceof ActesDto);
        assert($acte instanceof Actes);

        $acte->setCode($dto->code);
        $acte->setDescription($dto->description);

        $acte->setDateRealisation($dto->dateRealisation);
        $acte->setTypeActe($dto->typeActe);
        $acte->setNombreDeRealisation($dto->nombreDeRealisation);
        $acte->setAnnee($dto->annee);
        $acte->setMois($dto->mois);

        // Champs du trait HorodatageTrait
        $acte->setValidFrom($dto->validFrom);
        $acte->setValidTo($dto->validTo);
        $acte->setPeriodeType($dto->periodeType);
        $acte->setSource($dto->source);

        // Convertir l'IRI API Platform en entité Agent
        if ($dto->agent) {
            $agentId = basename($dto->agent->id);
            $agent = $this->agentRepository->find($agentId);
            if (!$agent) {
                throw new Exception('Agent non trouvé');
            }
            $acte->setAgent($agent);
        }

        // Mapper les nouveaux champs
        $acte->setInternum($dto->internum);
        $acte->setSemaineIso($dto->semaineIso);
        $acte->setTypeVenue($dto->typeVenue);
        $acte->setLibTypeVenue($dto->libTypeVenue);
        $acte->setActivite($dto->activite);
        $acte->setActiviteLib($dto->activiteLib);
        $acte->setIcrA($dto->icrA);
        $acte->setCoefficient($dto->coefficient);
        $acte->setLettreCoef($dto->lettreCoef);

        // Convertir l'IRI API Platform en entité UF Principal
        if ($dto->ufPrincipal) {
            $ufPrincipalId = basename($dto->ufPrincipal->id);
            $ufPrincipal = $this->ufsRepository->find($ufPrincipalId);
            if (!$ufPrincipal) {
                throw new Exception('UF Principal non trouvé');
            }
            $acte->setUfPrincipal($ufPrincipal);
        }

        // Convertir l'IRI API Platform en entité UF Demande
        if ($dto->ufDemande) {
            $ufDemandeId = basename($dto->ufDemande->id);
            $ufDemande = $this->ufsRepository->find($ufDemandeId);
            if (!$ufDemande) {
                throw new Exception('UF Demande non trouvé');
            }
            $acte->setUfDemande($ufDemande);
        }

        // Convertir l'IRI API Platform en entité UF Intervention
        if ($dto->ufIntervention) {
            $ufInterventionId = basename($dto->ufIntervention->id);
            $ufIntervention = $this->ufsRepository->find($ufInterventionId);
            if (!$ufIntervention) {
                throw new Exception('UF Intervention non trouvé');
            }
            $acte->setUfIntervention($ufIntervention);
        }

        return $acte;
    }
}
