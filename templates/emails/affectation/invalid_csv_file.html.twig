<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fichier CSV invalide</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #fd7e14; color: white; padding: 20px; border-radius: 5px; text-align: center; }
        .content { background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-top: 20px; }
        .warning-box { background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .example-box { background-color: white; padding: 15px; border-radius: 5px; margin: 15px 0; border-left: 4px solid #007bff; }
        .footer { text-align: center; margin-top: 20px; font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="header">
        <h1>⚠️ Fichier CSV invalide détecté</h1>
        <p>Entité Juridique: {{ entiteJuridique.code }}</p>
    </div>

    <div class="content">
        <h2>Problème de format détecté</h2>
        <p>Un fichier CSV avec un format invalide a été détecté le {{ timestamp|date('d/m/Y à H:i:s') }}.</p>

        <div class="warning-box">
            <h4 style="color: #856404; margin-top: 0;">📁 Fichier concerné</h4>
            <p style="color: #856404;"><strong>{{ filePath }}</strong></p>
            <p style="color: #856404;"><strong>Erreur :</strong> {{ errorMessage }}</p>
        </div>

        <div class="example-box">
            <h4 style="color: #007bff; margin-top: 0;">📋 Structure CSV correcte</h4>
            <p>Votre fichier CSV doit respecter exactement cette structure :</p>

            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 3px; font-family: monospace;">
                <strong>En-têtes (ligne 1) :</strong><br>
                {{ csvExample.headers }}<br><br>

                <strong>Données (lignes suivantes) :</strong><br>
                {{ csvExample.example1 }}<br>
                {{ csvExample.example2 }}
            </div>
        </div>

        <div style="background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0;">
            <h4 style="color: #0056b3; margin-top: 0;">📝 Points importants</h4>
            <ul style="color: #0056b3;">
                <li><strong>Séparateur :</strong> Point-virgule (;) uniquement</li>
                <li><strong>Encodage :</strong> UTF-8</li>
                <li><strong>Champs obligatoires :</strong> uConnexion et code_uf</li>
                <li><strong>Format des dates :</strong> YYYY-MM-DD (exemple: 2024-01-01)</li>
                <li><strong>Valeurs vides :</strong> Laisser vide après le point-virgule (pas d'espace)</li>
            </ul>
        </div>

        <div style="background-color: #d1ecf1; padding: 15px; border-radius: 5px; margin: 15px 0;">
            <h4 style="color: #0c5460; margin-top: 0;">🔧 Actions à effectuer</h4>
            <ol style="color: #0c5460;">
                <li>Corrigez le format de votre fichier CSV selon l'exemple ci-dessus</li>
                <li>Vérifiez que tous les champs obligatoires sont remplis</li>
                <li>Sauvegardez le fichier en UTF-8 avec séparateur point-virgule</li>
                <li>Redéposez le fichier corrigé dans le dossier surveillé</li>
            </ol>
        </div>

        <div style="margin-top: 20px;">
            <p><strong>Dossier surveillé :</strong> {{ entiteJuridique.affectationAgentFilesPaths }}</p>
            <p><small>Le fichier invalide a été déplacé vers le sous-dossier "errors" pour inspection.</small></p>
        </div>
    </div>

    <div class="footer">
        <p>Ce message a été généré automatiquement par le système SUPRA.<br>
        Entité Juridique: {{ entiteJuridique.code }}<br>
        Pour toute assistance technique, contactez : {{ entiteJuridique.adminEmail }}</p>
    </div>
</body>
</html>
