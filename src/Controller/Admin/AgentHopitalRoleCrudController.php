<?php

namespace App\Controller\Admin;

use App\Domain\Enum\AgentRoleType;
use App\Entity\Praticien\Agent;
use App\Entity\Praticien\AgentHopitalRole;
use App\Repository\AgentRepository;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\BooleanField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ChoiceField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateTimeField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\HttpFoundation\Response;

#[IsGranted('ROLE_ADMIN')]
class AgentHopitalRoleCrudController extends AbstractCrudController
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private AgentRepository $agentRepository
    ) {}

    public static function getEntityFqcn(): string
    {
        return AgentHopitalRole::class;
    }

    public function configureActions(Actions $actions): Actions
    {
        return $actions
            ->add(Crud::PAGE_INDEX, Action::DETAIL)
            ->setPermission(Action::NEW, 'ROLE_ADMIN')
            ->setPermission(Action::EDIT, 'ROLE_ADMIN')
            ->setPermission(Action::DELETE, 'ROLE_ADMIN');
    }

    public function createEntity(string $entityFqcn)
    {
        // Créer une entité vide - l'utilisateur saisira l'email et choisira le rôle
        return new AgentHopitalRole();
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setEntityLabelInSingular('Rôle Agent')
            ->setEntityLabelInPlural('Rôles des Agents')
            ->setPageTitle('index', 'Gestion des rôles des agents')
            ->setPageTitle('new', 'Assigner un nouveau rôle')
            ->setPageTitle('edit', 'Modifier le rôle')
            ->setPageTitle('detail', 'Détail du rôle de l\'agent')
            ->showEntityActionsInlined();
    }

    public function configureFields(string $pageName): iterable
    {
        /** @var Agent $currentUser */
        $currentUser = $this->getUser();
        $userRoles = $currentUser->getRoles();

        // Trouve le plus haut niveau de rôle de l'utilisateur actuel
        $maxUserLevel = 1; // Par défaut ROLE_USER
        foreach ($userRoles as $role) {
            $level = AgentRoleType::getRoleLevel($role);
            if ($level > $maxUserLevel) {
                $maxUserLevel = $level;
            }
        }

        // Récupère les rôles que l'utilisateur peut assigner
        $assignableRoles = [];
        foreach (AgentRoleType::ALL_ROLES as $role) {
            if (AgentRoleType::getRoleLevel($role) <= $maxUserLevel) {
                $assignableRoles[$role] = $role;
            }
        }

        $fields = [];
        // Utiliser une string au lieu de la constante
        if ($pageName === 'index') {
            $fields[] = TextField::new('id')->hideOnForm();
            $fields[] = BooleanField::new('isActif')->setLabel('Est actif');
            $fields[] = TextField::new('agent.email')->setLabel('Email de l\'agent');
            $fields[] = AssociationField::new('hopital')->setLabel('Hôpital');
            $fields[] = ChoiceField::new('role')
                ->setLabel('Rôle')
                ->setChoices($assignableRoles)
                ->renderAsBadges([
                    'ROLE_USER' => 'success',
                    'ROLE_ADMIN' => 'primary',
                    'ROLE_CHEF_DE_POLE' => 'secondary',
                    'ROLE_MANAGER' => 'info',
                    'ROLE_CME' => 'danger',
                    'ROLE_FIFAP' => 'danger',
                ]);
            $fields[] = DateTimeField::new('dateAffectation')->setLabel('Date d\'affectation');
        } else {
            $fields[] = TextField::new('id')->hideOnForm();
            $fields[] = BooleanField::new('isActif')->setLabel('Est actif');
            $fields[] = DateField::new('dateCreation')->setLabel('Date de création')->setDisabled(true);

            // Pour l'édition, pré-remplir avec l'email de l'agent actuel
            $entity = $this->getContext()->getEntity()->getInstance();
            $defaultEmail = '';
            if ($entity instanceof AgentHopitalRole && $entity->getAgent()) {
                $defaultEmail = $entity->getAgent()->getEmail();
            }

            $fields[] = TextField::new('agentEmail')
                ->setLabel('Email de l\'agent')
                ->setFormTypeOption('mapped', false)
                ->setFormTypeOption('data', $defaultEmail)
                ->setRequired(true)
                ->setHelp('Vérifiez d\'abord que l\'agent avec cet email existe dans la table Agents. Le système recherchera l\'agent correspondant à cet email.');
            $fields[] = AssociationField::new('hopital')->setLabel('Hôpital')->setRequired(true);
            $fields[] = ChoiceField::new('role')
                ->setLabel('Rôle')
                ->setChoices($assignableRoles)
                ->setRequired(true)
                ->setHelp('Vous ne pouvez assigner que les rôles de niveau inférieur ou égal au vôtre')
                ->renderAsBadges([
                    'ROLE_USER' => 'success',
                    'ROLE_ADMIN' => 'primary',
                    'ROLE_CHEF_DE_POLE' => 'secondary',
                    'ROLE_MANAGER' => 'info',
                    'ROLE_CME' => 'warning',
                    'ROLE_FIFAP' => 'danger',
                ]);
            $fields[] = DateTimeField::new('dateAffectation')->setLabel('Date d\'affectation')->setRequired(true);
        }
        return $fields;
    }
    public function persistEntity(EntityManagerInterface $entityManager, $entityInstance): void
    {
        /** @var AgentHopitalRole $entityInstance */
        if ($entityInstance instanceof AgentHopitalRole) {
            try {
                // Récupérer l'email de l'agent depuis le formulaire de manière plus robuste
                $request = $this->getContext()->getRequest();
                $formData = $request->request->all();

                // Essayer plusieurs chemins possibles pour récupérer l'email
                $agentEmail = null;
                if (isset($formData['AgentHopitalRole']['agentEmail'])) {
                    $agentEmail = $formData['AgentHopitalRole']['agentEmail'];
                } elseif (isset($formData['agentEmail'])) {
                    $agentEmail = $formData['agentEmail'];
                }

                if (empty($agentEmail)) {
                    throw new \InvalidArgumentException("L'email de l'agent est obligatoire");
                }

                // Nettoyer l'email (trim, lowercase)
                $agentEmail = trim(strtolower($agentEmail));

                // Rechercher l'agent par email
                $agent = $this->agentRepository->findByIdentifier($agentEmail);

                if (!$agent) {
                    throw new \InvalidArgumentException("Aucun agent trouvé avec l'email: $agentEmail. Vérifiez que l'agent existe dans la table des agents.");
                }

                // Définir l'agent trouvé dans l'entité
                $entityInstance->setAgent($agent);

                // Valider l'assignation de rôle
                $this->validateRoleAssignment($entityInstance);

                // Persister l'entité
                parent::persistEntity($entityManager, $entityInstance);

                // Synchroniser les rôles après persistance
                $this->syncAgentRoles($agent);

            } catch (\Exception $e) {
                // Log l'erreur pour debug
                error_log("Erreur dans persistEntity AgentHopitalRole: " . $e->getMessage());
                throw $e;
            }
        } else {
            parent::persistEntity($entityManager, $entityInstance);
        }
    }

    public function updateEntity(EntityManagerInterface $entityManager, $entityInstance): void
    {
        /** @var AgentHopitalRole $entityInstance */
        if ($entityInstance instanceof AgentHopitalRole) {
            try {
                // Récupérer l'email de l'agent depuis le formulaire de manière plus robuste
                $request = $this->getContext()->getRequest();
                $formData = $request->request->all();

                // Essayer plusieurs chemins possibles pour récupérer l'email
                $agentEmail = null;
                if (isset($formData['AgentHopitalRole']['agentEmail'])) {
                    $agentEmail = $formData['AgentHopitalRole']['agentEmail'];
                } elseif (isset($formData['agentEmail'])) {
                    $agentEmail = $formData['agentEmail'];
                }

                // Si pas d'email dans le formulaire, garder l'agent existant
                if (!empty($agentEmail)) {
                    // Nettoyer l'email (trim, lowercase)
                    $agentEmail = trim(strtolower($agentEmail));

                    // Rechercher l'agent par email
                    $agent = $this->agentRepository->findByIdentifier($agentEmail);

                    if (!$agent) {
                        throw new \InvalidArgumentException("Aucun agent trouvé avec l'email: $agentEmail. Vérifiez que l'agent existe dans la table des agents.");
                    }

                    // Définir l'agent trouvé dans l'entité
                    $entityInstance->setAgent($agent);
                }

                // Valider l'assignation de rôle
                $this->validateRoleAssignment($entityInstance);

                $agent = $entityInstance->getAgent();

                // Mettre à jour l'entité
                parent::updateEntity($entityManager, $entityInstance);

                // Synchroniser les rôles après mise à jour
                $this->syncAgentRoles($agent);

            } catch (\Exception $e) {
                // Log l'erreur pour debug
                error_log("Erreur dans updateEntity AgentHopitalRole: " . $e->getMessage());
                throw $e;
            }
        } else {
            parent::updateEntity($entityManager, $entityInstance);
        }
    }

    public function deleteEntity(EntityManagerInterface $entityManager, $entityInstance): void
    {
        /** @var AgentHopitalRole $entityInstance */
        if ($entityInstance instanceof AgentHopitalRole) {
            $agent = $entityInstance->getAgent();
            parent::deleteEntity($entityManager, $entityInstance);
            
            // Resynchronise après suppression
            $this->syncAgentRoles($agent);
        } else {
            parent::deleteEntity($entityManager, $entityInstance);
        }
    }

    /**
     * Synchronise les rôles dans Agent.roles[] avec tous les AgentHopitalRole
     * en respectant la hiérarchie des rôles (si un rôle supérieur est assigné,
     * tous les rôles inférieurs sont également inclus)
     */
    private function syncAgentRoles(Agent $agent): void
    {
        try {
            // Recharger l'agent depuis la base pour éviter les conflits
            $this->entityManager->refresh($agent);

            // Récupère tous les rôles depuis AgentHopitalRole
            $allRoles = $agent->getAllRoles(); // Cette méthode existe déjà

            // Ajoute toujours ROLE_USER
            if (!in_array('ROLE_USER', $allRoles)) {
                $allRoles[] = 'ROLE_USER';
            }

            // Vérification explicite pour ROLE_CHEF_DE_POLE
            if (in_array('ROLE_CHEF_DE_POLE', $allRoles) && !in_array('ROLE_ADMIN', $allRoles)) {
                $allRoles[] = 'ROLE_ADMIN'; // Ajoute explicitement ROLE_ADMIN si ROLE_CHEF_DE_POLE est présent
            }

            // Ajoute les rôles inférieurs selon la hiérarchie
            $expandedRoles = [];
            foreach ($allRoles as $role) {
                $expandedRoles[] = $role;
                $roleLevel = AgentRoleType::getRoleLevel($role);

                // Pour chaque rôle, ajoute tous les rôles de niveau inférieur
                foreach (AgentRoleType::ROLE_HIERARCHY as $lowerRole => $lowerLevel) {
                    if ($lowerLevel < $roleLevel && !in_array($lowerRole, $expandedRoles)) {
                        $expandedRoles[] = $lowerRole;
                    }
                }
            }

            // Met à jour le champ roles de l'agent
            $agent->setRoles(array_unique($expandedRoles));

            // Flush avec gestion d'erreur
            $this->entityManager->flush();

        } catch (\Exception $e) {
            // Log l'erreur mais ne pas faire échouer l'opération principale
            error_log("Erreur lors de la synchronisation des rôles pour l'agent {$agent->getId()}: " . $e->getMessage());
            // Optionnel : relancer l'exception si critique
            // throw $e;
        }
    }

    private function validateRoleAssignment(AgentHopitalRole $agentHopitalRole): void
    {
        /** @var Agent $currentUser */
        $currentUser = $this->getUser();
        $userRoles = $currentUser->getRoles();

        // Trouve le plus haut niveau de rôle de l'utilisateur actuel
        $maxUserLevel = 1;
        foreach ($userRoles as $role) {
            $level = AgentRoleType::getRoleLevel($role);
            if ($level > $maxUserLevel) {
                $maxUserLevel = $level;
            }
        }

        $targetRoleLevel = AgentRoleType::getRoleLevel($agentHopitalRole->getRole());

        if ($targetRoleLevel > $maxUserLevel) {
            throw new \Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException(
                'Vous ne pouvez pas assigner un rôle de niveau supérieur au vôtre.'
            );
        }
    }
}
