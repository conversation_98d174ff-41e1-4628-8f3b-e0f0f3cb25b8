<?php

namespace App\ApiResource\Activite\Partials;

use ApiPlatform\Metadata\ApiProperty;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Get;
use App\State\Partials\ActeTemporalStatsProvider;
use ApiPlatform\OpenApi\Model;


#[ApiResource(
    shortName: 'ActeTemporalStats',
    operations: [
        new Get(
            uriTemplate: '/actes/{acteCode}/nb-realisation-par-periode-par-mois-stats',
            requirements: ['acteCode' => '^[A-Z0-9]+$'],
            openapi: new Model\Operation(
                summary: 'Récupère les statistiques mensuelles pour un acte sur 3 périodes',
                description: 'Retourne un tableau des réalisations mensuelles d\'un acte médical pour 3 périodes distinctes',
            ),
            provider: ActeTemporalStatsProvider::class
        )
    ]
)]
class ActeTemporalStatsResource
{
    public function __construct(
        #[ApiProperty(identifier: true)]
        public readonly string $acteCode
    ) {}
}