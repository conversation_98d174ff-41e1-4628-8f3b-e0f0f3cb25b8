<?php

// This is a simple test script to verify that the source field is correctly set to the ejcode value from the route parameters

function testSourceFieldSetting() {
    echo "Testing source field setting in ActesBatchProcessor\n";
    echo "==================================================\n\n";

    // Simulate the createDtoFromArray method with different input scenarios
    $uriVariables = ['ejcode' => 'CHU001'];
    
    // Test Case 1: Without source field in input
    $item1 = [
        'code' => 'CCAM001',
        'description' => 'Consultation cardiologique',
        'dateRealisation' => '2025-08-27T14:30:00+02:00'
    ];
    
    echo "Test Case 1: Without source field in input\n";
    echo "Input: " . json_encode($item1) . "\n";
    echo "URI Variables: " . json_encode($uriVariables) . "\n";
    echo "Expected source: " . $uriVariables['ejcode'] . "\n\n";
    
    // Test Case 2: With source field in input (should be ignored)
    $item2 = [
        'code' => 'CCAM002',
        'description' => 'Échographie cardiaque',
        'dateRealisation' => '2025-08-28T10:15:00+02:00',
        'source' => 'Système externe'
    ];
    
    echo "Test Case 2: With source field in input (should be ignored)\n";
    echo "Input: " . json_encode($item2) . "\n";
    echo "URI Variables: " . json_encode($uriVariables) . "\n";
    echo "Expected source: " . $uriVariables['ejcode'] . " (ignoring '" . $item2['source'] . "')\n\n";
    
    // Test Case 3: Without ejcode in URI variables
    $emptyUriVariables = [];
    $item3 = [
        'code' => 'CCAM003',
        'description' => 'Radiographie thoracique',
        'dateRealisation' => '2025-08-29T08:45:00+02:00'
    ];
    
    echo "Test Case 3: Without ejcode in URI variables\n";
    echo "Input: " . json_encode($item3) . "\n";
    echo "URI Variables: " . json_encode($emptyUriVariables) . "\n";
    echo "Expected source: null\n\n";
    
    echo "To verify the fix, please run the actual batch processing with these test cases\n";
    echo "and check if the source in the response matches the expected values.\n";
    echo "The source field should be set to the ejcode value from the route parameters,\n";
    echo "regardless of whether it's provided in the input data.\n";
}

// Run the test
testSourceFieldSetting();