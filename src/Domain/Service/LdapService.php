<?php
namespace App\Domain\Service;

use App\Entity\Praticien\Agent;
use App\Entity\Structure\EntiteJuridique;
use Symfony\Component\Security\Core\Exception\AuthenticationException;

/**
 * Service for handling LDAP operations
 */
class LdapService
{
    private const DEFAULT_BASE_DN = "ou=_chu-nancy, dc=chu-nancy,dc=fr";

    public function __construct(
        private CoreService $coreService
    ) {}

    /**
     * Connect to LDAP server and return connection resource
     */
    public function connect(string $ldapHost): \LDAP\Connection
    {
        $ldapConn = ldap_connect($ldapHost);
        
        if (!$ldapConn) {
            throw new AuthenticationException('Échec de connexion au serveur LDAP');
        }
        
        ldap_set_option($ldapConn, LDAP_OPT_PROTOCOL_VERSION, 3);
        ldap_set_option($ldapConn, LDAP_OPT_REFERRALS, 0);
        
        return $ldapConn;
    }

    /**
     * Bind to LDAP server with service account credentials
     */
    public function bindWithServiceAccount(
        \LDAP\Connection $ldapConn,
        ?string $serviceAccountEmail = null,
        ?string $serviceAccountPassword = null,
        ?EntiteJuridique $hopital = null
    ): void
    {
        if ($hopital) {
            $email = $hopital->getLdapServiceAccountEmail();
            $password = $hopital->getLdapServiceAccountPassword();
        } else {
            $email = $serviceAccountEmail ?? $this->coreService->getLdapServiceAccountEmail();
            $password = $serviceAccountPassword ?? $this->coreService->getLdapServiceAccountPassword();
        }

        if (!@ldap_bind($ldapConn, $email, $password)) {
            throw new AuthenticationException('Échec de connexion au serveur LDAP avec le compte de service');
        }
    }

    /**
     * Bind to LDAP server with user credentials
     */
    public function bindWithUserCredentials(\LDAP\Connection $ldapConn, string $userDn, string $password): void
    {
        if (!@ldap_bind($ldapConn, $userDn, $password)) {
            throw new AuthenticationException('Échec de l\'authentification LDAP: identifiants invalides');
        }
    }

    /**
     * Search for user in LDAP directory
     */
    public function searchUser(\LDAP\Connection $ldapConn, string $username, ?string $baseDn = null): array
    {
        $baseDn = $baseDn ?? self::DEFAULT_BASE_DN;
        
        // Determine if username is an email address
        $filter = $this->buildUserFilter($username);
        
        $search = @ldap_search($ldapConn, $baseDn, $filter);
        
        if (!$search) {
            throw new AuthenticationException('Erreur lors de la recherche LDAP: ' . ldap_error($ldapConn));
        }
        
        $entries = ldap_get_entries($ldapConn, $search);
        
        if ($entries['count'] == 0) {
            throw new AuthenticationException('Utilisateur non trouvé dans LDAP');
        }
        
        return $entries;
    }

    /**
     * Build LDAP filter based on username format
     */
    private function buildUserFilter(string $username): string
    {
        return (strpos($username, '@') !== false) 
            ? "(mail=$username)" 
            : "(sAMAccountName=$username)";
    }

    /**
     * Extract user attributes from LDAP entries
     */
    public function extractUserAttributes(array $entries): array
    {
        if ($entries['count'] == 0) {
            return [];
        }
        
        return [
            'cn' => $entries[0]['cn'][0] ?? null,
            'sn' => $entries[0]['sn'][0] ?? null,
            'givenname' => $entries[0]['givenname'][0] ?? null,
            'displayname' => $entries[0]['displayname'][0] ?? null,
            'mail' => $entries[0]['mail'][0] ?? null,
            'department' => $entries[0]['department'][0] ?? null,
            'userPrincipalName' => $entries[0]['userprincipalname'][0] ?? null,
            'distinguishedName' => $entries[0]['distinguishedname'][0] ?? null,
            'groups' => isset($entries[0]['memberof']) 
                ? array_slice($entries[0]['memberof'], 0, $entries[0]['memberof']['count']) 
                : []
        ];
    }

    /**
     * Close LDAP connection
     */
    public function closeConnection(\LDAP\Connection $ldapConn): void
    {
        ldap_close($ldapConn);
    }
}