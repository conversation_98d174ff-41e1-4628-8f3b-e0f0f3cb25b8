<?php

// Script pour tester la vraie logique avec la vraie base de données
require_once 'vendor/autoload.php';

use Symfony\Component\Dotenv\Dotenv;

// Charger les variables d'environnement
$dotenv = new Dotenv();
$dotenv->load('.env.local', '.env');

// Créer la connexion à la base de données
$dsn = sprintf(
    'pgsql:host=%s;port=%s;dbname=%s',
    $_ENV['DATABASE_HOST'] ?? 'localhost',
    $_ENV['DATABASE_PORT'] ?? '5432',
    $_ENV['DATABASE_NAME'] ?? 'supra'
);

try {
    $pdo = new PDO($dsn, $_ENV['DATABASE_USER'], $_ENV['DATABASE_PASSWORD']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== TEST AVEC VRAIE BASE DE DONNÉES ===\n";
    
    $email = '<EMAIL>';
    $domain = substr(strrchr($email, "@"), 1);
    
    echo "Email: $email\n";
    echo "Domaine: $domain\n\n";
    
    // Requête pour récupérer les EntiteJuridique avec ldap_config
    $sql = "SELECT id, code, nom, ldap_config FROM entite_juridique WHERE ldap_config IS NOT NULL ORDER BY id ASC";
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    
    $entites = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Nombre d'EntiteJuridique trouvées: " . count($entites) . "\n\n";
    
    foreach ($entites as $ej) {
        echo "--- EntiteJuridique: {$ej['code']} ---\n";
        echo "ID: {$ej['id']}\n";
        echo "Nom: {$ej['nom']}\n";
        
        $ldapConfig = json_decode($ej['ldap_config'], true);
        $ldapHost = $ldapConfig['host'] ?? 'N/A';
        
        echo "LDAP Host: $ldapHost\n";
        
        // Extraire le nom d'hôte
        $hostName = '';
        if (preg_match('/ldap:\/\/([^:]+)/i', $ldapHost, $hostMatches)) {
            $hostName = $hostMatches[1];
            echo "Nom d'hôte extrait: $hostName\n";
            
            $hostParts = explode('.', strtolower($hostName));
            echo "Parties du nom d'hôte: " . json_encode($hostParts) . "\n";
            
            if (count($hostParts) > 2) {
                array_shift($hostParts);
                $hostDomain = implode('.', $hostParts);
                echo "Domaine hôte: $hostDomain\n";
                
                // Test de correspondance
                $match = testDomainMatch($domain, $hostDomain);
                echo "Correspondance: " . ($match ? "✅ OUI" : "❌ NON") . "\n";
                
                if ($match) {
                    echo "🎉 CODE EJ TROUVÉ: {$ej['code']}\n";
                    exit(0);
                }
            } else {
                echo "❌ Pas assez de parties dans le nom d'hôte\n";
            }
        } else {
            echo "❌ Impossible d'extraire le nom d'hôte\n";
        }
        
        echo "\n";
    }
    
    echo "❌ Aucune correspondance trouvée - EMAIL_AUTH sera retourné\n";
    
} catch (Exception $e) {
    echo "Erreur: " . $e->getMessage() . "\n";
}

function testDomainMatch(string $emailDomain, string $configDomain): bool
{
    // Normaliser les domaines en minuscules
    $emailDomain = strtolower($emailDomain);
    $configDomain = strtolower($configDomain);
    
    // Cas 1: Correspondance exacte
    if ($emailDomain === $configDomain) {
        return true;
    }
    
    // Cas 2: Gestion spéciale pour "chru" vs "chu"
    $normalizedEmailDomain = str_replace('chru', 'chu', $emailDomain);
    $normalizedConfigDomain = str_replace('chru', 'chu', $configDomain);
    
    if ($normalizedEmailDomain === $normalizedConfigDomain) {
        return true;
    }
    
    // Cas 3: Sous-domaines
    if (strpos($emailDomain, $configDomain) !== false || strpos($configDomain, $emailDomain) !== false) {
        return true;
    }
    
    // Cas 4: Parties principales
    $emailParts = explode('.', $emailDomain);
    $configParts = explode('.', $configDomain);
    
    $emailMainParts = array_slice($emailParts, max(0, count($emailParts) - 2));
    $configMainParts = array_slice($configParts, max(0, count($configParts) - 2));
    
    if (implode('.', $emailMainParts) === implode('.', $configMainParts)) {
        return true;
    }
    
    // Cas 5: Parties individuelles avec normalisation chru/chu
    foreach ($emailParts as $emailPart) {
        foreach ($configParts as $configPart) {
            $normalizedEmailPart = str_replace('chru', 'chu', $emailPart);
            $normalizedConfigPart = str_replace('chru', 'chu', $configPart);
            
            if ($normalizedEmailPart === $normalizedConfigPart && 
                (strpos($normalizedEmailPart, 'chu') !== false || strpos($normalizedConfigPart, 'chu') !== false)) {
                return true;
            }
        }
    }
    
    return false;
}
