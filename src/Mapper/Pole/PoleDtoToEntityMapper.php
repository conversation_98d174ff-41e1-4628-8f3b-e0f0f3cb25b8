<?php

namespace App\Mapper\Pole;

use App\ApiResource\Structure\PoleDto;
use App\Entity\Structure\Pole;
use App\Repository\EntiteJuridiqueRepository;
use App\Repository\PoleRepository;
use Doctrine\DBAL\Exception;
use Symfonycasts\MicroMapper\AsMapper;
use Symfonycasts\MicroMapper\MapperInterface;

#[AsMapper(from: PoleDto::class, to: Pole::class)]
class PoleDtoToEntityMapper implements MapperInterface
{
    public function __construct(
        private PoleRepository $poleRepository,
        private EntiteJuridiqueRepository $hopitalRepository
    ) {}

    public function load(object $from, string $toClass, array $context): object
    {
        $dto = $from;
        assert($dto instanceof PoleDto);

        // On récupère l'entité existante si un ID est fourni, sinon on en crée une nouvelle
        $pole = $dto->id ? $this->poleRepository->find($dto->id) : new Pole();
        if (!$pole) {
            throw new Exception('Pôle non trouvé');
        }

        return $pole;
    }

    public function populate(object $from, object $to, array $context): object
    {
        $dto = $from;
        $pole = $to;
        assert($dto instanceof PoleDto);
        assert($pole instanceof Pole);

        $pole->setEtab($dto->etab);
        $pole->setPolecode($dto->polecode);
        $pole->setDatdeb($dto->datdeb);
        $pole->setDatfin($dto->datfin);
        $pole->setLibelle($dto->libelle);
        $pole->setPfuser($dto->pfuser);
        $pole->setDmdacre($dto->dmdacre);
        $pole->setDmdamaj($dto->dmdamaj);

        // Champs d'horodatage
        $pole->setIsActif($dto->isActif);
        $pole->setValidFrom($dto->validFrom);
        $pole->setValidTo($dto->validTo);
        $pole->setPeriodeType($dto->periodeType);
        $pole->setSource($dto->source);

        // Convertir l'IRI API Platform en entité EntiteJuridique
        if ($dto->hopital) {
            // Extraire l'ID depuis l'URI "/api/hopitals/{id}"
            $hopitalId = basename($dto->hopital);
            $hopital = $this->hopitalRepository->find($hopitalId);
            if (!$hopital) {
                throw new Exception('Hôpital non trouvé');
            }
            $pole->setHopital($hopital);
        }

        return $pole;
    }
}
