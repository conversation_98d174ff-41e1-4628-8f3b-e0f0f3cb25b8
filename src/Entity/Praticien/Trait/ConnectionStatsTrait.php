<?php
namespace App\Entity\Praticien\Trait;

use Doctrine\ORM\Mapping as ORM;

trait ConnectionStatsTrait
{
    #[ORM\Column(type: 'json', nullable: true)]
    private ?array $connectionStats = [
        'total_connections' => 0,
        'first_connection' => null,
        'last_connection' => null,
        'monthly_stats' => [],
        'daily_stats' => []
    ];

    public function getConnectionStats(): ?array
    {
        return $this->connectionStats;
    }

    public function incrementConnectionStats(): void
    {
        if ($this->connectionStats === null) {
            $this->connectionStats = [
                'total_connections' => 0,
                'first_connection' => null,
                'last_connection' => null,
                'monthly_stats' => [],
                'daily_stats' => []
            ];
        }

        $now = new \DateTime();
        $monthKey = $now->format('Y-m');
        $dayKey = $now->format('Y-m-d');

        if ($this->connectionStats['first_connection'] === null) {
            $this->connectionStats['first_connection'] = $now->format('Y-m-d H:i:s');
        }

        $this->connectionStats['total_connections']++;
        $this->connectionStats['last_connection'] = $now->format('Y-m-d H:i:s');

        $this->connectionStats['monthly_stats'][$monthKey] =
            ($this->connectionStats['monthly_stats'][$monthKey] ?? 0) + 1;

        $this->connectionStats['daily_stats'][$dayKey] =
            ($this->connectionStats['daily_stats'][$dayKey] ?? 0) + 1;

        $this->cleanOldStats();
    }

    private function cleanOldStats(): void
    {
        $twelveMonthsAgo = (new \DateTime())->modify('-12 months')->format('Y-m');
        $thirtyDaysAgo = (new \DateTime())->modify('-30 days')->format('Y-m-d');

        // Nettoyage des stats mensuelles
        $this->connectionStats['monthly_stats'] = array_filter(
            $this->connectionStats['monthly_stats'],
            fn($month) => $month >= $twelveMonthsAgo,
            ARRAY_FILTER_USE_KEY
        );

        // Nettoyage des stats journalières
        $this->connectionStats['daily_stats'] = array_filter(
            $this->connectionStats['daily_stats'],
            fn($day) => $day >= $thirtyDaysAgo,
            ARRAY_FILTER_USE_KEY
        );
    }
}