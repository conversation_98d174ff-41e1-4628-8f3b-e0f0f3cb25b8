<?php

namespace App\Repository;

use App\Entity\Structure\Pole;
use App\Entity\Structure\Ufs;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Ufs>
 */
class UfsRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Ufs::class);
    }

    public function findPoleByUf(Ufs $uf): ?Pole
    {
        return $this->createQueryBuilder('ufs')
            ->select('p')
            ->from(Pole::class, 'p')
            ->join('p.crs', 'cr')
            ->join('cr.ufs', 'u')
            ->where('u.id = :ufId')
            ->setParameter('ufId', $uf->getId())
            ->getQuery()
            ->getOneOrNullResult();
    }

    public function findValidUfAt(string $ufcode, \DateTimeInterface $at, ?string $ejcode = null): ?object
    {
        $qb = $this->createQueryBuilder('u')
            ->where('u.ufcode = :ufcode')
            ->andWhere('u.isActif = :actif')
            ->andWhere('u.datdeb <= :at')
            ->andWhere('(u.datfin IS NULL OR u.datfin >= :at)');

        $qb->setParameter('ufcode', $ufcode)
            ->setParameter('actif', true)
            ->setParameter('at', $at);

        if ($ejcode) {
            $qb->join('u.cr', 'cr')
                ->join('cr.pole', 'pole')
                ->join('pole.hopital', 'hopital')
                ->andWhere('hopital.code = :ejcode')
                ->setParameter('ejcode', $ejcode);
        }

        $qb->orderBy('u.datdeb', 'DESC');

        return $qb->getQuery()
            ->setMaxResults(1)
            ->getOneOrNullResult();
    }
    public function findActiveUfForPeriod(string $ufcode, \DateTime $start, \DateTime $end)
    {
        $qb = $this->createQueryBuilder('u')
            ->where('u.ufcode = :ufcode')
            ->andWhere('u.isActif = :actif')
            ->andWhere('u.datdeb <= :end')
            ->andWhere('u.datfin >= :start');

        // Utilisation de setParameter individuel au lieu de setParameters
        $qb->setParameter('ufcode', $ufcode)
            ->setParameter('actif', true)
            ->setParameter('start', $start)
            ->setParameter('end', $end);

        return $qb->getQuery()
            ->getOneOrNullResult();
    }





    //    /**
    //     * @return Uf[] Returns an array of Uf objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('u')
    //            ->andWhere('u.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('u.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    //    public function findOneBySomeField($value): ?Uf
    //    {
    //        return $this->createQueryBuilder('u')
    //            ->andWhere('u.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->getQuery()
    //            ->getOneOrNullResult()
    //        ;
    //    }
}
