<?php

namespace App\Factory\Structure;

use App\Entity\Structure\Ufs;
use Zenstruck\Foundry\Persistence\PersistentProxyObjectFactory;

/**
 * @extends PersistentProxyObjectFactory<Ufs>
 */
final class UfsFactory extends PersistentProxyObjectFactory
{
    /**
     * @see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#factories-as-services
     *
     * @todo inject services if required
     */
    public function __construct()
    {
    }

    public static function class(): string
    {
        return Ufs::class;
    }

    /**
     * @see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#model-factories
     *
     * @todo add your default values here
     */
    protected function defaults(): array|callable
    {
        // Années et mois pour les dates (janvier à juin pour 2023, 2024, 2025)
        $years = [2023, 2024, 2025];
        $startMonths = [1, 2, 3]; // Janvier à Mars pour datdeb
        $endMonths = [4, 5, 6]; // Avril à Juin pour datfin
        
        // Choisir une année et des mois aléatoires
        $year = $years[array_rand($years)];
        $startMonth = $startMonths[array_rand($startMonths)];
        $endMonth = $endMonths[array_rand($endMonths)];
        
        // Créer les dates de début et de fin
        $startDay = random_int(1, 28); // Éviter les problèmes avec février
        $endDay = random_int(1, 28);
        $datdeb = new \DateTime("$year-$startMonth-$startDay");
        $datfin = new \DateTime("$year-$endMonth-$endDay");
        
        // S'assurer que datfin est après datdeb
        if ($datfin <= $datdeb) {
            // Si même année et mois de fin <= mois de début, passer à l'année suivante
            if ($year < 2025) {
                $datfin = new \DateTime(($year + 1) . "-$endMonth-$endDay");
            } else {
                // Si on est déjà en 2025, ajuster le jour
                $datfin = clone $datdeb;
                $datfin->modify('+1 month');
            }
        }
        
        // Date de clôture (optionnelle, après datfin)
        $datclos = null;
        if (self::faker()->boolean(30)) { // 30% de chance d'avoir une date de clôture
            $datclos = clone $datfin;
            $datclos->modify('+1 day'); // Au moins un jour après datfin
        }
        
        return [
            'etab' => self::faker()->regexify('[A-Z]{3}'),
            'ufcode' => self::faker()->unique()->regexify('[A-Z0-9]{4}'),
            'datdeb' => $datdeb,
            'datfin' => $datfin,
            'datclos' => $datclos,
            'libelle' => substr(strtoupper(self::faker()->word()) . ' UF', 0, 25),
            'tacode' => self::faker()->regexify('[A-Z0-9]{2}'),
            'cdcode' => self::faker()->regexify('[A-Z0-9]{3}'),
            'lettrebudg' => self::faker()->regexify('[A-Z0-9]{10}'),
            'secode' => self::faker()->regexify('[A-Z0-9]{10}'),
            'cgcode' => self::faker()->regexify('[A-Z0-9]{10}'),
            'umcode' => self::faker()->optional()->regexify('[A-Z0-9]{10}'),
            'pfuser' => self::faker()->regexify('[A-Z0-9]{8}'),
            'dmdacre' => $datdeb, // Même date que datdeb
            'dmdamaj' => new \DateTime(), // Date actuelle
            'topmedical' => self::faker()->optional()->regexify('[01]'),
            'crcode' => self::faker()->regexify('[A-Z0-9]{10}'),
            'sacode' => self::faker()->regexify('[A-Z0-9]{10}'),
            'service' => ServiceFactory::random(),
            'cr' => CrFactory::random(),
            'is_actif' => true,
            'validFrom' => $datdeb, // Même date que datdeb
            'validTo' => $datfin, // Même date que datfin
            'periodeType' => self::faker()->randomElement(['HEBDOMADAIRE', 'MENSUEL', 'ANNUEL']),
            'source' => self::faker()->optional()->randomElement(['IMPORT', 'MANUEL', 'MIGRATION']),
        ];
    }

    /**
     * @see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#initialization
     */
    protected function initialize(): static
    {
        return $this
            // ->afterInstantiate(function(Uf $ufs): void {})
        ;
    }
}
