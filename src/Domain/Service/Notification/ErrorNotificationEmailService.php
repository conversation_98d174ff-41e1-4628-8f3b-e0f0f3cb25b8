<?php

namespace App\Domain\Service\Notification;

use App\Entity\Structure\EntiteJuridique;
use Psr\Log\LoggerInterface;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Email;
use Twig\Environment;

/**
 * Service pour l'envoi d'emails de notification d'erreurs générales dans SUPRA.
 *
 * Ce service gère les notifications d'erreurs qui se produisent dans l'application
 * et qui doivent être signalées à l'administrateur selon la configuration de l'entité juridique.
 */
class ErrorNotificationEmailService
{
    public function __construct(
        private readonly MailerInterface $mailer,
        private readonly Environment $twig,
        private readonly LoggerInterface $logger
    ) {}

    /**
     * Envoie une notification d'erreur générale à l'admin si activé
     */
    public function sendErrorNotificationToAdmin(
        EntiteJuridique $entiteJuridique,
        string $context,
        string $errorMessage,
        array $additionalData = []
    ): void {
        // Vérifier si les notifications d'erreur admin sont activées
        if (!$entiteJuridique->isNotifierAdminDesErreurProduite()) {
            $this->logger->info(
                'Notifications d\'erreur admin désactivées, aucun email envoyé',
                [
                    'entiteJuridique' => $entiteJuridique->getCode(),
                    'context' => $context,
                    'error' => $errorMessage
                ]
            );
            return;
        }

        $adminEmail = $entiteJuridique->getAdminEmail();

        // Vérifier que l'email admin est valide
        if (empty($adminEmail) || !filter_var($adminEmail, FILTER_VALIDATE_EMAIL)) {
            $this->logger->warning(
                'Email admin invalide, impossible d\'envoyer la notification d\'erreur',
                ['entiteJuridique' => $entiteJuridique->getCode(), 'adminEmail' => $adminEmail]
            );
            return;
        }

        $subject = sprintf(
            '[%s] ERREUR SUPRA - %s',
            $entiteJuridique->getCode(),
            $context
        );

        $emailContext = [
            'entiteJuridique' => $entiteJuridique,
            'context' => $context,
            'errorMessage' => $errorMessage,
            'additionalData' => $additionalData,
            'timestamp' => new \DateTime()
        ];

        $this->sendErrorEmail(
            [$adminEmail],
            $subject,
            'emails/error/general_error_notification.html.twig',
            $emailContext
        );
    }

    /**
     * Envoie une notification d'erreur d'importation d'affectations
     */
    public function sendAffectationImportErrorNotification(
        EntiteJuridique $entiteJuridique,
        string $errorMessage,
        array $requestData = [],
        ?string $userInfo = null
    ): void {
        $additionalData = [
            'type' => 'Importation d\'affectations',
            'requestData' => $requestData,
            'userInfo' => $userInfo,
            'endpoint' => '/admin/import/affectations'
        ];

        $this->sendErrorNotificationToAdmin(
            $entiteJuridique,
            'Importation d\'affectations',
            $errorMessage,
            $additionalData
        );
    }

    /**
     * Envoie un email d'erreur
     */
    private function sendErrorEmail(
        array $recipients,
        string $subject,
        string $template,
        array $context
    ): void {
        // Filtrer les emails valides
        $validRecipients = array_filter($recipients, function($email) {
            return !empty($email) && filter_var($email, FILTER_VALIDATE_EMAIL);
        });

        if (empty($validRecipients)) {
            $this->logger->warning(
                'Aucun destinataire valide pour la notification d\'erreur',
                ['subject' => $subject, 'recipients' => $recipients]
            );
            return;
        }

        try {
            $htmlBody = $this->twig->render($template, $context);

            $email = (new Email())
                ->subject($subject)
                ->html($htmlBody);

            // Ajouter tous les destinataires
            foreach ($validRecipients as $recipient) {
                $email->addTo($recipient);
            }

            $this->mailer->send($email);

            $this->logger->info(
                'Email de notification d\'erreur envoyé avec succès',
                [
                    'subject' => $subject,
                    'recipients' => $validRecipients,
                    'template' => $template
                ]
            );

        } catch (\Exception $e) {
            $this->logger->error(
                'Erreur lors de l\'envoi d\'email de notification d\'erreur',
                [
                    'subject' => $subject,
                    'recipients' => $validRecipients,
                    'template' => $template,
                    'error' => $e->getMessage()
                ]
            );
        }
    }
}
