<?php

namespace App\Domain\Extension\CoreSupraFilter\Service;

/**
 * Filtre de codes de services à exclure du retour de l'API.
 *
 * OBJECTIF : Restreindre l'accès aux listes de services pour masquer certains services
 * dans les endpoints de collection (GET /api/services). Les accès individuels restent ouverts.
 *
 * FONCTIONNEMENT :
 * - Les codes listés dans EXCLUDED_SECODES sont filtrés des collections
 * - La comparaison est insensible à la casse (UPPER() en SQL)
 * - Les services avec secode = NULL sont INCLUS par défaut
 * - Le filtrage ne s'applique PAS aux accès individuels (GET /api/services/{id})
 *
 * CONFIGURATION ACTUELLE :
 * - Exclus : Services identifiés comme non pertinents pour l'affichage public
 * - Inclus : Services opérationnels + services sans code défini
 *
 * @todo Affiner la liste des codes avec l'équipe métier
 * @todo Vérifier si d'autres critères de filtrage sont nécessaires (dates, statuts)
 *
 * @see ServiceCategoryFilterExtension Pour l'implémentation Doctrine
 */
class ServiceCategoryExcludeFilter
{
    /**
     * Codes de services à exclure du retour API
     *
     * IMPORTANT : Un service n'étant pas rattaché directement à un pôle dans le système
     * d'information du CHRU, vous devez OBLIGATOIREMENT renseigner une liste de services
     * à ignorer dans ce tableau.
     *
     * ASTUCE : Respecter la structure par catégories pour faciliter la maintenance,
     * non bloquant si vous le faites pas mais ça aide à s'y retrouver merci
     */
    private const EXCLUDED_SECODES = [
        // Services administratifs et de direction
        "4010", "4030", "4290", "4370", "4520", "4700", "7000", "7002", "7003",
        "7041", "7070", "7250", "7610", "7630", "7650", "7680",

        // Services logistiques et techniques
        "4020", "4060", "4080", "4230", "4310", "4340", "4400", "4410", "4470",
        "4510", "4540", "7040", "7110", "7170", "7220", "7290", "7590",

        // Écoles et formation
        "6800", "7080", "7130", "7131", "7132", "7133", "7134", "7135", "7136", "7139",

        // Services externes et partenariats
        "8820", "8830", "8850", "8870", "8880", "8920", "8930", "8940", "8990", "9200", "9900",

        // Crèches et services sociaux
        "6700", "7200", "7210", "7270",

        // Services informatiques
        "7710", "7720", "7730", "7740", "9999"

        /**
         * SERVICES EXCLUS PAR DÉFAUT :
         * Cette liste exclut automatiquement tous les services non-médicaux identifiés :
         * - Directions et administration (16 services)
         * - Logistique, restauration, maintenance (17 services)
         * - Écoles et formations (10 services)
         * - Partenariats externes et GCS (11 services)
         * - Crèches et accompagnement social (4 services)
         * - Systèmes d'information (5 services)
         *
         * TOTAL : 63 services exclus pour ne conserver que les services de soins directs
         * aux patients dans les listes publiques de l'API.
         */
    ];

    public static function isExcluded(?string $secode): bool
    {
        if ($secode === null) {
            return false;
        }

        return in_array(strtoupper($secode), array_map('strtoupper', self::EXCLUDED_SECODES), true);
    }

    public static function getExcludedSecodes(): array
    {
        return self::EXCLUDED_SECODES;
    }
}