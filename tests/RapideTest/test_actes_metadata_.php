<?php

require_once dirname(__FILE__).'/vendor/autoload.php';

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\HttpKernelInterface;
use Symfony\Component\Dotenv\Dotenv;

// Load environment variables
$dotenv = new Dotenv();
$dotenv->loadEnv(dirname(__FILE__).'/.env');

// Get the Kernel
require_once dirname(__FILE__).'/bin/console';
$kernel = new \App\Kernel($_SERVER['APP_ENV'], (bool) $_SERVER['APP_DEBUG']);
$kernel->boot();

// Get the entity manager
$entityManager = $kernel->getContainer()->get('doctrine')->getManager();

// Get a valid ejcode from the database
$ejQuery = $entityManager->createQuery('
    SELECT ej.code
    FROM App\Entity\Structure\EntiteJuridique ej
    ORDER BY ej.id
    LIMIT 1
');

try {
    $ejCode = $ejQuery->getSingleScalarResult();
    echo "Found entity juridique with code: " . $ejCode . "\n";
} catch (\Exception $e) {
    echo "Error: Could not find any entity juridique in the database.\n";
    echo "Using hardcoded ejcode 'ej0001' from the bearer token.\n";
    $ejCode = "ej0001";
}

// il faut lui envoyer ce token bearer si non ca ne marche pas
$bearerToken = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ToYfqp5fsxBUHcC0dXXsr-Js-ylY-U62FEbYSBF2a4g';



// Create a request to the API
$request = Request::create(
    '/api/current/metadata/actes' . ($ejCode ? '?ejcode=' . $ejCode : ''),
    'GET'
);

// Set the request format to LD+JSON and add authorization header
$request->headers->set('Accept', 'application/ld+json');
$request->headers->set('Authorization', 'Bearer ' . $bearerToken);

// Execute the request
$response = $kernel->handle($request, HttpKernelInterface::MAIN_REQUEST, false);

// Get the response content
$content = $response->getContent();

// Check if the response is successful
if ($response->getStatusCode() === 200) {
    echo "API request successful!\n";
    
    // Decode the JSON response
    $data = json_decode($content, true);
    
    // Display the applied ejcode filter
    echo "Applied ejcode filter: " . ($data['appliedEjCode'] ?? 'None') . "\n";
    
    // Display the statistics
    echo "Total items: " . $data['totalItems'] . "\n";
    
    // Check that totalAgents and totalUfs fields are not present
    if (!isset($data['totalAgents']) && !isset($data['totalUfs'])) {
        echo "VERIFICATION PASSED: totalAgents and totalUfs fields have been removed as requested.\n";
    } else {
        echo "VERIFICATION FAILED: One or both of the fields that should have been removed are still present:\n";
        if (isset($data['totalAgents'])) echo "- totalAgents is still present\n";
        if (isset($data['totalUfs'])) echo "- totalUfs is still present\n";
    }
    
    // Display the distribution by type
    echo "\nDistribution by type:\n";
    foreach ($data['totalByType'] as $type => $count) {
        echo "- $type: $count\n";
    }
    
    // Display the date range
    echo "\nDate range: " . ($data['dateRangeMin'] ?? 'N/A') . " to " . ($data['dateRangeMax'] ?? 'N/A') . "\n";
    
    // Display the generation time
    echo "Generation time: " . $data['generationTimeMs'] . " ms\n";
    
    // Now create a request without ejcode parameter for comparison
    if ($ejCode) {
        echo "\n--- Comparing with request without ejcode parameter ---\n";
        
        $requestWithoutEjcode = Request::create(
            '/api/current/metadata/actes',
            'GET'
        );
        
        // Set the request format to LD+JSON and add authorization header
        $requestWithoutEjcode->headers->set('Accept', 'application/ld+json');
        $requestWithoutEjcode->headers->set('Authorization', 'Bearer ' . $bearerToken);
        
        // Execute the request
        $responseWithoutEjcode = $kernel->handle($requestWithoutEjcode, HttpKernelInterface::MAIN_REQUEST, false);
        
        // Get the response content
        $contentWithoutEjcode = $responseWithoutEjcode->getContent();
        
        // Check if the response is successful
        if ($responseWithoutEjcode->getStatusCode() === 200) {
            // Decode the JSON response
            $dataWithoutEjcode = json_decode($contentWithoutEjcode, true);
            
            // Compare the statistics
            echo "Total items with ejcode filter: " . $data['totalItems'] . "\n";
            echo "Total items without ejcode filter: " . $dataWithoutEjcode['totalItems'] . "\n";
            
            // Check if the filter is working
            if ($data['totalItems'] <= $dataWithoutEjcode['totalItems']) {
                echo "\nThe ejcode filter is working correctly! The filtered results contain fewer or equal items than the unfiltered results.\n";
            } else {
                echo "\nWarning: The filtered results contain more items than the unfiltered results. The filter might not be working correctly.\n";
            }
        } else {
            echo "Error: Request without ejcode parameter failed with status code " . $responseWithoutEjcode->getStatusCode() . "\n";
            echo "Response: " . $contentWithoutEjcode . "\n";
        }
    }
    
    // Print the full response for inspection
    echo "\nFull response data:\n";
    print_r($data);
    
} else {
    echo "Error: API request failed with status code " . $response->getStatusCode() . "\n";
    echo "Response: " . $content . "\n";
}