<?php

namespace App\Controller\Admin;

use App\Entity\Praticien\Agent;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\ArrayField;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\BooleanField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateField;
use EasyCorp\Bundle\EasyAdminBundle\Field\NumberField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ChoiceField;
use Symfony\Component\Form\Extension\Core\Type\PasswordType;

class AgentCrudController extends AbstractCrudController
{
    private \Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface $passwordHasher;
    
    public function __construct(\Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface $passwordHasher)
    {
        $this->passwordHasher = $passwordHasher;
    }
    
    public static function getEntityFqcn(): string
    {
        return Agent::class;
    }

    public function configureActions(Actions $actions): Actions
    {
        $importCsv = Action::new('importCsv', '📥 Importer CSV')
            ->linkToRoute('admin_import_agents_csv')
            ->setCssClass('btn btn-primary');

        return $actions
            ->add(Crud::PAGE_INDEX, Action::DETAIL)
            ->add(Crud::PAGE_NEW, $importCsv);
    }

    /**
     * Configure the fields displayed in the CRUD forms
     * 
     * Cette méthode configure les champs affichés dans les formulaires CRUD.
     * Des options spécifiques sont ajoutées pour éviter les erreurs de conversion
     * entre objets et valeurs scalaires, notamment pour résoudre l'erreur:
     * "Input value 'Agent' contains a non-scalar value"
     */
    public function configureFields(string $pageName): iterable
    {
        $fields = [
            // ID field
            TextField::new('id')
                ->setLabel('ID')
                ->setHelp('Identifiant unique de l\'agent (UUID)')
                ->hideOnForm()
                ->hideOnIndex(),
                
            // Common fields
            TextField::new('matricule')
                ->setLabel('Matricule')
                ->setHelp('Matricule interne optionnel de l\'agent')
                ->setFormTypeOption('attr', ['placeholder' => 'Ex: 004647'])
                ->hideOnIndex(),
                
            TextField::new('hrUser')
                ->setLabel('HR User')
                ->setRequired(true)
                ->setHelp('Identifiant RH obligatoire de l\'agent (utilisé pour les synchronisations)')
                ->setFormTypeOption('attr', ['placeholder' => 'Ex: U074647']),

            // BaseEntity fields
            BooleanField::new('isActif')
                ->setLabel('Est actif')
                ->setHelp('Indique si l\'agent est actif dans le système. Les agents inactifs ne peuvent pas se connecter.')
                ->hideOnIndex(),
            DateField::new('dateCreation')
                ->setLabel('Date de création')
                ->setHelp('Date à laquelle l\'agent a été créé dans le système. Ce champ est automatiquement rempli et ne peut pas être modifié.')
                ->setDisabled(true)
                ->hideOnIndex(),

            // Agent specific fields
            BooleanField::new('isLdapInitialized')
                ->setLabel('LDAP initialisé')
                ->setHelp('Indique si l\'agent a été synchronisé avec LDAP')
                ->setDisabled($pageName === Crud::PAGE_NEW)
                ->hideOnIndex(),

            // CollecteurTrait fields
            TextField::new('email')
                ->setLabel('Email')
                ->setHelp('Adresse email professionnelle de l\'agent, utilisée pour les communications et notifications'),
            TextField::new('nom')
                ->setLabel('Nom')
                ->setHelp('Nom de famille de l\'agent'),
            TextField::new('prenom')
                ->setLabel('Prénom')
                ->setHelp('Prénom de l\'agent'),
            TextField::new('titre')
                ->setLabel('Titre')
                ->setHelp('Titre professionnel de l\'agent (ex: Dr., Pr., etc.)')
                ->hideOnIndex(),
            TextField::new('categorie')
                ->setLabel('Catégorie')
                ->setHelp('Catégorie professionnelle de l\'agent (ex: Médecin, Infirmier, etc.)')
                ->hideOnIndex(),
            TextField::new('etablissement')
                ->setLabel('Établissement')
                ->setHelp('Établissement principal auquel l\'agent est rattaché')
                ->hideOnIndex(),
            DateField::new('dateDepart')
                ->setLabel('Date de départ')
                ->setHelp('Date à laquelle l\'agent a quitté ou quittera l\'établissement'),
            DateField::new('dateVenue')
                ->setLabel('Date d\'arrivée')
                ->setHelp('Date à laquelle l\'agent a rejoint l\'établissement'),
            DateField::new('dateMaj')
                ->setLabel('Date de mise à jour')
                ->setHelp('Date de la dernière mise à jour des informations de l\'agent')
                ->hideOnIndex(),
            TextField::new('createurFiche')
                ->setLabel('Créateur fiche')
                ->setHelp('Identifiant de la personne ayant créé la fiche de l\'agent')
                ->hideOnIndex(),
            BooleanField::new('isAdmin')
                ->setLabel('Admin')
                ->setHelp('Indique si l\'agent a des droits d\'administration sur le système')
                ->hideOnIndex(),
            BooleanField::new('isAnesthesiste')
                ->setLabel('Anesthésiste')
                ->setHelp('Indique si l\'agent est un anesthésiste')
                ->hideOnIndex(),

            // NativeFieldTrait fields
            TextField::new('fullName')
                ->setLabel('Nom complet')
                ->setHelp('Nom complet de l\'agent (prénom et nom). Ce champ est généré automatiquement.')
                ->hideOnIndex(),
            TextField::new('badge')
                ->setLabel('Badge')
                ->setHelp('Numéro ou identifiant du badge d\'accès de l\'agent')
                ->setFormTypeOption('attr', ['placeholder' => 'Ex: 123456'])
                ->hideOnIndex(),

            // Relations - Ajout d'options spécifiques pour éviter les erreurs de conversion
            AssociationField::new('hopital')
                ->setLabel('Hôpital')
                ->setHelp('Entité juridique (hôpital) à laquelle l\'agent est rattaché. Cette association détermine les droits d\'accès de l\'agent.')
                ->setFormTypeOption('choice_label', 'nom')
                ->setFormTypeOption('by_reference', true),
        ];

        // Add fields that should only appear in specific contexts
        if ($pageName === Crud::PAGE_NEW || $pageName === Crud::PAGE_EDIT) {
            $fields[] = TextField::new('password')
                ->setLabel('Mot de passe')
                ->setFormType(PasswordType::class)
                ->setRequired(false)
                ->setFormTypeOption('mapped', false)
                ->setHelp('Champ facultatif pour les utilisateurs standards (authentification LDAP). Recommandé uniquement pour les administrateurs car l\'espace admin n\'est pas lié au LDAP.');
        }

        // Add complex fields only for detail view
        if ($pageName === Crud::PAGE_DETAIL) {
            $fields[] = ArrayField::new('roles')
                ->setLabel('Rôles')
                ->setHelp('Liste des rôles de sécurité attribués à l\'agent dans l\'application');

            // Utiliser les nouveaux getters de l'entité Agent
            $fields[] = TextField::new('ldapSynchronized')
                ->setLabel('Statut LDAP')
                ->setHelp('Indique si l\'agent est correctement synchronisé avec l\'annuaire LDAP');

            $fields[] = TextField::new('ldapLastSync')
                ->setLabel('Dernière sync LDAP')
                ->setHelp('Date et heure de la dernière synchronisation avec l\'annuaire LDAP');

            $fields[] = TextField::new('ldapMessage')
                ->setLabel('Message LDAP')
                ->setHelp('Message d\'information ou d\'erreur concernant la dernière synchronisation LDAP');

            $fields[] = TextField::new('totalConnections')
                ->setLabel('Total connexions')
                ->setHelp('Nombre total de connexions de l\'agent à l\'application');

            $fields[] = TextField::new('firstConnection')
                ->setLabel('Première connexion')
                ->setHelp('Date et heure de la première connexion de l\'agent à l\'application');

            $fields[] = TextField::new('lastConnection')
                ->setLabel('Dernière connexion')
                ->setHelp('Date et heure de la dernière connexion de l\'agent à l\'application');

            // Utiliser le nouveau getter pour afficher les rôles de manière lisible
            $fields[] = TextField::new('hopitalRolesDisplay')
                ->setLabel('Rôles dans les hôpitaux')
                ->setHelp('Liste des rôles spécifiques attribués à l\'agent dans chaque hôpital');

            // Utiliser le nouveau getter pour afficher les UFs associées de manière lisible
            $fields[] = TextField::new('agentUfsDisplay')
                ->setLabel('UFs associées')
                ->setHelp('Liste des Unités Fonctionnelles auxquelles l\'agent est associé');
        }

        return $fields;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setEntityLabelInSingular('Agent')
            ->setEntityLabelInPlural('Agents')
            ->setPageTitle('index', 'Gestion des Agents')
            ->setPageTitle('detail', 'Détails de l\'Agent')
            ->setPageTitle('new', 'Créer un nouvel Agent')
            ->setPageTitle('edit', 'Modifier l\'Agent')
            ->setHelp('index', '<strong>Astuce :</strong> Pour gagner de la place, double-cliquez sur la barre de séparation pour masquer ou afficher le menu latéral.')
            ->showEntityActionsInlined();
    }
    
    /**
     * Custom logic to handle entity creation
     * 
     * Cette méthode permet de gérer le champ mot de passe lors de la création:
     * - Si l'utilisateur entre un mot de passe, celui-ci est défini sur l'entité
     * 
     * Sans cette méthode, le mot de passe n'est pas défini car le champ est configuré
     * avec 'mapped' => false dans configureFields().
     */
    public function persistEntity(\Doctrine\ORM\EntityManagerInterface $entityManager, $entityInstance): void
    {
        // Get the form data safely
        $formData = $this->getContext()->getRequest()->request->all();
        
        // Check if Agent data exists and is an array
        if (isset($formData['Agent']) && is_array($formData['Agent'])) {
            // Get the password safely
            $password = $formData['Agent']['password'] ?? null;
            
            // Hash and set the password if a non-empty value was provided
            if ($password && $entityInstance instanceof \App\Entity\Praticien\Agent) {
                // Hash the password using the password hasher
                $hashedPassword = $this->passwordHasher->hashPassword($entityInstance, $password);
                $entityInstance->setPassword($hashedPassword);
            }
        }
        
        // Process the entity creation with the parent method
        parent::persistEntity($entityManager, $entityInstance);
    }

    /**
     * Custom logic to handle entity updates
     * 
     * Cette méthode permet de gérer le champ mot de passe lors des mises à jour:
     * - Si l'utilisateur laisse le champ vide, le mot de passe n'est pas modifié
     * - Si l'utilisateur entre un nouveau mot de passe, celui-ci est mis à jour
     * 
     * Elle corrige également l'erreur: "Input value 'Agent' contains a non-scalar value"
     * en s'assurant que toutes les données du formulaire sont correctement traitées.
     * 
     * Résolution du problème:
     * 1. Récupération sécurisée des données du formulaire avec request->all()
     * 2. Vérification que les données 'Agent' existent et sont bien un tableau
     * 3. Récupération sécurisée du mot de passe avec l'opérateur de coalescence null
     * 4. Mise à jour du mot de passe uniquement si une valeur non vide est fournie
     */
    public function updateEntity(\Doctrine\ORM\EntityManagerInterface $entityManager, $entityInstance): void
    {
        // Get the form data safely
        $formData = $this->getContext()->getRequest()->request->all();
        
        // Check if Agent data exists and is an array
        if (isset($formData['Agent']) && is_array($formData['Agent'])) {
            // Get the password safely
            $password = $formData['Agent']['password'] ?? null;
            
            // Only update the password if a non-empty value was provided
            if ($password && $entityInstance instanceof \App\Entity\Praticien\Agent) {
                // Hash the password using the password hasher
                $hashedPassword = $this->passwordHasher->hashPassword($entityInstance, $password);
                $entityInstance->setPassword($hashedPassword);
            }
        }
        
        // Process the entity update with the parent method
        parent::updateEntity($entityManager, $entityInstance);
    }
}
