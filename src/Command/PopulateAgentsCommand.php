<?php

namespace App\Command;

use App\Entity\Praticien\Agent;
use App\Entity\Structure\EntiteJuridique;
use App\Repository\EntiteJuridiqueRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

/**
 * Commande pour peupler la table agent avec 100 agents fictifs
 */
#[AsCommand(
    name: 'app:populate-agents',
    description: 'Peuple la table agent avec 100 agents fictifs',
)]
class PopulateAgentsCommand extends Command
{
    private EntityManagerInterface $entityManager;
    private EntiteJuridiqueRepository $entiteJuridiqueRepository;

    public function __construct(
        EntityManagerInterface $entityManager,
        EntiteJuridiqueRepository $entiteJuridiqueRepository
    ) {
        parent::__construct();
        $this->entityManager = $entityManager;
        $this->entiteJuridiqueRepository = $entiteJuridiqueRepository;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->title('Génération de 100 agents fictifs');

        // Récupération ou création d'une entité juridique
        $entiteJuridique = $this->entiteJuridiqueRepository->findOneBy([]);
        if (!$entiteJuridique) {
            $io->section('Aucune entité juridique trouvée. Création d\'une entité juridique par défaut...');
            $entiteJuridique = new EntiteJuridique();
            $entiteJuridique->setCode('EJ001');
            $entiteJuridique->setNom('Hôpital Fictif');
            $this->entityManager->persist($entiteJuridique);
            $this->entityManager->flush();
            $io->success('Entité juridique créée avec succès.');
        }

        // Titres possibles
        $titres = ['Dr', 'Pr', 'M', 'Mme'];

        // Catégories possibles (max 10 caractères)
        $categories = ['PH', 'MCU-PH', 'PU-PH', 'CCA', 'FFI', 'Interne'];

        // Génération de 100 agents fictifs
        $io->section('Génération des agents...');
        $progressBar = $io->createProgressBar(100);
        $progressBar->start();

        for ($i = 1; $i <= 100; $i++) {
            // Création d'un nouvel agent
            $agent = new Agent();
            
            // Génération d'un nom et prénom aléatoire
            $nom = $this->generateRandomLastName();
            $prenom = $this->generateRandomFirstName();
            
            // Génération d'un email basé sur le nom et prénom
            $email = strtolower($this->transliterateToAscii($prenom) . '.' . $this->transliterateToAscii($nom) . '@hopital-fictif.fr');
            
            // Génération d'un hrUser unique
            $hrUser = 'USER' . str_pad($i, 4, '0', STR_PAD_LEFT);
            
            // Assignation des valeurs aux propriétés de l'agent
            $agent->setNom($nom);
            $agent->setPrenom($prenom);
            $agent->setEmail($email);
            $agent->setHrUser($hrUser);
            $agent->setTitre($titres[array_rand($titres)]);
            $agent->setCategorie($categories[array_rand($categories)]);
            $agent->setEtablissement('HF'); // Hôpital Fictif abrégé (max 10 caractères)
            
            // Dates aléatoires
            $dateVenue = new \DateTime();
            $dateVenue->modify('-' . rand(1, 10) . ' years');
            $agent->setDateVenue($dateVenue);
            
            // 20% de chance d'avoir une date de départ
            if (rand(1, 5) === 1) {
                $dateDepart = clone $dateVenue;
                $dateDepart->modify('+' . rand(1, 9) . ' years');
                $agent->setDateDepart($dateDepart);
            }
            
            $dateMaj = new \DateTime();
            $dateMaj->modify('-' . rand(1, 30) . ' days');
            $agent->setDateMaj($dateMaj);
            
            // Autres propriétés
            $agent->setCreateurFiche('Script de population');
            $agent->setIsAdmin(rand(1, 10) === 1); // 10% de chance d'être admin
            $agent->setIsAnesthesiste(rand(1, 5) === 1); // 20% de chance d'être anesthésiste
            
            // Relation avec l'entité juridique
            $agent->setHopital($entiteJuridique);
            
            // Génération d'un mot de passe (non utilisé en production)
            $agent->setPassword('$2y$13$' . bin2hex(random_bytes(20)));
            
            // Persistance de l'agent
            $this->entityManager->persist($agent);
            
            // Flush tous les 10 agents pour éviter de surcharger la mémoire
            if ($i % 10 === 0) {
                $this->entityManager->flush();
                $progressBar->advance(10);
            }
        }

        // Flush final pour s'assurer que tous les agents sont enregistrés
        $this->entityManager->flush();
        $progressBar->finish();
        
        $io->newLine(2);
        $io->success('100 agents ont été générés avec succès !');

        return Command::SUCCESS;
    }

    /**
     * Génère un nom de famille aléatoire
     */
    private function generateRandomLastName(): string {
        $lastNames = [
            'Martin', 'Bernard', 'Thomas', 'Petit', 'Robert', 'Richard', 'Durand', 'Dubois', 'Moreau', 'Laurent',
            'Simon', 'Michel', 'Lefebvre', 'Leroy', 'Roux', 'David', 'Bertrand', 'Morel', 'Fournier', 'Girard',
            'Bonnet', 'Dupont', 'Lambert', 'Fontaine', 'Rousseau', 'Vincent', 'Muller', 'Lefevre', 'Faure', 'Andre',
            'Mercier', 'Blanc', 'Guerin', 'Boyer', 'Garnier', 'Chevalier', 'Francois', 'Legrand', 'Gauthier', 'Garcia',
            'Perrin', 'Robin', 'Clement', 'Morin', 'Nicolas', 'Henry', 'Roussel', 'Mathieu', 'Gautier', 'Masson'
        ];
        
        return $lastNames[array_rand($lastNames)];
    }

    /**
     * Génère un prénom aléatoire
     */
    private function generateRandomFirstName(): string {
        $firstNames = [
            'Jean', 'Pierre', 'Michel', 'André', 'Philippe', 'René', 'Louis', 'Alain', 'Jacques', 'Bernard',
            'Marcel', 'Daniel', 'Roger', 'Robert', 'Claude', 'Paul', 'Christian', 'Henri', 'Georges', 'Nicolas',
            'Marie', 'Jeanne', 'Françoise', 'Monique', 'Catherine', 'Nathalie', 'Isabelle', 'Sylvie', 'Anne', 'Jacqueline',
            'Nicole', 'Sophie', 'Martine', 'Christiane', 'Madeleine', 'Christine', 'Suzanne', 'Denise', 'Yvonne', 'Claire',
            'Camille', 'Dominique', 'Claude', 'Stéphane', 'Alex', 'Charlie', 'Maxime', 'Sacha', 'Morgan', 'Lou'
        ];
        
        return $firstNames[array_rand($firstNames)];
    }

    /**
     * Convertit une chaîne avec accents en ASCII simple
     */
    private function transliterateToAscii(string $string): string {
        $string = transliterator_transliterate('Any-Latin; Latin-ASCII', $string);
        return preg_replace('/[^a-zA-Z0-9]/', '', $string);
    }
}