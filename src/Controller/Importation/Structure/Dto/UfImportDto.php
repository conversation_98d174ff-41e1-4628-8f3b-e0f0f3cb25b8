<?php

namespace App\Controller\Importation\Structure\Dto;

/**
 * Data Transfer Object pour l'importation des ufs.
 *
 * @see \App\Domain\Service\Importation\StructureImportService::importUfs()
 */
class UfImportDto
{
    public ?string $etab = null;
    public ?string $ufCode = null;
    public ?string $datDeb = null;
    public ?string $datFin = null;
    public ?string $datClos = null;
    public ?string $libelle = null;
    public ?string $taCode = null;
    public ?string $cdCode = null;
    public ?string $lettreBudg = null;
    public ?string $seCode = null;
    public ?string $cgCode = null;
    public ?string $umCode = null;
    public ?string $pfUser = null;
    public ?string $dmdaCre = null;
    public ?string $dmdAcre = null;
    public ?string $dmdaMaj = null;
    public ?string $dmdAmaj = null;
    public ?string $topMedical = null;
    public ?string $crCode = null;
    public ?string $saCode = null;
}