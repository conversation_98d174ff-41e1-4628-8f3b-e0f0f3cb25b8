<?php

namespace App\Controller\Importation\Agent;

use App\Domain\Service\Importation\AffectationAutoImportService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Contrôleur pour le chargement automatique des affectations.
 * 
 * Ce contrôleur permet de déclencher manuellement le processus de chargement automatique
 * des fichiers d'affectation pour toutes les entités juridiques ayant cette fonctionnalité activée.
 */
class AffectationAutoImportController extends AbstractController
{
    public function __construct(
        private readonly AffectationAutoImportService $autoImportService
    ) {}

    #[Route('/admin/import/affectations/auto', name: 'import_affectations_auto', methods: ['POST'])]
    public function processAutoImport(): JsonResponse
    {
        try {
            $results = $this->autoImportService->processAllEntitesJuridiques();

            if (empty($results)) {
                return $this->json([
                    'status' => 'info',
                    'message' => 'Aucune entité juridique n\'a le chargement automatique activé.',
                    'results' => []
                ]);
            }

            $totalProcessed = 0;
            $totalCreated = 0;
            $totalUpdated = 0;
            $totalErrors = 0;

            foreach ($results as $result) {
                if (!isset($result['error'])) {
                    $totalProcessed += $result['filesProcessed'];
                    $totalCreated += $result['totalCreated'];
                    $totalUpdated += $result['totalUpdated'];
                    $totalErrors += count($result['errors']);
                }
            }

            return $this->json([
                'status' => 'success',
                'message' => 'Traitement automatique terminé',
                'summary' => [
                    'entitesJuridiques' => count($results),
                    'fichiersTraites' => $totalProcessed,
                    'affectationsCrees' => $totalCreated,
                    'affectationsMisesAJour' => $totalUpdated,
                    'erreurs' => $totalErrors
                ],
                'results' => $results
            ]);

        } catch (\Exception $e) {
            return $this->json([
                'status' => 'error',
                'message' => 'Erreur lors du traitement automatique',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    #[Route('/admin/import/{ejCode}/affectations/auto', name: 'import_affectations_auto_single', methods: ['POST'])]
    public function processAutoImportForSingleEj(string $ejCode): JsonResponse
    {
        try {
            $result = $this->autoImportService->processEntiteJuridique($ejCode);

            if (isset($result['error'])) {
                return $this->json([
                    'status' => 'error',
                    'message' => $result['error']
                ], 400);
            }

            return $this->json([
                'status' => 'success',
                'message' => "Traitement automatique terminé pour l'entité juridique {$ejCode}",
                'result' => $result
            ]);

        } catch (\Exception $e) {
            return $this->json([
                'status' => 'error',
                'message' => 'Erreur lors du traitement automatique',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
