<?php

namespace App\ApiResource\Activite;

use ApiPlatform\Metadata\ApiProperty;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\GetCollection;
use App\ApiResource\Base\BaseDto;
use App\State\Partials\ActesMetadataProvider;

/**
 * DTO pour les metadonnees des actes medicaux.
 *  Ce fichier est en @todo a finir et a voir pourquoi j'ai un 404
 *
 * Fournit des informations rapides sur le dataset :
 * - Nombre total d'actes
 * - Repartition par type (CCAM, NGAP, LABO)
 * - Plage de dates disponibles
 * - Derniere mise a jour
 *
 * Exemple Postman :
 * GET http://localhost:8000/api/actes/metadata
 * GET http://localhost:8000/api/actes/metadata?p1Start=2024-01-01&p1End=2024-12-31
 * GET http://localhost:8000/api/actes/metadata?ejcode=EJ001
 * GET http://localhost:8000/api/actes/metadata?p1Start=2024-01-01&p1End=2024-12-31&ejcode=EJ001
 *
 * Filtrage par entité juridique :
 * Le paramètre ejcode permet de filtrer les statistiques pour une entité juridique spécifique.
 * La relation utilisée est : acte -> ufIntervention -> cr -> pole -> hopital (EntiteJuridique)
 */
#[ApiResource(
    operations: [
        new Get(
            uriTemplate: '/current/metadata/actes',
            description: 'Recupere les metadonnees des actes medicaux (totaux, repartition par type, plage de dates)',
        )
    ],
    cacheHeaders: [
        'max_age' => 1800,    // 30 minutes cote client
        'shared_max_age' => 1800, // 30 minutes cote proxy
        'public' => true,
        'vary' => ['Accept']
    ],
    provider: ActesMetadataProvider::class
)]
class ActesMetadataDto
{
    #[ApiProperty(description: "Nombre total d'actes", example: 15420)]
    public int $totalItems = 0;

    #[ApiProperty(description: "Repartition par type d'acte", example: ["CCAM" => 8500, "NGAP" => 4200, "LABO" => 2720])]
    public array $totalByType = [];

    #[ApiProperty(description: "Date de debut de la plage disponible", example: "2022-01-01")]
    public ?string $dateRangeMin = null;

    #[ApiProperty(description: "Date de fin de la plage disponible", example: "2024-12-31")]
    public ?string $dateRangeMax = null;

    #[ApiProperty(description: "Derniere mise a jour des donnees", example: "2024-03-15T10:30:00Z")]
    public ?\DateTimeInterface $lastUpdate = null;

    #[ApiProperty(description: "Periodes filtrees appliquees", example: ["p1" => "2024-01-01 to 2024-12-31"])]
    public array $appliedPeriods = [];

    #[ApiProperty(description: "Entité juridique filtrée (code)", example: "EJ001")]
    public ?string $appliedEjCode = null;

    #[ApiProperty(description: "Temps de generation en millisecondes", example: 45)]
    public int $generationTimeMs = 0;
}
