<?php

// Script de debug pour tester la logique de correspondance des domaines
// Reproduit la logique de EmailAuthService::findEjCodeFromEmail()

$email = '<EMAIL>';
$ldapHost = 'LDAP://SELVE.CHU-NANCY.FR:389';

echo "=== DEBUG EMAIL AUTH ===\n";
echo "Email: $email\n";
echo "LDAP Host: $ldapHost\n\n";

// 1. Extraire le domaine de l'email
$domain = substr(strrchr($email, "@"), 1);
echo "1. Domaine extrait de l'email: '$domain'\n";

// 2. Extraire le nom d'hôte du LDAP host
$hostName = '';
if (preg_match('/ldap:\/\/([^:]+)/i', $ldapHost, $hostMatches)) {
    $hostName = $hostMatches[1];
    echo "2. Nom d'hôte LDAP extrait: '$hostName'\n";
} else {
    echo "2. ERREUR: Impossible d'extraire le nom d'hôte LDAP\n";
    exit;
}

// 3. Traiter le nom d'hôte LDAP
$hostParts = explode('.', strtolower($hostName));
echo "3. Parties du nom d'hôte: " . json_encode($hostParts) . "\n";

if (count($hostParts) > 2) {
    array_shift($hostParts); // Supprimer le premier segment (ex: "selve")
    $hostDomain = implode('.', $hostParts);
    echo "4. Domaine hôte après suppression du premier segment: '$hostDomain'\n";
} else {
    echo "4. ERREUR: Pas assez de parties dans le nom d'hôte\n";
    exit;
}

// 4. Fonction de correspondance des domaines (reproduite)
function checkDomainMatch(string $emailDomain, string $configDomain): bool
{
    echo "\n--- Vérification de correspondance ---\n";
    
    // Normaliser les domaines en minuscules
    $emailDomain = strtolower($emailDomain);
    $configDomain = strtolower($configDomain);
    
    echo "Email domain (normalized): '$emailDomain'\n";
    echo "Config domain (normalized): '$configDomain'\n";
    
    // Cas 1: Correspondance exacte
    if ($emailDomain === $configDomain) {
        echo "✅ Correspondance exacte trouvée\n";
        return true;
    }
    echo "❌ Pas de correspondance exacte\n";
    
    // Cas 2: Gestion spéciale pour "chru" vs "chu"
    $normalizedEmailDomain = str_replace('chru', 'chu', $emailDomain);
    $normalizedConfigDomain = str_replace('chru', 'chu', $configDomain);
    
    echo "Email domain (chru->chu): '$normalizedEmailDomain'\n";
    echo "Config domain (chru->chu): '$normalizedConfigDomain'\n";
    
    if ($normalizedEmailDomain === $normalizedConfigDomain) {
        echo "✅ Correspondance trouvée après normalisation chru/chu\n";
        return true;
    }
    echo "❌ Pas de correspondance après normalisation chru/chu\n";
    
    // Cas 3: Sous-domaines
    if (strpos($emailDomain, $configDomain) !== false || strpos($configDomain, $emailDomain) !== false) {
        echo "✅ Correspondance trouvée par sous-domaine\n";
        return true;
    }
    echo "❌ Pas de correspondance par sous-domaine\n";
    
    // Cas 4: Parties principales
    $emailParts = explode('.', $emailDomain);
    $configParts = explode('.', $configDomain);
    
    $emailMainParts = array_slice($emailParts, max(0, count($emailParts) - 2));
    $configMainParts = array_slice($configParts, max(0, count($configParts) - 2));
    
    echo "Email main parts: " . json_encode($emailMainParts) . "\n";
    echo "Config main parts: " . json_encode($configMainParts) . "\n";
    
    if (implode('.', $emailMainParts) === implode('.', $configMainParts)) {
        echo "✅ Correspondance trouvée par parties principales\n";
        return true;
    }
    echo "❌ Pas de correspondance par parties principales\n";
    
    // Cas 5: Parties individuelles avec normalisation chru/chu
    foreach ($emailParts as $emailPart) {
        foreach ($configParts as $configPart) {
            $normalizedEmailPart = str_replace('chru', 'chu', $emailPart);
            $normalizedConfigPart = str_replace('chru', 'chu', $configPart);
            
            if ($normalizedEmailPart === $normalizedConfigPart && 
                (strpos($normalizedEmailPart, 'chu') !== false || strpos($normalizedConfigPart, 'chu') !== false)) {
                echo "✅ Correspondance trouvée par partie individuelle: '$emailPart' <-> '$configPart'\n";
                return true;
            }
        }
    }
    echo "❌ Pas de correspondance par parties individuelles\n";
    
    return false;
}

// 5. Tester la correspondance
$match = checkDomainMatch($domain, $hostDomain);

echo "\n=== RÉSULTAT ===\n";
if ($match) {
    echo "✅ CORRESPONDANCE TROUVÉE - Code EJ devrait être retourné\n";
} else {
    echo "❌ AUCUNE CORRESPONDANCE - EMAIL_AUTH sera retourné\n";
}
