<?php

namespace App\Mapper\Pole;

use App\ApiResource\Structure\EntiteJuridiqueDto;
use App\ApiResource\Structure\PoleDto;
use App\Entity\Structure\Pole;
use Symfonycasts\MicroMapper\AsMapper;
use Symfonycasts\MicroMapper\MapperInterface;
use Symfonycasts\MicroMapper\MicroMapperInterface;

#[AsMapper(from: Pole::class, to: PoleDto::class)]
class PoleEntityToDtoMapper implements MapperInterface
{
    /**
     * À la mémoire de Ryan Weaver à jamais dans nos cœurs.
     *
     * Merci pour SymfonyCasts, pour tes contributions à Symfony,
     * et pour avoir rendu le développement web accessible à tant d'entre nous.
     * Ton héritage perdurera dans chaque ligne de code que tu nous as appris à écrire.
     */
    public function __construct(private MicroMapperInterface $microMapper) {}

    public function load(object $from, string $toClass, array $context): object
    {
        $pole = $from;
        assert($pole instanceof Pole);

        $dto = new PoleDto();
        $dto->id = $pole->getId();

        return $dto;
    }

    public function populate(object $from, object $to, array $context): object
    {
        $pole = $from;
        $dto = $to;
        assert($pole instanceof Pole);
        assert($dto instanceof PoleDto);

        $dto->dateCreation = $pole->getDateCreation();
        $dto->etab = $pole->getEtab();
        $dto->polecode = $pole->getPolecode();
        $dto->datdeb = $pole->getDatdeb();
        $dto->datfin = $pole->getDatfin();
        $dto->libelle = $pole->getLibelle();
        $dto->pfuser = $pole->getPfuser();
        $dto->dmdacre = $pole->getDmdacre();
        $dto->dmdamaj = $pole->getDmdamaj();

        // Champs d'horodatage
        $dto->isActif = $pole->isActive();
        $dto->validFrom = $pole->getValidFrom();
        $dto->validTo = $pole->getValidTo();
        $dto->periodeType = $pole->getPeriodeType();
        $dto->source = $pole->getSource();

        // Convertir EntiteJuridique en URI API Platform
        if ($pole->getHopital()) {
            $dto->hopital = $this->microMapper->map($pole->getHopital(), EntiteJuridiqueDto::class, [
                MicroMapperInterface::MAX_DEPTH => 0, // On ne charge que l'ID
            ]);
        }

        return $dto;
    }
}
