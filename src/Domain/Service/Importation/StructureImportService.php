<?php

namespace App\Domain\Service\Importation;

use App\Command\ImportStructureCommand;
use App\Controller\Importation\Structure\Dto\CrImportDto;
use App\Controller\Importation\Structure\Dto\PoleImportDto;
use App\Controller\Importation\Structure\Dto\ServiceImportDto;
use App\Controller\Importation\Structure\Dto\UfImportDto;
use App\Controller\Importation\Structure\StructureImportController;
use App\Entity\Structure\Cr;
use App\Entity\Structure\EntiteJuridique;
use App\Entity\Structure\Pole;
use App\Entity\Structure\Service;
use App\Entity\Structure\Ufs;
use App\Domain\Service\Notification\ErrorNotificationEmailService;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Doctrine\ORM\EntityManagerInterface;

/**
 * Service d'importation des structures organisationnelles depuis le data_integrator.
 *
 * Gère l'import de la hiérarchie complète : Pôles → Services → CRs → UFs
 *
 * Comportement UPSERT pour toutes les entités :
 * - Pas de création : Aucune nouvelle entité n'est créée si le code existe déjà
 * - Mise à jour complète : Tous les champs sont mis à jour avec les nouvelles valeurs
 * - Synchronisation : Les entités locales sont synchronisées avec les données du data_integrator
 * - Pas de doublon : Les codes étant uniques, impossible d'avoir des doublons
 * - Pagination adaptative : S'adapte automatiquement au pageSize configuré dans l'API
 * - Protection anti-boucle : Limite de sécurité à 10000 pages maximum
 *
 * @see StructureImportController
 * @see ImportStructureCommand
 */
class StructureImportService
{
    /**
     * @var bool Flag to track if default pole was used
     */
    private $defaultPoleUsed = false;
    
    /**
     * @var array List of CRs assigned to default pole
     */
    private $crsAssignedToDefaultPole = [];
    
    /**
     * @var bool Flag to track if default CR was used
     */
    private $defaultCrUsed = false;
    
    /**
     * @var array List of UFs assigned to default CR
     */
    private $ufsAssignedToDefaultCr = [];
    
    public function __construct(
        private readonly HttpClientInterface $client,
        private readonly EntityManagerInterface $em,
        #[Autowire('%data_integrator_base_uri%')] private readonly string $baseUri,
        private readonly ?ErrorNotificationEmailService $errorNotificationService = null,
    ) {}

    public function importPoles(): void
    {
        $refresh = $this->client->request('GET', $this->baseUri .'/ot-pole/refresh')->toArray();
        if (($refresh['success'] ?? null) !== true) {
            throw new \Exception('Erreur lors du refresh des pôles : ' . ($refresh['message'] ?? ''));
        }

        $page = 1;
        $totalPages = 1;

        do {
            $res = $this->client->request('GET', $this->baseUri . '/ot-pole', [
                'query' => ['page' => $page]
            ])->toArray();

            // Utiliser le pageSize retourné par l'API comme référence
            $currentPageSize = $res['pageSize'] ?? 50;
            $ejCode = $res['ejCode'] ?? null;

            foreach ($res['data'] as $poleData) {
                // Création du DTO
                $dto = new PoleImportDto();
                $dto->etab = $poleData['etab'] ?? null;
                $dto->poleCode = $poleData['poleCode'] ?? null;
                $dto->datDeb = $poleData['datDeb'] ?? null;
                $dto->datFin = $poleData['datFin'] ?? null;
                $dto->libelle = $poleData['libelle'] ?? null;
                $dto->pfUser = $poleData['pfUser'] ?? null;
                $dto->dmdaCre = $poleData['dmdaCre'] ?? null;
                $dto->dmdaMaj = $poleData['dmdaMaj'] ?? null;

                // Persistance
                $this->upsertPole($poleData, $ejCode);
            }

            $totalPages = $res['paginationTotal'] ?? 1;
            $page++;

            // Vérification de sécurité pour éviter les boucles infinies
            if ($page > 10000) {
                throw new \Exception('Trop de pages à traiter, vérifiez la pagination de l\'API [data-integrator]');
            }

        } while ($page <= $totalPages);

        $this->em->flush();
    }

    private function upsertPole(array $data, ?string $ejCode): void
    {
        $pole = $this->em->getRepository(Pole::class)->findOneBy(['polecode' => $data['poleCode']]);

        if (!$pole) {
            $pole = new Pole();
            
            // Tronquer polecode à 5 caractères si nécessaire (contrainte VARCHAR(5))
            $poleCode = $data['poleCode'] ?? '';
            if (strlen($poleCode) > 5) {
                $poleCode = substr($poleCode, 0, 5);
            }
            $pole->setPolecode($poleCode);
            
            $this->em->persist($pole);
        }

        $pole->setLibelle($data['libelle'] ?? '');
        
        // Tronquer etab à 5 caractères si nécessaire (contrainte VARCHAR(5))
        $etab = $data['etab'] ?? null;
        if ($etab !== null && strlen($etab) > 5) {
            $etab = substr($etab, 0, 5);
        }
        $pole->setEtab($etab);
        if (!empty($data['datDeb'])) {
            $pole->setDatdeb(new \DateTime($data['datDeb']));
        }
        if (!empty($data['datFin'])) {
            $pole->setDatfin(new \DateTime($data['datFin']));
        }
        $pole->setPfuser($data['pfUser'] ?? null);
        if (!empty($data['dmdaCre'])) {
            $pole->setDmdacre(new \DateTime($data['dmdaCre']));
        }
        if (!empty($data['dmdaMaj'])) {
            $pole->setDmdamaj(new \DateTime($data['dmdaMaj']));
        }
        // Utilisation du ejCode transmis
        if (!empty($ejCode)) {
            $ej = $this->em->getRepository(EntiteJuridique::class)->findOneBy(['code' => $ejCode]);
            if ($ej) {
                $pole->setHopital($ej);
            }
        }
    }

    /***************************************************************/

    public function importServices(): void
    {
        $refresh = $this->client->request('GET', $this->baseUri .'/ot-service/refresh')->toArray();
        if (($refresh['success'] ?? null) !== true) {
            throw new \Exception('Erreur lors du refresh des services : ' . ($refresh['message'] ?? ''));
        }

        $page = 1;
        $totalPages = 1;

        do {
            $res = $this->client->request('GET', $this->baseUri . '/ot-service', [
                'query' => ['page' => $page]
            ])->toArray();

            // Utiliser le pageSize retourné par l'API comme référence
            $currentPageSize = $res['pageSize'] ?? 50;
            $ejCode = $res['ejCode'] ?? null;

            foreach ($res['data'] as $serviceData) {
                // Création du DTO
                $dto = new ServiceImportDto();
                $dto->etab = $serviceData['etab'] ?? null;
                $dto->seCode = $serviceData['seCode'] ?? null;
                $dto->datDeb = $serviceData['datDeb'] ?? null;
                $dto->datFin = $serviceData['datFin'] ?? null;
                $dto->libelle = $serviceData['libelle'] ?? null;
                $dto->cdCode = $serviceData['cdCode'] ?? null;
                $dto->taCode = $serviceData['taCode'] ?? null;
                $dto->pfUser = $serviceData['pfUser'] ?? null;
                $dto->dmdAcre = $serviceData['dmdAcre'] ?? null;
                $dto->dmdAmaj = $serviceData['dmdAmaj'] ?? null;

                // Persistance
                $this->upsertService($serviceData, $ejCode);
            }

            $totalPages = $res['paginationTotal'] ?? 1;
            $page++;

            // Vérification de sécurité pour éviter les boucles infinies
            if ($page > 10000) {
                throw new \Exception('Trop de pages à traiter, vérifiez la pagination de l\'API [data-integrator]');
            }

        } while ($page <= $totalPages);

        $this->em->flush();
    }

    private function upsertService(array $data, ?string $ejCode): void
    {
        // Recherche de l’EntiteJuridique liée
        $ej = null;
        if (!empty($ejCode)) {
            $ej = $this->em->getRepository(EntiteJuridique::class)->findOneBy(['code' => $ejCode]);
        }
        if (!$ej) {
            throw new \Exception('EntiteJuridique non trouvée pour le Service ' . ($data['seCode'] ?? ''));
        }

        // Recherche par code unique
        $service = $this->em->getRepository(Service::class)->findOneBy(['secode' => $data['seCode'] ?? null]);
        if (!$service) {
            $service = new Service();
            $service->setSecode($data['seCode'] ?? '');
            $this->em->persist($service);
        }

        // Mise à jour des champs
        $service->setLibelle($data['libelle'] ?? '');
        $service->setEtab($data['etab'] ?? null);
        if (!empty($data['datDeb'])) {
            $service->setDatdeb(new \DateTime($data['datDeb']));
        }
        if (!empty($data['datFin'])) {
            $service->setDatfin(new \DateTime($data['datFin']));
        }
        $service->setCdcode($data['cdCode'] ?? null);
        $service->setTacode($data['taCode'] ?? null);
        $service->setPfuser($data['pfUser'] ?? null);
        if (!empty($data['dmdAcre'])) {
            $service->setDmdacre(new \DateTime($data['dmdAcre']));
        }
        if (!empty($data['dmdAmaj'])) {
            $service->setDmdamaj(new \DateTime($data['dmdAmaj']));
        }
        $service->setHopital($ej);
    }

    /***************************************************************/

    public function importCrs(): void
    {
        // Reset tracking variables
        $this->defaultPoleUsed = false;
        $this->crsAssignedToDefaultPole = [];
        
        // 1. Refresh
        $refresh = $this->client->request('GET', $this->baseUri .'/ot-cr/refresh')->toArray();
        if (($refresh['success'] ?? null) !== true) {
            throw new \Exception('Erreur lors du refresh des CR : ' . ($refresh['message'] ?? ''));
        }

        // 2. Pagination
        $page = 1;
        $totalPages = 1;

        do {
            $res = $this->client->request('GET', $this->baseUri . '/ot-cr', [
                'query' => ['page' => $page]
            ])->toArray();

            // Utiliser le pageSize retourné par l'API comme référence
            $currentPageSize = $res['pageSize'] ?? 50;
            $ejCode = $res['ejCode'] ?? null;

            foreach ($res['data'] as $crData) {
                // Création du DTO
                $dto = new CrImportDto();
                $dto->etab = $crData['etab'] ?? null;
                $dto->crCode = $crData['crCode'] ?? null;
                $dto->datDeb = $crData['datDeb'] ?? null;
                $dto->datFin = $crData['datFin'] ?? null;
                $dto->datClos = $crData['datClos'] ?? null;
                $dto->libelle = $crData['libelle'] ?? null;
                $dto->nomResp = $crData['nomResp'] ?? null;
                $dto->poleCode = $crData['poleCode'] ?? null;
                $dto->pfUser = $crData['pfUser'] ?? null;
                $dto->dmdAcre = $crData['dmdAcre'] ?? null;
                $dto->dmdAmaj = $crData['dmdAmaj'] ?? null;

                // Persistance
                $this->upsertCr($crData, $ejCode);
            }

            $totalPages = $res['paginationTotal'] ?? 1;
            $page++;

            // Vérification de sécurité pour éviter les boucles infinies
            if ($page > 10000) {
                throw new \Exception('Trop de pages à traiter, vérifiez la pagination de l\'API [data-integrator]');
            }

        } while ($page <= $totalPages);

        $this->em->flush();
        
        // Envoyer une notification si des CRs ont été assignés au pôle par défaut
        if ($this->defaultPoleUsed && $this->errorNotificationService !== null) {
            $this->notifyAdminsAboutDefaultPoleUsage();
        }
    }
    
    /**
     * Notifie les administrateurs que des CRs ont été assignés au pôle par défaut
     */
    private function notifyAdminsAboutDefaultPoleUsage(): void
    {
        // Vérifier que le service de notification est disponible
        if ($this->errorNotificationService === null) {
            error_log('Service de notification non disponible, impossible d\'envoyer les notifications pour pôle par défaut');
            return;
        }
        
        try {
            // Récupérer toutes les entités juridiques
            $entitesJuridiques = $this->em->getRepository(EntiteJuridique::class)->findAll();
            
            // Filtrer les entités qui ont les notifications d'erreur activées
            $entitesJuridiques = array_filter($entitesJuridiques, function($ej) {
                $parametre = $ej->getParametre();
                return isset($parametre['notifications']['NotifierAdminDesErreurProduite']) && 
                       $parametre['notifications']['NotifierAdminDesErreurProduite'] === true;
            });
            
            foreach ($entitesJuridiques as $entiteJuridique) {
                $this->errorNotificationService->sendErrorNotificationToAdmin(
                    $entiteJuridique,
                    'Import de la structure organisationnelle - Pôle par défaut utilisé',
                    'Des CRs sans pôle ont été assignés au pôle par défaut (code 000)',
                    [
                        'type' => 'Import structure',
                        'action' => 'Assignation au pôle par défaut',
                        'crs' => $this->crsAssignedToDefaultPole,
                        'date' => (new \DateTime())->format('Y-m-d H:i:s')
                    ]
                );
            }
        } catch (\Exception $e) {
            // Ne pas bloquer le processus si l'envoi de notification échoue
            error_log('Erreur lors de l\'envoi de notifications pour pôle par défaut : ' . $e->getMessage());
        }
    }

    private function upsertCr(array $data, ?string $ejCode = null): void
    {
        // Recherche du Pole lié
        $pole = $this->em->getRepository(Pole::class)->findOneBy(['polecode' => $data['poleCode'] ?? null]);
        
        // Si le pole n'existe pas, on utilise ou crée un pole par défaut avec le code "000"
        if (!$pole) {
            $pole = $this->getOrCreateDefaultPole($ejCode);
            $this->defaultPoleUsed = true;
            $this->crsAssignedToDefaultPole[] = $data['crCode'] ?? 'Unknown';
        }

        // Recherche par code unique
        $cr = $this->em->getRepository(Cr::class)->findOneBy(['crcode' => $data['crCode'] ?? null]);
        if (!$cr) {
            $cr = new Cr();
            $cr->setCrcode($data['crCode'] ?? '');
            $this->em->persist($cr);
        }

        // Mise à jour des champs
        $cr->setLibelle($data['libelle'] ?? '');
        
        // Tronquer etab à 5 caractères si nécessaire (contrainte VARCHAR(5))
        $etab = $data['etab'] ?? null;
        if ($etab !== null && strlen($etab) > 5) {
            $etab = substr($etab, 0, 5);
        }
        $cr->setEtab($etab);
        
        if (!empty($data['datDeb'])) {
            $cr->setDatdeb(new \DateTime($data['datDeb']));
        }
        if (!empty($data['datFin'])) {
            $cr->setDatfin(new \DateTime($data['datFin']));
        }
        $cr->setNomresp($data['nomResp'] ?? null);
        
        // Récupérer le code du pôle et s'assurer qu'il respecte la contrainte VARCHAR(5)
        $poleCode = $pole->getPolecode();
        if ($poleCode !== null && strlen($poleCode) > 5) {
            $poleCode = substr($poleCode, 0, 5);
        }
        $cr->setPolecode($poleCode); // Utiliser le code du pole trouvé ou créé
        $cr->setPfuser($data['pfUser'] ?? null);
        if (!empty($data['dmdAcre'])) {
            $cr->setDmdacre(new \DateTime($data['dmdAcre']));
        }
        if (!empty($data['dmdAmaj'])) {
            $cr->setDmdamaj(new \DateTime($data['dmdAmaj']));
        }
        $cr->setPole($pole);
    }
    
    /**
     * Récupère ou crée un pole par défaut avec le code "000"
     * 
     * @param string|null $ejCode Code de l'entité juridique à utiliser pour le pôle par défaut
     * @return Pole
     */
    private function getOrCreateDefaultPole(?string $ejCode = null): Pole
    {
        $defaultPole = $this->em->getRepository(Pole::class)->findOneBy(['polecode' => '000']);
        
        if (!$defaultPole) {
            // Récupérer l'EntiteJuridique correspondant à l'ejCode si fourni
            $entiteJuridique = null;
            if (!empty($ejCode)) {
                $entiteJuridique = $this->em->getRepository(EntiteJuridique::class)->findOneBy(['code' => $ejCode]);
            }
            
            // Si pas d'EntiteJuridique trouvée avec l'ejCode, prendre la première disponible
            if (!$entiteJuridique) {
                $entiteJuridique = $this->em->getRepository(EntiteJuridique::class)->findOneBy([]);
            }
            
            if (!$entiteJuridique) {
                throw new \Exception('Aucune EntiteJuridique trouvée pour créer le pôle par défaut');
            }
            
            $defaultPole = new Pole();
            $defaultPole->setPolecode('000');
            $defaultPole->setLibelle('Pôle par défaut pour CRs sans affectation dans le CA');
            $defaultPole->setEtab('DEF'); // Limité à 5 caractères pour respecter la contrainte VARCHAR(5)
            $defaultPole->setDatdeb(new \DateTime());
            $defaultPole->setPfuser('SYSTEM');
            $defaultPole->setDmdacre(new \DateTime());
            $defaultPole->setHopital($entiteJuridique); // Définir l'EntiteJuridique
            $this->em->persist($defaultPole);
            $this->em->flush($defaultPole); // Flush immédiatement pour pouvoir l'utiliser
        }
        
        return $defaultPole;
    }



    /***************************************************************/
    public function importUfs(): void
    {
        // Reset tracking variables
        $this->defaultCrUsed = false;
        $this->ufsAssignedToDefaultCr = [];
        
        // 1. Refresh
        $refresh = $this->client->request('GET', $this->baseUri .'/ot-uf/refresh')->toArray();
        if (($refresh['success'] ?? null) !== true) {
            throw new \Exception('Erreur lors du refresh des UF : ' . ($refresh['message'] ?? ''));
        }

        // 2. Pagination
        $page = 1;
        $totalPages = 1;

        do {
            $res = $this->client->request('GET', $this->baseUri . '/ot-uf', [
                'query' => ['page' => $page]
            ])->toArray();

            // Utiliser le pageSize retourné par l'API comme référence
            $currentPageSize = $res['pageSize'] ?? 50;
            $ejCode = $res['ejCode'] ?? null;

            foreach ($res['data'] as $ufData) {
                // Création du DTO
                $dto = new UfImportDto();
                $dto->etab = $ufData['etab'] ?? null;
                $dto->ufCode = $ufData['ufCode'] ?? null;
                $dto->crCode = $ufData['crCode'] ?? null;
                $dto->seCode = $ufData['seCode'] ?? null;
                $dto->umCode = $ufData['umCode'] ?? null;
                $dto->datDeb = $ufData['datDeb'] ?? null;
                $dto->datFin = $ufData['datFin'] ?? null;
                $dto->libelle = $ufData['libelle'] ?? null;
                $dto->pfUser = $ufData['pfUser'] ?? null;
                $dto->dmdAcre = $ufData['dmdAcre'] ?? null;
                $dto->dmdAmaj = $ufData['dmdAmaj'] ?? null;

                // Persistance
                $this->upsertUf($ufData, $ejCode);
            }

            $totalPages = $res['paginationTotal'] ?? 1;
            $page++;

            // Vérification de sécurité pour éviter les boucles infinies
            if ($page > 10000) {
                throw new \Exception('Trop de pages à traiter, vérifiez la pagination de l\'API [data-integrator]');
            }

        } while ($page <= $totalPages);

        $this->em->flush();
        
        // Envoyer une notification si des UFs ont été assignés au CR par défaut
        if ($this->defaultCrUsed && $this->errorNotificationService !== null) {
            $this->notifyAdminsAboutDefaultCrUsage();
        }
    }
    
    /**
     * Notifie les administrateurs que des UFs ont été assignés au CR par défaut
     */
    private function notifyAdminsAboutDefaultCrUsage(): void
    {
        // Vérifier que le service de notification est disponible
        if ($this->errorNotificationService === null) {
            error_log('Service de notification non disponible, impossible d\'envoyer les notifications pour CR par défaut');
            return;
        }
        
        try {
            // Récupérer toutes les entités juridiques
            $entitesJuridiques = $this->em->getRepository(EntiteJuridique::class)->findAll();
            
            // Filtrer les entités qui ont les notifications d'erreur activées
            $entitesJuridiques = array_filter($entitesJuridiques, function($ej) {
                $parametre = $ej->getParametre();
                return isset($parametre['notifications']['NotifierAdminDesErreurProduite']) && 
                       $parametre['notifications']['NotifierAdminDesErreurProduite'] === true;
            });
            
            foreach ($entitesJuridiques as $entiteJuridique) {
                $this->errorNotificationService->sendErrorNotificationToAdmin(
                    $entiteJuridique,
                    'Import de la structure organisationnelle - CR par défaut utilisé',
                    'Des UFs sans CR ont été assignés au CR par défaut (code 000)',
                    [
                        'type' => 'Import structure',
                        'action' => 'Assignation au CR par défaut',
                        'ufs' => $this->ufsAssignedToDefaultCr,
                        'date' => (new \DateTime())->format('Y-m-d H:i:s')
                    ]
                );
            }
        } catch (\Exception $e) {
            // Ne pas bloquer le processus si l'envoi de notification échoue
            error_log('Erreur lors de l\'envoi de notifications pour CR par défaut : ' . $e->getMessage());
        }
    }

    private function upsertUf(array $data, ?string $ejCode = null): void
    {
        // Recherche du Service lié
        $service = $this->em->getRepository(Service::class)->findOneBy(['secode' => $data['seCode'] ?? null]);
        if (!$service) {
            throw new \Exception('Service non trouvé pour l\'UF ' . ($data['ufCode'] ?? ''));
        }

        // Recherche du CR lié
        $cr = $this->em->getRepository(Cr::class)->findOneBy(['crcode' => $data['crCode'] ?? null]);
        
        // Si le CR n'existe pas, on utilise ou crée un CR par défaut
        if (!$cr) {
            $cr = $this->getOrCreateDefaultCr($ejCode);
            $this->defaultCrUsed = true;
            $this->ufsAssignedToDefaultCr[] = $data['ufCode'] ?? 'Unknown';
        }

        // Recherche par code unique
        $uf = $this->em->getRepository(Ufs::class)->findOneBy(['ufcode' => $data['ufCode'] ?? null]);
        if (!$uf) {
            $uf = new Ufs();
            $uf->setUfcode($data['ufCode'] ?? '');
            $this->em->persist($uf);
        }

        // Mise à jour des champs
        $uf->setTacode($data['taCode'] ?? null);
        
        // Tronquer cdcode à 5 caractères si nécessaire (contrainte VARCHAR(5))
        $cdcode = $data['cdCode'] ?? null;
        if ($cdcode !== null && strlen($cdcode) > 5) {
            $cdcode = substr($cdcode, 0, 5);
        }
        $uf->setCdcode($cdcode);
        
        $uf->setLettrebudg($data['lettreBudg'] ?? null);
        $uf->setCgcode($data['cgCode'] ?? null);
        $uf->setTopmedical($data['topMedical'] ?? null);
        $uf->setSacode($data['saCode'] ?? null);
        $uf->setLibelle($data['libelle'] ?? '');
        
        // Tronquer etab à 5 caractères si nécessaire (contrainte VARCHAR(5))
        $etab = $data['etab'] ?? null;
        if ($etab !== null && strlen($etab) > 5) {
            $etab = substr($etab, 0, 5);
        }
        $uf->setEtab($etab);
        $uf->setUmcode($data['umCode'] ?? null);
        if (!empty($data['datDeb'])) {
            $uf->setDatdeb(new \DateTime($data['datDeb']));
        }
        if (!empty($data['datFin'])) {
            $uf->setDatfin(new \DateTime($data['datFin']));
        }
        if (!empty($data['datClos'])) {
            $uf->setDatclos(new \DateTime($data['datClos']));
        }
        $uf->setPfuser($data['pfUser'] ?? null);
        if (!empty($data['dmdAcre'])) {
            $uf->setDmdacre(new \DateTime($data['dmdAcre']));
        }
        if (!empty($data['dmdAmaj'])) {
            $uf->setDmdamaj(new \DateTime($data['dmdAmaj']));
        }
        $uf->setService($service);
        $uf->setCr($cr);
        $uf->setCrcode($cr->getCrcode());
    }
    
    /**
     * Récupère ou crée un CR par défaut
     * 
     * @param string|null $ejCode Code de l'entité juridique à utiliser pour le CR par défaut
     * @return Cr
     */
    private function getOrCreateDefaultCr(?string $ejCode = null): Cr
    {
        $defaultCr = $this->em->getRepository(Cr::class)->findOneBy(['crcode' => '000']);
        
        if (!$defaultCr) {
            // Récupérer le pôle par défaut en passant l'ejCode
            $defaultPole = $this->getOrCreateDefaultPole($ejCode);
            
            $defaultCr = new Cr();
            $defaultCr->setCrcode('000');
            $defaultCr->setLibelle('CR par défaut pour UFs sans affectation dans le CA');
            $defaultCr->setEtab('DEF'); // Limité à 5 caractères pour respecter la contrainte VARCHAR(5)
            $defaultCr->setDatdeb(new \DateTime());
            $defaultCr->setPfuser('SYSTEM');
            $defaultCr->setDmdacre(new \DateTime());
            $defaultCr->setPolecode($defaultPole->getPolecode());
            $defaultCr->setPole($defaultPole);
            $this->em->persist($defaultCr);
            $this->em->flush($defaultCr); // Flush immédiatement pour pouvoir l'utiliser
        }
        
        return $defaultCr;
    }

}