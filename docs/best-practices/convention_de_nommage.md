# 📌 Convention de Nommage

Ce document définit les conventions de nommage utilisées dans le projet pour assurer une cohérence et une lisibilité optimale.

## **1️⃣ Nommage des Entités (`src/Entity/`)
- Utiliser le **singulier** (`Praticien.php`, `Service.php`).
- Utiliser **PascalCase** (`NomDeLEntite`).

## **2️⃣ Nommage des Propriétés
- Utiliser **camelCase** (`dateDebut`, `isActif`).
- Préférer des noms explicites (`createdAt` plutôt que `date`).

## **3️⃣ Nommage des Relations
- `ManyToOne` → `entiteAssociee` (`praticien`, `service`).
- `OneToMany` → `collectionAssociee` (`praticiens`, `services`).

## **4️⃣ Nommage des Fichiers
- Entités : `Praticien.php`
- Traits : `HorodatageTrait.php`
- Classes abstraites : `AbstractEntity.php`

## **5️⃣ Nommage des Migrations
- Toujours **générées automatiquement** avec `doctrine:migrations:diff`.
- Ne jamais modifier une migration après exécution.

## ✅ Bonnes Pratiques
- **Consistance** : Suivre ces conventions dans tout le projet.
- **Lisibilité** : Utiliser des noms clairs et explicites.
- **Éviter les abréviations** sauf si standard (`id`, `uuid`).

📌 Respecter ces conventions garantit un code **structuré, lisible et maintenable** !

