<?php

namespace App\Domain\Extension\CoreSupraFilter\Agent;

/**
 * Filtre de catégories d'agents pour l'API publique.
 *
 * OBJECTIF : Restreindre l'accès aux listes d'agents pour ne montrer que le personnel médical
 * dans les endpoints de collection (GET /api/agents). Les accès individuels restent ouverts.
 *
 * FONCTIONNEMENT :
 * - Les catégories listées dans EXCLUDED_CATEGORIES sont filtrées des collections
 * - La comparaison est insensible à la casse (UPPER() en SQL)
 * - Les agents avec categorie = NULL sont INCLUS par défaut
 * - Le filtrage ne s'applique PAS aux accès individuels (GET /api/agents/{id})
 *
 * CONFIGURATION ACTUELLE :
 * - Exclus : Personnel administratif et technique identifié
 * - Inclus : Personnel médical + agents sans catégorie définie
 *
 * @todo Affiner la liste des catégories avec M. PetitCOLAS ou équipe métier
 * @todo Vérifier si le champ 'categorie' du collecteur suffit pour distinguer médical/non-médical
 * @todo Si besoin d'un nouveau champ discriminant, voir le tuto dans README.md pour l'ajout d'attributs API
 *
 * @see AgentCategoryFilterExtension Pour l'implémentation Doctrine
 * @see README.md Section "Ajout de nouvelle fonctionnalité à l'API"
 */
class AgentCategoryExcludeFilter
{
    // Catégories à exclure du retour API
    // Par  defaut j'ai exclu les categorie de Vincent, JC, Anas et Justine Pate ET M. CALVO
    private const EXCLUDED_CATEGORIES = [
        '5182',
        '0018',
        '0028',
        '5813'
    ];


    public static function getExcludedCategories(): array
    {
        return self::EXCLUDED_CATEGORIES;
    }
}