<?php

namespace App\Controller;

use App\Entity\Praticien\Agent;
use App\Domain\Service\JwtTokenService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

class HomeController  extends AbstractController
{


    #[Route('/', name: 'home')]
    public function index(): Response
    {
        return new Response(
            '<html><body><h1>Bienvenue sur le microservice Supra {Auth}</h1></body></html>'
        );
    }

    #[Route('/test-token', name: 'test_token')]
    public function testJWT(JwtTokenService $jwtTokenService): JsonResponse
    {
        $jwtSecret = $_ENV['JWT_SECRET'] ?? 'Non défini';

        $user = new Agent();
        $user->setEmail('<EMAIL>');
        $user->setRoles(['ROLE_USER']);
        $user->setId(100); // Set ID for the token

        // Génère le token JWT avec le service personnalisé
        $token = $jwtTokenService->generate($user, 'TEST-EJ');

        return new JsonResponse([
            'env' => [
                'JWT_SECRET' => $jwtSecret,
            ],
            'token' => $token,
        ]);
    }

}