# Cache Warming pour les API Endpoints

## Introduction

Le cache warming (préchauffage du cache) est une technique qui consiste à remplir le cache de manière proactive avant que les utilisateurs n'en aient besoin. Cette approche permet d'améliorer les performances perçues par les utilisateurs en évitant les "cache misses" (requêtes non mises en cache) lors des premières utilisations.

## Commande de Cache Warming

SUPRA API inclut une commande Symfony dédiée pour le cache warming des endpoints API :

```bash
bin/console app:warmup-api-cache
```

Cette commande précharge les données des endpoints API dans le cache Redis, en effectuant des requêtes HTTP sur les endpoints spécifiés.

## Options disponibles

La commande accepte plusieurs options pour personnaliser son comportement :

| Option | Description | Valeur par défaut |
|--------|-------------|-------------------|
| `--base-url` | URL de base de l'API | http://localhost:8000 |
| `--endpoints` | Liste d'endpoints à préchauffer (séparés par des virgules) | actes,agents,ufs |
| `--pages` | Nombre de pages à préchauffer pour chaque endpoint | 3 |
| `--items-per-page` | Nombre d'éléments par page | 10 |
| `--filters` | Filtres à appliquer (format JSON) | {} |

## Exemples d'utilisation

### Préchauffer les endpoints par défaut
```bash
bin/console app:warmup-api-cache
```

### Préchauffer un endpoint spécifique
```bash
bin/console app:warmup-api-cache --endpoints=actes
```

### Préchauffer plusieurs endpoints avec plus de pages
```bash
bin/console app:warmup-api-cache --endpoints=actes,agents --pages=5
```

### Préchauffer avec des filtres spécifiques
```bash
bin/console app:warmup-api-cache --endpoints=actes --filters='{"typeActe":"CCAM"}'
```

### Préchauffer avec une URL de base différente (environnement de production)
```bash
bin/console app:warmup-api-cache --base-url=https://api.supra.chru-nancy.fr
```

## Intégration avec les déploiements

Il est recommandé d'exécuter cette commande après chaque déploiement pour s'assurer que le cache est rempli avant que les utilisateurs n'accèdent à l'application. Vous pouvez l'ajouter à votre script de déploiement ou à votre pipeline CI/CD.

Exemple dans un script de déploiement :
```bash
#!/bin/bash
# Déploiement de l'application
git pull
composer install --no-dev --optimize-autoloader
bin/console cache:clear
bin/console doctrine:migrations:migrate --no-interaction

# Préchauffage du cache
bin/console app:warmup-api-cache --base-url=https://api.supra.chru-nancy.fr
```

## Surveillance et débogage

Pour vérifier que le cache a bien été rempli, vous pouvez utiliser les commandes Redis :

```bash
# Connexion à Redis
redis-cli

# Lister les clés du cache
KEYS *supra:api:cache*

# Vérifier le TTL d'une clé spécifique
TTL nom_de_la_clé
```

Le TTL des clés devrait correspondre à la valeur définie dans la variable d'environnement `API_CACHE_TTL` (3600 secondes / 1 heure par défaut).

Consultez le [Redis Cheat Sheet](redis-cheat-sheet.md) pour plus d'informations sur les commandes Redis.
