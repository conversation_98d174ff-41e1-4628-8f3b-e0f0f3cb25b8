<?php

namespace App\ApiResource\Praticien;

use ApiPlatform\Doctrine\Orm\Filter\SearchFilter;
use ApiPlatform\Doctrine\Orm\State\Options;
use ApiPlatform\Metadata\ApiFilter;
use ApiPlatform\Metadata\ApiProperty;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\GetCollection;
use ApiPlatform\Metadata\Post;
use App\ApiResource\Base\BaseDto;
use App\ApiResource\Structure\UfsDto;
use App\Controller\Importation\Agent\AffectationImportController;
use App\Domain\Enum\PeriodeType;
use App\Entity\Praticien\AgentUfs;
use App\State\EntityClassDtoStateProcessor;
use App\State\EntityToDtoStateProvider;

/**
 * DTO pour l'entité AgentUfs
 * Représente l'affectation d'un agent à une UFS (Unité Fonctionnelle de Soins)
 * Inclut les champs de DamAffectationTrait et HorodatageTrait
 * Permet de rechercher par les champs de l'agent (hrUser, matricule, email) et de l'UFS (codeUf)
 *  pour le [POST] @see AffectationImportController
 */
#[ApiResource(
    shortName: 'affectations',
    operations: [
        new Get(),
        new GetCollection(),
        new Post(
            formats: ['json' => ['application/json']]
        )
    ],
    paginationItemsPerPage: 10,
    provider: EntityToDtoStateProvider::class,
    processor: EntityClassDtoStateProcessor::class,
    stateOptions: new Options(entityClass: AgentUfs::class),
)]
#[ApiFilter(SearchFilter::class, properties: [
    'agent.hrUser' => 'partial',
    'agent.id' => 'exact',
    'ufs.ufcode' => 'exact',
    'ufs.libelle' => 'partial',
])]
class AgentUfsDto extends BaseDto
{

    #[ApiProperty(description: "Date de début", example: "2024-01-01T00:00:00")]
    public ?\DateTimeInterface $dateDebut = null;

    #[ApiProperty(description: "Date de fin", example: "2024-12-31T23:59:59")]
    public ?\DateTimeInterface $dateFin = null;

    // Champs du DamAffectationTrait
    #[ApiProperty(description: "Code RGT")]
    #[ApiFilter(SearchFilter::class, strategy: 'partial')]
    public ?string $rgt = null;

    #[ApiProperty(description: "ETP Statutaire", example: "1.00")]
    public ?float $etpStatutaire = null;

    #[ApiProperty(description: "Taux d'affectation en pourcentage", example: "100")]
    public ?int $tauxAffectation = null;

    #[ApiProperty(description: "ETP", example: "0.80")]
    public ?float $etp = null;

    #[ApiProperty(description: "Somme ETP", example: "0.80")]
    public ?float $sommeEtp = null;

    #[ApiProperty(description: "Affectation principale (O/N)", example: "O")]
    public ?string $affectationPrincipale = null;

    #[ApiProperty(description: "Type de grade")]
    public ?string $typeGrade = null;

    #[ApiProperty(description: "Libellé du grade")]
    public ?string $libelleGrade = null;

    #[ApiProperty(description: "Nombre d'absences")]
    public ?int $absences = null;

    // Champs du trait HorodatageTrait
    #[ApiProperty(description: "Date de début de validité", readable: false, writable: false, example: "2024-01-01T00:00:00",)]
    public ?\DateTimeInterface $validFrom = null;

    #[ApiProperty(description: "Date de fin de validité", readable: false, writable: false, example: "2024-12-31T23:59:59")]
    public ?\DateTimeInterface $validTo = null;

    #[ApiProperty(description: "Type de période", example: "HEBDOMADAIRE")]
    public string $periodeType = PeriodeType::HEBDOMADAIRE;

    #[ApiProperty(description: "Source des données", example: "Système externe")]
    public ?string $source = null;


    // Les champs suivants sont des relations
    #[ApiProperty(description: "Lien vers le praticien associé", example: "/api/praticiens/1")]
    public ?AgentDto $agent = null;

    #[ApiProperty(description: "Lien vers l'UFS associée", example: "/api/ufs/1")]
    public  $ufs = null;
//    public ?UfsDto $ufs = null;

    // champs pour N
    #[ApiProperty(description: "Nombre d'agents affectés à cette UFS avec affectation principale", example: "5")]
    public ?int $effectifDeUf = null;

}