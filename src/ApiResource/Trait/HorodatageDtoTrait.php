<?php

namespace App\ApiResource\Trait;

use ApiPlatform\Metadata\ApiProperty;

trait HorodatageDtoTrait
{
    #[ApiProperty(description: "Date de début de validité")]
    public ?\DateTimeInterface $validFrom = null;

    #[ApiProperty(description: "Date de fin de validité")]
    public ?\DateTimeInterface $validTo = null;

    #[ApiProperty(description: "Type de période (hebdomadaire, mensuel, annuel)", example: "HEBDOMADAIRE")]
    public string $periodeType;

    #[ApiProperty(description: "Source de la donnée", example: "import-csv")]
    public ?string $source = null;
}