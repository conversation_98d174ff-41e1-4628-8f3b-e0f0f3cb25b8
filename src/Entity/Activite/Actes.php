<?php

namespace App\Entity\Activite;

use ApiPlatform\Doctrine\Orm\Filter\SearchFilter;
use ApiPlatform\Metadata\ApiFilter;
use App\Domain\Filter\MultiFieldSearchFilter;
use App\Entity\Base\BaseEntity;
use App\Entity\Praticien\Agent;
use App\Entity\Structure\Ufs;
use App\Entity\Trait\HorodatageTrait;
use App\Repository\ActesRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: ActesRepository::class)]
#[ORM\Table(name: "actes", indexes: [
    new ORM\Index(name: "idx_valid_from", columns: ["validFrom"]),
    new ORM\Index(name: "idx_valid_to", columns: ["validTo"]),
    new ORM\Index(name: "idx_periode_type", columns: ["periodeType"]),
    // Index composites
    new ORM\Index(name: "idx_actes_type_date", columns: ["type_acte", "date_realisation"]),
    new ORM\Index(name: "idx_actes_periods", columns: ["validFrom", "validTo", "date_realisation"]),
    new ORM\Index(name: "idx_actes_agent_date", columns: ["agent_id", "date_realisation"]),
    new ORM\Index(name: "idx_actes_uf_date", columns: ["uf_intervention_id", "date_realisation"]),
    // Index pour les
    new ORM\Index(name: "idx_actes_code", columns: ["code"]),
    new ORM\Index(name: "idx_actes_internum", columns: ["internum"]),
    new ORM\Index(name: "idx_actes_active", columns: ["isActif"])
])]
#[ApiFilter(SearchFilter::class, properties: ['typeActe' => 'exact','internum'=> 'exact','code'=>'exact','activite'=>'exact','agent.hrUser'=>'exact','ufIntervention.ufcode'=>'exact'])]
#[ApiFilter(MultiFieldSearchFilter::class)]
class Actes extends BaseEntity
{
    use HorodatageTrait;


    #[ORM\Column(length: 20, nullable: true)]
    private ?string $internum = null;

    #[ORM\Column(length: 255)]
    private ?string $code = null;

    #[ORM\Column(type: Types::TEXT)]
    private ?string $description = null;

    #[ORM\Column(name:'type_acte',length: 255)]
    private ?string $typeActe = null;

    #[ORM\Column(type: Types::INTEGER,nullable: true)]
    private ?int $annee = null;

    #[ORM\Column(type: Types::INTEGER,nullable: true)]
    private ?int $mois = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $date_realisation = null; // dateActe du collecteur

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    private ?int $semaineIso = null;

    #[ORM\Column(type: Types::INTEGER, options: ["default" => 1])]
    private ?int $nombre_de_realisation = 1; // champs nombreActe du collecteur

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    private ?int $typeVenue = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $libTypeVenue = null;

    #[ORM\ManyToOne(inversedBy: 'actes')]
    private ?Agent $agent = null;     // on le find par le praticienMatricule du collecteur qui a l'occurence correspond au hrUser c'est a dire au U non

    #[ORM\ManyToOne(inversedBy: "actesPrincipal")]
    #[ORM\JoinColumn(nullable: true)]
    private ?Ufs $ufPrincipal = null;

    #[ORM\ManyToOne(inversedBy: "actesDemande")]
    #[ORM\JoinColumn(nullable: true)]
    private ?Ufs $ufDemande = null;

    #[ORM\ManyToOne(inversedBy: "actesIntervention")]
    #[ORM\JoinColumn(nullable: true)]
    private ?Ufs $ufIntervention = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    private ?int $icrA = null;

    #[ORM\Column(type: Types::FLOAT, nullable: true)]
    private ?float $coefficient = null;

    #[ORM\Column(length: 10, nullable: true)]
    private ?string $lettreCoef = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $regroupement = null;

    #[ORM\Column(length: 10, nullable: true)]
    private ?string $activite = null; // nouveau : l'activite type [PMSI] exemple 1, 2, 3 ou 4

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $activiteLib = null;

    public function __construct()
    {
        parent::__construct();
        $this->dateCreation = new \DateTime(); // Initialise la date de création
        if (!$this->isActive()){
            $this->setIsActif(true);
        }
    }


    public function getCode(): ?string
    {
        return $this->code;
    }

    public function setCode(string $code): static
    {
        $this->code = $code;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(string $description): static
    {
        $this->description = $description;

        return $this;
    }

    public function getDateRealisation(): ?\DateTimeInterface
    {
        return $this->date_realisation;
    }

    public function setDateRealisation(?\DateTimeInterface $date_realisation): static
    {
        $this->date_realisation = $date_realisation;

        return $this;
    }

    public function getNombreDeRealisation(): ?int
    {
        return $this->nombre_de_realisation;
    }

    public function setNombreDeRealisation(int $nb_acte): self
    {
        $this->nombre_de_realisation = $nb_acte;
        return $this;
    }

    public function getAnnee(): ?int
    {
        return $this->annee;
    }

    public function getMois(): ?int
    {
        return $this->mois;
    }

    public function getAgent(): ?Agent
    {
        return $this->agent;
    }

    public function setAgent(?Agent $agent): static
    {
        $this->agent = $agent;
        return $this;
    }

    public function getTypeActe(): ?string
    {
        return $this->typeActe;
    }

    public function setTypeActe(string $typeActe): static
    {
        $this->typeActe = $typeActe;

        return $this;
    }



    public function getInternum(): ?string
    {
        return $this->internum;
    }

    public function setInternum(?string $internum): static
    {
        $this->internum = $internum;

        return $this;
    }

    public function getSemaineIso(): ?int
    {
        return $this->semaineIso;
    }

    public function setSemaineIso(?int $semaineIso): static
    {
        $this->semaineIso = $semaineIso;

        return $this;
    }

    public function getTypeVenue(): ?int
    {
        return $this->typeVenue;
    }

    public function setTypeVenue(?int $typeVenue): static
    {
        $this->typeVenue = $typeVenue;

        return $this;
    }

    public function getLibTypeVenue(): ?string
    {
        return $this->libTypeVenue;
    }

    public function setLibTypeVenue(?string $libTypeVenue): static
    {
        $this->libTypeVenue = $libTypeVenue;

        return $this;
    }

    public function getUfPrincipal(): ?Ufs
    {
        return $this->ufPrincipal;
    }

    public function setUfPrincipal(?Ufs $ufPrincipal): static
    {
        $this->ufPrincipal = $ufPrincipal;

        return $this;
    }

    public function getUfDemande(): ?Ufs
    {
        return $this->ufDemande;
    }

    public function setUfDemande(?Ufs $ufDemande): static
    {
        $this->ufDemande = $ufDemande;

        return $this;
    }

    public function getUfIntervention(): ?Ufs
    {
        return $this->ufIntervention;
    }

    public function setUfIntervention(?Ufs $ufIntervention): static
    {
        $this->ufIntervention = $ufIntervention;

        return $this;
    }

    public function getIcrA(): ?int
    {
        return $this->icrA;
    }

    public function setIcrA(?int $icrA): static
    {
        $this->icrA = $icrA;

        return $this;
    }

    public function getCoefficient(): ?float
    {
        return $this->coefficient;
    }

    public function setCoefficient(?float $coefficient): static
    {
        $this->coefficient = $coefficient;

        return $this;
    }

    public function getLettreCoef(): ?string
    {
        return $this->lettreCoef;
    }

    public function setLettreCoef(?string $lettreCoef): static
    {
        $this->lettreCoef = $lettreCoef;

        return $this;
    }

    // ✅ Nouvelle logique pour ne garder que mois et année
    public function setAnnee(int $annee): self
    {
        $this->annee = $annee;
        $this->updateDateRealisation();
        return $this;
    }

    public function setMois(int $mois): self
    {
        $this->mois = $mois;
        $this->updateDateRealisation();
        return $this;
    }

    private function updateDateRealisation(): void
    {
        // Only update dateRealisation if it's not already set or if annee/mois were explicitly changed
        if ($this->annee !== null && $this->mois !== null && $this->date_realisation === null) {
            // On fixe le jour à 1 pour éviter toute ambiguïté
            $this->date_realisation = new \DateTime("{$this->annee}-{$this->mois}-01");
        }
    }

    public function getRegroupement(): ?string
    {
        return $this->regroupement;
    }

    public function setRegroupement(?string $regroupement): void
    {
        $this->regroupement = $regroupement;
    }

    public function getActivite(): ?string
    {
        return $this->activite;
    }

    public function setActivite(?string $activite): void
    {
        $this->activite = $activite;
    }

    public function getActiviteLib(): ?string
    {
        return $this->activiteLib;
    }

    public function setActiviteLib(?string $activiteLib): void
    {
        $this->activiteLib = $activiteLib;
    }


}

