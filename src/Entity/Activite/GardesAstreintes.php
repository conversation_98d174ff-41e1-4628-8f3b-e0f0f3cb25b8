<?php

namespace App\Entity\Activite;

use App\Entity\Base\BaseEntity;
use App\Entity\Praticien\Agent;
use App\Entity\Trait\HorodatageTrait;
use App\Repository\GardesAstreintesRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: GardesAstreintesRepository::class)]
#[ORM\Table(name: "gardes_astreintes", indexes: [
    new ORM\Index(name: "idx_valid_from", columns: ["validFrom"]),
    new ORM\Index(name: "idx_valid_to", columns: ["validTo"]),
    new ORM\Index(name: "idx_periode_type", columns: ["periodeType"])
])]
class GardesAstreintes extends BaseEntity
{
    use HorodatageTrait;
    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $dateDebut = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $dateFin = null;

    #[ORM\Column(nullable: true)]
    private ?int $totalGarde = null;

    #[ORM\Column(nullable: true)]
    private ?int $totalAstreinte = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $date_garde = null;

    #[ORM\Column(length: 255)]
    private ?string $type_garde = null;

    #[ORM\ManyToOne(inversedBy: 'gardesAstreintes')]
    private ?Agent $agent = null;
    

    public function getDateDebut(): ?\DateTimeInterface
    {
        return $this->dateDebut;
    }

    public function setDateDebut(?\DateTimeInterface $dateDebut): static
    {
        $this->dateDebut = $dateDebut;
        return $this;
    }

    public function getDateFin(): ?\DateTimeInterface
    {
        return $this->dateFin;
    }

    public function setDateFin(?\DateTimeInterface $dateFin): static
    {
        $this->dateFin = $dateFin;
        return $this;
    }

    public function getTotalGarde(): ?int
    {
        return $this->totalGarde;
    }

    public function setTotalGarde(?int $totalGarde): static
    {
        $this->totalGarde = $totalGarde;
        return $this;
    }

    public function getTotalAstreinte(): ?int
    {
        return $this->totalAstreinte;
    }

    public function setTotalAstreinte(?int $totalAstreinte): static
    {
        $this->totalAstreinte = $totalAstreinte;
        return $this;
    }

    public function getDateGarde(): ?\DateTimeInterface
    {
        return $this->date_garde;
    }

    public function setDateGarde(?\DateTimeInterface $date_garde): static
    {
        $this->date_garde = $date_garde;

        return $this;
    }

    public function getTypeGarde(): ?string
    {
        return $this->type_garde;
    }

    public function setTypeGarde(string $type_garde): static
    {
        $this->type_garde = $type_garde;

        return $this;
    }

    public function getAgent(): ?Agent
    {
        return $this->agent;
    }

    public function setAgent(?Agent $agent): static
    {
        $this->agent = $agent;
        return $this;
    }
}
