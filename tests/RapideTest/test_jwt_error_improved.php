<?php

// Enhanced script to test JWT token error messages with multiple tokens
// Run with: php tests/RapideTest/test_jwt_error_improved.php

// Function to test a token and return the results
function testToken($token, $description, $expectedErrorPattern = null, $expectedSuccess = false) {
    echo "\n=== Testing $description ===\n";
    
    // URL to test
    $url = 'http://localhost:8000/api/poles';
    
    // Initialize cURL
    $ch = curl_init($url);
    
    // Set cURL options
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    
    // Set headers based on whether we're testing a missing token
    if ($token === null) {
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/ld+json',
            'Accept: application/ld+json'
        ]);
    } else {
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $token,
            'Content-Type: application/ld+json',
            'Accept: application/ld+json'
        ]);
    }
    
    // Execute the request
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    // Close cURL
    curl_close($ch);
    
    // Output the results
    echo "HTTP Status Code: " . $httpCode . "\n";
    
    // Parse the response
    $responseData = json_decode($response, true);
    
    // Show a truncated version of the response for successful requests (to avoid huge output)
    if ($httpCode === 200) {
        echo "Response: Successfully retrieved data (response truncated for readability)\n";
        if (isset($responseData['@type']) && $responseData['@type'] === 'Collection') {
            echo "Collection type: " . $responseData['@type'] . "\n";
            echo "Total items: " . ($responseData['totalItems'] ?? 'unknown') . "\n";
        }
    } else {
        echo "Response Body:\n";
        echo json_encode($responseData, JSON_PRETTY_PRINT) . "\n";
    }
    
    // For successful authentication, we expect HTTP 200
    if ($expectedSuccess) {
        $success = ($httpCode === 200);
        if ($success) {
            echo "\nSuccess! Authentication succeeded as expected.\n";
        } else {
            echo "\nFailed! Expected successful authentication (HTTP 200) but got HTTP $httpCode.\n";
            if (isset($responseData['message'])) {
                echo "Error message: " . $responseData['message'] . "\n";
            }
        }
        return $success;
    }
    
    // For error cases, check if the error message contains "JWT" or matches the expected pattern
    $hasJwt = false;
    $matchesPattern = true;
    
    // Check for JWT in the message
    if (isset($responseData['message']) && strpos($responseData['message'], 'JWT') !== false) {
        $hasJwt = true;
    } elseif (isset($responseData['detail']) && strpos($responseData['detail'], 'authentication') !== false) {
        // Special case for Symfony's core security error (missing token)
        $hasJwt = true; // Consider this a success for the missing token case
    }
    
    // Check if the error message matches the expected pattern
    if ($expectedErrorPattern) {
        if (isset($responseData['message'])) {
            $matchesPattern = preg_match('/' . $expectedErrorPattern . '/i', $responseData['message']) === 1;
        } elseif (isset($responseData['detail'])) {
            // Special case for Symfony's core security error (missing token)
            $matchesPattern = preg_match('/' . $expectedErrorPattern . '/i', $responseData['detail']) === 1;
        } else {
            $matchesPattern = false;
        }
    }
    
    // Output the results
    if ($hasJwt) {
        echo "\nSuccess! The error message contains 'JWT' or is a valid authentication error.\n";
    } else {
        echo "\nFailed! The error message does not contain 'JWT' or is not a valid authentication error.\n";
        echo "Actual message: " . ($responseData['message'] ?? $responseData['detail'] ?? 'No message found') . "\n";
    }
    
    if ($expectedErrorPattern) {
        if ($matchesPattern) {
            echo "Success! The error message matches the expected pattern: $expectedErrorPattern\n";
        } else {
            echo "Failed! The error message does not match the expected pattern: $expectedErrorPattern\n";
        }
    }
    
    // Check for additional error details
    if (isset($responseData['details'])) {
        echo "\nAdditional Details: " . $responseData['details'] . "\n";
    }
    
    // Check for token debugging information
    if (isset($responseData['token_info'])) {
        echo "\nToken Debugging Information:\n";
        echo json_encode($responseData['token_info'], JSON_PRETTY_PRINT) . "\n";
    }
    
    return $hasJwt && $matchesPattern;
}

// Create various test tokens
// 1. The token provided by the user (reported as valid but getting "Invalid credentials" error)
$userProvidedToken = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.y8-MaMVkSUpxuTLDXXfxx9Hnaj_7g1jFfncD9fkm7O0';

// 2. A clearly invalid token
$clearlyInvalidToken = 'invalid.jwt.token';

// 3. A malformed token (only 2 parts instead of 3)
$malformedToken = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxMjM0NTY3ODkwIn0';

// 4. A token with invalid signature
$invalidSignatureToken = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.INVALID_SIGNATURE';

// 5. No token (null)
$noToken = null;

// Run tests with expected error patterns
$results = [];
// For the user-provided token, we expect successful authentication (HTTP 200)
$results['userToken'] = testToken($userProvidedToken, "User-Provided Token", null, true);
// For the other tokens, we expect JWT-specific error messages
$results['invalidToken'] = testToken($clearlyInvalidToken, "Clearly Invalid Token", "Malformed token|Invalid token");
$results['malformedToken'] = testToken($malformedToken, "Malformed Token (2 parts)", "Malformed token|Invalid token");
$results['invalidSignature'] = testToken($invalidSignatureToken, "Invalid Signature Token", "Invalid (token )?signature");
// For the missing token, we expect an authentication error (which might not contain "JWT")
$results['noToken'] = testToken($noToken, "Missing Token", "authentication|required");

// Summary
echo "\n=== Summary ===\n";
foreach ($results as $test => $success) {
    echo "$test: " . ($success ? "SUCCESS" : "FAILED") . "\n";
}

$overallSuccess = !in_array(false, $results);
if ($overallSuccess) {
    echo "\nOverall: SUCCESS - All tests passed with the expected results.\n";
} else {
    echo "\nOverall: FAILED - At least one test did not produce the expected result.\n";
}

echo "\nThis improved test script verifies that:\n";
echo "1. Valid tokens successfully authenticate (HTTP 200)\n";
echo "2. Invalid tokens produce detailed JWT-specific error messages\n";
echo "3. Missing tokens produce appropriate authentication errors\n";
echo "\nThe enhanced error messages help users understand what's wrong with their JWT token and how to fix it.\n";