<?php

namespace App\Factory\Praticien;

use App\Domain\Enum\AgentRoleType;
use App\Entity\Praticien\AgentHopitalRole;
use App\Factory\Structure\EntiteJuridiqueFactory;
use Zenstruck\Foundry\Persistence\PersistentProxyObjectFactory;

/**
 * @extends PersistentProxyObjectFactory<AgentHopitalRole>
 */
final class AgentHopitalRoleFactory extends PersistentProxyObjectFactory
{
    public function __construct()
    {
    }

    public static function class(): string
    {
        return AgentHopitalRole::class;
    }

    /**
     * Assigne des rôles aux agents dans différents hôpitaux.
     */
    public static function assignRolesToAgents(): void
    {
        $agents = AgentFactory::all();
        $hopitaux = EntiteJuridiqueFactory::all();

        foreach ($agents as $agent) {
            // On prend des hôpitaux aléatoires pour l'agent
            $randomHopitaux = EntiteJuridiqueFactory::randomRange(1, 3);

            foreach ($randomHopitaux as $hopital) {
                self::createOne([
                    'agent' => $agent,
                    'hopital' => $hopital,
                    'role' => self::faker()->randomElement(AgentRoleType::ALL_ROLES),
                    'dateAffectation' => self::faker()->dateTimeBetween('-3 years', 'now'),
                ]);
            }
        }
    }

    protected function defaults(): array|callable
    {
        return [
            'agent' => AgentFactory::random(),
            'hopital' => EntiteJuridiqueFactory::random(),
            'role' => self::faker()->randomElement(AgentRoleType::ALL_ROLES),
            'dateAffectation' => self::faker()->dateTimeBetween('-3 years', 'now'),
        ];
    }

    protected function initialize(): static
    {
        return $this;
    }
}