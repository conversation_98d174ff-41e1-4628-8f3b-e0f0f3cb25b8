<?php
namespace App\Domain\Service;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Security\Core\Exception\AuthenticationException;

/**
 * Service for validating request data
 */
class RequestValidator
{
    /**
     * Extract and validate authentication credentials from request
     */
    public function extractAuthCredentials(Request $request): array
    {
        $content = json_decode($request->getContent(), true);
        
        if (!is_array($content)) {
            throw new AuthenticationException('Format de requête invalide');
        }
        
        $username = $content['username'] ?? '';
        $password = $content['password'] ?? '';
        $hopitalCode = $content['hopital_code'] ?? null;
        
        // Validate required fields
        $this->validateRequiredFields($username, $password, $hopitalCode);
        
        // Sanitize inputs
        $username = $this->sanitizeUsername($username);
        
        return [
            'username' => $username,
            'password' => $password,
            'hopitalCode' => $hopitalCode
        ];
    }
    
    /**
     * Validate that all required fields are present
     */
    private function validateRequiredFields(string $username, string $password, ?string $hopitalCode): void
    {
        if (empty($username)) {
            throw new AuthenticationException('Le nom d\'utilisateur est requis');
        }
        
        if (empty($password)) {
            throw new AuthenticationException('Le mot de passe est requis');
        }
        
        if (empty($hopitalCode)) {
            throw new AuthenticationException('Le code de l\'hôpital est requis');
        }
    }
    
    /**
     * Sanitize username input
     */
    private function sanitizeUsername(string $username): string
    {
        // Convert to lowercase and trim whitespace
        return strtolower(trim($username));
    }
}