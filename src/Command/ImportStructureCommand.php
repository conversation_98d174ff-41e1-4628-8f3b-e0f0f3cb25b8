<?php

namespace App\Command;

use App\Command\DataIntegratorGlobalCacheClearCommand;
use App\Domain\Service\Importation\StructureImportService;
use App\Domain\Service\Notification\ErrorNotificationEmailService;
use App\Entity\Importation\Importation;
use App\Entity\Structure\EntiteJuridique;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\ArrayInput;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\Console\Application;

/**
 * Commande pour l'importation complète de la structure organisationnelle.
 * 
 * Cette commande synchronise toute la hiérarchie structurelle depuis le data_integrator :
 * - Pôles (départements)
 * - Services
 * - Centres de Responsabilité (CRs)
 * - Unités Fonctionnelles (UFs)
 * 
 * L'importation suit un ordre hiérarchique pour respecter les dépendances.
 * En cas d'erreur critique, notifie automatiquement tous les admins des entités
 * juridiques ayant activé les notifications d'erreur (NotifierAdminDesErreurProduite = true).
 * 
 * Usage :
 * php bin/console app:import:structure
 * 
 * Exemple de cron Linux (tous les 5 du mois à 3h00) :
 * 0 3 5 * * /usr/bin/php /path/to/supra/bin/console app:import:structure >> /var/log/supra-structure-import.log 2>&1
 */
#[AsCommand(
    name: 'app:import:structure',
    description: 'Importe toute la structure (pôles, services, CRs, UFs)',
)]
class ImportStructureCommand extends Command
{
    private ?Application $application = null;
    
    public function __construct(
        private readonly StructureImportService $importService,
        private readonly ErrorNotificationEmailService $errorNotificationService,
        private readonly EntityManagerInterface $em
    ) {
        parent::__construct();
    }
    
    protected function initialize(InputInterface $input, OutputInterface $output): void
    {
        $this->application = $this->getApplication();
    }
    
    /**
     * Execute la commande de nettoyage du cache global du data integrator
     * 
     * @return array|null La réponse de l'API contenant success et ejcode, ou null en cas d'erreur
     */
    private function executeDataIntegratorCacheClearCommand(OutputInterface $output): ?array
    {
        if (!$this->application) {
            return null;
        }
        
        try {
            $command = $this->application->find('app:data-integrator:global-cache-clear');
            
            // Exécuter la commande pour l'affichage dans la console
            $arguments = new ArrayInput([]);
            $command->run($arguments, $output);
            
            // Appeler directement la méthode pour récupérer la réponse de l'API
            if ($command instanceof DataIntegratorGlobalCacheClearCommand) {
                return $command->clearGlobalCacheAndGetResponse();
            }
        } catch (\Exception $e) {
            // Log l'erreur mais ne pas interrompre le processus
            $output->writeln('<error>Erreur lors de la récupération de la réponse API: ' . $e->getMessage() . '</error>');
        }
        
        return null;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $result = Command::FAILURE;
        
        try {
            // Nettoyer le cache du data integrator avant l'importation
            $io->section('Nettoyage du cache du data integrator avant importation');
            $this->executeDataIntegratorCacheClearCommand($output);
            
            // Start transaction
            $this->em->beginTransaction();
            
            try {
                $io->section('Début de l\'importation de la structure');
    
                $io->text('Import des pôles...');
                $this->importService->importPoles();
    
                $io->text('Import des services...');
                $this->importService->importServices();
    
                $io->text('Import des CRs...');
                $this->importService->importCrs();
    
                $io->text('Import des UFs...');
                $this->importService->importUfs();
                
                // Commit transaction if everything is successful
                $this->em->commit();
    
                $io->success('✅ Importation de la structure terminée avec succès');
                $result = Command::SUCCESS;
    
            } catch (\Throwable $e) {
                // Rollback transaction in case of error
                if ($this->em->getConnection()->isTransactionActive()) {
                    $this->em->rollback();
                }
                
                $io->error('❌ Erreur lors de l\'importation : ' . $e->getMessage());
    
                // Notifier toutes les entités juridiques qui ont activé les notifications d'erreur
                $this->notifyAdminsOfError($e);
                
                $result = Command::FAILURE;
            }
            
            // Nettoyer le cache du data integrator après l'importation (même en cas d'erreur)
            $io->section('Nettoyage du cache du data integrator après importation');
            $this->executeDataIntegratorCacheClearCommand($output);
            
            // Enregistrer le résultat de l'importation
            $io->section('Enregistrement du résultat de l\'importation');
            try {
                $importation = new Importation();
                $importation->setNomRessource('Structure');
                $importation->setEstReussie($result === Command::SUCCESS);
                
                // Persister l'entité
                $this->em->persist($importation);
                $this->em->flush();
                
                $io->success('Résultat de l\'importation enregistré avec succès');
            } catch (\Throwable $e) {
                $io->warning('Impossible d\'enregistrer le résultat de l\'importation : ' . $e->getMessage());
                // Ne pas modifier le résultat de la commande si l'enregistrement échoue
            }
            
            return $result;
            
        } catch (\Throwable $e) {
            $io->error('❌ Erreur lors du nettoyage du cache : ' . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * Notifie tous les admins des entités juridiques ayant activé les notifications d'erreur
     */
    private function notifyAdminsOfError(\Throwable $exception): void
    {
        try {
            // Récupérer toutes les entités juridiques avec notifications d'erreur activées
            $entitesJuridiques = $this->em->getRepository(EntiteJuridique::class)
                ->findAll();
                
            // Filtrer les entités qui ont les notifications d'erreur activées
            $entitesJuridiques = array_filter($entitesJuridiques, function($ej) {
                $parametre = $ej->getParametre();
                return isset($parametre['notifications']['NotifierAdminDesErreurProduite']) && 
                       $parametre['notifications']['NotifierAdminDesErreurProduite'] === true;
            });

            foreach ($entitesJuridiques as $entiteJuridique) {
                $this->errorNotificationService->sendErrorNotificationToAdmin(
                    $entiteJuridique,
                    'Import de la structure organisationnelle',
                    $exception->getMessage(),
                    [
                        'type' => 'Commande console',
                        'command' => 'app:import:structure',
                        'structures' => ['Pôles', 'Services', 'CRs', 'UFs'],
                        'stackTrace' => $exception->getTraceAsString(),
                        'file' => $exception->getFile(),
                        'line' => $exception->getLine()
                    ]
                );
            }

        } catch (\Exception $notificationError) {
            // Si l'envoi de notification échoue, on ne fait que logger pour ne pas masquer l'erreur principale
            error_log('Erreur lors de l\'envoi de notifications d\'erreur : ' . $notificationError->getMessage());
        }
    }
}