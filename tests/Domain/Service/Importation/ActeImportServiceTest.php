<?php

namespace App\Tests\Domain\Service\Importation;

use App\Domain\Service\Importation\ActeImportService;
use App\Entity\Activite\Actes;
use App\Entity\Praticien\Agent;
use App\Entity\Structure\EntiteJuridique;
use App\Entity\Structure\Ufs;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Doctrine\ORM\QueryBuilder;
use Doctrine\ORM\AbstractQuery;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;

it('can be instantiated', function () {
    $httpClient = $this->createMock(HttpClientInterface::class);
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $baseUri = 'http://data-integrator.example.com';
    
    $service = new ActeImportService($httpClient, $entityManager, $baseUri);
    
    expect($service)->toBeInstanceOf(ActeImportService::class);
});

it('imports actes successfully', function () {
    // Mock HTTP client
    $httpClient = $this->createMock(HttpClientInterface::class);
    $response = $this->createMock(ResponseInterface::class);
    
    // Create a mock response that will be configured for each URL
    $refreshResponse = $this->createMock(ResponseInterface::class);
    $refreshResponse->method('toArray')->willReturn(['status' => 'success']);
    
    $activitesResponse = $this->createMock(ResponseInterface::class);
    $activitesResponse->method('toArray')->willReturn([
        'ejCode' => 'EJ001',
        'data' => [
            [
                'internum' => '12345',
                'codeActe' => 'ABCD',
                'descriptionActe' => 'Test Acte',
                'dateActe' => '2025-08-09',
                'praticienMatricule' => 'U123456',
                'ufPrincipalCode' => 'UF001',
                'ejCode' => 'EJ001'
            ]
        ],
        'paginationTotal' => 1,
        'pageSize' => 50
    ]);
    
    // Mock the request method to return the appropriate response based on the URL
    $httpClient->method('request')
        ->willReturnCallback(function ($method, $url, $options = []) use ($refreshResponse, $activitesResponse) {
            echo "Request to URL: $url\n";
            
            if ($url === 'http://data-integrator.example.com/api/actes/refresh') {
                return $refreshResponse;
            }
            
            if ($url === 'http://data-integrator.example.com/api/activites') {
                return $activitesResponse;
            }
            
            echo "Unhandled URL: $url\n";
            return $this->createMock(ResponseInterface::class);
        });
    
    // Mock entity manager and repositories
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $entiteJuridiqueRepository = $this->createMock(EntityRepository::class);
    $agentRepository = $this->createMock(EntityRepository::class);
    $actesRepository = $this->createMock(EntityRepository::class);
    
    $entityManager->method('getRepository')
        ->willReturnCallback(function ($entityClass) use ($entiteJuridiqueRepository, $agentRepository, $actesRepository) {
            if ($entityClass === EntiteJuridique::class) {
                return $entiteJuridiqueRepository;
            }
            if ($entityClass === Agent::class) {
                return $agentRepository;
            }
            if ($entityClass === Actes::class) {
                return $actesRepository;
            }
            return null;
        });
    
    // Mock EntiteJuridique
    $entiteJuridique = $this->createMock(EntiteJuridique::class);
    $entiteJuridiqueRepository->method('findOneBy')
        ->willReturn($entiteJuridique);
    
    // Mock Agent
    $agent = $this->createMock(Agent::class);
    $agentRepository->method('findOneBy')
        ->willReturn($agent);
    
    // Mock Actes
    $actesRepository->method('findOneBy')
        ->willReturn(null);
    
    // Mock query builder for UF search
    $queryBuilder = $this->createMock(QueryBuilder::class);
    $query = $this->getMockBuilder(\Doctrine\ORM\Query::class)
        ->disableOriginalConstructor()
        ->getMock();
    $ufs = $this->createMock(Ufs::class);
    
    $entityManager->method('createQueryBuilder')
        ->willReturn($queryBuilder);
    
    $queryBuilder->method('select')->willReturnSelf();
    $queryBuilder->method('from')->willReturnSelf();
    $queryBuilder->method('join')->willReturnSelf();
    $queryBuilder->method('where')->willReturnSelf();
    $queryBuilder->method('andWhere')->willReturnSelf();
    $queryBuilder->method('setParameter')->willReturnSelf();
    $queryBuilder->method('orderBy')->willReturnSelf();
    $queryBuilder->method('getQuery')->willReturn($query);
    
    $query->method('getOneOrNullResult')
        ->willReturn($ufs);
    
    $baseUri = 'http://data-integrator.example.com';
    
    $service = new ActeImportService($httpClient, $entityManager, $baseUri);
    
    $result = $service->importActes();
    
    expect($result)->toBeArray()
        ->and($result)->toHaveKey('processed')
        ->and($result)->toHaveKey('created')
        ->and($result)->toHaveKey('updated')
        ->and($result)->toHaveKey('errors');
});

it('handles refresh error', function () {
    // Mock HTTP client
    $httpClient = $this->createMock(HttpClientInterface::class);
    $response = $this->createMock(ResponseInterface::class);
    
    // Mock response for refresh endpoint with error
    $httpClient->method('request')
        ->willReturnCallback(function ($method, $url, $options = []) use ($response) {
            if ($url === 'http://data-integrator.example.com/api/actes/refresh') {
                $response->method('toArray')->willReturn([
                    'status' => 'error',
                    'message' => 'Refresh failed'
                ]);
                return $response;
            }
            
            return $response;
        });
    
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $baseUri = 'http://data-integrator.example.com';
    
    $service = new ActeImportService($httpClient, $entityManager, $baseUri);
    
    expect(fn() => $service->importActes())
        ->toThrow(\Exception::class, 'Erreur lors du refresh des actes : Refresh failed');
});