SELECT
    *
FROM
    homere.EX_MOIS_ACTES2
WHERE ROWNUM <= 10;

-------------- load Acte ------------------------------
-------------- Version optimiser -------------------------------------
SELECT
    CODE_ACTE,
    COALESCE(LIBELLEC_A, LIBELLEL_A) AS description_acte,  -- Assure d'avoir un libellé correct
    TYPEACTE,
    ANNEE_ACTE,
    MOIS_ACTE,
    TO_DATE(ANNEE_ACTE || '-' || MOIS_ACTE || '-01', 'YYYY-MM-DD') AS date_realisation_estimee,
    NB AS nombre_actes,  -- Alias plus clair pour indiquer qu'il s'agit du nombre d'actes réalisés
    MATPAIEPRINC AS praticien_id,
    UFPRINCIPAL AS uf_code
FROM homere.EX_MOIS_ACTES2
WHERE TYPEACTE IN ('CCAM', 'NGAP', 'LABO')
  AND ANNEE_ACTE = 2024  -- Optimisation pour limiter la période
ORDER BY ANNEE_ACTE DESC, MOIS_ACTE DESC;

----------------- CLASSIFICATION----------------------------------------
SELECT DISTINCT
    HIERARCHIE2 AS categorie_principale,
    HIERARCHIE2LIB AS categorie_principale_libelle,
    HIERARCHIE5 AS sous_categorie,
    HIERARCHIE5LIB AS sous_categorie_libelle,
    HIERARCHIE8 AS niveau_detaille,
    HIERARCHIE8LIB AS niveau_detaille_libelle,
    COUNT(*) AS nb_actes
FROM homere.EX_MOIS_ACTES2
WHERE TYPEACTE = 'CCAM'
GROUP BY HIERARCHIE2, HIERARCHIE2LIB,
         HIERARCHIE5, HIERARCHIE5LIB,
         HIERARCHIE8, HIERARCHIE8LIB
ORDER BY HIERARCHIE2, HIERARCHIE5, HIERARCHIE8;
--------------------------------------------------------------------+
---- EXPORT ACTES AVEC CHAMPS STATIQUE
---------------------------------------------------------------------
SELECT
    CODE_ACTE,
    COALESCE(LIBELLEC_A, LIBELLEL_A) AS description_acte,  -- Assure d'avoir un libellé correct
    TYPEACTE,
    HIERARCHIE2 AS categorie_principale,
    HIERARCHIE2LIB AS categorie_principale_libelle,
    ANNEE_ACTE,
    MOIS_ACTE,
    TO_DATE(ANNEE_ACTE || '-' || MOIS_ACTE || '-01', 'YYYY-MM-DD') AS validFrom,  -- validFrom
    CASE
        WHEN ANNEE_ACTE < EXTRACT(YEAR FROM CURRENT_DATE)
            THEN TO_DATE(ANNEE_ACTE || '-12-31', 'YYYY-MM-DD')  -- validTo = 31 décembre de l’année importée
        ELSE NULL
        END AS validTo,
    'Annuel' AS periodeType,  -- Période par défaut (modifiable plus tard)
    NB AS nombre_actes,  -- Nombre total d'actes réalisés
    MATPAIEPRINC AS praticien_MATRICULE,
    UFPRINCIPAL AS uf_code,
    'Collecteur' AS source  -- Indique que les données viennent du collecteur
FROM homere.EX_MOIS_ACTES2
WHERE TYPEACTE IN ('CCAM', 'NGAP', 'LABO')
  AND ANNEE_ACTE = 2024  -- Pour récupérer les données depuis 2023 (ou ajustable)
ORDER BY ANNEE_ACTE DESC, MOIS_ACTE DESC;