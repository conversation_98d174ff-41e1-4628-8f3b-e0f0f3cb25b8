<?php

namespace App\Domain\Extension\CoreSupraFilter;

/**
 * Classe de base pour toutes les extensions de filtrage personnalisées.
 *
 * Fournit des méthodes utilitaires communes pour déterminer le contexte d'exécution
 * et éviter l'application des filtres dans des contextes non-API (comme EasyAdmin).
 */
abstract class BaseCustomFilterExtension
{
    /**
     * Détermine si nous sommes dans le contexte d'API Platform.
     *
     * Cette méthode permet d'éviter que les filtres s'appliquent dans EasyAdmin
     * ou d'autres contextes non-API.
     */
    protected function isApiPlatformContext(array $context): bool
    {
        return isset($context['collection_operation_name']) ||
            isset($context['item_operation_name']) ||
            array_key_exists('filters', $context);
    }
}