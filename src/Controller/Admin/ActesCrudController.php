<?php

namespace App\Controller\Admin;

use App\Domain\Enum\PeriodeType;
use App\Domain\Service\Activite\ActesDetectionProblemService;
use App\Entity\Activite\Actes;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\ArrayField;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\BooleanField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ChoiceField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateTimeField;
use EasyCorp\Bundle\EasyAdminBundle\Field\IntegerField;
use EasyCorp\Bundle\EasyAdminBundle\Field\NumberField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;

class ActesCrudController extends AbstractCrudController
{
    public function __construct(
        private readonly ActesDetectionProblemService $detectionProblemService
    ) {}
    
    public static function getEntityFqcn(): string
    {
        return Actes::class;
    }

    public function configureActions(Actions $actions): Actions
    {
        $importCsv = Action::new('importCsv', '📥 Importer CSV')
            ->linkToRoute('admin_import_actes_csv')
            ->setCssClass('btn btn-primary');

        return $actions
            ->add(Crud::PAGE_INDEX, Action::DETAIL)
            ->update(Crud::PAGE_INDEX, Action::DETAIL, function (Action $action) {
                return $action->setLabel('Voir')->setIcon('fa fa-eye');
            })
            ->add(Crud::PAGE_NEW, $importCsv);
    }

    public function configureFields(string $pageName): iterable
    {
        $fields = [];
        if ($pageName === Crud::PAGE_INDEX) {
            $fields[] = TextField::new('id')->hideOnForm();
            $fields[] = TextField::new('code')
                ->setLabel('Code Acte')
                ->setHelp('Code unique identifiant l\'acte (ex: AAAA001 pour un acte CCAM).');
            $fields[] = TextField::new('description')
                ->setLabel('Description')
                ->setHelp('Description détaillée de l\'acte médical.');
            $fields[] = ChoiceField::new('typeActe')
                ->setLabel('Type d\'acte')
                ->setHelp('Classification de l\'acte : CCAM, NGAP ou NABM.')
                ->setChoices([
                    'CCAM' => 'CCAM',
                    'NGAP' => 'NGAP',
                    'NABM' => 'LABO',
                ]);
            $fields[] = IntegerField::new('nombre_de_realisation')
                ->setLabel('Nb')
                ->setHelp('Nombre de fois que l\'acte a été réalisé.');
            $fields[] = AssociationField::new('agent')
                ->setLabel('Agent')
                ->setHelp('Praticien ayant réalisé l\'acte.');
            $fields[] = DateTimeField::new('date_realisation')
                ->setLabel('Date de Réalisation')
                ->setHelp('Date et heure exactes de réalisation de l\'acte.')
                ->setDisabled(false);
            $fields[] = AssociationField::new('ufIntervention')
                ->setLabel('UF Intervention')
                ->setHelp('Unité Fonctionnelle où l\'acte a été réalisé.');
        } else {
            $fields[] = TextField::new('id')->hideOnForm();
            $fields[] = BooleanField::new('isActif')
                ->setLabel('Est actif')
                ->setHelp('Indique si l\'acte est actif dans le système. Les actes inactifs ne sont pas pris en compte dans les statistiques.');
            $fields[] = DateTimeField::new('dateCreation')
                ->setLabel('Date de création')
                ->setHelp('Date à laquelle l\'acte a été créé dans le système. Ce champ est automatiquement rempli.')
                ->setDisabled(true);
            $fields[] = TextField::new('code')
                ->setLabel('Code Acte')
                ->setHelp('Code unique identifiant l\'acte (ex: AAAA001 pour un acte CCAM).');
            $fields[] = TextField::new('description')
                ->setLabel('Description')
                ->setHelp('Description détaillée de l\'acte médical.');
            $fields[] = ChoiceField::new('typeActe')
                ->setLabel('Type d\'acte')
                ->setHelp('Classification de l\'acte : CCAM, NGAP ou NABM (LABO).')
                ->setChoices([
                    'CCAM' => 'CCAM',
                    'NGAP' => 'NGAP',
                    'NABM' => 'LABO',
                ]);
            $fields[] = IntegerField::new('nombre_de_realisation')
                ->setLabel('Nb')
                ->setHelp('Nombre de fois que l\'acte a été réalisé.');
            $fields[] = IntegerField::new('annee')
                ->setLabel('Année')
                ->setHelp('Année de réalisation de l\'acte.');
            $fields[] = IntegerField::new('mois')
                ->setLabel('Mois')
                ->setHelp('Mois de réalisation de l\'acte (1-12).');
            $fields[] = DateTimeField::new('date_realisation')
                ->setLabel('Date de Réalisation')
                ->setHelp('Date et heure exactes de réalisation de l\'acte. Si ce champ n\'est pas renseigné, il sera automatiquement calculé à partir de l\'année et du mois fournis.')
                ->setDisabled(false);
            $fields[] = AssociationField::new('agent')
                ->setLabel('Agent')
                ->setHelp('Praticien ayant réalisé l\'acte.');
            $fields[] = TextField::new('internum')
                ->setLabel('Numéro d\'intervention')
                ->setHelp('Numéro d\'identification de l\'intervention associée à cet acte.');
            $fields[] = AssociationField::new('ufPrincipal')
                ->setLabel('UF principale')
                ->setHelp('Unité Fonctionnelle principale responsable de l\'acte.');
            $fields[] = AssociationField::new('ufDemande')
                ->setLabel('UF demande')
                ->setHelp('Unité Fonctionnelle ayant demandé l\'acte.');
            $fields[] = AssociationField::new('ufIntervention')
                ->setLabel('UF intervention')
                ->setHelp('Unité Fonctionnelle où l\'acte a été réalisé.');
            $fields[] = IntegerField::new('icrA')
                ->setLabel('ICR A')
                ->setHelp('Indice de Coût Relatif Anesthésie associé à cet acte.');
            $fields[] = NumberField::new('coefficient')
                ->setLabel('Coefficient')
                ->setHelp('Coefficient multiplicateur appliqué à la valorisation de l\'acte.');
            $fields[] = TextField::new('lettreCoef')
                ->setLabel('Lettre coef')
                ->setHelp('Lettre clé associée au coefficient (ex: K, KC, Z...).');
            $fields[] = TextField::new('regroupement')
                ->setLabel('Regroupement')
                ->setHelp('Catégorie de regroupement de l\'acte pour les analyses statistiques.');
            $fields[] = TextField::new('activite')
                ->setLabel('Activité')
                ->setHelp('Code de l\'activité associée à cet acte.');
            $fields[] = TextField::new('activiteLib')
                ->setLabel('Libellé activité')
                ->setHelp('Description de l\'activité associée à cet acte.');
            
            // Afficher le champ de détection de problème uniquement en mode détail/vue
            if ($pageName === Crud::PAGE_DETAIL) {
                // Utilisation du service pour afficher les informations de détection de problème
                $fields[] = TextField::new('id')
                    ->setLabel('Détection problème')
                    ->setHelp('Affiche les informations globales de détection de problème pour les actes.')
                    ->setTemplatePath('admin/field/detection_probleme.html.twig')
                    ->setCustomOption('detectionProblemService', $this->detectionProblemService)
                    ->renderAsHtml();
            }
        }
        return $fields;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setEntityLabelInSingular('Acte')
            ->setEntityLabelInPlural('Actes')
            ->setPageTitle('index', 'Gestion des Actes')
            ->setPageTitle('detail', 'Détail de l\'Acte')
            ->setPageTitle('new', 'Créer un nouvel Acte')
            ->setPageTitle('edit', 'Modifier l\'Acte')
            ->showEntityActionsInlined();
    }
}
