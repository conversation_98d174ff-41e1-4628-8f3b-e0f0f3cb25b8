<?php

namespace App\Mapper\Tenant;

use App\ApiResource\Structure\TenantDto;
use App\Entity\Structure\Tenant;
use App\Repository\TenantRepository;
use Doctrine\DBAL\Exception;
use Symfonycasts\MicroMapper\AsMapper;
use Symfonycasts\MicroMapper\MapperInterface;

#[AsMapper(from: TenantDto::class, to: Tenant::class)]
class TenantDtoToEntityMapper implements MapperInterface
{
    public function __construct(
        private TenantRepository $tenantRepository
    ) {}

    public function load(object $from, string $toClass, array $context): object
    {
        $dto = $from;
        assert($dto instanceof TenantDto);

        // On récupère l'entité existante si un ID est fourni, sinon on en crée une nouvelle
        $tenant = $dto->id ? $this->tenantRepository->find($dto->id) : new Tenant();
        if (!$tenant) {
            throw new Exception('Tenant non trouvé');
        }

        return $tenant;
    }

    public function populate(object $from, object $to, array $context): object
    {
        $dto = $from;
        $tenant = $to;
        assert($dto instanceof TenantDto);
        assert($tenant instanceof Tenant);

        $tenant->setNom($dto->nom);
        $tenant->setDescription($dto->description);
        $tenant->setDateCreation($dto->dateCreation);

        return $tenant;
    }
}
