<?php

namespace App\State\Partials;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use App\ApiResource\Structure\Partials\UfOrganisationDto;
use App\Repository\ServiceRepository;
use App\Repository\UfsRepository;
use Symfony\Contracts\Cache\CacheInterface;
use Symfony\Component\DependencyInjection\Attribute\Autowire;

class UfOrganisationStateProvider implements ProviderInterface
{
    public function __construct(
        private readonly UfsRepository $ufsRepository,
        private readonly ServiceRepository $serviceRepository,
        private readonly CacheInterface $cache,
        #[Autowire('%env(int:API_CACHE_TTL)%')] private readonly int $cacheTtl
    ) {}

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): object|array|null
    {
        $ufcode = $context['filters']['code'] ?? null;
        $p1Start = $context['filters']['p1Start'] ?? null;
        $p1End = $context['filters']['p1End'] ?? null;

        if (!$ufcode) {
            throw new \InvalidArgumentException('Le code UF est requis');
        }

        if (!$p1Start || !$p1End) {
            throw new \InvalidArgumentException('Les dates p1Start et p1End sont requises');
        }

        $searchStart = new \DateTime($p1Start);
        $searchEnd = new \DateTime($p1End);

        return $this->cache->get(
            "uf_organisation_{$ufcode}_{$p1Start}_{$p1End}",
            function () use ($ufcode, $searchStart, $searchEnd) {
                $uf = $this->ufsRepository->findActiveUfForPeriod(
                    $ufcode,
                    $searchStart,
                    $searchEnd
                );

                if (!$uf) {
                    throw new \Exception("UF non trouvée pour cette période");
                }

                // Trouver le service via le secode de l'UF
                $service = $this->serviceRepository->findOneBy(['secode' => $uf->getSecode()]);


                return new UfOrganisationDto(
                    ufcode: $uf->getUfcode(),
                    ufLibelle: $uf->getLibelle(),
                    poleCode: $uf->getCr()?->getPole()?->getPolecode() ?? '',
                    poleLibelle: $uf->getCr()?->getPole()?->getLibelle() ?? '',
                    crCode: $uf->getCr()?->getCrcode() ?? '',
                    crLibelle: $uf->getCr()?->getLibelle() ?? '',
                    dateDebut: $uf->getDatdeb(),
                    dateFin: $uf->getDatfin(),
                    searchStart: $searchStart,
                    searchEnd: $searchEnd
                );
            }
        );
    }
}