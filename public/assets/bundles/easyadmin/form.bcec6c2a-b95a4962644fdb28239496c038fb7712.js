(()=>{var e={872:function(e){var t;t=function(){return function(e){var t={};function n(r){if(t[r])return t[r].exports;var i=t[r]={i:r,l:!1,exports:{}};return e[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)n.d(r,i,function(t){return e[t]}.bind(null,i));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=0)}([function(e,t){function n(e){return function(e){if(Array.isArray(e)){for(var t=0,n=new Array(e.length);t<e.length;t++)n[t]=e[t];return n}}(e)||function(e){if(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e))return Array.from(e)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}function r(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var i=function(){function e(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.form=t,this.isDirty=!1,this.initialValues={},this.fields=[].concat(n(this.form.elements),n(this.form.querySelectorAll("trix-editor"))),this.message=r.message||"You have unsaved changes!",this.setupFields(),this.setFormHandlers()}var t,i,o;return t=e,(i=[{key:"setupFields",value:function(){var e=this;this.fields.forEach((function(t){t.name&&"submit"!=t.type&&"button"!=t.type&&"hidden"!=t.type&&(e.initialValues[t.name]=t.value,"TRIX-EDITOR"==t.nodeName?t.addEventListener("trix-change",e.checkValue.bind(e)):(t.addEventListener("change",e.checkValue.bind(e)),t.addEventListener("input",e.checkValue.bind(e))))}))}},{key:"setFormHandlers",value:function(){var e=this;window.addEventListener("submit",this.handleSubmit.bind(this)),this.form.addEventListener("submit",this.handleSubmit.bind(this)),window.onbeforeunload=function(){if(e.isDirty)return e.message},"undefined"!=typeof Turbolinks&&document.addEventListener("turbolinks:before-visit",(function(t){e.isDirty&&!confirm(e.message)?t.preventDefault():e.isDirty=!1}))}},{key:"checkValue",value:function(e){var t=e.target;this.initialValues[t.name]!=t.value&&(this.isDirty=!0)}},{key:"handleSubmit",value:function(){this.isDirty=!1}}])&&r(t.prototype,i),o&&r(t,o),e}();e.exports=i}])},e.exports=t()}},t={};function n(r){var i=t[r];if(void 0!==i)return i.exports;var o=t[r]={exports:{}};return e[r].call(o.exports,o,o.exports,n),o.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";var e=n(872),t=n.n(e);function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function i(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,a(r.key),r)}}function o(e,t,n){return t&&i(e.prototype,t),n&&i(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function a(e){var t=function(e,t){if("object"!=r(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!=r(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==r(t)?t:t+""}function u(e,t){(function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")})(e,t),t.add(e)}function c(e,t,n){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:n;throw new TypeError("Private element is not present on this object")}document.addEventListener("DOMContentLoaded",(function(){new l}));var s=new WeakSet,l=o((function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),u(this,s),c(s,this,f).call(this),c(s,this,d).call(this),c(s,this,v).call(this),c(s,this,b).call(this)}));function f(){var e=window.location.hash;if(e){var t=e.substring(1),n="tablist-".concat(t);c(s,this,m).call(this,n)}document.querySelectorAll('a[data-bs-toggle="tab"]').forEach((function(e){e.addEventListener("shown.bs.tab",(function(e){var t="#"+e.target.getAttribute("href").substring(1);history.pushState({},"",t)}))}))}function d(){[".ea-new-form",".ea-edit-form"].forEach((function(e){var n=document.querySelector(e);null!==n&&new(t())(n)}))}function v(){var e=this;[".ea-new-form",".ea-edit-form"].forEach((function(t){var n=document.querySelector(t);null!==n&&function(t,n){var r=e;document.querySelector(".ea-edit, .ea-new").querySelectorAll('[type="submit"]').forEach((function(e){e.addEventListener("click",(function(e){var i=!1;if(document.querySelectorAll(".form-tabs-tablist .nav-item .badge-danger.badge").forEach((function(e){e.parentElement.removeChild(e)})),null===t.getAttribute("novalidate")&&(t.querySelectorAll("input, select, textarea").forEach((function(e){if(!e.disabled&&!e.validity.valid){i=!0;var t=e.closest("div.tab-pane");if(t){var n=document.querySelector('[data-bs-target="#'.concat(t.id,'"], a[href="#').concat(t.id,'"]'));if(n){n.classList.add("has-error");var r=n.querySelector(".badge");if(r)r.textContent=(parseInt(r.textContent)+1).toString();else{var o=document.createElement("span");o.classList.add("badge","badge-danger"),o.textContent="1",n.appendChild(o)}}}var a=e.closest("div.form-group");a.classList.add("has-error"),a.addEventListener("click",(function e(){a.classList.remove("has-error"),a.removeEventListener("click",e)}))}})),i)){e.preventDefault(),e.stopPropagation();var o=document.querySelector(".form-tabs-tablist .nav-tabs .nav-item .nav-link.has-error");null!==o&&c(s,r,m).call(r,o.id),document.dispatchEvent(new CustomEvent("ea.form.error",{cancelable:!0,detail:{page:n,form:t}}))}}))})),t.addEventListener("submit",(function(e){var r=new CustomEvent("ea.form.submit",{cancelable:!0,detail:{page:n,form:t}});!1===document.dispatchEvent(r)&&(e.preventDefault(),e.stopPropagation())}))}(n,t.includes("-new-")?"new":"edit")}))}function m(e){var t=document.getElementById(e);t&&new(0,bootstrap.Tab)(t).show()}function b(){[".ea-new-form",".ea-edit-form"].forEach((function(e){var t=document.querySelector(e);null!==t&&t.addEventListener("submit",(function(){setTimeout((function(){document.querySelector(".ea-edit, .ea-new").querySelectorAll('[type="submit"]').forEach((function(e){e.setAttribute("disabled","disabled")}))}),1)}),!1)}))}})()})();