APP_ENV=dev
APP_SECRET=adfb8a4a888228853dfdc7fcab2cce5d

###> doctrine/doctrine-bundle ###
# Variables pour construire DATABASE_URL avec support Docker
DATABASE_USER=${DATABASE_USER:-postgres}
DATABASE_PASSWORD=${DATABASE_PASSWORD:-changeme}
DATABASE_HOST=${DATABASE_HOST:-localhost}
DATABASE_PORT=${DATABASE_PORT:-5432}
DATABASE_NAME=${DATABASE_NAME:-supra_db}

DATABASE_URL="postgresql://${DATABASE_USER}:${DATABASE_PASSWORD}@${DATABASE_HOST}:${DATABASE_PORT}/${DATABASE_NAME}?serverVersion=15&charset=utf8"
###< doctrine/doctrine-bundle ###

###> LDAP Configuration ###
# LDAP (Active Directory)
# Non bloquant, pour les future EJ, reperer vous sur le fichier Structure/Trait/LdapConfigTrait.php
LDAP_HOST=ldap://k12adc01.chu-nancy.fr:389
# Domaine AD
LDAP_DOMAIN=U001PRD
# Base DN pour la recherche
LDAP_BASE_DN=DC=u001prd,DC=local
###< LDAP Configuration ###


###> nelmio/cors-bundle ###
CORS_ALLOW_ORIGIN='^https?://(localhost|127\.0\.0\.1)(:[0-9]+)?$'
###< nelmio/cors-bundle ###

###> JWT Firebase (legacy) ###
# JWT_SECRET is defined in .env.dev.local
###< JWT Firebase ###


###> symfony/messenger ###
# Commented out as it's not currently used in the application
# MESSENGER_TRANSPORT_DSN=doctrine://default?auto_setup=0
###< symfony/messenger ###

###> symfony/cache ###
# URL for Redis cache connection
CACHE_DSN=redis://localhost:6379
# Cache TTL (Time To Live) in seconds for API entities
API_CACHE_TTL=3600
# 1 hour by default
###< symfony/cache ###

###> symfony/mailer ###
MAILER_DSN=sendmail://default
MAILER_FROM_EMAIL=<EMAIL>
###< symfony/mailer ###

###> SUPRA Services ###
# Base URI for the Data Integrator API
DATA_INTEGRATOR_BASE_URI=http://localhost:3000/api
###< SUPRA Services ###
