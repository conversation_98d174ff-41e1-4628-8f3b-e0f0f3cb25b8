<?php

namespace App\Domain\Enum;


final class PeriodeType
{
    public const HEBDOMADAIRE = 'hebdomadaire';
    public const MENSUEL = 'mensuel';
    public const ANNUEL = 'annuel';
    public const QUOTIDIEN = 'quotidien';

    public const VALUES = [
        self::HEBDOMADAIRE,
        self::MENSUEL,
        self::ANNUEL,
        self::QUOTIDIEN,
    ];

    public static function isValidOld(string $value): bool
    {
        return in_array($value, self::VALUES, true);
    }

    public static function isValid(string $value): bool
    {
        return in_array(self::normalize($value), self::VALUES, true);
    }

    public static function badges(): array
    {
        return [
            ucfirst(self::HEBDOMADAIRE)  => 'primary',
            ucfirst(self::MENSUEL)  => 'success',
            ucfirst(self::ANNUEL)  => 'warning',
            ucfirst(self::QUOTIDIEN)  => 'success',
        ];
    }
    public static function choices(): array
    {
        return [
            'Hebdomadaire' => self::normalize(self::HEBDOMADAIRE),
            'Mensuel' => self::normalize(self::MENSUEL),
            'Annuel' => self::normalize(self::ANNUEL),
            'Quotidien' => self::normalize(self::QUOTIDIEN),
        ];
    }

    public static function normalize(string $value): string
    {
        return strtolower(trim($value));
    }

}