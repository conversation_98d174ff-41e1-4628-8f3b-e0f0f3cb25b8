<?php

// This is a simple test script to verify that the response from ActesBatchProcessor only includes the specified fields

class MockActes {
    private $id = 1;
    private $code = 'CCAM001';
    private $description = 'Consultation cardiologique';
    private $dateRealisation;
    private $typeActe = 'CCAM';
    private $nombreDeRealisation = 1;
    private $annee = 2025;
    private $mois = 8;
    private $internum = '45904301';
    private $semaineIso = 35;
    private $typeVenue = 1;
    private $libTypeVenue = 'Consultation';
    private $activite = '5';
    private $activiteLib = 'Consultation spécialisée';
    private $icrA = 51;
    private $coefficient = 1.5;
    private $lettreCoef = 'A';
    private $validFrom;
    private $validTo;
    private $periodeType = 'hebdomadaire';
    private $source = 'CHU001';
    private $isActif = true;
    private $agent;

    public function __construct() {
        $this->dateRealisation = new DateTime('2025-08-27T14:30:00+02:00');
        $this->validFrom = $this->dateRealisation;
        $this->validTo = null;
        $this->agent = new MockAgent();
    }

    public function getId() { return $this->id; }
    public function getCode() { return $this->code; }
    public function getDescription() { return $this->description; }
    public function getDateRealisation() { return $this->dateRealisation; }
    public function getTypeActe() { return $this->typeActe; }
    public function getNombreDeRealisation() { return $this->nombreDeRealisation; }
    public function getAnnee() { return $this->annee; }
    public function getMois() { return $this->mois; }
    public function getInternum() { return $this->internum; }
    public function getSemaineIso() { return $this->semaineIso; }
    public function getTypeVenue() { return $this->typeVenue; }
    public function getLibTypeVenue() { return $this->libTypeVenue; }
    public function getActivite() { return $this->activite; }
    public function getActiviteLib() { return $this->activiteLib; }
    public function getIcrA() { return $this->icrA; }
    public function getCoefficient() { return $this->coefficient; }
    public function getLettreCoef() { return $this->lettreCoef; }
    public function getValidFrom() { return $this->validFrom; }
    public function getValidTo() { return $this->validTo; }
    public function getPeriodeType() { return $this->periodeType; }
    public function getSource() { return $this->source; }
    public function getIsActif() { return $this->isActif; }
    public function getAgent() { return $this->agent; }
}

class MockAgent {
    private $id = '1f081eda-d392-6b82-b1fd-c316a25bc400';

    public function getId() { return $this->id; }
}

function testResponseDto() {
    echo "Testing response DTO from ActesBatchProcessor\n";
    echo "============================================\n\n";

    $entity = new MockActes();

    // Original implementation using ActesDto
    echo "Original implementation (using ActesDto):\n";
    $originalDto = createOriginalResponseDto($entity);
    echo json_encode($originalDto, JSON_PRETTY_PRINT) . "\n\n";

    // New implementation using stdClass
    echo "New implementation (using stdClass):\n";
    $newDto = createNewResponseDto($entity);
    echo json_encode($newDto, JSON_PRETTY_PRINT) . "\n\n";

    // Compare the two responses
    echo "Comparison:\n";
    $originalFields = get_object_vars($originalDto);
    $newFields = get_object_vars($newDto);
    
    echo "Original response has " . count($originalFields) . " fields\n";
    echo "New response has " . count($newFields) . " fields\n\n";
    
    echo "Fields in original response but not in new response:\n";
    $missingFields = array_diff(array_keys($originalFields), array_keys($newFields));
    if (empty($missingFields)) {
        echo "None\n";
    } else {
        foreach ($missingFields as $field) {
            echo "- $field\n";
        }
    }
    
    echo "\nFields in new response but not in original response:\n";
    $extraFields = array_diff(array_keys($newFields), array_keys($originalFields));
    if (empty($extraFields)) {
        echo "None\n";
    } else {
        foreach ($extraFields as $field) {
            echo "- $field\n";
        }
    }
}

function createOriginalResponseDto($entity) {
    // Simulate the original implementation using a stdClass to represent ActesDto
    $dto = new stdClass();
    $dto->id = $entity->getId();
    $dto->code = $entity->getCode();
    $dto->description = $entity->getDescription();
    $dto->dateRealisation = $entity->getDateRealisation();
    $dto->typeActe = $entity->getTypeActe();
    $dto->nombreDeRealisation = $entity->getNombreDeRealisation();
    $dto->annee = $entity->getAnnee();
    $dto->mois = $entity->getMois();
    $dto->internum = $entity->getInternum();
    
    // These fields would be null in the original implementation
    $dto->semaineIso = null;
    $dto->typeVenue = null;
    $dto->libTypeVenue = null;
    $dto->activite = null;
    $dto->activiteLib = null;
    $dto->agent = null;
    $dto->ufPrincipal = null;
    $dto->ufDemande = null;
    $dto->ufIntervention = null;
    $dto->icrA = null;
    $dto->coefficient = null;
    $dto->lettreCoef = null;
    $dto->validFrom = null;
    $dto->validTo = null;
    $dto->periodeType = 'hebdomadaire';
    $dto->source = null;
    $dto->isActif = true;
    $dto->dateCreation = null;

    return $dto;
}

function createNewResponseDto($entity) {
    // Simulate the new implementation
    $dto = new stdClass();
    $dto->id = $entity->getId();
    $dto->code = $entity->getCode();
    $dto->description = $entity->getDescription();
    $dto->dateRealisation = $entity->getDateRealisation();
    $dto->typeActe = $entity->getTypeActe();
    $dto->nombreDeRealisation = $entity->getNombreDeRealisation();
    $dto->annee = $entity->getAnnee();
    $dto->mois = $entity->getMois();
    $dto->internum = $entity->getInternum();

    return $dto;
}

// Run the test
testResponseDto();