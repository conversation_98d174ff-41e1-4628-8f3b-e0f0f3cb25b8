<?php

namespace App\Domain\Service;

use App\Entity\Praticien\Agent;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Email;
use Symfony\Contracts\Cache\CacheInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Twig\Environment;

/**
 * Service pour l'authentification par email avec code de vérification
 *
 * Inspiré de AuthLdap mais pour l'authentification par email
 * Utilise Redis pour le cache et les domaines autorisés configurés
 */
class EmailAuthService
{
    private array $emailAuthConfig;

    public function __construct(
        private EntityManagerInterface $entityManager,
        private JwtTokenService $jwtTokenService,
        private UserManager $userManager,
        private MailerInterface $mailer,
        private CacheInterface $cache,
        private Environment $twig,
        private ?LoggerInterface $logger = null,
        #[Autowire('%kernel.environment%')] private string $environment,
        #[Autowire('%kernel.project_dir%')] private string $projectDir,
        #[Autowire('%env(MAILER_FROM_EMAIL)%')] private string $mailerFromEmail
    ) {
        // Charger la configuration des domaines autorisés
        $this->emailAuthConfig = require $this->projectDir . '/config/email_auth_domains.php';
    }

    /**
     * Initie le processus d'authentification par email
     *
     * @param string $email
     * @return array
     */
    public function initiateEmailAuth(string $email): array
    {
        try {
            // Vérifier si le domaine email est autorisé
            if (!$this->isEmailDomainAllowed($email)) {
                return [
                    'success' => false,
                    'message' => 'Domaine email non autorisé pour l\'authentification par email'
                ];
            }

            // Récupérer les paramètres depuis la configuration
            $settings = $this->emailAuthConfig['default_settings'] ?? [];
            $codeLength = $settings['code_length'] ?? 4;
            $codeTtl = $settings['code_ttl'] ?? 300;
            $maxAttempts = $settings['max_attempts'] ?? 3;

            // Générer un code avec la longueur configurée
            $min = pow(10, $codeLength - 1);
            $max = pow(10, $codeLength) - 1;
            $code = sprintf('%0' . $codeLength . 'd', random_int($min, $max));

            // Clé de cache sécurisée avec préfixe et timestamp pour éviter les collisions
            // Utilisation de underscores au lieu de deux-points pour éviter les caractères réservés
            $cacheKey = sprintf('supra_email_auth_%s_%d', md5($email), time());

            $authData = [
                'code' => $code,
                'attempts' => 0,
                'created_at' => time(),
                'email' => $email,
                'user_exists' => false,
                'max_attempts' => $maxAttempts
            ];

            // Vérifier si l'utilisateur existe déjà
            $agent = $this->entityManager->getRepository(Agent::class)
                ->findByIdentifier($email);

            if ($agent) {
                $authData['agent_id'] = $agent->getId();
                $authData['user_exists'] = true;
            } else {
                $authData['agent_id'] = null;
            }

            // Supprimer l'ancien code s'il existe (clé sans timestamp)
            // Utilisation de underscores au lieu de deux-points pour éviter les caractères réservés
            $oldCacheKey = sprintf('supra_email_auth_%s', md5($email));
            $this->cache->delete($oldCacheKey);

            // Stocker le nouveau code avec TTL
            $cacheItem = $this->cache->getItem($cacheKey);
            $cacheItem->set($authData);
            $cacheItem->expiresAfter($codeTtl);
            $this->cache->save($cacheItem);

            // Stocker aussi avec la clé simple pour la vérification
            $simpleCacheItem = $this->cache->getItem($oldCacheKey);
            $simpleCacheItem->set(['current_key' => $cacheKey]);
            $simpleCacheItem->expiresAfter($codeTtl);
            $this->cache->save($simpleCacheItem);

            // Envoyer l'email avec le code
            $this->sendVerificationEmail($email, $code);

            $this->logger?->info('Email auth code sent', [
                'email' => $email,
                'user_exists' => $authData['user_exists'],
                'code_length' => $codeLength,
                'ttl' => $codeTtl,
                'cache_key' => $cacheKey
            ]);

            return [
                'success' => true,
                'message' => 'Code de vérification envoyé par email'
            ];

        } catch (\Exception $e) {
            $this->logger?->error('Email auth initiation error', [
                'email' => $email,
                'error' => $e->getMessage()
            ]);

            // Préparer la réponse d'erreur
            $errorResponse = [
                'success' => false,
                'message' => 'Erreur lors de l\'envoi du code de vérification'
            ];
            
            // Inclure les détails de l'erreur uniquement en environnement de développement
            // NOTE: Ces détails ne doivent pas être exposés en production pour des raisons de sécurité
            if ($this->environment === 'dev') {
                $errorResponse['details'] = $e->getMessage();
            }
            
            return $errorResponse;
        }
    }

    /**
     * Vérifie le code de vérification et génère un token JWT
     * Inspiré de AuthLdap::onAuthenticationSuccess
     *
     * @param string $email
     * @param string $code
     * @return array
     */
    public function verifyEmailCode(string $email, string $code): array
    {
        try {
            // Récupérer la clé actuelle via la clé simple
            // Utilisation de underscores au lieu de deux-points pour éviter les caractères réservés
            $simpleCacheKey = sprintf('supra_email_auth_%s', md5($email));
            $simpleCacheItem = $this->cache->getItem($simpleCacheKey);

            if (!$simpleCacheItem->isHit()) {
                return [
                    'success' => false,
                    'message' => 'Code expiré ou invalide'
                ];
            }

            $cacheInfo = $simpleCacheItem->get();
            $actualCacheKey = $cacheInfo['current_key'] ?? null;

            if (!$actualCacheKey) {
                return [
                    'success' => false,
                    'message' => 'Code expiré ou invalide'
                ];
            }

            // Récupérer les données du code
            $cachedData = $this->cache->getItem($actualCacheKey);

            if (!$cachedData->isHit()) {
                return [
                    'success' => false,
                    'message' => 'Code expiré ou invalide'
                ];
            }

            $authData = $cachedData->get();

            // Récupérer le max_attempts depuis les données stockées ou la config
            $maxAttempts = $authData['max_attempts'] ?? $this->emailAuthConfig['default_settings']['max_attempts'] ?? 3;

            // Vérifier le nombre de tentatives
            if ($authData['attempts'] >= $maxAttempts) {
                $this->cache->delete($simpleCacheKey);
                $this->cache->delete($actualCacheKey);
                return [
                    'success' => false,
                    'message' => 'Trop de tentatives. Veuillez redemander un code.'
                ];
            }

            // Vérifier le code
            if ($authData['code'] !== $code) {
                $authData['attempts']++;
                $cachedData->set($authData);
                $this->cache->save($cachedData);

                return [
                    'success' => false,
                    'message' => 'Code incorrect',
                    'attempts_remaining' => $maxAttempts - $authData['attempts']
                ];
            }

            // Code valide, supprimer du cache (KISS - nettoyage simple)
            $this->cache->delete($simpleCacheKey);
            $this->cache->delete($actualCacheKey);

            // Gérer les utilisateurs existants et nouveaux
            if ($authData['user_exists'] && $authData['agent_id']) {
                // Utilisateur existant - récupérer depuis la BDD
                $agent = $this->entityManager->getRepository(Agent::class)
                    ->find($authData['agent_id']);

                if (!$agent) {
                    return [
                        'success' => false,
                        'message' => 'Utilisateur existant non trouvé'
                    ];
                }

                // Mettre à jour les stats de connexion pour utilisateur existant
                $this->userManager->updateConnectionStats($agent);
            } else {
                // Nouvel utilisateur - authentification simple basée sur email + domaine autorisé
                // Créer un Agent fantôme avec toutes les données nécessaires pour le token
                $agent = new Agent();
                $agent->setEmail($email);
                $agent->setRoles(['ROLE_USER', 'ROLE_EMAIL_AUTH']);

                // Compléter les données fantômes pour que le token ressemble à celui d'AuthLdap
                $agent->setHrUser($email); // Utiliser l'email comme identifiant
                $agent->setMatricule('EMAIL_' . strtoupper(substr(md5($email), 0, 8))); // Matricule virtuel

                // Extraire nom/prénom de l'email si possible (ex: <EMAIL>)
                $emailParts = explode('@', $email);
                $localPart = $emailParts[0];
                $nameParts = explode('.', $localPart);

                if (count($nameParts) >= 2) {
                    $agent->setPrenom(ucfirst($nameParts[0]));
                    $agent->setNom(ucfirst($nameParts[1]));
                } else {
                    $agent->setPrenom('Utilisateur');
                    $agent->setNom('Email');
                }

                // Définir un ID virtuel pour éviter les erreurs
                $agent->setId(9999999); // ID fantôme

                $this->logger?->info('Authentification email réussie pour nouvel utilisateur', [
                    'email' => $email,
                    'matricule' => $agent->getMatricule(),
                    'nom_complet' => $agent->getPrenom() . ' ' . $agent->getNom()
                ]);
            }

            // Trouver le code EJ correspondant à l'email
            $code_ej = $this->findEjCodeFromEmail($email);

            // Générer le token JWT
            $jwtToken = $this->jwtTokenService->generate($agent, $code_ej);

            $this->logger?->info('Email auth successful', [
                'email' => $email,
                'agent_id' => $agent->getId(),
                'hopital_code' => $code_ej,
                'was_new_user' => !$authData['user_exists']
            ]);

            // Préparer la réponse (même format que AuthLdap)
            $response = [
                'success' => true,
                'message' => 'Authentification par email réussie',
                'token' => $jwtToken
            ];

            // En développement seulement : inclure les données utilisateur (comme AuthLdap)
            if ($this->environment === 'dev') {
                $response['user'] = [
                    'matricule' => $agent->getMatricule(),
                    'hrUser' => $agent->getHrUser(),
                    'email' => $agent->getEmail(),
                    'nom' => $agent->getNom(),
                    'prenom' => $agent->getPrenom(),
                    'id' => $agent->getId(),
                    'hopital_code' => $code_ej,
                    'was_new_user' => !$authData['user_exists']
                ];
            }

            return $response;

        } catch (\Exception $e) {
            $this->logger?->error('Email auth verification error', [
                'email' => $email,
                'error' => $e->getMessage()
            ]);

            // Préparer la réponse d'erreur
            $errorResponse = [
                'success' => false,
                'message' => 'Erreur lors de la vérification du code'
            ];
            
            // Inclure les détails de l'erreur uniquement en environnement de développement
            // NOTE: Ces détails ne doivent pas être exposés en production pour des raisons de sécurité
            if ($this->environment === 'dev') {
                $errorResponse['details'] = $e->getMessage();
            }
            
            return $errorResponse;
        }
    }

    /**
     * Vérifie si le domaine de l'email est autorisé pour l'authentification
     */
    private function isEmailDomainAllowed(string $email): bool
    {
        $domain = substr(strrchr($email, "@"), 1);

        if (empty($domain)) {
            return false;
        }

        $allowedDomains = $this->emailAuthConfig['allowed_domains'] ?? [];

        return in_array($domain, $allowedDomains, true);
    }
    
    /**
     * Trouve le code de l'EntiteJuridique correspondant à l'email de l'utilisateur
     * 
     * Cette fonction ultra rapide extrait le domaine de l'email et cherche l'EntiteJuridique
     * dont la configuration LDAP host correspond au domaine.
     * 
     * @param string $email L'email de l'utilisateur
     * @return string Le code de l'EntiteJuridique ou 'EMAIL_AUTH' par défaut
     */
    private function findEjCodeFromEmail(string $email): string
    {
        // Extraire le domaine de l'email (ex: "chu-nancy.fr" de "<EMAIL>")
        $domain = substr(strrchr($email, "@"), 1);
        
        if (empty($domain)) {
            return 'EMAIL_AUTH'; // Valeur par défaut si le domaine ne peut pas être extrait
        }
        
        try {
            // Optimisation: utiliser une requête DQL directe pour filtrer les entités
            // Cette approche est beaucoup plus rapide que de récupérer toutes les entités
            $entiteJuridiqueRepository = $this->entityManager->getRepository('App\\Entity\\Structure\\EntiteJuridique');
            
            // Créer une requête personnalisée pour trouver les EJ avec un ldapConfig contenant le domaine
            $queryBuilder = $entiteJuridiqueRepository->createQueryBuilder('ej');
            
            // Extraire les parties du domaine pour la recherche
            $domainParts = explode('.', $domain);
            $mainPart = $domainParts[0]; // Ex: "chu-nancy" de "chu-nancy.fr"
            
            // Rechercher les EJ dont le base_dn contient la partie principale du domaine
            // Utiliser JSON_EXTRACT pour accéder aux données JSON (ldapConfig)
            $query = $queryBuilder
                ->where('ej.ldapConfig IS NOT NULL')
                ->orderBy('ej.id', 'ASC')
                ->getQuery();
            
            $entitesJuridiques = $query->getResult();
            
            // Parcourir les résultats pour trouver une correspondance
            foreach ($entitesJuridiques as $ej) {
                // Récupérer uniquement le host LDAP pour la correspondance
                $ldapHost = $ej->getLdapHost();
                
                // Journaliser les informations LDAP pour le débogage
                $this->logger?->debug('Vérification de correspondance avec LDAP host', [
                    'email_domain' => $domain,
                    'ldap_host' => $ldapHost
                ]);
                
                // Extraire le nom d'hôte du LDAP host (ex: "selve.chu-nancy.fr" de "ldap://selve.chu-nancy.fr:389")
                $hostName = '';
                if (preg_match('/ldap:\/\/([^:]+)/', $ldapHost, $hostMatches)) {
                    $hostName = $hostMatches[1];
                    $this->logger?->debug('Nom d\'hôte LDAP extrait', ['host_name' => $hostName]);
                }
                
                // Vérifier si le domaine correspond au nom d'hôte LDAP
                if (!empty($hostName)) {
                    $hostParts = explode('.', $hostName);
                    // Ignorer le premier segment qui est souvent le nom du serveur (ex: "selve")
                    if (count($hostParts) > 2) {
                        array_shift($hostParts);
                        $hostDomain = implode('.', $hostParts);
                        
                        // Vérifier si le domaine de l'email correspond au domaine de l'hôte LDAP
                        // avec gestion spéciale pour "chru" vs "chu"
                        $hostMatch = $this->checkDomainMatch($domain, $hostDomain);
                        
                        if ($hostMatch) {
                            $this->logger?->info('Correspondance EJ trouvée via host LDAP', [
                                'email_domain' => $domain,
                                'host_domain' => $hostDomain,
                                'ej_code' => $ej->getCode()
                            ]);
                            
                            return $ej->getCode(); // Retourner le code de l'EJ correspondant
                        }
                    }
                }
            }
            
            // Si aucune correspondance n'est trouvée, journaliser et retourner le code par défaut
            $this->logger?->info('Aucune correspondance EJ trouvée pour le domaine email', [
                'email_domain' => $domain,
                'default_code' => 'EMAIL_AUTH'
            ]);
            
            return 'EMAIL_AUTH';
            
        } catch (\Exception $e) {
            $this->logger?->error('Erreur lors de la recherche du code EJ', [
                'email' => $email,
                'error' => $e->getMessage()
            ]);
            
            // En cas d'erreur, retourner le code par défaut
            return 'EMAIL_AUTH';
        }
    }
    
    /**
     * Vérifie si deux domaines correspondent, avec gestion spéciale pour "chru" vs "chu"
     * 
     * @param string $emailDomain Le domaine de l'email (ex: "chru-nancy.fr")
     * @param string $configDomain Le domaine de la configuration LDAP (ex: "chu-nancy.fr")
     * @return bool True si les domaines correspondent, false sinon
     */
    private function checkDomainMatch(string $emailDomain, string $configDomain): bool
    {
        // Normaliser les domaines en minuscules
        $emailDomain = strtolower($emailDomain);
        $configDomain = strtolower($configDomain);
        
        // Cas 1: Correspondance exacte
        if ($emailDomain === $configDomain) {
            return true;
        }
        
        // Cas 2: Gestion spéciale pour "chru" vs "chu"
        // Remplacer "chru" par "chu" dans les deux domaines et comparer
        $normalizedEmailDomain = str_replace('chru', 'chu', $emailDomain);
        $normalizedConfigDomain = str_replace('chru', 'chu', $configDomain);
        
        if ($normalizedEmailDomain === $normalizedConfigDomain) {
            $this->logger?->debug('Correspondance trouvée après normalisation chru/chu', [
                'email_domain' => $emailDomain,
                'config_domain' => $configDomain,
                'normalized_email' => $normalizedEmailDomain,
                'normalized_config' => $normalizedConfigDomain
            ]);
            return true;
        }
        
        // Cas 3: Vérifier si le domaine de configuration est un sous-domaine du domaine email
        // ou vice versa (ex: "chu-nancy.fr" et "dept.chu-nancy.fr")
        if (strpos($emailDomain, $configDomain) !== false || strpos($configDomain, $emailDomain) !== false) {
            $this->logger?->debug('Correspondance trouvée par sous-domaine', [
                'email_domain' => $emailDomain,
                'config_domain' => $configDomain
            ]);
            return true;
        }
        
        // Cas 4: Vérifier les parties du domaine individuellement
        $emailParts = explode('.', $emailDomain);
        $configParts = explode('.', $configDomain);
        
        // Vérifier si les parties principales correspondent (ignorer les sous-domaines)
        // Ex: pour "dept.chu-nancy.fr" et "chu-nancy.fr", comparer "chu-nancy" et "fr"
        $emailMainParts = array_slice($emailParts, max(0, count($emailParts) - 2));
        $configMainParts = array_slice($configParts, max(0, count($configParts) - 2));
        
        if (implode('.', $emailMainParts) === implode('.', $configMainParts)) {
            $this->logger?->debug('Correspondance trouvée par parties principales du domaine', [
                'email_domain' => $emailDomain,
                'config_domain' => $configDomain,
                'email_main_parts' => implode('.', $emailMainParts),
                'config_main_parts' => implode('.', $configMainParts)
            ]);
            return true;
        }
        
        // Cas 5: Vérifier spécifiquement les variations "chru" vs "chu" dans les parties du domaine
        foreach ($emailParts as $emailPart) {
            foreach ($configParts as $configPart) {
                // Normaliser les parties pour gérer "chru" vs "chu"
                $normalizedEmailPart = str_replace('chru', 'chu', $emailPart);
                $normalizedConfigPart = str_replace('chru', 'chu', $configPart);
                
                if ($normalizedEmailPart === $normalizedConfigPart && 
                    (strpos($normalizedEmailPart, 'chu') !== false || strpos($normalizedConfigPart, 'chu') !== false)) {
                    $this->logger?->debug('Correspondance trouvée par partie de domaine avec variation chru/chu', [
                        'email_part' => $emailPart,
                        'config_part' => $configPart,
                        'normalized_email_part' => $normalizedEmailPart,
                        'normalized_config_part' => $normalizedConfigPart
                    ]);
                    return true;
                }
            }
        }
        
        return false;
    }

    /**
     * Envoie l'email de vérification avec le code
     */
    private function sendVerificationEmail(string $email, string $code): void
    {
        try {
            $subject = 'Code de vérification SUPRA';
            
            // Extraire le nom de l'email si possible (ex: <EMAIL> -> balde)
            $emailParts = explode('@', $email);
            $localPart = $emailParts[0];
            $nameParts = explode('.', $localPart);
            
            // Selon l'issue, pour <EMAIL>, on veut utiliser "balde" comme nom
            // et non pas "i" comme prénom
            if (count($nameParts) >= 2) {
                // Utiliser la partie après le premier point comme nom
                $prenom = ucfirst($nameParts[1]);
            } else if (count($nameParts) >= 1) {
                // S'il n'y a pas de point, utiliser la partie locale complète
                $prenom = ucfirst($nameParts[0]);
            } else {
                $prenom = 'Utilisateur';
            }
            
            $nom = '';
            $hopital_nom = 'by chru-nancy';

            $htmlContent = $this->twig->render('emails/auth/verification_code.html.twig', [
                'code' => $code,
                'email' => $email,
                'prenom' => $prenom,
                'nom' => $nom,
                'hopital_nom' => $hopital_nom
            ]);

            $emailMessage = (new Email())
                ->from($this->mailerFromEmail)
                ->to($email)
                ->subject($subject)
                ->html($htmlContent);

            $this->mailer->send($emailMessage);

            $this->logger?->info('Email de vérification envoyé avec succès', [
                'email' => $email
            ]);

        } catch (\Exception $e) {
            $this->logger?->error('Erreur lors de l\'envoi d\'email de vérification', [
                'email' => $email,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
}
