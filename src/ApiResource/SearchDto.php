<?php

namespace App\ApiResource;

use ApiPlatform\Metadata\ApiProperty;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\GetCollection;
use App\State\EntitiesForSearchToDtoStateProvider;

#[ApiResource(
    shortName: 'Search',
    operations: [
        new GetCollection(
            uriTemplate: '/search',
            description: 'Récupère toutes les entités pour la recherche côté frontend avec Fuse.js'
        )
    ],
    paginationEnabled: false, // Pas de pagination, on récupère tout pour Fuse.js
    provider: EntitiesForSearchToDtoStateProvider::class,
)]
class SearchDto
{
    #[ApiProperty(readable: true, writable: false, identifier: true)]
    public ?string $id = null;

    #[ApiProperty(description: "Type d'entité (praticien, pole, service, ufs)")]
    public ?string $type = null;

    #[ApiProperty(description: "Nom de l'entité")]
    public ?string $nom = null;

    #[ApiProperty(description: "Prénom (pour les praticiens uniquement)")]
    public ?string $prenom = null;

    #[ApiProperty(description: "Spécialité (pour les praticiens uniquement)")]
    public ?string $specialite = null;

    #[ApiProperty(description: "Matricule (pour les praticiens uniquement)")]
    public ?string $matricule = null;

    #[ApiProperty(description: "Informations supplémentaires")]
    public ?string $extra = null;

    #[ApiProperty(description: "Nom complet pour la recherche")]
    public ?string $fullName = null;

    #[ApiProperty(description: "Mots-clés pour la recherche")]
    public ?array $keywords = [];
}
