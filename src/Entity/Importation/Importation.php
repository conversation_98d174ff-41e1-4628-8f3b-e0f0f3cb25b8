<?php

namespace App\Entity\Importation;

use App\Entity\Activite\Trait\DetectImportProblemTrait;
use App\Entity\Base\BaseEntity;
use App\Entity\Structure\EntiteJuridique;
use App\Repository\ImportationRepository;
use Doctrine\ORM\Mapping as ORM;

/**
 * Entité pour stocker les résultats des importations.
 * 
 * Cette entité garde une trace des importations effectuées (Agent, Structure, Actes)
 * avec leur statut (succès ou échec) et la date d'importation.
 * Elle est liée à une entité juridique.
 */
#[ORM\Entity(repositoryClass: ImportationRepository::class)]
class Importation extends BaseEntity
{
    use DetectImportProblemTrait;
    /**
     * Date de l'importation
     */
    #[ORM\Column(type: 'datetime')]
    private ?\DateTimeInterface $dateImportation = null;

    /**
     * Nom de la ressource importée (Agent, Structure, Actes)
     */
    #[ORM\Column(length: 50)]
    private ?string $nomRessource = null;

    /**
     * Indique si l'importation s'est bien passée
     */
    #[ORM\Column(type: 'boolean')]
    private ?bool $estReussie = null;

    /**
     * Entité juridique associée à cette importation (peut être null)
     */
    #[ORM\ManyToOne(targetEntity: EntiteJuridique::class)]
    #[ORM\JoinColumn(nullable: true)]
    private ?EntiteJuridique $entiteJuridique = null;

    public function __construct()
    {
        parent::__construct();
        $this->dateImportation = new \DateTime('now');
    }

    public function getDateImportation(): ?\DateTimeInterface
    {
        return $this->dateImportation;
    }

    public function setDateImportation(\DateTimeInterface $dateImportation): static
    {
        $this->dateImportation = $dateImportation;

        return $this;
    }

    public function getNomRessource(): ?string
    {
        return $this->nomRessource;
    }

    public function setNomRessource(string $nomRessource): static
    {
        $this->nomRessource = $nomRessource;

        return $this;
    }

    public function getEstReussie(): ?bool
    {
        return $this->estReussie;
    }

    public function setEstReussie(bool $estReussie): static
    {
        $this->estReussie = $estReussie;

        return $this;
    }

    public function getEntiteJuridique(): ?EntiteJuridique
    {
        return $this->entiteJuridique;
    }

    public function setEntiteJuridique(?EntiteJuridique $entiteJuridique): static
    {
        $this->entiteJuridique = $entiteJuridique;

        return $this;
    }
    /**
     * Formate les informations de détection de problème pour l'affichage
     * 
     * @return string Les informations formatées en HTML
     */
    public function getDetectProblemeDisplay(): string
    {
        $detectProbleme = $this->getDetectProbleme();
        
        if (empty($detectProbleme)) {
            return 'Aucune information de détection de problème.';
        }
        
        $output = '';
        if (isset($detectProbleme['lastRefresh'])) {
            $output .= "<strong>Date du dernier chargement :</strong> " . $detectProbleme['lastRefresh'] . "<br>";
        }
        if (isset($detectProbleme['total'])) {
            $output .= "<strong>Total des interventions :</strong> " . $detectProbleme['total'] . "<br>";
        }
        if (isset($detectProbleme['enrichies'])) {
            $output .= "<strong>Interventions enrichies :</strong> " . $detectProbleme['enrichies'] . "<br>";
        }
        if (isset($detectProbleme['nonEnrichies'])) {
            $output .= "<strong>Interventions non enrichies :</strong> " . $detectProbleme['nonEnrichies'] . "<br>";
        }
        if (!empty($detectProbleme['logs'])) {
            $output .= "<strong>Logs :</strong><ul>";
            foreach ($detectProbleme['logs'] as $log) {
                $output .= "<li>" . htmlspecialchars($log) . "</li>";
            }
            $output .= "</ul>";
        }
        return $output;
    }
    
    /**
     * Vérifie si un internum fait partie des interventions non enrichies
     * 
     * @param string $internum Le numéro d'intervention à vérifier
     * @return bool True si l'internum fait partie des interventions non enrichies, false sinon
     */
    public function isInterventionNonEnrichie(string $internum): bool
    {
        $detectProbleme = $this->getDetectProbleme();
        if (!$detectProbleme || empty($detectProbleme['interventionsNonEnrichies'])) {
            return false;
        }
        
        return in_array($internum, $detectProbleme['interventionsNonEnrichies']);
    }
}