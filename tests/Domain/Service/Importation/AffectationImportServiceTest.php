<?php

namespace App\Tests\Domain\Service\Importation;

use App\Controller\Importation\Agent\Dto\AffectationAgentUfDto;
use App\Domain\Converter\DataConverter;
use App\Domain\Service\Core\AgentFinderService;
use App\Domain\Service\Core\UfsFinderService;
use App\Domain\Service\Importation\AffectationImportService;
use App\Domain\Service\Notification\ErrorNotificationEmailService;
use App\Entity\Praticien\Agent;
use App\Entity\Praticien\AgentUfs;
use App\Entity\Structure\EntiteJuridique;
use App\Entity\Structure\Ufs;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Doctrine\ORM\QueryBuilder;
use Doctrine\ORM\Query;

it('can be instantiated', function () {
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $ufsFinderService = $this->createMock(UfsFinderService::class);
    $agentFinderService = $this->createMock(AgentFinderService::class);
    $errorNotificationService = $this->createMock(ErrorNotificationEmailService::class);
    
    $service = new AffectationImportService($entityManager, $ufsFinderService, $agentFinderService, $errorNotificationService);
    
    expect($service)->toBeInstanceOf(AffectationImportService::class);
});

it('imports affectations successfully', function () {
    // Mock dependencies
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $ufsFinderService = $this->createMock(UfsFinderService::class);
    $agentFinderService = $this->createMock(AgentFinderService::class);
    $errorNotificationService = $this->createMock(ErrorNotificationEmailService::class);
    $entiteJuridiqueRepository = $this->createMock(EntityRepository::class);
    $agentUfsRepository = $this->createMock(EntityRepository::class);
    
    $entityManager->method('getRepository')
        ->willReturnCallback(function ($entityClass) use ($entiteJuridiqueRepository, $agentUfsRepository) {
            if ($entityClass === EntiteJuridique::class) {
                return $entiteJuridiqueRepository;
            }
            if ($entityClass === AgentUfs::class) {
                return $agentUfsRepository;
            }
            return null;
        });
    
    // Mock EntiteJuridique
    $entiteJuridique = $this->createMock(EntiteJuridique::class);
    $entiteJuridiqueRepository->method('findOneBy')
        ->willReturn($entiteJuridique);
    
    // Mock notification settings
    $entiteJuridique->method('isNotifierAdminDesErreurProduite')
        ->willReturn(false);
    $entiteJuridique->method('isNotifierErreurAuAdminPourAffectation')
        ->willReturn(false);
    $entiteJuridique->method('isNotifierErreurAuResponssableDamAffectation')
        ->willReturn(false);
    
    // Mock Agent
    $agent = $this->createMock(Agent::class);
    $agentFinderService->method('findAgentByConnexionAndEj')
        ->willReturn($agent);
    
    // Mock UF
    $ufs = $this->createMock(Ufs::class);
    $ufsFinderService->method('findUfByCodeAndEj')
        ->willReturn($ufs);
    
    // Mock AgentUfs query builder
    $queryBuilder = $this->createMock(QueryBuilder::class);
    $query = $this->createMock(Query::class);
    
    $agentUfsRepository->method('createQueryBuilder')
        ->willReturn($queryBuilder);
    
    $queryBuilder->method('where')->willReturnSelf();
    $queryBuilder->method('andWhere')->willReturnSelf();
    $queryBuilder->method('setParameter')->willReturnSelf();
    $queryBuilder->method('getQuery')->willReturn($query);
    
    // Return null to simulate new AgentUfs
    $query->method('getOneOrNullResult')
        ->willReturn(null);
    
    // Expect persist to be called for a new AgentUfs
    $entityManager->expects($this->once())
        ->method('persist')
        ->with($this->isInstanceOf(AgentUfs::class));
    
    // Expect flush to be called at the end
    $entityManager->expects($this->once())
        ->method('flush');
    
    $service = new AffectationImportService($entityManager, $ufsFinderService, $agentFinderService, $errorNotificationService);
    
    // Create test DTOs
    $dto = new AffectationAgentUfDto();
    $dto->uConnexion = 'U123456';
    $dto->matricule = 'MAT123';
    $dto->dateDebut = '01/01/2023';
    $dto->dateFin = '31/12/2025';
    $dto->codeUf = 'UF001';
    $dto->typeGrade = 'PH';
    $dto->libelleGrade = 'Praticien Hospitalier';
    $dto->etpStatutaire = '1.0';
    $dto->tauxAffectation = '100';
    $dto->sommeEtp = '1.0';
    $dto->affectationPrincipale = 'O';
    $dto->absences = '0';
    $dto->rgt = 'RGT001';
    
    $dtos = [$dto];
    
    $result = $service->importAffectations($dtos, 'EJ001');
    
    expect($result)->toBeArray()
        ->and($result)->toHaveKey('processed')
        ->and($result)->toHaveKey('created')
        ->and($result)->toHaveKey('updated')
        ->and($result)->toHaveKey('errors')
        ->and($result['processed'])->toBe(1)
        ->and($result['created'])->toBe(1)
        ->and($result['updated'])->toBe(0)
        ->and($result['errors'])->toBeEmpty();
});

it('updates existing affectation', function () {
    // Mock dependencies
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $ufsFinderService = $this->createMock(UfsFinderService::class);
    $agentFinderService = $this->createMock(AgentFinderService::class);
    $errorNotificationService = $this->createMock(ErrorNotificationEmailService::class);
    $entiteJuridiqueRepository = $this->createMock(EntityRepository::class);
    $agentUfsRepository = $this->createMock(EntityRepository::class);
    
    $entityManager->method('getRepository')
        ->willReturnCallback(function ($entityClass) use ($entiteJuridiqueRepository, $agentUfsRepository) {
            if ($entityClass === EntiteJuridique::class) {
                return $entiteJuridiqueRepository;
            }
            if ($entityClass === AgentUfs::class) {
                return $agentUfsRepository;
            }
            return null;
        });
    
    // Mock EntiteJuridique
    $entiteJuridique = $this->createMock(EntiteJuridique::class);
    $entiteJuridiqueRepository->method('findOneBy')
        ->willReturn($entiteJuridique);
    
    // Mock notification settings
    $entiteJuridique->method('isNotifierAdminDesErreurProduite')
        ->willReturn(false);
    $entiteJuridique->method('isNotifierErreurAuAdminPourAffectation')
        ->willReturn(false);
    $entiteJuridique->method('isNotifierErreurAuResponssableDamAffectation')
        ->willReturn(false);
    
    // Mock Agent
    $agent = $this->createMock(Agent::class);
    $agentFinderService->method('findAgentByConnexionAndEj')
        ->willReturn($agent);
    
    // Mock UF
    $ufs = $this->createMock(Ufs::class);
    $ufsFinderService->method('findUfByCodeAndEj')
        ->willReturn($ufs);
    
    // Mock AgentUfs query builder
    $queryBuilder = $this->createMock(QueryBuilder::class);
    $query = $this->createMock(Query::class);
    
    $agentUfsRepository->method('createQueryBuilder')
        ->willReturn($queryBuilder);
    
    $queryBuilder->method('where')->willReturnSelf();
    $queryBuilder->method('andWhere')->willReturnSelf();
    $queryBuilder->method('setParameter')->willReturnSelf();
    $queryBuilder->method('getQuery')->willReturn($query);
    
    // Return existing AgentUfs to simulate update
    $existingAgentUfs = $this->createMock(AgentUfs::class);
    $existingAgentUfs->method('getId')->willReturn('1'); // Mock getId to return a non-null string value
    $query->method('getOneOrNullResult')
        ->willReturn($existingAgentUfs);
    
    // Expect persist NOT to be called for an existing AgentUfs
    $entityManager->expects($this->never())
        ->method('persist');
    
    // Expect flush to be called at the end
    $entityManager->expects($this->once())
        ->method('flush');
    
    $service = new AffectationImportService($entityManager, $ufsFinderService, $agentFinderService, $errorNotificationService);
    
    // Create test DTOs
    $dto = new AffectationAgentUfDto();
    $dto->uConnexion = 'U123456';
    $dto->matricule = 'MAT123';
    $dto->dateDebut = '01/01/2023';
    $dto->dateFin = '31/12/2025';
    $dto->codeUf = 'UF001';
    $dto->typeGrade = 'PH';
    $dto->libelleGrade = 'Praticien Hospitalier';
    $dto->etpStatutaire = '1.0';
    $dto->tauxAffectation = '100';
    $dto->sommeEtp = '1.0';
    $dto->affectationPrincipale = 'O';
    $dto->absences = '0';
    $dto->rgt = 'RGT001';
    
    $dtos = [$dto];
    
    $result = $service->importAffectations($dtos, 'EJ001');
    
    expect($result)->toBeArray()
        ->and($result)->toHaveKey('processed')
        ->and($result)->toHaveKey('created')
        ->and($result)->toHaveKey('updated')
        ->and($result)->toHaveKey('errors')
        ->and($result['processed'])->toBe(1)
        ->and($result['created'])->toBe(0)
        ->and($result['updated'])->toBe(1)
        ->and($result['errors'])->toBeEmpty();
});

it('handles missing EntiteJuridique', function () {
    // Mock dependencies
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $ufsFinderService = $this->createMock(UfsFinderService::class);
    $agentFinderService = $this->createMock(AgentFinderService::class);
    $errorNotificationService = $this->createMock(ErrorNotificationEmailService::class);
    $entiteJuridiqueRepository = $this->createMock(EntityRepository::class);
    
    $entityManager->method('getRepository')
        ->willReturnCallback(function ($entityClass) use ($entiteJuridiqueRepository) {
            if ($entityClass === EntiteJuridique::class) {
                return $entiteJuridiqueRepository;
            }
            return null;
        });
    
    // Mock EntiteJuridique - return null to simulate missing entity
    $entiteJuridiqueRepository->method('findOneBy')
        ->willReturn(null);
    
    $service = new AffectationImportService($entityManager, $ufsFinderService, $agentFinderService, $errorNotificationService);
    
    // Create test DTOs
    $dto = new AffectationAgentUfDto();
    $dto->uConnexion = 'U123456';
    
    $dtos = [$dto];
    
    expect(fn() => $service->importAffectations($dtos, 'EJ001'))
        ->toThrow(\Exception::class, 'Entité juridique non trouvée : EJ001');
});

it('handles missing Agent', function () {
    // Mock dependencies
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $ufsFinderService = $this->createMock(UfsFinderService::class);
    $agentFinderService = $this->createMock(AgentFinderService::class);
    $errorNotificationService = $this->createMock(ErrorNotificationEmailService::class);
    $entiteJuridiqueRepository = $this->createMock(EntityRepository::class);
    
    $entityManager->method('getRepository')
        ->willReturnCallback(function ($entityClass) use ($entiteJuridiqueRepository) {
            if ($entityClass === EntiteJuridique::class) {
                return $entiteJuridiqueRepository;
            }
            return null;
        });
    
    // Mock EntiteJuridique
    $entiteJuridique = $this->createMock(EntiteJuridique::class);
    $entiteJuridiqueRepository->method('findOneBy')
        ->willReturn($entiteJuridique);
    
    // Mock notification settings
    $entiteJuridique->method('isNotifierAdminDesErreurProduite')
        ->willReturn(false);
    $entiteJuridique->method('isNotifierErreurAuAdminPourAffectation')
        ->willReturn(false);
    $entiteJuridique->method('isNotifierErreurAuResponssableDamAffectation')
        ->willReturn(false);
    
    // Mock Agent - return null to simulate missing agent
    $agentFinderService->method('findAgentByConnexionAndEj')
        ->willReturn(null);
    
    $service = new AffectationImportService($entityManager, $ufsFinderService, $agentFinderService, $errorNotificationService);
    
    // Create test DTOs
    $dto = new AffectationAgentUfDto();
    $dto->uConnexion = 'U123456';
    
    $dtos = [$dto];
    
    $result = $service->importAffectations($dtos, 'EJ001');
    
    expect($result)->toBeArray()
        ->and($result)->toHaveKey('processed')
        ->and($result)->toHaveKey('created')
        ->and($result)->toHaveKey('updated')
        ->and($result)->toHaveKey('errors')
        ->and($result['processed'])->toBe(0)
        ->and($result['created'])->toBe(0)
        ->and($result['updated'])->toBe(0)
        ->and($result['errors'])->toHaveCount(1)
        ->and($result['errors'][0])->toHaveKey('uConnexion')
        ->and($result['errors'][0])->toHaveKey('error')
        ->and($result['errors'][0]['uConnexion'])->toBe('U123456');
});

it('handles missing UF', function () {
    // Mock dependencies
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $ufsFinderService = $this->createMock(UfsFinderService::class);
    $agentFinderService = $this->createMock(AgentFinderService::class);
    $errorNotificationService = $this->createMock(ErrorNotificationEmailService::class);
    $entiteJuridiqueRepository = $this->createMock(EntityRepository::class);
    
    $entityManager->method('getRepository')
        ->willReturnCallback(function ($entityClass) use ($entiteJuridiqueRepository) {
            if ($entityClass === EntiteJuridique::class) {
                return $entiteJuridiqueRepository;
            }
            return null;
        });
    
    // Mock EntiteJuridique
    $entiteJuridique = $this->createMock(EntiteJuridique::class);
    $entiteJuridiqueRepository->method('findOneBy')
        ->willReturn($entiteJuridique);
    
    // Mock notification settings
    $entiteJuridique->method('isNotifierAdminDesErreurProduite')
        ->willReturn(false);
    $entiteJuridique->method('isNotifierErreurAuAdminPourAffectation')
        ->willReturn(false);
    $entiteJuridique->method('isNotifierErreurAuResponssableDamAffectation')
        ->willReturn(false);
    
    // Mock Agent
    $agent = $this->createMock(Agent::class);
    $agentFinderService->method('findAgentByConnexionAndEj')
        ->willReturn($agent);
    
    // Mock UF - return null to simulate missing UF
    $ufsFinderService->method('findUfByCodeAndEj')
        ->willReturn(null);
    
    $service = new AffectationImportService($entityManager, $ufsFinderService, $agentFinderService, $errorNotificationService);
    
    // Create test DTOs
    $dto = new AffectationAgentUfDto();
    $dto->uConnexion = 'U123456';
    $dto->dateDebut = '01/01/2023';
    $dto->dateFin = '31/12/2025';
    $dto->codeUf = 'UF001';
    
    $dtos = [$dto];
    
    $result = $service->importAffectations($dtos, 'EJ001');
    
    expect($result)->toBeArray()
        ->and($result)->toHaveKey('processed')
        ->and($result)->toHaveKey('created')
        ->and($result)->toHaveKey('updated')
        ->and($result)->toHaveKey('errors')
        ->and($result['processed'])->toBe(0)
        ->and($result['created'])->toBe(0)
        ->and($result['updated'])->toBe(0)
        ->and($result['errors'])->toHaveCount(1)
        ->and($result['errors'][0])->toHaveKey('uConnexion')
        ->and($result['errors'][0])->toHaveKey('codeUf')
        ->and($result['errors'][0])->toHaveKey('error')
        ->and($result['errors'][0]['uConnexion'])->toBe('U123456')
        ->and($result['errors'][0]['codeUf'])->toBe('UF001');
});

it('handles invalid dates', function () {
    // Mock dependencies
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $ufsFinderService = $this->createMock(UfsFinderService::class);
    $agentFinderService = $this->createMock(AgentFinderService::class);
    $errorNotificationService = $this->createMock(ErrorNotificationEmailService::class);
    $entiteJuridiqueRepository = $this->createMock(EntityRepository::class);
    
    $entityManager->method('getRepository')
        ->willReturnCallback(function ($entityClass) use ($entiteJuridiqueRepository) {
            if ($entityClass === EntiteJuridique::class) {
                return $entiteJuridiqueRepository;
            }
            return null;
        });
    
    // Mock EntiteJuridique
    $entiteJuridique = $this->createMock(EntiteJuridique::class);
    $entiteJuridiqueRepository->method('findOneBy')
        ->willReturn($entiteJuridique);
    
    // Mock notification settings
    $entiteJuridique->method('isNotifierAdminDesErreurProduite')
        ->willReturn(false);
    $entiteJuridique->method('isNotifierErreurAuAdminPourAffectation')
        ->willReturn(false);
    $entiteJuridique->method('isNotifierErreurAuResponssableDamAffectation')
        ->willReturn(false);
    
    // Mock Agent
    $agent = $this->createMock(Agent::class);
    $agentFinderService->method('findAgentByConnexionAndEj')
        ->willReturn($agent);
    
    $service = new AffectationImportService($entityManager, $ufsFinderService, $agentFinderService, $errorNotificationService);
    
    // Create test DTOs with invalid dates
    $dto = new AffectationAgentUfDto();
    $dto->uConnexion = 'U123456';
    $dto->dateDebut = 'invalid-date';
    $dto->dateFin = '31/12/2025';
    $dto->codeUf = 'UF001';
    
    $dtos = [$dto];
    
    $result = $service->importAffectations($dtos, 'EJ001');
    
    expect($result)->toBeArray()
        ->and($result)->toHaveKey('processed')
        ->and($result)->toHaveKey('created')
        ->and($result)->toHaveKey('updated')
        ->and($result)->toHaveKey('errors')
        ->and($result['processed'])->toBe(0)
        ->and($result['created'])->toBe(0)
        ->and($result['updated'])->toBe(0)
        ->and($result['errors'])->toHaveCount(1)
        ->and($result['errors'][0])->toHaveKey('uConnexion')
        ->and($result['errors'][0])->toHaveKey('error')
        ->and($result['errors'][0]['uConnexion'])->toBe('U123456');
});