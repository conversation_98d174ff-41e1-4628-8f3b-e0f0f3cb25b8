<?php

namespace App\EventListener;

use App\Entity\Praticien\Agent;
use App\Domain\Service\AgentRoleAssignmentService;
use Doctrine\Bundle\DoctrineBundle\Attribute\AsEntityListener;
use Doctrine\ORM\Event\PostPersistEventArgs;
use Doctrine\ORM\Events;

#[AsEntityListener(event: Events::postPersist, method: 'postPersist', entity: Agent::class)]
class AgentRoleAssignmentListener
{
    public function __construct(
        private AgentRoleAssignmentService $roleAssignmentService
    ) {}

    /**
     * Se déclenche automatiquement après la création d'un Agent en base
     */
    public function postPersist(Agent $agent, PostPersistEventArgs $event): void
    {
        // Assigne automatiquement les rôles prédéfinis si le hrUser correspond
        $this->roleAssignmentService->assignPredefinedRoles($agent);

        // Flush les changements (AgentHopitalRole créés)
        $event->getObjectManager()->flush();
    }
}
