<?php

namespace App\Controller\Admin\Domain;

use App\Controller\Importation\Structure\Dto\CrImportDto;
use App\Controller\Importation\Structure\Dto\PoleImportDto;
use App\Controller\Importation\Structure\Dto\ServiceImportDto;
use App\Controller\Importation\Structure\Dto\UfImportDto;
use App\Entity\Structure\Cr;
use App\Entity\Structure\EntiteJuridique;
use App\Entity\Structure\Pole;
use App\Entity\Structure\Service;
use App\Entity\Structure\Ufs;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class StructureCsvImportController extends AbstractController
{
    // Mapping des entités vers leurs DTOs d'importation
    private array $entityToDtoMapping = [
        Pole::class => PoleImportDto::class,
        Cr::class => CrImportDto::class,
        Service::class => ServiceImportDto::class,
        Ufs::class => UfImportDto::class,
    ];

    #[Route('/admin/import/structures/{entity}', name: 'admin_import_structures_csv')]
    public function importStructuresCsv(
        Request $request,
        string $entity,
        EntityManagerInterface $entityManager
    ): Response {
        // Vérifier que l'entité est autorisée
        $allowedEntities = [
            Pole::class,
            Cr::class,
            Service::class,
            Ufs::class,
        ];

        if (!in_array($entity, $allowedEntities)) {
            $this->addFlash('danger', '❌ Type d\'entité non autorisé pour l\'import.');
            return $this->redirectToRoute('admin');
        }

        $flashBag = $request->getSession()->getFlashBag();

        // Récupérer toutes les entités juridiques disponibles
        $entitesJuridiques = $entityManager->getRepository(EntiteJuridique::class)->findAll();

        if ($request->isMethod('POST')) {
            /** @var UploadedFile $file */
            $file = $request->files->get('csv_file');
            $entiteJuridiqueId = $request->request->get('entite_juridique_id');

            // Validation de l'entité juridique
            if (empty($entiteJuridiqueId)) {
                $flashBag->add('danger', '❌ Veuillez sélectionner une entité juridique.');
                return $this->render('admin/import_csv.html.twig', [
                    'entity' => $entity,
                    'entitesJuridiques' => $entitesJuridiques
                ]);
            }

            $entiteJuridique = $entityManager->getRepository(EntiteJuridique::class)->find($entiteJuridiqueId);
            if (!$entiteJuridique) {
                $flashBag->add('danger', '❌ Entité juridique introuvable.');
                return $this->render('admin/import_csv.html.twig', [
                    'entity' => $entity,
                    'entitesJuridiques' => $entitesJuridiques
                ]);
            }

            if (!$file || $file->getClientOriginalExtension() !== 'csv') {
                $flashBag->add('danger', '❌ Veuillez fournir un fichier CSV valide.');
                return $this->render('admin/import_csv.html.twig', [
                    'entity' => $entity,
                    'entitesJuridiques' => $entitesJuridiques
                ]);
            }

            try {
                $result = $this->processImport($file, $entity, $entiteJuridique, $entityManager);

                if ($result['success']) {
                    $flashBag->add('success', sprintf('✅ Importation réussie pour <strong>%s</strong> dans "%s" avec %d lignes !',
                        (new \ReflectionClass($entity))->getShortName(), $entiteJuridique->getNom(), $result['count']));
                } else {
                    $flashBag->add('danger', '⚠️ Des erreurs sont survenues lors de l\'import.');
                    foreach (array_slice($result['errors'], 0, 10) as $error) {
                        $flashBag->add('danger', $error);
                    }
                    if (count($result['errors']) > 10) {
                        $flashBag->add('danger', sprintf('⚠️ %d autres erreurs non affichées.', count($result['errors']) - 10));
                    }
                }
            } catch (\Exception $e) {
                $flashBag->add('danger', '❌ Erreur lors de l\'import : ' . $e->getMessage());
            }

            return $this->redirectToRoute('admin');
        }

        return $this->render('admin/import_csv.html.twig', [
            'entity' => $entity,
            'entitesJuridiques' => $entitesJuridiques
        ]);
    }

    private function processImport(UploadedFile $file, string $entity, EntiteJuridique $entiteJuridique, EntityManagerInterface $entityManager): array
    {
        $csv = new \SplFileObject($file->getPathname(), 'r');
        $csv->setFlags(\SplFileObject::READ_CSV);
        $csv->setCsvControl(';');

        $batchSize = 1000;
        $errors = [];
        $count = 0;

        // Désactivation temporaire des logs Doctrine
        $entityManager->getConnection()->getConfiguration()->setSQLLogger(null);

        // Lire l'entête
        $header = $csv->fgetcsv();
        if (!$header || count($header) === 1) {
            throw new \InvalidArgumentException('Le fichier CSV est vide ou mal formé.');
        }

        // Nettoyer l'entête
        $header = array_map(fn($col) => strtoupper(trim($col)), $header);

        while (!$csv->eof()) {
            $row = $csv->fgetcsv();

            if (!is_array($row) || count($row) !== count($header)) {
                continue;
            }

            $row = array_map(fn($value) => trim($value), $row);
            $record = array_combine($header, $row);

            try {
                $obj = $this->processCsvRecord($entity, $record, $entiteJuridique, $entityManager);
                if ($obj) {
                    $entityManager->persist($obj);
                    $count++;

                    if ($count % $batchSize === 0) {
                        $entityManager->flush();
                        $entityManager->clear();
                        // Recharger l'entité après clear
                        $entiteJuridique = $entityManager->getRepository(EntiteJuridique::class)->find($entiteJuridique->getId());
                    }
                }
            } catch (\Exception $e) {
                $errors[] = "⚠️ Erreur ligne " . ($count + 1) . " : " . $e->getMessage();
            }
        }

        $entityManager->flush();
        $entityManager->clear();

        return [
            'success' => empty($errors),
            'count' => $count,
            'errors' => $errors
        ];
    }

    private function processCsvRecord(string $entity, array $record, EntiteJuridique $entiteJuridique, EntityManagerInterface $entityManager): ?object
    {
        // Obtenir le DTO correspondant à l'entité
        $dtoClass = $this->entityToDtoMapping[$entity] ?? null;
        if (!$dtoClass) {
            throw new \InvalidArgumentException("⚠️ Aucun DTO défini pour l'entité : $entity");
        }

        // Créer une instance du DTO et la remplir avec les données CSV
        $dto = $this->createDtoFromCsvRecord($dtoClass, $record);

        switch ($entity) {
            case Pole::class:
                return $this->createPoleFromDto($dto, $entiteJuridique, $entityManager);
            case Cr::class:
                return $this->createCrFromDto($dto, $entiteJuridique, $entityManager);
            case Service::class:
                return $this->createServiceFromDto($dto, $entiteJuridique, $entityManager);
            case Ufs::class:
                return $this->createUfsFromDto($dto, $entiteJuridique, $entityManager);
            default:
                throw new \InvalidArgumentException("⚠️ Type d'entité non supporté : $entity");
        }
    }

    private function createDtoFromCsvRecord(string $dtoClass, array $record): object
    {
        $dto = new $dtoClass();
        $reflection = new \ReflectionClass($dtoClass);

        foreach ($reflection->getProperties(\ReflectionProperty::IS_PUBLIC) as $property) {
            $propertyName = $property->getName();
            $csvColumnName = $this->convertToSnakeCase($propertyName);

            if (isset($record[$csvColumnName])) {
                $value = $record[$csvColumnName];

                if (!empty($value)) {
                    $value = $this->convertValueToPropertyType($value, $property);
                }

                $property->setValue($dto, $value);
            }
        }

        return $dto;
    }

    /**
     * Convertit un nom de propriété camelCase en SNAKE_CASE pour correspondre aux en-têtes CSV
     * Par exemple: crCode -> CR_CODE
     */
    private function convertToSnakeCase(string $input): string
    {
        $result = preg_replace('/([a-z])([A-Z])/', '$1_$2', $input);
        return strtoupper($result);
    }

    private function convertValueToPropertyType($value, \ReflectionProperty $property)
    {
        $type = $property->getType();

        if (!$type) {
            return $value;
        }

        $typeName = $type->getName();

        switch ($typeName) {
            case 'int':
                return (int) $value;
            case 'float':
                return (float) $value;
            case 'bool':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case 'string':
            default:
                return $value;
        }
    }

    private function createPoleFromDto(PoleImportDto $dto, EntiteJuridique $entiteJuridique, EntityManagerInterface $entityManager): Pole
    {
        if (empty($dto->libelle)) {
            throw new \InvalidArgumentException('⚠️ Le libellé est obligatoire pour les pôles');
        }
        
        if (empty($dto->poleCode)) {
            throw new \InvalidArgumentException('⚠️ Le code Pôle est obligatoire');
        }

        $pole = new Pole();
        $pole->setEtab($dto->etab);
        $pole->setPolecode($dto->poleCode);
        $pole->setLibelle($dto->libelle);
        $pole->setPfuser($dto->pfUser);

        if ($dto->datDeb) {
            try {
                $datDeb = \DateTime::createFromFormat('d/m/Y', $dto->datDeb);
                if ($datDeb === false) {
                    throw new \InvalidArgumentException('Failed to parse time string: ' . $dto->datDeb);
                }
                $pole->setDatdeb($datDeb);
            } catch (\Exception $e) {
                throw new \InvalidArgumentException('Failed to parse time string: ' . $dto->datDeb);
            }
        }
        if ($dto->datFin) {
            try {
                $datFin = \DateTime::createFromFormat('d/m/Y', $dto->datFin);
                if ($datFin === false) {
                    throw new \InvalidArgumentException('Failed to parse time string: ' . $dto->datFin);
                }
                $pole->setDatfin($datFin);
            } catch (\Exception $e) {
                throw new \InvalidArgumentException('Failed to parse time string: ' . $dto->datFin);
            }
        }

        // Handle dmdaCre field
        if ($dto->dmdaCre) {
            try {
                $dmdaCre = \DateTime::createFromFormat('d/m/Y', $dto->dmdaCre);
                if ($dmdaCre === false) {
                    throw new \InvalidArgumentException('Failed to parse time string: ' . $dto->dmdaCre);
                }
                $pole->setDmdacre($dmdaCre);
            } catch (\Exception $e) {
                throw new \InvalidArgumentException('Failed to parse time string: ' . $dto->dmdaCre);
            }
        }

        // Handle dmdaMaj field
        if ($dto->dmdaMaj) {
            try {
                $dmdaMaj = \DateTime::createFromFormat('d/m/Y', $dto->dmdaMaj);
                if ($dmdaMaj === false) {
                    throw new \InvalidArgumentException('Failed to parse time string: ' . $dto->dmdaMaj);
                }
                $pole->setDmdamaj($dmdaMaj);
            } catch (\Exception $e) {
                throw new \InvalidArgumentException('Failed to parse time string: ' . $dto->dmdaMaj);
            }
        }

        // Associer l'entité juridique sélectionnée
        $pole->setHopital($entiteJuridique);

        return $pole;
    }

    private function createCrFromDto(CrImportDto $dto, EntiteJuridique $entiteJuridique, EntityManagerInterface $entityManager): Cr
    {
        if (empty($dto->libelle)) {
            throw new \InvalidArgumentException('⚠️ Le libellé est obligatoire pour les CRs');
        }
        
        if (empty($dto->crCode)) {
            throw new \InvalidArgumentException('⚠️ Le code CR est obligatoire');
        }
        
        if (empty($dto->poleCode)) {
            throw new \InvalidArgumentException('⚠️ Le code Pôle est obligatoire pour les CRs');
        }

        $cr = new Cr();
        $cr->setEtab($dto->etab);
        $cr->setCrcode($dto->crCode);
        $cr->setLibelle($dto->libelle);
        $cr->setNomresp($dto->nomResp);
        $cr->setPolecode($dto->poleCode);
        $cr->setPfuser($dto->pfUser);

        if ($dto->datDeb) {
            try {
                $datDeb = \DateTime::createFromFormat('d/m/Y', $dto->datDeb);
                if ($datDeb === false) {
                    throw new \InvalidArgumentException('Failed to parse time string: ' . $dto->datDeb);
                }
                $cr->setDatdeb($datDeb);
            } catch (\Exception $e) {
                throw new \InvalidArgumentException('Failed to parse time string: ' . $dto->datDeb);
            }
        }
        if ($dto->datFin) {
            try {
                $datFin = \DateTime::createFromFormat('d/m/Y', $dto->datFin);
                if ($datFin === false) {
                    throw new \InvalidArgumentException('Failed to parse time string: ' . $dto->datFin);
                }
                $cr->setDatfin($datFin);
            } catch (\Exception $e) {
                throw new \InvalidArgumentException('Failed to parse time string: ' . $dto->datFin);
            }
        }

        // Handle dmdaCre field
        if ($dto->dmdaCre || $dto->dmdAcre) {
            try {
                $dmdaCre = \DateTime::createFromFormat('d/m/Y', $dto->dmdaCre ?? $dto->dmdAcre);
                if ($dmdaCre === false) {
                    throw new \InvalidArgumentException('Failed to parse time string: ' . ($dto->dmdaCre ?? $dto->dmdAcre));
                }
                $cr->setDmdacre($dmdaCre);
            } catch (\Exception $e) {
                throw new \InvalidArgumentException('Failed to parse time string: ' . ($dto->dmdaCre ?? $dto->dmdAcre));
            }
        }

        // Handle dmdaMaj field
        if ($dto->dmdaMaj || $dto->dmdAmaj) {
            try {
                $dmdaMaj = \DateTime::createFromFormat('d/m/Y', $dto->dmdaMaj ?? $dto->dmdAmaj);
                if ($dmdaMaj === false) {
                    throw new \InvalidArgumentException('Failed to parse time string: ' . ($dto->dmdaMaj ?? $dto->dmdAmaj));
                }
                $cr->setDmdamaj($dmdaMaj);
            } catch (\Exception $e) {
                throw new \InvalidArgumentException('Failed to parse time string: ' . ($dto->dmdaMaj ?? $dto->dmdAmaj));
            }
        }

        // Associer au pôle si le code pôle est fourni (dans la même entité juridique)
        if ($dto->poleCode) {
            $pole = $entityManager->getRepository(Pole::class)->findOneBy([
                'polecode' => $dto->poleCode,
                'hopital' => $entiteJuridique
            ]);
            if ($pole) {
                $cr->setPole($pole);
            }
        }

        return $cr;
    }

    private function createServiceFromDto(ServiceImportDto $dto, EntiteJuridique $entiteJuridique, EntityManagerInterface $entityManager): Service
    {
        if (empty($dto->libelle)) {
            throw new \InvalidArgumentException('⚠️ Le libellé est obligatoire pour les services');
        }
        
        if (empty($dto->seCode)) {
            throw new \InvalidArgumentException('⚠️ Le code Service est obligatoire');
        }

        $service = new Service();
        $service->setEtab($dto->etab);
        $service->setSecode($dto->seCode);
        $service->setLibelle($dto->libelle);
        $service->setCdcode($dto->cdCode);
        $service->setTacode($dto->taCode);
        $service->setPfuser($dto->pfUser);

        if ($dto->datDeb) {
            try {
                $datDeb = \DateTime::createFromFormat('d/m/Y', $dto->datDeb);
                if ($datDeb === false) {
                    throw new \InvalidArgumentException('Failed to parse time string: ' . $dto->datDeb);
                }
                $service->setDatdeb($datDeb);
            } catch (\Exception $e) {
                throw new \InvalidArgumentException('Failed to parse time string: ' . $dto->datDeb);
            }
        }
        if ($dto->datFin) {
            try {
                $datFin = \DateTime::createFromFormat('d/m/Y', $dto->datFin);
                if ($datFin === false) {
                    throw new \InvalidArgumentException('Failed to parse time string: ' . $dto->datFin);
                }
                $service->setDatfin($datFin);
            } catch (\Exception $e) {
                throw new \InvalidArgumentException('Failed to parse time string: ' . $dto->datFin);
            }
        }

        // Handle dmdaCre field
        if ($dto->dmdaCre || $dto->dmdAcre) {
            try {
                $dmdaCre = \DateTime::createFromFormat('d/m/Y', $dto->dmdaCre ?? $dto->dmdAcre);
                if ($dmdaCre === false) {
                    throw new \InvalidArgumentException('Failed to parse time string: ' . ($dto->dmdaCre ?? $dto->dmdAcre));
                }
                $service->setDmdacre($dmdaCre);
            } catch (\Exception $e) {
                throw new \InvalidArgumentException('Failed to parse time string: ' . ($dto->dmdaCre ?? $dto->dmdAcre));
            }
        }

        // Handle dmdaMaj field
        if ($dto->dmdaMaj || $dto->dmdAmaj) {
            try {
                $dmdaMaj = \DateTime::createFromFormat('d/m/Y', $dto->dmdaMaj ?? $dto->dmdAmaj);
                if ($dmdaMaj === false) {
                    throw new \InvalidArgumentException('Failed to parse time string: ' . ($dto->dmdaMaj ?? $dto->dmdAmaj));
                }
                $service->setDmdamaj($dmdaMaj);
            } catch (\Exception $e) {
                throw new \InvalidArgumentException('Failed to parse time string: ' . ($dto->dmdaMaj ?? $dto->dmdAmaj));
            }
        }

        // Associer l'entité juridique sélectionnée
        $service->setHopital($entiteJuridique);

        return $service;
    }

    private function createUfsFromDto(UfImportDto $dto, EntiteJuridique $entiteJuridique, EntityManagerInterface $entityManager): Ufs
    {
        if (empty($dto->libelle)) {
            throw new \InvalidArgumentException('⚠️ Le libellé est obligatoire pour les UFs');
        }

        if (empty($dto->ufCode)) {
            throw new \InvalidArgumentException('⚠️ Le code UF est obligatoire');
        }

        if (empty($dto->crCode)) {
            throw new \InvalidArgumentException('⚠️ Le code CR est obligatoire pour les UFs');
        }

        // Recherche du CR lié dans la même entité juridique
        $cr = $entityManager->getRepository(Cr::class)->findOneBy([
            'crcode' => $dto->crCode
        ]);

        if (!$cr) {
            throw new \InvalidArgumentException('⚠️ CR non trouvé : ' . $dto->crCode . ' pour l\'entité juridique : ' . $entiteJuridique->getNom());
        }

        $ufs = new Ufs();
        $ufs->setEtab($dto->etab);
        $ufs->setUfcode($dto->ufCode);
        $ufs->setLibelle($dto->libelle);
        $ufs->setTacode($dto->taCode);
        $ufs->setCdcode($dto->cdCode);
        $ufs->setLettrebudg($dto->lettreBudg);
        
        // Gestion des dates pour SCD type 2
        if ($dto->datDeb) {
            try {
                $datDeb = \DateTime::createFromFormat('d/m/Y', $dto->datDeb);
                if ($datDeb === false) {
                    throw new \InvalidArgumentException('Failed to parse time string: ' . $dto->datDeb);
                }
                $ufs->setDatdeb($datDeb);
                // Set validFrom based on datDeb
                $ufs->setValidFrom($datDeb);
            } catch (\Exception $e) {
                throw new \InvalidArgumentException('Failed to parse time string: ' . $dto->datDeb);
            }
        }
        
        if ($dto->datFin) {
            try {
                $datFin = \DateTime::createFromFormat('d/m/Y', $dto->datFin);
                if ($datFin === false) {
                    throw new \InvalidArgumentException('Failed to parse time string: ' . $dto->datFin);
                }
                $ufs->setDatfin($datFin);
                // Set validTo based on datFin
                $ufs->setValidTo($datFin);
            } catch (\Exception $e) {
                throw new \InvalidArgumentException('Failed to parse time string: ' . $dto->datFin);
            }
        }
        
        if ($dto->datClos) {
            try {
                $datClos = \DateTime::createFromFormat('d/m/Y', $dto->datClos);
                if ($datClos === false) {
                    throw new \InvalidArgumentException('Failed to parse time string: ' . $dto->datClos);
                }
                $ufs->setDatclos($datClos);
            } catch (\Exception $e) {
                throw new \InvalidArgumentException('Failed to parse time string: ' . $dto->datClos);
            }
        }
        
        // Handle dmdaCre field
        if ($dto->dmdaCre || $dto->dmdAcre) {
            try {
                $dmdaCre = \DateTime::createFromFormat('d/m/Y', $dto->dmdaCre ?? $dto->dmdAcre);
                if ($dmdaCre === false) {
                    throw new \InvalidArgumentException('Failed to parse time string: ' . ($dto->dmdaCre ?? $dto->dmdAcre));
                }
                $ufs->setDmdacre($dmdaCre);
            } catch (\Exception $e) {
                throw new \InvalidArgumentException('Failed to parse time string: ' . ($dto->dmdaCre ?? $dto->dmdAcre));
            }
        }
        
        // Handle dmdaMaj field
        if ($dto->dmdaMaj || $dto->dmdAmaj) {
            try {
                $dmdaMaj = \DateTime::createFromFormat('d/m/Y', $dto->dmdaMaj ?? $dto->dmdAmaj);
                if ($dmdaMaj === false) {
                    throw new \InvalidArgumentException('Failed to parse time string: ' . ($dto->dmdaMaj ?? $dto->dmdAmaj));
                }
                $ufs->setDmdamaj($dmdaMaj);
            } catch (\Exception $e) {
                throw new \InvalidArgumentException('Failed to parse time string: ' . ($dto->dmdaMaj ?? $dto->dmdAmaj));
            }
        }

        
        // Associer le CR
        $ufs->setCr($cr);

        return $ufs;
    }
}
