<?php

namespace App\State\Batch\Base;

use ApiPlatform\State\ProcessorInterface;
use App\Entity\Importation\Importation;
use Doctrine\ORM\EntityManagerInterface;

/**
 * Base class for batch processors that need to create Importation records
 * 
 * This class provides a common method for creating Importation records
 * when batch processing is successful.
 */
abstract class BatchProcessorBase implements ProcessorInterface
{
    /**
     * Creates and persists a new Importation record
     * 
     * @param EntityManagerInterface $entityManager The entity manager
     * @param string $resourceName The name of the resource being imported (e.g., "Actes", "Sigaps")
     * @param bool $isSuccessful Whether the import was successful
     * @param string|null $entiteJuridiqueId Optional ID of the associated EntiteJuridique
     * @return Importation The created Importation entity
     */
    protected function createImportationRecord(
        EntityManagerInterface $entityManager,
        string $resourceName,
        bool $isSuccessful = true,
        ?string $entiteJuridiqueId = null
    ): Importation {
        try {
            $importation = new Importation();
            $importation->setNomRessource($resourceName);
            $importation->setEstReussie($isSuccessful);
            
            // Set the EntiteJuridique if an ID is provided
            if ($entiteJuridiqueId) {
                try {
                    // Use find() instead of getReference() to check if the entity actually exists
                    $entiteJuridique = $entityManager->getRepository('App\Entity\Structure\EntiteJuridique')->find($entiteJuridiqueId);
                    
                    if ($entiteJuridique) {
                        $importation->setEntiteJuridique($entiteJuridique);
                    } else {
                        // Log warning only if EntiteJuridique not found
                        error_log("WARNING: EntiteJuridique with ID {$entiteJuridiqueId} not found. Creating Importation without reference.");
                    }
                } catch (\Exception $e) {
                    // If there's an issue with the EntiteJuridique reference, log it but continue
                    error_log("Error setting EntiteJuridique in Importation: " . $e->getMessage());
                }
            }
            
            // Persist the Importation entity
            $entityManager->persist($importation);
            
            // Check if we're in a transaction
            $inTransaction = $entityManager->getConnection()->isTransactionActive();
            
            // If we're not in a transaction, flush immediately
            // Otherwise, let the calling code handle the flush as part of its transaction
            if (!$inTransaction) {
                try {
                    $entityManager->flush($importation);
                    
                    // Verify the Importation was actually persisted
                    $checkImportation = $entityManager->getRepository('App\Entity\Importation\Importation')->find($importation->getId());
                    if (!$checkImportation) {
                        error_log("WARNING: Could not verify Importation in database despite successful flush");
                    }
                } catch (\Exception $e) {
                    error_log("Error flushing Importation: " . $e->getMessage());
                    
                    // If flush failed and we have an EntiteJuridique reference, try again without it
                    if ($importation->getEntiteJuridique() !== null) {
                        $importation->setEntiteJuridique(null);
                        try {
                            $entityManager->flush($importation);
                        } catch (\Exception $retryException) {
                            error_log("Error on retry flush: " . $retryException->getMessage());
                        }
                    }
                }
            }
            
            return $importation;
        } catch (\Exception $e) {
            // Log the error but don't throw it to avoid disrupting the main process
            error_log("Critical error creating Importation record: " . $e->getMessage());
            
            // Return a new Importation object without persisting it
            // This allows the calling code to continue without errors
            $fallbackImportation = new Importation();
            $fallbackImportation->setNomRessource($resourceName);
            $fallbackImportation->setEstReussie($isSuccessful);
            
            return $fallbackImportation;
        }
    }
}