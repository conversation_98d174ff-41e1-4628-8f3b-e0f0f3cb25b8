<?php

namespace App\Domain\Service\Core;


use App\Entity\Praticien\Agent;
use App\Entity\Structure\EntiteJuridique;
use Doctrine\ORM\EntityManagerInterface;

/**
 * Service spécialisé pour la recherche d'agents avec gestion multi-tenant.
 */
class AgentFinderService
{
    public function __construct(
        private readonly EntityManagerInterface $em
    )
    {
    }

    /**
     * Recherche un agent par son identifiant de connexion dans une entité juridique donnée.
     */
    public function findAgentByConnexionAndEj(string $uConnexion, EntiteJuridique $entiteJuridique): ?Agent
    {
        return $this->em->getRepository(Agent::class)
            ->createQueryBuilder('a')
            ->where('a.hrUser = :uConnexion')
            ->andWhere('a.hopital = :entiteJuridique')
            ->setParameter('uConnexion', $uConnexion)
            ->setParameter('entiteJuridique', $entiteJuridique)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * Recherche un agent par son matricule dans une entité juridique donnée.
     */
    public function findAgentByMatriculeAndEj(string $matricule, EntiteJuridique $entiteJuridique): ?Agent
    {
        return $this->em->getRepository(Agent::class)
            ->createQueryBuilder('a')
            ->where('a.matricule = :matricule')
            ->andWhere('a.hopital = :entiteJuridique')
            ->setParameter('matricule', $matricule)
            ->setParameter('entiteJuridique', $entiteJuridique)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * Recherche un agent par connexion ou matricule dans une entité juridique donnée.
     */
    public function findAgentByIdentifierAndEj(string $identifier, EntiteJuridique $entiteJuridique, string $identifierType = 'hrUser'): ?Agent
    {
        $field = $identifierType === 'matricule' ? 'matricule' : 'hrUser';

        return $this->em->getRepository(Agent::class)
            ->createQueryBuilder('a')
            ->where("a.{$field} = :identifier")
            ->andWhere('a.hopital = :entiteJuridique')
            ->setParameter('identifier', $identifier)
            ->setParameter('entiteJuridique', $entiteJuridique)
            ->getQuery()
            ->getOneOrNullResult();
    }
}