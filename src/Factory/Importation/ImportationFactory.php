<?php

namespace App\Factory\Importation;

use App\Entity\Importation\Importation;
use App\Factory\Structure\EntiteJuridiqueFactory;
use Zenstruck\Foundry\Persistence\PersistentProxyObjectFactory;

/**
 * @extends PersistentProxyObjectFactory<Importation>
 */
final class ImportationFactory extends PersistentProxyObjectFactory
{
    /**
     * @see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#factories-as-services
     */
    public function __construct()
    {
    }

    public static function class(): string
    {
        return Importation::class;
    }

    /**
     * @see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#model-factories
     */
    protected function defaults(): array|callable
    {
        // Ressources possibles pour l'importation
        $ressources = ['Agent', 'Structure', 'Actes'];
        
        // Date d'importation aléatoire dans les 30 derniers jours
        $date = new \DateTime();
        $date->modify('-' . self::faker()->numberBetween(0, 30) . ' days');
        $date->modify('-' . self::faker()->numberBetween(0, 23) . ' hours');
        $date->modify('-' . self::faker()->numberBetween(0, 59) . ' minutes');
        
        return [
            'dateImportation' => $date,
            'nomRessource' => self::faker()->randomElement($ressources),
            'estReussie' => self::faker()->boolean(80), // 80% de chance de succès
            'entiteJuridique' => self::faker()->boolean(70) ? EntiteJuridiqueFactory::random() : null,
            'isActif' => true,
        ];
    }

    /**
     * @see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#initialization
     */
    protected function initialize(): static
    {
        return $this
            // ->afterInstantiate(function(Importation $importation): void {})
        ;
    }
}