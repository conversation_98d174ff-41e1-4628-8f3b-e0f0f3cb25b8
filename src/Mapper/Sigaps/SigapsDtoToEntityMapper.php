<?php

namespace App\Mapper\Sigaps;

use App\ApiResource\Activite\SigapsDto;
use App\Entity\Activite\Sigaps;
use App\Repository\AgentRepository;
use App\Repository\SigapsRepository;
use Doctrine\DBAL\Exception;
use Symfonycasts\MicroMapper\AsMapper;
use Symfonycasts\MicroMapper\MapperInterface;

#[AsMapper(from: SigapsDto::class, to: Sigaps::class)]
class SigapsDtoToEntityMapper implements MapperInterface
{
    public function __construct(
        private SigapsRepository $sigapsRepository,
        private AgentRepository $agentRepository
    ) {}

    public function load(object $from, string $toClass, array $context): object
    {
        $dto = $from;
        assert($dto instanceof SigapsDto);

        // On récupère l'entité existante si un ID est fourni, sinon on en crée une nouvelle
        $sigaps = $dto->id ? $this->sigapsRepository->find($dto->id) : new Sigaps();
        if (!$sigaps) {
            throw new Exception('SIGAPS non trouvé');
        }

        return $sigaps;
    }

    public function populate(object $from, object $to, array $context): object
    {
        $dto = $from;
        $sigaps = $to;
        assert($dto instanceof SigapsDto);
        assert($sigaps instanceof Sigaps);

        if (!$dto->dateDebut) {
            throw new Exception('dateDebut is required', 400);
        }

        $sigaps->setDateDebut($dto->dateDebut);
        $sigaps->setDateFin($dto->dateFin);
        $sigaps->setScore($dto->score);
        
        // Gestion de la répartition par catégorie
        // Si les champs individuels sont fournis, on construit le JSON
        $hasIndividualCategories = $dto->repartition_A_plus !== null || 
                                  $dto->repartition_A !== null || 
                                  $dto->repartition_B !== null || 
                                  $dto->repartition_C !== null || 
                                  $dto->repartition_D !== null;
        
        if ($hasIndividualCategories) {
            $repartition = [];
            if ($dto->repartition_A_plus !== null) {
                $repartition['A+'] = $dto->repartition_A_plus;
            }
            if ($dto->repartition_A !== null) {
                $repartition['A'] = $dto->repartition_A;
            }
            if ($dto->repartition_B !== null) {
                $repartition['B'] = $dto->repartition_B;
            }
            if ($dto->repartition_C !== null) {
                $repartition['C'] = $dto->repartition_C;
            }
            if ($dto->repartition_D !== null) {
                $repartition['D'] = $dto->repartition_D;
            }
            $sigaps->setRepartitionParCategorie($repartition);
        } else {
            // Si aucun champ individuel n'est fourni, on utilise le champ global s'il existe
            if ($dto->repartitionParCategorie === null) {
                $sigaps->setRepartitionParCategorie(null);
            } else {
                $sigaps->setRepartitionParCategorie($dto->repartitionParCategorie); // vous pouvez activer le writable: true coté SigapsDto si besoin
            }
        }
        
        $sigaps->setCategorie($dto->categorie);
        $sigaps->setNombrePublication($dto->nombrePublication);

        // Convertir le hrUser en entité Agent
        if ($dto->agentHrU) {
            $agent = $this->agentRepository->findByIdentifier($dto->agentHrU);
            if (!$agent) {
                throw new Exception('Agent non trouvé avec agentHrU : ' . $dto->agentHrU);
            }
            $sigaps->setAgent($agent);
        }

        // Génération automatique des champs non fournis par le client
        $sigaps->setValidFrom($dto->dateDebut);
        $sigaps->setValidTo(null);

        // Calcul du type de période
        $periodeType = \App\Domain\Enum\PeriodeType::ANNUEL;
        if ($dto->dateDebut && $dto->dateFin) {
            $interval = $dto->dateDebut->diff($dto->dateFin);
            $days = (int)$interval->format('%a');
            if ($days < 32) {
                $periodeType = \App\Domain\Enum\PeriodeType::HEBDOMADAIRE;
            } elseif ($days < 366) {
                $periodeType = \App\Domain\Enum\PeriodeType::MENSUEL;
            } else {
                $periodeType = \App\Domain\Enum\PeriodeType::ANNUEL;
            }
        }
        $sigaps->setPeriodeType($periodeType);

        // Source : si non fournie, on met l'ej de l'agent
        if (!empty($dto->source)) {
            $sigaps->setSource($dto->source);
        } else {
            $agent = null;
            if ($dto->agentHrU) {
                $agent = $this->agentRepository->findByIdentifier($dto->agentHrU);
            }
            $sigaps->setSource($agent ? $agent->getHopital()->getCode() : null);
        }

        return $sigaps;
    }
}
