<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Import Batch avec Erreurs - {{ entiteJuridique.code }}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 700px; margin: 0 auto; background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background-color: #dc3545; color: white; padding: 20px; margin: -30px -30px 30px -30px; border-radius: 8px 8px 0 0; }
        .header h1 { margin: 0; font-size: 24px; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 15px; margin: 20px 0; }
        .stat-card { background-color: #f8f9fa; padding: 15px; border-radius: 6px; text-align: center; }
        .stat-card.success { border-left: 4px solid #28a745; }
        .stat-card.error { border-left: 4px solid #dc3545; }
        .stat-number { font-size: 20px; font-weight: bold; margin-bottom: 5px; }
        .stat-number.success { color: #28a745; }
        .stat-number.error { color: #dc3545; }
        .stat-label { font-size: 12px; color: #6c757d; text-transform: uppercase; }
        .info-section { margin: 20px 0; padding: 15px; background-color: #e9ecef; border-radius: 6px; }
        .error-section { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 6px; margin: 20px 0; }
        .error-list { max-height: 300px; overflow-y: auto; margin-top: 15px; }
        .error-item { background-color: white; margin: 10px 0; padding: 10px; border-radius: 4px; border-left: 3px solid #dc3545; }
        .error-index { font-weight: bold; color: #dc3545; }
        .error-message { margin: 5px 0; font-family: monospace; font-size: 13px; }
        .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6; font-size: 12px; color: #6c757d; }
        .duration { color: #007bff; font-weight: bold; }
        .success-rate { font-size: 18px; font-weight: bold; }
        .success-rate.good { color: #28a745; }
        .success-rate.warning { color: #ffc107; }
        .success-rate.danger { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚠️ Import Batch avec Erreurs</h1>
            <p style="margin: 10px 0 0 0; opacity: 0.9;">
                {{ batchStats.resource_type }} - {{ entiteJuridique.code }}
            </p>
        </div>

        <div class="info-section">
            <h3 style="margin-top: 0; color: #495057;">Informations du batch</h3>
            <p><strong>ID du batch :</strong> {{ batchId }}</p>
            <p><strong>Type de ressource :</strong> {{ batchStats.resource_type }}</p>
            <p><strong>Début :</strong> {{ batchStats.start_time|date('d/m/Y H:i:s') }}</p>
            <p><strong>Fin :</strong> {{ batchStats.end_time|date('d/m/Y H:i:s') }}</p>
            <p><strong>Durée :</strong> <span class="duration">{{ batchStats.duration.format('%H:%I:%S') }}</span></p>
            {% if batchStats.emails_disabled %}
            <p><strong>⚡ Optimisations activées :</strong> Notifications désactivées pendant l'import</p>
            {% endif %}
        </div>

        {% set successRate = (batchStats.processed / batchStats.total_records * 100)|round(1) %}
        {% set rateClass = successRate >= 90 ? 'good' : (successRate >= 70 ? 'warning' : 'danger') %}
        
        <div style="text-align: center; margin: 20px 0; padding: 15px; background-color: #f8f9fa; border-radius: 6px;">
            <div class="success-rate {{ rateClass }}">Taux de succès : {{ successRate }}%</div>
            <small style="color: #6c757d;">{{ batchStats.processed }} sur {{ batchStats.total_records }} enregistrements traités</small>
        </div>

        <div class="stats-grid">
            <div class="stat-card success">
                <div class="stat-number success">{{ batchStats.total_records }}</div>
                <div class="stat-label">Total</div>
            </div>
            <div class="stat-card success">
                <div class="stat-number success">{{ batchStats.processed }}</div>
                <div class="stat-label">Traités</div>
            </div>
            <div class="stat-card success">
                <div class="stat-number success">{{ batchStats.created }}</div>
                <div class="stat-label">Créés</div>
            </div>
            <div class="stat-card success">
                <div class="stat-number success">{{ batchStats.updated }}</div>
                <div class="stat-label">Mis à jour</div>
            </div>
            <div class="stat-card error">
                <div class="stat-number error">{{ errors|length }}</div>
                <div class="stat-label">Erreurs</div>
            </div>
        </div>

        <div class="error-section">
            <h3 style="margin-top: 0;">🚨 Erreurs rencontrées ({{ errors|length }})</h3>
            
            {% if errors|length > 10 %}
                <p><strong>Attention :</strong> {{ errors|length }} erreurs détectées. Seules les 10 premières sont affichées ci-dessous.</p>
            {% endif %}

            <div class="error-list">
                {% for error in errors|slice(0, 10) %}
                <div class="error-item">
                    <div class="error-index">Enregistrement #{{ error.index }}</div>
                    <div class="error-message">{{ error.error }}</div>
                    {% if error.data %}
                    <details style="margin-top: 10px;">
                        <summary style="cursor: pointer; color: #6c757d; font-size: 12px;">Voir les données</summary>
                        <pre style="background-color: #f8f9fa; padding: 10px; margin: 5px 0; border-radius: 4px; font-size: 11px; overflow-x: auto;">{{ error.data|json_encode(constant('JSON_PRETTY_PRINT')) }}</pre>
                    </details>
                    {% endif %}
                </div>
                {% endfor %}
            </div>

            {% if errors|length > 10 %}
                <p style="margin-top: 15px; font-style: italic; color: #6c757d;">
                    ... et {{ errors|length - 10 }} autres erreurs. Consultez les logs pour plus de détails.
                </p>
            {% endif %}
        </div>

        <div class="info-section">
            <h3 style="margin-top: 0; color: #495057;">Résumé</h3>
            <p>L'import de <strong>{{ batchStats.total_records }}</strong> enregistrements de type <strong>{{ batchStats.resource_type }}</strong> s'est terminé avec <strong>{{ errors|length }} erreurs</strong>.</p>
            
            {% if batchStats.processed > 0 %}
                <p><strong>{{ batchStats.processed }}</strong> enregistrements ont été traités avec succès :</p>
                <ul>
                    {% if batchStats.created > 0 %}<li>{{ batchStats.created }} nouveaux enregistrements créés</li>{% endif %}
                    {% if batchStats.updated > 0 %}<li>{{ batchStats.updated }} enregistrements mis à jour</li>{% endif %}
                </ul>
            {% endif %}

            {% if successRate >= 90 %}
                <p style="color: #28a745;"><strong>✅ Bon taux de succès</strong> - La plupart des données ont été importées correctement.</p>
            {% elseif successRate >= 70 %}
                <p style="color: #ffc107;"><strong>⚠️ Taux de succès moyen</strong> - Vérifiez les erreurs pour améliorer la qualité des données.</p>
            {% else %}
                <p style="color: #dc3545;"><strong>❌ Taux de succès faible</strong> - Vérifiez la structure et la qualité des données source.</p>
            {% endif %}
        </div>

        <div class="footer">
            <p>Cet email a été généré automatiquement par le système SUPRA.</p>
            <p>Entité juridique : {{ entiteJuridique.code }} | {{ batchStats.end_time|date('d/m/Y H:i:s') }}</p>
            <p style="margin-top: 10px; color: #dc3545;"><strong>Action requise :</strong> Vérifiez et corrigez les erreurs avant de relancer l'import.</p>
        </div>
    </div>
</body>
</html>
