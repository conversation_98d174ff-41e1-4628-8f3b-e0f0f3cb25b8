<?php
namespace App\Entity\Praticien\Trait;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

trait CollecteurTrait
{
    #[ORM\Column(length: 180, nullable: true)]
    private ?string $email = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $nom = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $prenom = null;

    #[ORM\Column(length: 50, nullable: true)]
    private ?string $hrUser = null;

    #[ORM\Column(length: 10, nullable: true)]
    private ?string $titre = null;

    #[ORM\Column(length: 10, nullable: true)]
    private ?string $categorie = null;

    #[ORM\Column(length: 10, nullable: true)]
    private ?string $etablissement = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $dateDepart = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $dateVenue = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $dateMaj = null;

    #[ORM\Column(length: 50, nullable: true)]
    private ?string $createurFiche = null;

    #[ORM\Column(type: Types::BOOLEAN, options: ["default" => false])]
    private bool $isAdmin = false;

    #[ORM\Column(type: Types::BOOLEAN, options: ["default" => false])]
    private bool $isAnesthesiste = false;

    // Getters et setters
    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(?string $email): static
    {
        $this->email = $email;
        return $this;
    }
    public function getNom(): ?string
    {
        return $this->nom;
    }

    public function setNom(?string $nom): static
    {
        $this->nom = $nom;
        return $this;
    }

    public function getPrenom(): ?string
    {
        return $this->prenom;
    }

    public function setPrenom(?string $prenom): static
    {
        $this->prenom = $prenom;
        return $this;
    }

    public function getHrUser(): ?string
    {
        return $this->hrUser;
    }

    public function setHrUser(?string $hrUser): static
    {
        $this->hrUser = $hrUser;
        return $this;
    }

    public function getTitre(): ?string
    {
        return $this->titre;
    }

    public function setTitre(?string $titre): static
    {
        $this->titre = $titre;
        return $this;
    }

    public function getCategorie(): ?string
    {
        return $this->categorie;
    }

    public function setCategorie(?string $categorie): static
    {
        $this->categorie = $categorie;
        return $this;
    }

    public function getEtablissement(): ?string
    {
        return $this->etablissement;
    }

    public function setEtablissement(?string $etablissement): static
    {
        $this->etablissement = $etablissement;
        return $this;
    }

    public function getDateDepart(): ?\DateTimeInterface
    {
        return $this->dateDepart;
    }

    public function setDateDepart(?\DateTimeInterface $dateDepart): static
    {
        $this->dateDepart = $dateDepart;
        return $this;
    }

    public function getDateVenue(): ?\DateTimeInterface
    {
        return $this->dateVenue;
    }

    public function setDateVenue(?\DateTimeInterface $dateVenue): static
    {
        $this->dateVenue = $dateVenue;
        return $this;
    }

    public function getDateMaj(): ?\DateTimeInterface
    {
        return $this->dateMaj;
    }

    public function setDateMaj(?\DateTimeInterface $dateMaj): static
    {
        $this->dateMaj = $dateMaj;
        return $this;
    }

    public function getCreateurFiche(): ?string
    {
        return $this->createurFiche;
    }

    public function setCreateurFiche(?string $createurFiche): static
    {
        $this->createurFiche = $createurFiche;
        return $this;
    }

    public function isAdmin(): bool
    {
        return $this->isAdmin;
    }

    public function setIsAdmin(bool $isAdmin): static
    {
        $this->isAdmin = $isAdmin;
        return $this;
    }

    public function isAnesthesiste(): bool
    {
        return $this->isAnesthesiste;
    }

    public function setIsAnesthesiste(bool $isAnesthesiste): static
    {
        $this->isAnesthesiste = $isAnesthesiste;
        return $this;
    }
}
