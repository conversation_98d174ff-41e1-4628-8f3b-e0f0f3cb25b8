<?php

namespace App\ApiResource\Base;

use ApiPlatform\Doctrine\Orm\Filter\BooleanFilter;
use ApiPlatform\Doctrine\Orm\Filter\OrderFilter;
use ApiPlatform\Doctrine\Orm\Filter\SearchFilter;
use ApiPlatform\Metadata\ApiFilter;
use ApiPlatform\Metadata\ApiProperty;

/**
 * Classe de base pour tous les DTOs.
 *
 * Fournit un identifiant UUID unique et un champ `isActif` pour gérer l'état des objets.
 */
#[ApiFilter(OrderFilter::class,properties: [])]
abstract class BaseDto
{
    #[ApiProperty(readable: false, writable: false, identifier: true)]
    public ?string $id = null;

    #[ApiProperty(description: "État d'activation de l'objet", example: true)]
    #[ApiFilter(BooleanFilter::class)]
    public bool $isActif = true;

    #[ApiProperty(description: "Date de création de l'objet", example: "2024-03-20T10:00:00+00:00")]
    public ?\DateTimeInterface $dateCreation = null;
}