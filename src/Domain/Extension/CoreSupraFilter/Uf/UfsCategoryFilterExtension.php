<?php

namespace App\Domain\Extension\CoreSupraFilter\Uf;

use ApiPlatform\Doctrine\Orm\Extension\QueryCollectionExtensionInterface;
use ApiPlatform\Doctrine\Orm\Util\QueryNameGeneratorInterface;
use ApiPlatform\Metadata\Operation;
use App\Domain\Extension\CoreSupraFilter\BaseCustomFilterExtension;
use App\Domain\Extension\CoreSupraFilter\Cr\CrCategoryExcludeFilter;
use App\Domain\Extension\CoreSupraFilter\Pole\PoleCategoryExcludeFilter;
use App\Entity\Structure\Ufs;
use Doctrine\ORM\QueryBuilder;

/**
 * Pôle exclu → ses CRs et UFs sont automatiquement exclus
 * CR exclu → ses UFs sont automatiquement exclues
 * UF exclue → exclusion directe
 */
class UfsCategoryFilterExtension extends BaseCustomFilterExtension implements QueryCollectionExtensionInterface
{
    public function applyToCollection(
        QueryBuilder $queryBuilder,
        QueryNameGeneratorInterface $queryNameGenerator,
        string $resourceClass,
        ?Operation $operation = null,
        array $context = []
    ): void {
        if ($resourceClass !== Ufs::class) {
            return;
        }

        if (!$this->isApiPlatformContext($context)) {
            return;
        }

        $this->addUfcodeFilter($queryBuilder, $queryNameGenerator);
    }

    private function addUfcodeFilter(QueryBuilder $queryBuilder, QueryNameGeneratorInterface $queryNameGenerator): void
    {
        $rootAlias = $queryBuilder->getRootAliases()[0];
        $excludedUfcodes = UfsCategoryExcludeFilter::getExcludedUfcodes();
        $excludedCrcodes = CrCategoryExcludeFilter::getExcludedCrcodes();
        $excludedPolecodes = PoleCategoryExcludeFilter::getExcludedPolecodes();

        $conditions = [];

        // 1. Exclusion directe des UFs par ufcode
        if (!empty($excludedUfcodes)) {
            $parameterName = $queryNameGenerator->generateParameterName('excludedUfcodes');
            $conditions[] = sprintf(
                '(UPPER(%s.ufcode) NOT IN (:%s) OR %s.ufcode IS NULL)',
                $rootAlias,
                $parameterName,
                $rootAlias
            );
            $queryBuilder->setParameter($parameterName, array_map('strtoupper', $excludedUfcodes));
        }

        // 2. Exclusion en cascade : UFs dont le CR parent est exclu
        if (!empty($excludedCrcodes)) {
            $this->ensureCrJoin($queryBuilder, $rootAlias);

            $parameterName = $queryNameGenerator->generateParameterName('excludedCrcodes');
            $conditions[] = sprintf(
                '(UPPER(cr.crcode) NOT IN (:%s) OR cr.crcode IS NULL)',
                $parameterName
            );
            $queryBuilder->setParameter($parameterName, array_map('strtoupper', $excludedCrcodes));
        }

        // 3. Exclusion en cascade : UFs dont le pôle grand-parent est exclu
        if (!empty($excludedPolecodes)) {
            $this->ensurePoleJoin($queryBuilder, $rootAlias);

            $parameterName = $queryNameGenerator->generateParameterName('excludedPolecodes');
            $conditions[] = sprintf(
                '(UPPER(pole.polecode) NOT IN (:%s) OR pole.polecode IS NULL)',
                $parameterName
            );
            $queryBuilder->setParameter($parameterName, array_map('strtoupper', $excludedPolecodes));
        }

        // Appliquer toutes les conditions
        if (!empty($conditions)) {
            $queryBuilder->andWhere(implode(' AND ', $conditions));
        }
    }

    private function ensureCrJoin(QueryBuilder $queryBuilder, string $rootAlias): void
    {
        $aliases = $queryBuilder->getAllAliases();

        if (!in_array('cr', $aliases)) {
            $queryBuilder->leftJoin($rootAlias . '.cr', 'cr');
        }
    }

    private function ensurePoleJoin(QueryBuilder $queryBuilder, string $rootAlias): void
    {
        $aliases = $queryBuilder->getAllAliases();

        // Assurer d'abord la jointure CR si elle n'existe pas
        $this->ensureCrJoin($queryBuilder, $rootAlias);

        // Puis la jointure Pole via CR
        if (!in_array('pole', $aliases)) {
            $queryBuilder->leftJoin('cr.pole', 'pole');
        }
    }


    /**
     * Version alternative excluant aussi les UFs avec ufcode NULL
     */
    /*
    private function addUfcodeFilter_SI_TU_VEUX_IGNORER_LES_UFS_AVEC_UFCODE_NULL(
        QueryBuilder $queryBuilder,
        QueryNameGeneratorInterface $queryNameGenerator
    ): void {
        $rootAlias = $queryBuilder->getRootAliases()[0];
        $excludedUfcodes = UfsCategoryExcludeFilter::getExcludedUfcodes();
        $excludedCrcodes = CrCategoryExcludeFilter::getExcludedCrcodes();
        $excludedPolecodes = PoleCategoryExcludeFilter::getExcludedPolecodes();

        $conditions = [];

        // Force ufcode NOT NULL
        $conditions[] = sprintf('%s.ufcode IS NOT NULL', $rootAlias);

        // Exclusion directe des UFs
        if (!empty($excludedUfcodes)) {
            $parameterName = $queryNameGenerator->generateParameterName('excludedUfcodes');
            $conditions[] = sprintf('UPPER(%s.ufcode) NOT IN (:%s)', $rootAlias, $parameterName);
            $queryBuilder->setParameter($parameterName, array_map('strtoupper', $excludedUfcodes));
        }

        // Exclusion cascade des CRs
        if (!empty($excludedCrcodes)) {
            $this->ensureCrJoin($queryBuilder, $rootAlias);
            $parameterName = $queryNameGenerator->generateParameterName('excludedCrcodes');
            $conditions[] = sprintf(
                '(UPPER(cr.crcode) NOT IN (:%s) OR cr.crcode IS NULL)',
                $parameterName
            );
            $queryBuilder->setParameter($parameterName, array_map('strtoupper', $excludedCrcodes));
        }

        // Exclusion cascade des pôles
        if (!empty($excludedPolecodes)) {
            $this->ensurePoleJoin($queryBuilder, $rootAlias);
            $parameterName = $queryNameGenerator->generateParameterName('excludedPolecodes');
            $conditions[] = sprintf(
                '(UPPER(pole.polecode) NOT IN (:%s) OR pole.polecode IS NULL)',
                $parameterName
            );
            $queryBuilder->setParameter($parameterName, array_map('strtoupper', $excludedPolecodes));
        }

        if (!empty($conditions)) {
            $queryBuilder->andWhere(implode(' AND ', $conditions));
        }
    }
    */
}