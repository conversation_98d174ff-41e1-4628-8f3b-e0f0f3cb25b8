# see https://symfony.com/doc/current/reference/configuration/framework.html
framework:
    secret: '%env(APP_SECRET)%'
    #csrf_protection: true
    trusted_proxies: '%env(TRUSTED_PROXIES)%'
    trusted_headers: [ x-forwarded-for, x-forwarded-host, x-forwarded-proto, x-forwarded-port ]

    # Note that the session will be started ONLY if you read or write from it.
#    session: true
    session:
        storage_factory_id: session.storage.factory.native
    #esi: true
    #fragments: true

when@test:
    framework:
        test: true
        session:
            storage_factory_id: session.storage.factory.mock_file
