(()=>{"use strict";function t(t,e){e?(t.classList.remove("d-block"),t.classList.add("d-none")):(t.classList.remove("d-none"),t.classList.add("d-block"))}function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},e(t)}function n(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=function(t,e){if(t){if("string"==typeof t)return i(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?i(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0,o=function(){};return{s:o,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var l,a=!0,c=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return a=t.done,t},e:function(t){c=!0,l=t},f:function(){try{a||null==n.return||n.return()}finally{if(c)throw l}}}}function i(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=Array(e);n<e;n++)i[n]=t[n];return i}function r(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,l(i.key),i)}}function o(t,e,n){return e&&r(t.prototype,e),n&&r(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function l(t){var n=function(t,n){if("object"!=e(t)||!t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var r=i.call(t,n||"default");if("object"!=e(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"==e(n)?n:n+""}function a(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}function c(t,e){return t.get(s(t,e))}function s(t,e,n){if("function"==typeof t?t===e:t.has(e))return arguments.length<3?e:n;throw new TypeError("Private element is not present on this object")}var u=function(t){document.querySelectorAll('.ea-fileupload input[type="file"]').forEach((function(t){new d(t)}))};window.addEventListener("DOMContentLoaded",u),document.addEventListener("ea.collection.item-added",u);var f=new WeakMap,h=new WeakSet,d=o((function t(e){var n,i;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),a(n=this,i=h),i.add(n),function(t,e,n){a(t,e),e.set(t,n)}(this,f,void 0),this.field=e,function(t,e,n){t.set(s(t,e),n)}(f,this,this.field.closest(".ea-fileupload")),this.field.addEventListener("change",s(h,this,y).bind(this));var r=s(h,this,m).call(this);r&&r.addEventListener("click",s(h,this,p).bind(this))}));function y(){var t=this;if(0!==this.field.files.length){var e,i=1===this.field.files.length?this.field.files[0].name:this.field.files.length+" "+this.field.getAttribute("data-files-label"),r=0,o=n(this.field.files);try{for(o.s();!(e=o.n()).done;){r+=e.value.size}}catch(t){o.e(t)}finally{o.f()}s(h,this,b).call(this).innerHTML=i,s(h,this,m).call(this).style.display="block",s(h,this,g).call(this).childNodes.forEach((function(e){e.nodeType===Node.TEXT_NODE&&s(h,t,g).call(t).removeChild(e)})),s(h,this,g).call(this).prepend(s(h,this,v).call(this,r))}}function p(){var e=this,n=c(f,this).querySelector("input[type=checkbox].form-check-input"),i=c(f,this).querySelector(".fileupload-list");n&&(n.checked=!0,n.click()),this.field.value="",s(h,this,b).call(this).innerHTML="",t(s(h,this,m).call(this),!0),s(h,this,g).call(this).childNodes.forEach((function(t){t.nodeType===Node.TEXT_NODE&&s(h,e,g).call(e).removeChild(t)})),null!==i&&t(i,!0)}function v(t){var e=Math.trunc(Math.floor(Math.log(t)/Math.log(1024)));return Math.trunc(t/Math.pow(1024,e))+["B","K","M","G","T","P","E","Z","Y"][e]}function b(){return c(f,this).querySelector(".custom-file-label")}function m(){return c(f,this).querySelector(".ea-fileupload-delete-btn")}function g(){return c(f,this).querySelector(".input-group-text")}})();