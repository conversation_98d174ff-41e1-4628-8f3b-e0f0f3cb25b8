<?php

namespace App\ApiResource\Praticien;

use ApiPlatform\Doctrine\Orm\State\Options;
use ApiPlatform\Metadata\ApiProperty;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\GetCollection;
use App\ApiResource\Base\BaseDto;
use App\ApiResource\Structure\EntiteJuridiqueDto;
use App\ApiResource\Structure\TenantDto;
use App\Entity\Praticien\AgentHopitalRole;
use App\State\EntityClassDtoStateProcessor;
use App\State\EntityToDtoStateProvider;

#[ApiResource(
    shortName: 'agent-tenant-role',
    operations: [
        new Get(),
        new GetCollection(),
    ],
    paginationItemsPerPage: 10,
    provider: EntityToDtoStateProvider::class,
    processor: EntityClassDtoStateProcessor::class,
    stateOptions: new Options(entityClass: AgentHopitalRole::class),
)]
class AgentHopitalRoleDto extends BaseDto
{

    #[ApiProperty(description: "Lien vers l'agent", example: "/api/agents/1")]
    public ?AgentDto $agent = null;

    #[ApiProperty(description: "Lien vers l'hopital", example: "/api/hopital/1")]
    public ?EntiteJuridiqueDto $hopital = null;

    #[ApiProperty(description: "Rôle attribué", example: "ADMIN")]
    public ?string $role = null;

    #[ApiProperty(description: "Date d'affectation", example: "2024-01-01T00:00:00")]
    public ?\DateTimeInterface $dateAffectation = null;
}
