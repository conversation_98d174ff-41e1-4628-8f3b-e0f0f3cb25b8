<?php

namespace App\Entity\Praticien;

use ApiPlatform\Doctrine\Orm\Filter\SearchFilter;
use ApiPlatform\Metadata\ApiFilter;
use App\Entity\Activite\Actes;
use App\Entity\Activite\GardesAstreintes;
use App\Entity\Activite\Liberal;
use App\Entity\Activite\Sigaps;
use App\Entity\Base\BaseEntity;
use App\Entity\Praticien\Trait\CollecteurTrait;
use App\Entity\Praticien\Trait\ConnectionStatsTrait;
use App\Entity\Praticien\Trait\LdapAttributesTrait;
use App\Entity\Praticien\Trait\NativeFieldTrait;
use App\Entity\Structure\EntiteJuridique;
use App\Repository\AgentRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;
use Symfony\Component\Security\Core\User\UserInterface;

/**
 * @prompt
 *
 */

#[ORM\Entity(repositoryClass: AgentRepository::class)]
#[ORM\Table(name: '`agent`')]
#[ORM\UniqueConstraint(name: 'UNIQ_IDENTIFIER_HRUSER', fields: ['hrUser'])]
#[ApiFilter(SearchFilter::class, properties: [
    'email' => 'partial',
    'nom' => 'partial',
    'prenom' => 'partial',
    'hrUser' => 'partial',
    'titre' => 'partial',
    'categorie' => 'partial',
    'fullName' => 'partial'
])]
class Agent extends BaseEntity implements UserInterface, PasswordAuthenticatedUserInterface
{
    use NativeFieldTrait;
    use CollecteurTrait;
    use LdapAttributesTrait;
    use ConnectionStatsTrait;

    #[ORM\Column(type: 'boolean' ,options: ['default'=>false])]
    protected ?bool $isLdapInitialized  = false;


    #[ORM\Column(length: 50, nullable: true)]
    private ?string $matricule = null; // A DAM field

    // les relation avec les autres entités
    #[ORM\ManyToOne(inversedBy: 'users', cascade: ['persist'])]
    #[ORM\JoinColumn(nullable: false)]
    private ?EntiteJuridique $hopital = null;

    #[ORM\OneToMany(targetEntity: AgentHopitalRole::class, mappedBy: "agent", cascade: ["persist", "remove"])]
    private Collection $hopitalRoles;

    #[ORM\OneToMany(targetEntity: GardesAstreintes::class, mappedBy: 'agent')]
    private Collection $gardesAstreintes;

    #[ORM\OneToMany(targetEntity: Liberal::class, mappedBy: 'agent')]
    private Collection $liberals;

    #[ORM\OneToMany(targetEntity: Sigaps::class, mappedBy: 'agent')]
    private Collection $sigaps;

    /**
     * @var Collection<int, Actes>
     */
    #[ORM\OneToMany(targetEntity: Actes::class, mappedBy: 'agent')]
    private Collection $actes;


    /**
     * @var Collection<int, AgentUfs>
     */
    #[ORM\OneToMany(targetEntity: AgentUfs::class, mappedBy: 'agent', cascade: ['persist', 'remove'])]
    private Collection $agentUfs;

    /**
     * @var list<string> The user roles
     */
    #[ORM\Column]
    private array $roles = [];

    public function __construct()
    {
        parent::__construct();

        $this->hopitalRoles = new ArrayCollection();
        $this->gardesAstreintes = new ArrayCollection();
        $this->liberals = new ArrayCollection();
        $this->sigaps = new ArrayCollection();
        $this->actes = new ArrayCollection();

        $this->agentUfs = new ArrayCollection();
    }

    public function getIsLdapInitialized(): ?bool
    {
        return $this->isLdapInitialized;
    }

    public function setIsLdapInitialized(?bool $isLdapInitialized): void
    {
        $this->isLdapInitialized = $isLdapInitialized;
    }


    public function getMatricule(): ?string
    {
        return $this->matricule;
    }

    public function setMatricule(?string $matricule): static
    {
        $this->matricule = $matricule;
        return $this;
    }

    public function getHopital(): ?EntiteJuridique
    {
        return $this->hopital;
    }

    public function setHopital(?EntiteJuridique $hopital): static
    {
        $this->hopital = $hopital;

        return $this;
    }

     public function getHopitalRoles(): Collection
    {
        return $this->hopitalRoles;
    }

    public function setHopitalRoles(Collection $hopitalRoles): void
    {
        $this->hopitalRoles = $hopitalRoles;
    }

    public function getAllRoles(): array
    {
        return array_unique($this->hopitalRoles->map(fn (AgentHopitalRole $tr) => $tr->getRole())->toArray());
    }
    // Ajoute cette méthode :
    public function __toString(): string
    {
        return $this->nom . ' ' . $this->prenom . ' (' . $this->email . ')';
    }

     public function getGardesAstreintes(): Collection
    {
        return $this->gardesAstreintes;
    }

    public function getLiberals(): Collection
    {
        return $this->liberals;
    }

    public function getSigaps(): Collection
    {
        return $this->sigaps;
    }

    public function getActes(): Collection
    {
        return $this->actes;
    }


    public function addGardesAstreinte(GardesAstreintes $gardesAstreinte): static
    {
        if (!$this->gardesAstreintes->contains($gardesAstreinte)) {
            $this->gardesAstreintes->add($gardesAstreinte);
            $gardesAstreinte->setAgent($this);
        }
        return $this;
    }

    public function removeGardesAstreinte(GardesAstreintes $gardesAstreinte): static
    {
        if ($this->gardesAstreintes->removeElement($gardesAstreinte)) {
            if ($gardesAstreinte->getAgent() === $this) {
                $gardesAstreinte->setAgent(null);
            }
        }
        return $this;
    }

    public function addLiberal(Liberal $liberal): static
    {
        if (!$this->liberals->contains($liberal)) {
            $this->liberals->add($liberal);
            $liberal->setAgent($this);
        }
        return $this;
    }

    public function removeLiberal(Liberal $liberal): static
    {
        if ($this->liberals->removeElement($liberal)) {
            if ($liberal->getAgent() === $this) {
                $liberal->setAgent(null);
            }
        }
        return $this;
    }

    public function addSigap(Sigaps $sigap): static
    {
        if (!$this->sigaps->contains($sigap)) {
            $this->sigaps->add($sigap);
            $sigap->setAgent($this);
        }
        return $this;
    }

    public function removeSigap(Sigaps $sigap): static
    {
        if ($this->sigaps->removeElement($sigap)) {
            if ($sigap->getAgent() === $this) {
                $sigap->setAgent(null);
            }
        }
        return $this;
    }

    public function addActe(Actes $acte): static
    {
        if (!$this->actes->contains($acte)) {
            $this->actes->add($acte);
            $acte->setAgent($this);
        }
        return $this;
    }

    public function removeActe(Actes $acte): static
    {
        if ($this->actes->removeElement($acte)) {
            if ($acte->getAgent() === $this) {
                $acte->setAgent(null);
            }
        }
        return $this;
    }

    /**
     * @return Collection<int, AgentUfs>
     */
    public function getAgentUfs(): Collection
    {
        return $this->agentUfs;
    }

    public function addAgentUf(AgentUfs $agentUf): static
    {
        if (!$this->agentUfs->contains($agentUf)) {
            $this->agentUfs->add($agentUf);
            $agentUf->setAgent($this);
        }
        return $this;
    }

    public function removeAgentUf(AgentUfs $agentUf): static
    {
        if ($this->agentUfs->removeElement($agentUf)) {
            if ($agentUf->getAgent() === $this) {
                $agentUf->setAgent(null);
            }
        }
        return $this;
    }

    /**
     * @see UserInterface
     */
    public function getRoles(): array
    {
        $roles = $this->roles;
        // guarantee every user at least has ROLE_USER
        $roles[] = 'ROLE_USER';

        return array_unique($roles);
    }

    public function setRoles(array $roles): static
    {
        $this->roles = $roles;
        return $this;
    }

    // Getters spéciaux pour EasyAdmin - Données de connexion
    public function getTotalConnections(): string
    {
        $stats = $this->getConnectionStats();
        return $stats['total_connections'] ?? '0';
    }

    public function getFirstConnection(): string
    {
        $stats = $this->getConnectionStats();
        return $stats['first_connection'] ?? 'Jamais connecté';
    }

    public function getLastConnection(): string
    {
        $stats = $this->getConnectionStats();
        return $stats['last_connection'] ?? 'Jamais connecté';
    }

    // Getters spéciaux pour EasyAdmin - Données LDAP
    public function getLdapSynchronized(): string
    {
        $ldap = $this->getLdapAttributes();
        return $ldap['status']['success'] ?? false ? 'Oui' : 'Non';
    }

    public function getLdapLastSync(): string
    {
        $ldap = $this->getLdapAttributes();
        return $ldap['status']['last_sync'] ?? 'Jamais synchronisé';
    }

    public function getLdapMessage(): string
    {
        $ldap = $this->getLdapAttributes();
        return $ldap['status']['message'] ?? 'Non synchronisé avec LDAP';
    }

    // Getter spécial pour EasyAdmin - Rôles dans les hôpitaux
    public function getHopitalRolesDisplay(): string
    {
        if ($this->hopitalRoles->isEmpty()) {
            return 'Aucun rôle défini';
        }

        $roles = [];
        foreach ($this->hopitalRoles as $hopitalRole) {
            $hopitalName = $hopitalRole->getHopital() ? $hopitalRole->getHopital()->getNom() : 'Hôpital inconnu';
            $roleName = $hopitalRole->getRole() ?? 'Rôle non défini';
            $roles[] = "• {$roleName} dans {$hopitalName}";
        }

        return implode("\n", $roles);
    }

    // Getter spécial pour EasyAdmin - UFs associées
    public function getAgentUfsDisplay(): string
    {
        if ($this->agentUfs->isEmpty()) {
            return 'Aucune UF associée';
        }

        $ufs = [];
        foreach ($this->agentUfs as $agentUf) {
            $ufsName = $agentUf->getUfs() ? $agentUf->getUfs()->getLibelle() : 'UF inconnue';
            $dateDebut = $agentUf->getDateDebut() ? $agentUf->getDateDebut()->format('d/m/Y') : 'Date inconnue';
            $dateFin = $agentUf->getDateFin() ? ' au ' . $agentUf->getDateFin()->format('d/m/Y') : ' (en cours)';
            $ufs[] = "• {$ufsName} (du {$dateDebut}{$dateFin})";
        }

        return implode("\n", $ufs);
    }

    /**
     * A string representation of this user
     *
     * @see UserInterface
     */
    public function getUserIdentifier(): string
    {
        return (string) $this->email;
    }

    /**
     * @see PasswordAuthenticatedUserInterface
     */
    public function getPassword(): ?string
    {
        return $this->password ?? null;
    }

    public function setPassword(string $password): static
    {
        $this->password = $password;
        return $this;
    }

    /**
     * @see UserInterface
     */
    public function eraseCredentials(): void
    {
        // If you store any temporary, sensitive data on the user, clear it here
        // $this->plainPassword = null;
    }
}
