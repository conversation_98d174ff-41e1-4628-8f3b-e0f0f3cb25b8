<?php

namespace App\Controller\Importation\Agent\Dto;

/**
 * Data Transfer Object pour l'importation des affectations d'agents dans les unités fonctionnelles.
 *
 * Il contient les propriétés fournies par la DAM pour chaque affectation d'agent.
 *
 * @see \App\Domain\Service\Importation\AffectationImportService::importAffectations()
 */
class AffectationAgentUfDto
{
    public ?string $uConnexion = null;

    public ?string $matricule = null;
    public ?string $dateDebut = null;
    public ?string $dateFin = null;
    public ?string $codeUf = null;
    public ?string $typeGrade = null;
    public ?string $libelleGrade = null;
    public ?string $rgt = null;
    public ?string $etpStatutaire = null;
    public ?string $tauxAffectation = null;
    public ?string $sommeEtp = null;
    public ?string $affectationPrincipale = null;
    public ?string $absences = null;
}