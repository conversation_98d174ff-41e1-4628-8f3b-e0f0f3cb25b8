<?php

namespace App\Domain\Service;

use App\Domain\Exception\Security\InsufficientRoleException;
use App\Domain\Exception\Security\TooManyLoginAttemptsException;
use App\Domain\Service\Security\LoginAttemptService;
use App\Domain\Service\Security\AuthCacheService;
use App\Entity\Praticien\Agent;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Exception\AuthenticationException;
use Symfony\Component\Security\Core\Exception\BadCredentialsException;
use Symfony\Component\Security\Core\Exception\UserNotFoundException;
use Symfony\Component\Security\Http\Authenticator\AbstractLoginFormAuthenticator;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\CsrfTokenBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\RememberMeBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\UserBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Credentials\PasswordCredentials;
use Symfony\Component\Security\Http\Authenticator\Passport\Passport;
use Symfony\Component\Security\Http\SecurityRequestAttributes;
use Symfony\Component\Security\Http\Util\TargetPathTrait;
use Symfony\Component\Validator\Constraints\Email;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Validator\ValidatorInterface;

/**
 * Authenticator spécialisé pour l'interface EasyAdmin
 * Gère l'authentification par formulaire avec session
 */
class AdminFormAuthenticator extends AbstractLoginFormAuthenticator
{
    use TargetPathTrait;

    public const LOGIN_ROUTE = 'app_login';

    public function __construct(
        private EntityManagerInterface $entityManager,
        private UrlGeneratorInterface $urlGenerator,
        private UserPasswordHasherInterface $passwordHasher,
        private LoginAttemptService $loginAttemptService,
        private ValidatorInterface $validator,
        private ?AuthCacheService $authCacheService = null,
        private ?RequestStack $requestStack = null
    ) {
    }

    public function authenticate(Request $request): Passport
    {
        $email = $request->getPayload()->getString('email');
        $password = $request->getPayload()->getString('password');
        $csrfToken = $request->getPayload()->getString('_token');

        // OPTIMISATION 1: Validation email simplifiée et plus rapide
        if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new BadCredentialsException('Adresse email invalide.');
        }

        // OPTIMISATION 2: Normalisation simple sans sanitization lourde
        $email = strtolower(trim($email));

        // OPTIMISATION 3: Rate limiting asynchrone (ne pas bloquer l'auth)
        $this->checkRateLimitingAsync($email);

        $request->getSession()->set(SecurityRequestAttributes::LAST_USERNAME, $email);

        return new Passport(
            new UserBadge($email, function ($userIdentifier) {
                // OPTIMISATION 4: Requête optimisée avec vérification de rôle intégrée
                $user = $this->findUserWithAdminRole($userIdentifier);

                if (!$user) {
                    throw new UserNotFoundException('Identifiants invalides ou droits insuffisants.');
                }

                return $user;
            }),
            new PasswordCredentials($password),
            [
                new CsrfTokenBadge('authenticate', $csrfToken),
                new RememberMeBadge(),
            ]
        );
    }

    /**
     * OPTIMISATION: Vérification rate limiting ultra-rapide avec cache Redis
     */
    private function checkRateLimitingAsync(string $email): void
    {
        // Fallback si les services ne sont pas disponibles
        if (!$this->authCacheService || !$this->requestStack) {
            try {
                $this->loginAttemptService->checkLoginAllowed($email);
            } catch (TooManyLoginAttemptsException $e) {
                error_log("Rate limiting triggered for: $email (fallback)");
                throw $e;
            }
            return;
        }

        // Récupérer l'IP de la requête
        $request = $this->getRequest();
        $ip = $request ? $request->getClientIp() : '127.0.0.1';

        // ULTRA-RAPIDE: Vérification depuis le cache Redis
        $rateLimitInfo = $this->authCacheService->isRateLimited($email, $ip);

        if (!$rateLimitInfo['allowed']) {
            error_log("Rate limiting triggered for: $email (cached)");
            throw new TooManyLoginAttemptsException($rateLimitInfo['timeUntilAllowed']);
        }
    }

    private function getRequest(): ?Request
    {
        // Helper pour récupérer la requête courante
        return $this->requestStack?->getCurrentRequest();
    }

    /**
     * OPTIMISATION: Cache Redis + requête PostgreSQL ultra-rapide
     */
    private function findUserWithAdminRole(string $email): ?Agent
    {
        // ULTRA-RAPIDE: Vérification depuis le cache Redis (si disponible)
        if ($this->authCacheService) {
            $cachedUser = $this->authCacheService->getCachedAdminUser($email);
            if ($cachedUser) {
                // Retourner l'agent depuis le cache (évite la requête DB)
                return $this->entityManager->getRepository(Agent::class)->find($cachedUser['id']);
            }
        }

        // Cache miss ou service indisponible : requête PostgreSQL optimisée
        $sql = "SELECT a.* FROM agent a
                WHERE a.email = :email
                AND a.roles::jsonb @> :adminRole
                LIMIT 1";

        $stmt = $this->entityManager->getConnection()->prepare($sql);
        $result = $stmt->executeQuery([
            'email' => $email,
            'adminRole' => '["ROLE_ADMIN"]'
        ]);

        $userData = $result->fetchAssociative();

        if (!$userData) {
            return null;
        }

        // Hydrater l'entité Agent depuis les données
        $agent = $this->entityManager->getRepository(Agent::class)->find($userData['id']);

        // CACHE: Stocker l'agent pour les prochaines connexions (si service disponible)
        if ($agent && $this->authCacheService) {
            $this->authCacheService->cacheAdminUser($email, $agent);
        }

        return $agent;
    }
    
    /**
     * Record failed login attempts and handle custom exceptions
     * OPTIMISATION: Enregistrement asynchrone
     */
    public function onAuthenticationFailure(Request $request, AuthenticationException $exception): Response
    {
        // OPTIMISATION: Enregistrement asynchrone des échecs
        if (!($exception instanceof TooManyLoginAttemptsException) &&
            !($exception instanceof InsufficientRoleException)) {
            $email = $request->getPayload()->getString('email', '');
            if (!empty($email)) {
                $this->recordLoginAttemptAsync($email, false);
            }
        }
        
        // For insufficient role exception, we want to keep the user on the login page
        // with a specific message about lacking the required role
        if ($exception instanceof InsufficientRoleException) {
            $request->getSession()->set(SecurityRequestAttributes::AUTHENTICATION_ERROR, $exception);
            
            // Redirect back to login page
            return new RedirectResponse($this->urlGenerator->generate(self::LOGIN_ROUTE));
        }
        
        return parent::onAuthenticationFailure($request, $exception);
    }

    /**
     * Record successful login attempts and redirect to dashboard
     * OPTIMISATION: Rôles déjà vérifiés dans authenticate()
     */
    public function onAuthenticationSuccess(Request $request, TokenInterface $token, string $firewallName): ?Response
    {
        // OPTIMISATION: Enregistrement asynchrone des tentatives de connexion
        $user = $token->getUser();
        if ($user instanceof Agent) {
            $this->recordLoginAttemptAsync($user->getEmail(), true);
        }

        if ($targetPath = $this->getTargetPath($request->getSession(), $firewallName)) {
            return new RedirectResponse($targetPath);
        }

        // Rediriger vers le dashboard EasyAdmin après connexion réussie
        return new RedirectResponse($this->urlGenerator->generate('admin'));
    }

    protected function getLoginUrl(Request $request): string
    {
        return $this->urlGenerator->generate(self::LOGIN_ROUTE);
    }

    /**
     * OPTIMISATION: Enregistrement ultra-rapide avec cache Redis
     */
    private function recordLoginAttemptAsync(string $email, bool $success): void
    {
        try {
            // Utiliser le cache Redis si disponible
            if ($this->authCacheService && $this->requestStack) {
                $request = $this->getRequest();
                $ip = $request ? $request->getClientIp() : '127.0.0.1';

                if ($success) {
                    // Succès : nettoyer le cache de rate limiting
                    $this->authCacheService->invalidateRateLimitCache($email, $ip);
                } else {
                    // Échec : enregistrer dans le cache Redis (ultra-rapide)
                    $this->authCacheService->cacheFailedAttempt($email, $ip);
                }
            }

            // Enregistrement DB (toujours faire)
            $this->entityManager->wrapInTransaction(function() use ($email, $success) {
                $this->loginAttemptService->recordLoginAttempt($email, $success);
            });
        } catch (\Exception $e) {
            // Log l'erreur mais ne pas faire échouer l'authentification
            error_log("Erreur enregistrement tentative de connexion pour $email: " . $e->getMessage());
        }
    }
}
