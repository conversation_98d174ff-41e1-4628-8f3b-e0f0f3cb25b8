<?php

namespace App\Mapper\GaredeAstreinte;

use App\ApiResource\Activite\GardesAstreintesDto;
use App\Entity\Activite\GardesAstreintes;
use App\Repository\AgentRepository;
use App\Repository\GardesAstreintesRepository;
use Doctrine\DBAL\Exception;
use Symfonycasts\MicroMapper\AsMapper;
use Symfonycasts\MicroMapper\MapperInterface;

#[AsMapper(from: GardesAstreintesDto::class, to: GardesAstreintes::class)]
class GardesAstreintesDtoToEntityMapper implements MapperInterface
{
    public function __construct(
        private GardesAstreintesRepository $gardesAstreintesRepository,
        private AgentRepository $agentRepository
    ) {}

    public function load(object $from, string $toClass, array $context): object
    {
        $dto = $from;
        assert($dto instanceof GardesAstreintesDto);

        // On récupère l'entité existante si un ID est fourni, sinon on en crée une nouvelle
        $garde = $dto->id ? $this->gardesAstreintesRepository->find($dto->id) : new GardesAstreintes();
        if (!$garde) {
            throw new Exception('Garde/Astreinte non trouvée');
        }

        return $garde;
    }

    public function populate(object $from, object $to, array $context): object
    {
        $dto = $from;
        $garde = $to;
        assert($dto instanceof GardesAstreintesDto);
        assert($garde instanceof GardesAstreintes);

        $garde->setDateDebut($dto->dateDebut);
        $garde->setDateFin($dto->dateFin);
        $garde->setTotalGarde($dto->totalGarde);
        $garde->setTotalAstreinte($dto->totalAstreinte);
        $garde->setDateGarde($dto->dateGarde);
        $garde->setTypeGarde($dto->typeGarde === null ? "Non reinseigner" : $dto->typeGarde);
        
        

        // Convertir le hrUser en entité Agent
        if ($dto->agentHrU) {
            $agent = $this->agentRepository->findByIdentifier($dto->agentHrU);
            if (!$agent) {
                throw new Exception('Agent non trouvé avec agentHrU : ' . $dto->agentHrU);
            }
            $garde->setAgent($agent);
        }

        // Génération automatique des champs non fournis par le client
        $garde->setValidFrom($dto->dateGarde);
        $garde->setValidTo(null);

        // Calcul du type de période
        $periodeType = \App\Domain\Enum\PeriodeType::MENSUEL;
        if ($dto->dateDebut && $dto->dateFin) {
            $interval = $dto->dateDebut->diff($dto->dateFin);
            $days = (int)$interval->format('%a');
            if ($days < 32) {
                $periodeType = \App\Domain\Enum\PeriodeType::HEBDOMADAIRE;
            } elseif ($days < 366) {
                $periodeType = \App\Domain\Enum\PeriodeType::MENSUEL;
            } else {
                $periodeType = \App\Domain\Enum\PeriodeType::ANNUEL;
            }
        }
        $garde->setPeriodeType($periodeType);

        // Source : si non fournie, on met l'ej de l'agent
        if (!empty($dto->source)) {
            $garde->setSource($dto->source);
        } else {
            $agent = null;
            if ($dto->agentHrU) {
                $agent = $this->agentRepository->findByIdentifier($dto->agentHrU);
            }
            $garde->setSource($agent ? $agent->getHopital()->getCode() : null);
        }

        return $garde;
    }
}
