<?php

namespace App\Tests\Domain\Service\Core;

use App\Domain\Service\Core\AgentFinderService;
use App\Entity\Praticien\Agent;
use App\Entity\Structure\EntiteJuridique;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Doctrine\ORM\QueryBuilder;
use Doctrine\ORM\Query;
use PHPUnit\Framework\MockObject\MockObject;

it('can be instantiated', function () {
    $entityManager = $this->createMock(EntityManagerInterface::class);
    
    $service = new AgentFinderService($entityManager);
    
    expect($service)->toBeInstanceOf(AgentFinderService::class);
});

it('finds agent by connexion and entity juridique', function () {
    // Create mocks
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $repository = $this->createMock(EntityRepository::class);
    $queryBuilder = $this->createMock(QueryBuilder::class);
    $query = $this->createMock(Query::class);
    $agent = $this->createMock(Agent::class);
    $entiteJuridique = $this->createMock(EntiteJuridique::class);
    
    // Configure mocks
    $entityManager->method('getRepository')
        ->with(Agent::class)
        ->willReturn($repository);
    
    $repository->method('createQueryBuilder')
        ->willReturn($queryBuilder);
    
    $queryBuilder->method('where')->willReturnSelf();
    $queryBuilder->method('andWhere')->willReturnSelf();
    $queryBuilder->method('setParameter')->willReturnSelf();
    $queryBuilder->method('getQuery')->willReturn($query);
    
    $query->method('getOneOrNullResult')
        ->willReturn($agent);
    
    // Create service and call method
    $service = new AgentFinderService($entityManager);
    $result = $service->findAgentByConnexionAndEj('U123456', $entiteJuridique);
    
    // Assertions
    expect($result)->toBe($agent);
});

it('returns null when agent not found by connexion', function () {
    // Create mocks
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $repository = $this->createMock(EntityRepository::class);
    $queryBuilder = $this->createMock(QueryBuilder::class);
    $query = $this->createMock(Query::class);
    $entiteJuridique = $this->createMock(EntiteJuridique::class);
    
    // Configure mocks
    $entityManager->method('getRepository')
        ->with(Agent::class)
        ->willReturn($repository);
    
    $repository->method('createQueryBuilder')
        ->willReturn($queryBuilder);
    
    $queryBuilder->method('where')->willReturnSelf();
    $queryBuilder->method('andWhere')->willReturnSelf();
    $queryBuilder->method('setParameter')->willReturnSelf();
    $queryBuilder->method('getQuery')->willReturn($query);
    
    $query->method('getOneOrNullResult')
        ->willReturn(null);
    
    // Create service and call method
    $service = new AgentFinderService($entityManager);
    $result = $service->findAgentByConnexionAndEj('U123456', $entiteJuridique);
    
    // Assertions
    expect($result)->toBeNull();
});

it('finds agent by matricule and entity juridique', function () {
    // Create mocks
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $repository = $this->createMock(EntityRepository::class);
    $queryBuilder = $this->createMock(QueryBuilder::class);
    $query = $this->createMock(Query::class);
    $agent = $this->createMock(Agent::class);
    $entiteJuridique = $this->createMock(EntiteJuridique::class);
    
    // Configure mocks
    $entityManager->method('getRepository')
        ->with(Agent::class)
        ->willReturn($repository);
    
    $repository->method('createQueryBuilder')
        ->willReturn($queryBuilder);
    
    $queryBuilder->method('where')->willReturnSelf();
    $queryBuilder->method('andWhere')->willReturnSelf();
    $queryBuilder->method('setParameter')->willReturnSelf();
    $queryBuilder->method('getQuery')->willReturn($query);
    
    $query->method('getOneOrNullResult')
        ->willReturn($agent);
    
    // Create service and call method
    $service = new AgentFinderService($entityManager);
    $result = $service->findAgentByMatriculeAndEj('MAT123', $entiteJuridique);
    
    // Assertions
    expect($result)->toBe($agent);
});

it('finds agent by identifier and entity juridique with hrUser type', function () {
    // Create mocks
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $repository = $this->createMock(EntityRepository::class);
    $queryBuilder = $this->createMock(QueryBuilder::class);
    $query = $this->createMock(Query::class);
    $agent = $this->createMock(Agent::class);
    $entiteJuridique = $this->createMock(EntiteJuridique::class);
    
    // Configure mocks
    $entityManager->method('getRepository')
        ->with(Agent::class)
        ->willReturn($repository);
    
    $repository->method('createQueryBuilder')
        ->willReturn($queryBuilder);
    
    $queryBuilder->method('where')->willReturnSelf();
    $queryBuilder->method('andWhere')->willReturnSelf();
    $queryBuilder->method('setParameter')->willReturnSelf();
    $queryBuilder->method('getQuery')->willReturn($query);
    
    $query->method('getOneOrNullResult')
        ->willReturn($agent);
    
    // Create service and call method
    $service = new AgentFinderService($entityManager);
    $result = $service->findAgentByIdentifierAndEj('U123456', $entiteJuridique, 'hrUser');
    
    // Assertions
    expect($result)->toBe($agent);
});

it('finds agent by identifier and entity juridique with matricule type', function () {
    // Create mocks
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $repository = $this->createMock(EntityRepository::class);
    $queryBuilder = $this->createMock(QueryBuilder::class);
    $query = $this->createMock(Query::class);
    $agent = $this->createMock(Agent::class);
    $entiteJuridique = $this->createMock(EntiteJuridique::class);
    
    // Configure mocks
    $entityManager->method('getRepository')
        ->with(Agent::class)
        ->willReturn($repository);
    
    $repository->method('createQueryBuilder')
        ->willReturn($queryBuilder);
    
    $queryBuilder->method('where')->willReturnSelf();
    $queryBuilder->method('andWhere')->willReturnSelf();
    $queryBuilder->method('setParameter')->willReturnSelf();
    $queryBuilder->method('getQuery')->willReturn($query);
    
    $query->method('getOneOrNullResult')
        ->willReturn($agent);
    
    // Create service and call method
    $service = new AgentFinderService($entityManager);
    $result = $service->findAgentByIdentifierAndEj('MAT123', $entiteJuridique, 'matricule');
    
    // Assertions
    expect($result)->toBe($agent);
});