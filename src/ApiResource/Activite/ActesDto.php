<?php

namespace App\ApiResource\Activite;

use ApiPlatform\Doctrine\Orm\Filter\SearchFilter;
use ApiPlatform\Doctrine\Orm\State\Options;
use ApiPlatform\Metadata\ApiFilter;
use ApiPlatform\Metadata\ApiProperty;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\GetCollection;
use ApiPlatform\Metadata\Link;
use ApiPlatform\Metadata\Post;
use App\ApiResource\Base\BaseDto;
use App\ApiResource\Praticien\AgentDto;
use App\ApiResource\Structure\UfsDto;
use App\Domain\Enum\PeriodeType;
use App\Domain\Filter\MultiFieldSearchFilter;
use App\Entity\Activite\Actes;
use App\Entity\Structure\Cr;
use App\Entity\Structure\Pole;
use App\Entity\Structure\Service;
use App\State\Batch\ActesBatchProcessor;
use App\State\EntityClassDtoStateProcessor;
use App\State\EntityToDtoStateProvider;
use App\State\Partials\ActesCrProvider;
use App\State\Partials\ActesMetadataProvider;
use App\State\Partials\ActesPoleProvider;
use App\State\Partials\ActesServiceProvider;

#[ApiResource(
    shortName: 'actes',
    operations: [
        new Get(),
        new GetCollection(
            uriTemplate: '/actes',
        ),
        new GetCollection(
            uriTemplate: '/{ejcode}/actes',
            uriVariables: [
                'ejcode' => new Link(
                    schema: ['type' => 'string'],
                    description: 'Code de l\'entité juridique',
                    required: true
                )
            ]
        ),
        new GetCollection(
            uriTemplate: '/actes/pole/{id}',
            uriVariables: [
                'id' => new Link(
                    fromProperty: 'id',
                    fromClass: Pole::class,
                    schema: ['type' => 'string'],
                    description: 'UUID du pôle',
                    required: true
                )
            ],
            description: 'Récupère tous les actes d\'un pôle spécifique',
            provider: ActesPoleProvider::class
        ),
        new GetCollection(
            uriTemplate: '/actes/cr/{id}',
            uriVariables: [
                'id' => new Link(
                    fromProperty: 'id',
                    fromClass: Cr::class,
                    schema: ['type' => 'string'],
                    description: 'UUID du CR (Centre de Responsabilité)',
                    required: true
                )
            ],
            description: 'Récupère tous les actes d\'un CR (Centre de Responsabilité) spécifique',
            provider: ActesCrProvider::class
        ),
        new GetCollection(
            uriTemplate: '/actes/service/{secode}/{id}',
            uriVariables: [
                'secode' => new Link(
                    schema: ['type' => 'string'],
                    description: 'Code du service (secode)',
                    required: true
                ),
                'id' => new Link(
                    fromProperty: 'id',
                    fromClass: Service::class,
                    schema: ['type' => 'string'],
                    description: 'UUID du service',
                    required: true
                )
            ],
            description: 'Récupère tous les actes d\'un service spécifique par code et UUID',
            provider: ActesServiceProvider::class
        ),
        new Post(
            formats: ['json' => ['application/json']]
        ),
        new Post(
            uriTemplate: '/{ejcode}/actes/batch',
            formats: ['json' => ['application/json']],
            uriVariables: [
                'ejcode' => new Link(
                    schema: ['type' => 'string'],
                    description: 'Code de l\'entité juridique',
                    required: true
                )
            ], // pour le traitement en batch CSV (juste pour accepter un tableau d'objets)
            processor: ActesBatchProcessor::class
        )
    ],
    cacheHeaders: [
        'max_age' => 3600,    // 1h côté client
        'shared_max_age' => 3600, // 1h côté proxy
        'public' => true,
        'vary' => ['Accept', 'Accept-Language']
    ],
    paginationEnabled: true,
    paginationItemsPerPage: 10,
    provider: EntityToDtoStateProvider::class,
    processor: EntityClassDtoStateProcessor::class,
    stateOptions: new Options(entityClass: Actes::class)
)]
#[ApiFilter(MultiFieldSearchFilter::class)]
class ActesDto extends BaseDto
{
    #[ApiProperty(description: "Code de l'acte médical", example: "CCAM123")]
    public ?string $code = null; // A retirer le type null pour obliger le CA de nous donne des actes avec code

    #[ApiProperty(description: "Description de l'acte médical", example: "Consultation cardiologique")]
    public ?string $description = null;

    #[ApiProperty(description: "Date de réalisation de l'acte", example: "2024-03-01T10:00:00"   )]
    public ?\DateTimeInterface $dateRealisation = null;

    #[ApiProperty(description: "Type d'acte", example: "CCAM",openapiContext: [
        'type'=>'string',
        'example'=>'CCAM',
        'enum'=>['CCAM','NGAP','LABO']
    ])]
    #[ApiFilter(SearchFilter::class,strategy: 'exact')]
    public ?string $typeActe =null;

    #[ApiProperty(description: "Nombre de réalisations de l'acte", example: 1)]
    public ?int $nombreDeRealisation = 1;

    #[ApiProperty(description: "Année de l'acte", example: 2024   )]
    public ?int $annee = null;

    #[ApiProperty(description: "Mois de l'acte", example: 3   )]
    public ?int $mois = null;

    #[ApiProperty(description: "Numéro d'intervention", example: "45904300")]
    #[ApiFilter(SearchFilter::class,strategy: 'exact')]
    public ?string $internum = null;

    #[ApiProperty(description: "Semaine ISO", example: 21)]
    public ?int $semaineIso = null;

    #[ApiProperty(description: "Type de venue", example: 1)]
    public ?int $typeVenue = null;

    #[ApiProperty(description: "Libellé du type de venue", example: "Consultation")]
    public ?string $libTypeVenue = null;


    #[ApiProperty(description: "Un numero de l'activité", example: "5")]
    #[ApiFilter(SearchFilter::class,strategy: 'exact')]
    public ?string $activite = null;

    #[ApiProperty(description: "Libellé de l'activité ", example: "circulation extracorporelle [CEC]")]
    public ?string $activiteLib = null;

    #[ApiProperty(description: "Lien vers l'Agent", example: "/api/agents/1"   )]
    #[ApiFilter(SearchFilter::class,properties: ['agent.hrUser'=>'exact'])]
    public  $agent = null;

    #[ApiProperty(description: "Lien vers l'UF principale", example: "/api/ufs/1")]
    public  $ufPrincipal = null;

    #[ApiProperty(description: "Lien vers l'UF de demande", example: "/api/ufs/1")]
    public  $ufDemande = null;

    #[ApiProperty(description: "Lien vers l'UF d'intervention", example: "/api/ufs/1")]
    #[ApiFilter(SearchFilter::class,properties: ['ufIntervention.ufcode'=>'exact'])]
    public  $ufIntervention = null;

    #[ApiProperty(description: "ICR A", example: 51)]
    public ?int $icrA = null;

    #[ApiProperty(description: "Coefficient", example: 0)]
    public ?float $coefficient = null;

    #[ApiProperty(description: "Lettre coefficient", example: "")]
    public ?string $lettreCoef = null;

    // Champs du trait HorodatageTrait
    #[ApiProperty(description: "Date de début de validité", example: "2024-01-01T00:00:00"   )]
    public ?\DateTimeInterface $validFrom = null;

    #[ApiProperty(description: "Date de fin de validité", example: "2024-12-31T23:59:59"   )]
    public ?\DateTimeInterface $validTo = null;

    #[ApiProperty(description: "Type de période", example: "HEBDOMADAIRE")]
    public string $periodeType = PeriodeType::HEBDOMADAIRE;

    #[ApiProperty(description: "Source des données", example: "Système externe"   )]
    public ?string $source = null;
}
