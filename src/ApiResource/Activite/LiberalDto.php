<?php

namespace App\ApiResource\Activite;

use ApiPlatform\Doctrine\Orm\Filter\SearchFilter;
use ApiPlatform\Doctrine\Orm\State\Options;
use ApiPlatform\Metadata\ApiFilter;
use ApiPlatform\Metadata\ApiProperty;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\GetCollection;
use ApiPlatform\Metadata\Link;
use ApiPlatform\Metadata\Post;
use App\ApiResource\Base\BaseDto;
use App\Domain\Enum\PeriodeType;
use App\Entity\Activite\Liberal;
use App\State\Batch\LiberalBatchProcessor;
use App\State\EntityClassDtoStateProcessor;
use App\State\EntityToDtoStateProvider;

#[ApiResource(
    shortName: 'liberal',
    operations: [
        new Get(),
        new GetCollection(
            uriTemplate: '/liberals'
        ),
        new GetCollection(
            uriTemplate: '/{ejcode}/liberals',
            uriVariables: [
                'ejcode' => new Link(
                    schema: ['type' => 'string'],
                    description: 'Code de l\'entité juridique',
                    required: true
                )
            ]
        ),
        new Post(
            formats: ['json' => ['application/json']]
        ),
        new Post(
            uriTemplate: '/liberal/batch',
            formats: ['json' => ['application/json']],
            processor: LiberalBatchProcessor::class // pour le traitement en batch CSV (juste pour accepter un tableau d'objets)
        )
    ],
    paginationItemsPerPage: 10,
    provider: EntityToDtoStateProvider::class,
    processor: EntityClassDtoStateProcessor::class,
    stateOptions: new Options(entityClass: Liberal::class),
)]
#[ApiFilter(SearchFilter::class, properties: [
    'typeActe'=>'exact',
    'agent.hrUser' => 'exact'  // Filtre sur le hrUser de l'agent lié
])]
class LiberalDto extends BaseDto
{
    #[ApiProperty(description: "Code de l'acte", example: "ACT123")]
    #[ApiFilter(SearchFilter::class,strategy: 'exact')]
    public ?string $codeActe = null;
    #[ApiProperty(description: "Nom de l'acte libéral", example: "Consultation spécialisée")]
    public ?string $nomActe = null;

    #[ApiProperty(description: "Type d'acte libéral", example: "Consultation")]
    public string $typeActe;

    #[ApiProperty(description: "Date de réalisation de l'acte", example: "2024-03-15T10:30:00")]
    public ?\DateTimeInterface $dateRealisation = null;

    #[ApiProperty(description: "Date de début de l'acte", example: "2024-03-01T08:00:00")]
    public ?\DateTimeInterface $dateDebut = null;

    #[ApiProperty(description: "Date de fin de l'acte", example: "2024-03-01T09:00:00")]
    public ?\DateTimeInterface $dateFin = null;

    #[ApiProperty(description: "Tarif de l'acte", example: "150.00")]
    public ?string $tarif = null;

    #[ApiProperty(description: "Agent lié à l'acte libéral, transmis par hrUser", example: "u12345")]
    public ?string $agentHrU = null;

    #[ApiProperty(description: "Dernière affectation connue de l'agent", example: "Service de cardiologie", readable: true, writable: false)]
    public ?string $derniereAffectionDeAgent = null;

    #[ApiProperty(description: "Nom, prénom et titre de l'agent", example: "Dr. John Doe", readable: true, writable: false)]
    public ?string $agentNomPrenomTitre = null;



    // Champs du trait HorodatageTrait
    #[ApiProperty(description: "Date de début de validité", readable: true, writable: false, example: "2024-01-01T00:00:00")]
    public ?\DateTimeInterface $validFrom = null;

    #[ApiProperty(description: "Date de fin de validité", readable: true, writable: false, example: "2024-12-31T23:59:59")]
    public ?\DateTimeInterface $validTo = null;

    #[ApiProperty(description: "Type de période", readable: true, writable: false, example: "HEBDOMADAIRE")]
    public string $periodeType = PeriodeType::HEBDOMADAIRE;

    #[ApiProperty(description: "Source des données", readable: true, writable: false, example: "CNAM")]
    public ?string $source = null;
}
