<?php

namespace App\Factory\Activite;

use App\Domain\Enum\PeriodeType;
use App\Entity\Activite\Sigaps;
use App\Factory\Praticien\AgentFactory;
use Zenstruck\Foundry\Persistence\PersistentProxyObjectFactory;

/**
 * @extends PersistentProxyObjectFactory<Sigaps>
 */
final class SigapsFactory extends PersistentProxyObjectFactory
{
    public function __construct()
    {
    }

    public static function class(): string
    {
        return Sigaps::class;
    }

    protected function defaults(): array|callable
    {
        $validFrom = self::faker()->dateTimeBetween('-3 years', '-6 months');
        $validTo = (self::faker()->boolean(70)) ? self::faker()->dateTimeBetween($validFrom, 'now') : null;
        
        $dateDebut = self::faker()->dateTimeBetween('-1 year', 'now');
        $dateFin = self::faker()->dateTimeBetween($dateDebut, '+1 year');

        // Génération d'une répartition par catégorie réaliste
        $repartitionParCategorie = [
            'A+' => self::faker()->numberBetween(0, 5),
            'A' => self::faker()->numberBetween(5, 15),
            'B' => self::faker()->numberBetween(10, 25),
            'C' => self::faker()->numberBetween(5, 20),
            'D' => self::faker()->numberBetween(0, 10),
        ];

        return [
            'agent' => AgentFactory::random(),
            'dateDebut' => $dateDebut,
            'dateFin' => $dateFin,
            'score' => self::faker()->randomFloat(2, 10, 500),
            'repartition_par_categorie' => $repartitionParCategorie,
            'categorie' => self::faker()->randomElement(['A+', 'A', 'B', 'C', 'D']),
            'nombre_publication' => self::faker()->numberBetween(0, 50),
            'validFrom' => $validFrom,
            'validTo' => $validTo,
            'periodeType' => self::faker()->randomElement(PeriodeType::VALUES),
            'source' => self::faker()->optional()->randomElement(['CNAM', 'RPPS', 'MANUAL']),
        ];
    }

    protected function initialize(): static
    {
        return $this;
    }
}
