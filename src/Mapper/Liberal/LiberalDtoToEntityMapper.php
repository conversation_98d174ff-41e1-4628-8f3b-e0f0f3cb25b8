<?php

namespace App\Mapper\Liberal;

use App\ApiResource\Activite\LiberalDto;
use App\Entity\Activite\Liberal;
use App\Repository\AgentRepository;
use App\Repository\LiberalRepository;
use App\Repository\PraticienRepository;
use Doctrine\DBAL\Exception;
use Symfonycasts\MicroMapper\AsMapper;
use Symfonycasts\MicroMapper\MapperInterface;

#[AsMapper(from: LiberalDto::class, to: Liberal::class)]
class LiberalDtoToEntityMapper implements MapperInterface
{
    public function __construct(
        private LiberalRepository $liberalRepository,
        private AgentRepository $agentRepository
    ) {}

    public function load(object $from, string $toClass, array $context): object
    {
        $dto = $from;
        assert($dto instanceof LiberalDto);

        // On récupère l'entité existante si un ID est fourni, sinon on en crée une nouvelle
        $liberal = $dto->id ? $this->liberalRepository->find($dto->id) : new Liberal();
        if (!$liberal) {
            throw new Exception('Activité libéral non trouvé');
        }

        return $liberal;
    }

    public function populate(object $from, object $to, array $context): object
    {
        $dto = $from;
        $liberal = $to;
        assert($dto instanceof LiberalDto);
        assert($liberal instanceof Liberal);

        $liberal->setNomActe($dto->nomActe);
        $liberal->setDateRealisation($dto->dateRealisation);
        $liberal->setDateDebut($dto->dateDebut);
        $liberal->setDateFin($dto->dateFin);
        $liberal->setTarif($dto->tarif);
        $liberal->setCodeActe($dto->codeActe);
//        $liberal->setTypeActe($dto->typeActe);
        $liberal->setTypeActe($dto->typeActe === null ? "Non reinseigner" : $dto->typeActe);


        // Champs du trait HorodatageTrait
        $liberal->setPeriodeType($dto->periodeType);
        $liberal->setSource($dto->source);

        // Convertir le hrUser en entité Agent
        if ($dto->agentHrU) {
            $agent = $this->agentRepository->findByIdentifier($dto->agentHrU);
            if (!$agent) {
                throw new Exception('Agent non trouvé avec hrUser : ' . $dto->agentHrU);
            }
            $liberal->setAgent($agent);
        }

        // Génération automatique des champs non fournis par le client
        // Dans la reponse POST (vous verrez juste les champs transmis pas d'inquietude ceux generees automatiquement seront bien en GET)
        $liberal->setValidFrom($dto->dateRealisation);
        $liberal->setValidTo(null);
        // Calcul du type de période
        $periodeType = \App\Domain\Enum\PeriodeType::HEBDOMADAIRE;
        if ($dto->dateDebut && $dto->dateFin) {
            $interval = $dto->dateDebut->diff($dto->dateFin);
            $days = (int)$interval->format('%a');
            if ($days < 32) {
                $periodeType = \App\Domain\Enum\PeriodeType::HEBDOMADAIRE;
            } elseif ($days < 366) {
                $periodeType = \App\Domain\Enum\PeriodeType::MENSUEL;
            } else {
                $periodeType = \App\Domain\Enum\PeriodeType::ANNUEL;
            }
        }
        $liberal->setPeriodeType($periodeType);
        // Source : si non fournie, on met l'ej de l'agent
        if (!empty($dto->source)) {
            $liberal->setSource($dto->source);
        } else {
            $agent = null;
            if ($dto->agentHrU) {
                $agent = $this->agentRepository->findByIdentifier($dto->agentHrU);
            }
            $liberal->setSource($agent ? $agent->getHopital()->getCode() : null);
        }

        return $liberal;
    }
}
