<?php

namespace App\Entity\Structure;

use App\Entity\Base\BaseEntity;
use App\Repository\TenantRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: TenantRepository::class)]
class Tenant extends BaseEntity
{
    #[ORM\Column(length: 50, unique: true)]
    private ?string $code = null;

    #[ORM\Column(length: 255)]
    private ?string $nom = null;

    #[ORM\Column(length: 255)]
    private ?string $description = null;

    /**
     * @var Collection<int, EntiteJuridique>
     */
    #[ORM\OneToMany(targetEntity: EntiteJuridique::class, mappedBy: 'tenant', orphanRemoval: true)]
    private Collection $hopitals;

    public function __construct()
    {
        parent::__construct();
        $this->hopitals = new ArrayCollection();
        $this->dateCreation = new \DateTime('now');
    }

    public function getNom(): ?string
    {
        return $this->nom;
    }

    public function setNom(string $nom): static
    {
        $this->nom = $nom;

        return $this;
    }

    public function getCode(): ?string
    {
        return $this->code;
    }

    public function setCode(string $code): static
    {
        $this->code = $code;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(string $description): static
    {
        $this->description = $description;

        return $this;
    }

    /**
     * @return Collection<int, EntiteJuridique>
     */
    public function getHopitals(): Collection
    {
        return $this->hopitals;
    }

    public function addHopital(EntiteJuridique $hopital): static
    {
        if (!$this->hopitals->contains($hopital)) {
            $this->hopitals->add($hopital);
            $hopital->setTenant($this);
        }

        return $this;
    }

    public function removeHopital(EntiteJuridique $hopital): static
    {
        if ($this->hopitals->removeElement($hopital)) {
            // set the owning side to null (unless already changed)
            if ($hopital->getTenant() === $this) {
                $hopital->setTenant(null);
            }
        }

        return $this;
    }

    public function __toString(): string
    {
        return sprintf(
            '%s (%s)',
            $this->nom,
            strlen($this->description) > 10 ? substr($this->description, 0, 5) . '...' : $this->description
        );
    }
}
