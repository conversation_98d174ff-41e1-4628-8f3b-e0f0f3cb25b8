{% extends '@EasyAdmin/page/content.html.twig' %}

{% block main %}
    <div class="content-wrapper">
        <div class="content">
            <h1 class="title">Bienvenue dans l'Administration Supra, {{ user_name }}</h1>
            <p class="text-muted mb-4">Cette page vous guide dans l'utilisation de l'interface d'administration de Supra.</p>

            <!-- Section Métier -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h2 class="card-title">
                        <i class="fas fa-briefcase"></i> Guide Métier
                    </h2>
                    <p class="card-subtitle">Comprendre et utiliser l'administration Supra</p>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h3><i class="fas fa-question-circle text-primary"></i> Qu'est-ce que Supra ?</h3>
                            <p>Supra est une application interne du CHRU de Nancy pour la consultation des données d'activités médicales. Elle permet de :</p>
                            <ul>
                                <li>Gérer la structure organisationnelle des établissements</li>
                                <li>Suivre les activités médicales (actes, gardes, libéral, publications)</li>
                                <li>Administrer les agents et leurs rôles</li>
                                <li>Importer et exporter des données d'activité</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h3><i class="fas fa-sitemap text-success"></i> Architecture Multi-Tenant</h3>
                            <div class="alert alert-info">
                                <p><strong>Structure hiérarchique :</strong></p>
                                <ul>
                                    <li><strong>Tenant</strong> → Peut gérer plusieurs Entités Juridiques</li>
                                    <li><strong>Entités Juridiques</strong> (hôpitaux) → Contiennent des Pôles</li>
                                    <li><strong>Pôles</strong> → Regroupent des Services</li>
                                    <li><strong>Services</strong> → Contiennent des CRs</li>
                                    <li><strong>CRs</strong> (Centres de Responsabilité) → Regroupent des UFs</li>
                                    <li><strong>UFs</strong> (Unités Fonctionnelles) → Unités de base</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <div class="row mt-4">
                        <div class="col-md-6">
                            <h3><i class="fas fa-user-tag text-warning"></i> Rôles et Permissions</h3>
                            <div class="table-responsive">
                                <table class="table table-sm table-bordered">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>Niveau</th>
                                            <th>Rôle</th>
                                            <th>Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td class="text-center"><span class="badge bg-danger">5</span></td>
                                            <td>FIFAP/CME</td>
                                            <td>Directeurs - Accès complet</td>
                                        </tr>
                                        <tr>
                                            <td class="text-center"><span class="badge bg-warning">4</span></td>
                                            <td>MANAGER</td>
                                            <td>Gestion globale</td>
                                        </tr>
                                        <tr>
                                            <td class="text-center"><span class="badge bg-info">3</span></td>
                                            <td>CHEF_DE_POLE</td>
                                            <td>Gestion d'un pôle</td>
                                        </tr>
                                        <tr>
                                            <td class="text-center"><span class="badge bg-primary">2</span></td>
                                            <td>ADMIN</td>
                                            <td>Administration locale</td>
                                        </tr>
                                        <tr>
                                            <td class="text-center"><span class="badge bg-secondary">1</span></td>
                                            <td>USER</td>
                                            <td>Consultation uniquement</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h3><i class="fas fa-tasks text-info"></i> Guide de démarrage rapide</h3>
                            <div class="alert alert-success">
                                <ol>
                                    <li><strong>Explorez la structure</strong> - Commencez par consulter les Entités Juridiques et leur organisation</li>
                                    <li><strong>Gérez les agents</strong> - Vérifiez les agents et leurs affectations</li>
                                    <li><strong>Consultez les activités</strong> - Explorez les différents types d'activités médicales</li>
                                    <li><strong>Utilisez les importations</strong> - Importez de nouvelles données via l'interface dédiée</li>
                                </ol>
                            </div>
                            <div class="mt-4">
                                <h4>Liens rapides</h4>
                                <div class="d-flex flex-wrap gap-2">
                                    <a href="{{ ea_url().setController('App\\\\Controller\\\\Admin\\\\EntiteJuridiqueCrudController') }}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-hospital"></i> Entités Juridiques
                                    </a>
                                    <a href="{{ ea_url().setController('App\\\\Controller\\\\Admin\\\\AgentCrudController') }}" class="btn btn-sm btn-outline-info">
                                        <i class="fas fa-user"></i> Agents
                                    </a>
                                    <a href="{{ ea_url().setController('App\\\\Controller\\\\Admin\\\\ImportationCrudController') }}" class="btn btn-sm btn-outline-success">
                                        <i class="fas fa-sync"></i> Importations
                                    </a>
                                    <a href="{{ ea_url().setController('App\\\\Controller\\\\Admin\\\\ActesCrudController') }}" class="btn btn-sm btn-outline-warning">
                                        <i class="fas fa-file-medical"></i> Actes
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section Intégrateur -->
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h2 class="card-title">
                        <i class="fas fa-cogs"></i> Guide Intégrateur
                    </h2>
                    <p class="card-subtitle">Intégrer et configurer de nouveaux éléments dans Supra</p>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h3><i class="fas fa-plus-circle text-success"></i> Ajouter une nouvelle Entité Juridique</h3>
                            <div class="alert alert-secondary">
                                <ol>
                                    <li><strong>Accédez à la section Entités Juridiques</strong> dans le menu Structure</li>
                                    <li><strong>Cliquez sur "Ajouter une Entité Juridique"</strong> pour ouvrir le formulaire</li>
                                    <li><strong>Renseignez les informations requises</strong> :
                                        <ul>
                                            <li>Nom et code de l'entité</li>
                                            <li>Sélectionnez le Tenant parent</li>
                                            <li>Définissez les paramètres spécifiques</li>
                                        </ul>
                                    </li>
                                    <li><strong>Validez la création</strong> et vérifiez l'intégration dans la structure</li>
                                    <li><strong>Configurez les pôles et services</strong> associés à cette nouvelle entité</li>
                                </ol>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h3><i class="fas fa-cogs text-primary"></i> Architecture technique</h3>
                            <p>L'administration Supra est basée sur :</p>
                            <ul>
                                <li><strong>Symfony</strong> - Framework PHP pour le backend</li>
                                <li><strong>EasyAdmin</strong> - Bundle d'administration</li>
                                <li><strong>Doctrine</strong> - ORM pour la gestion des entités</li>
                                <li><strong>Twig</strong> - Moteur de templates</li>
                            </ul>
                            <div class="alert alert-warning mt-3">
                                <h4><i class="fas fa-exclamation-triangle"></i> Points d'attention</h4>
                                <ul>
                                    <li>Respectez l'architecture multi-tenant lors de l'ajout d'entités</li>
                                    <li>Vérifiez les contraintes de sécurité et les permissions</li>
                                    <li>Testez les importations de données avant mise en production</li>
                                    <li>Consultez la documentation technique pour plus de détails</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h3><i class="fas fa-question-circle text-info"></i> FAQ Intégrateur</h3>
                            <div class="accordion" id="integratorFaqAccordion">
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="headingOne">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="false" aria-controls="collapseOne">
                                            Comment étendre les fonctionnalités d'une entité existante ?
                                        </button>
                                    </h2>
                                    <div id="collapseOne" class="accordion-collapse collapse" aria-labelledby="headingOne" data-bs-parent="#integratorFaqAccordion">
                                        <div class="accordion-body">
                                            Pour étendre une entité, modifiez la classe correspondante dans <code>src/Entity</code>, ajoutez les propriétés et méthodes nécessaires, puis mettez à jour le contrôleur CRUD associé dans <code>src/Controller/Admin</code>. N'oubliez pas de créer une migration avec <code>php bin/console make:migration</code> et de l'appliquer.
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="headingTwo">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                                            Comment personnaliser l'affichage d'une entité dans l'admin ?
                                        </button>
                                    </h2>
                                    <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="headingTwo" data-bs-parent="#integratorFaqAccordion">
                                        <div class="accordion-body">
                                            Modifiez le contrôleur CRUD correspondant dans <code>src/Controller/Admin</code>. Utilisez les méthodes <code>configureFields()</code>, <code>configureActions()</code> et <code>configureFilters()</code> pour personnaliser l'interface. Pour des personnalisations avancées, créez des templates Twig spécifiques dans <code>templates/admin/field</code>.
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="headingThree">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                                            Comment gérer les permissions et la sécurité ?
                                        </button>
                                    </h2>
                                    <div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="headingThree" data-bs-parent="#integratorFaqAccordion">
                                        <div class="accordion-body">
                                            La sécurité est gérée via le système de rôles de Symfony. Consultez <code>config/packages/security.yaml</code> pour la configuration globale. Les permissions spécifiques sont définies dans les voters (<code>src/Security/Voter</code>) et appliquées dans les contrôleurs. Respectez toujours la hiérarchie des rôles définie dans l'application.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact et Support -->
            <div class="alert alert-info mt-4">
                <div class="d-flex align-items-center">
                    <div>
                        <i class="fas fa-headset fa-2x me-3"></i>
                    </div>
                    <div>
                        <h4 class="alert-heading">Besoin d'aide ?</h4>
                        <p class="mb-0">Pour toute question ou assistance, contactez l'équipe support à <strong><EMAIL></strong> ou consultez la documentation complète.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}