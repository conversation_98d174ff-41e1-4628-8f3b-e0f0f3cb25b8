# =============================================================================
# SUPRA - Configuration Cron Linux
# =============================================================================
# Ce fichier contient toutes les tâches cron pour l'application SUPRA
# Pour installer : crontab < supra-cron.txt
# Pour voir les crons actifs : crontab -l
# =============================================================================

# Variables d'environnement
SHELL=/bin/bash
PATH=/usr/local/sbin:/usr/local/bin:/sbin:/bin:/usr/sbin:/usr/bin
MAILTO=<EMAIL>

# Chemin vers l'application SUPRA
SUPRA_PATH=/path/to/supra/bin/console
PHP_BIN=/usr/bin/php

# =============================================================================
# IMPORTS MENSUELS (5 du mois) - Ordre logique des dépendances
# =============================================================================

# Import de la structure organisationnelle - 5 du mois à 3h00
# Pôles, Services, CRs, UFs (doit être fait en premier)
0 3 5 * * $PHP_BIN $SUPRA_PATH app:import:structure >> /var/log/supra-structure-import.log 2>&1

# Import des agents - 5 du mois à 4h00
# Praticiens, personnel médical et non-médical (après la structure)
0 4 5 * * $PHP_BIN $SUPRA_PATH app:import:agent >> /var/log/supra-agent-import.log 2>&1

# Import automatique des affectations - 5 du mois à 6h00
# Traitement des fichiers CSV déposés automatiquement (après agents et structure)
0 6 5 * * $PHP_BIN $SUPRA_PATH app:affectation:auto-import >> /var/log/supra-affectation-auto-import.log 2>&1

# =============================================================================
# IMPORTS QUOTIDIENS
# =============================================================================

# Import des actes - Tous les jours à 7h00
# Utilise la logique J-7 par défaut du code (après tous les autres imports)
0 7 * * * $PHP_BIN $SUPRA_PATH app:import:actes >> /var/log/supra-actes-import.log 2>&1

# =============================================================================
# MAINTENANCE ET NETTOYAGE
# =============================================================================

# Nettoyage des logs anciens - 1er du mois à 1h00
# Supprime les logs de plus de 30 jours
0 1 1 * * find /var/log/supra-*.log -mtime +30 -delete >> /var/log/supra-cleanup.log 2>&1

# Cache clear Symfony - Tous les dimanche à 5h00
# Nettoyage préventif du cache
0 5 * * 0 $PHP_BIN $SUPRA_PATH cache:clear --env=prod >> /var/log/supra-cache-clear.log 2>&1

# =============================================================================
# NOTES
# =============================================================================
# - Tous les logs sont redirigés vers /var/log/supra-*.log
# - Les erreurs sont capturées avec 2>&1
# - Variables d'environnement définies en haut du fichier
# - Notifications d'erreur gérées par l'application via email
# - Pour activer : sudo crontab < supra-cron.txt
# - Pour éditer : sudo crontab -e
# =============================================================================
