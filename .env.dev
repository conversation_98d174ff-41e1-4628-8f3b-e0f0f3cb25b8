###> Environment: Development (Shared) ###
APP_ENV=dev
APP_DEBUG=true
APP_SECRET=dev_shared_secret_change_in_production

## For production, generate a secure key with:
## php bin/console secrets:generate-keys
## Consider using Symfony's secrets management for sensitive information:
## https://symfony.com/doc/current/configuration/secrets.html

###> Database Development ###
DATABASE_USER=supra_dev
DATABASE_PASSWORD=dev_secure_password
DATABASE_HOST=db-dev.chu-nancy.fr
DATABASE_PORT=5432
DATABASE_NAME=supra_dev

DATABASE_URL="postgresql://${DATABASE_USER}:${DATABASE_PASSWORD}@${DATABASE_HOST}:${DATABASE_PORT}/${DATABASE_NAME}?serverVersion=15&charset=utf8"

###> JWT Development ###
JWT_PASSPHRASE=dev_jwt_passphrase_secure
# JWT_SECRET is defined in .env.dev.local
JWT_SECRET_KEY=%kernel.project_dir%/config/jwt/private.pem
JWT_PUBLIC_KEY=%kernel.project_dir%/config/jwt/public.pem

###> Cache Development ###
CACHE_DSN=redis://redis-dev.chu-nancy.fr:6379
API_CACHE_TTL=300

###> Mail Development ###
MAILER_DSN=smtp://mail-dev.chu-nancy.fr:587?encryption=tls&auth_mode=login&username=<EMAIL>&password=dev_mail_password
MAILER_FROM_EMAIL=<EMAIL>

###> LDAP Development ###
LDAP_HOST=ldap://k12adc01-dev.chu-nancy.fr:389
LDAP_DOMAIN=U001DEV
LDAP_BASE_DN=DC=u001dev,DC=local

###> Services Development ###
DATA_INTEGRATOR_BASE_URI=http://data-integrator-dev.chu-nancy.fr:3000/api

###> CORS Development ###
CORS_ALLOW_ORIGIN='^https?://(dev\.supra\.chu-nancy\.fr|localhost|127\.0\.0\.1)(:[0-9]+)?$'