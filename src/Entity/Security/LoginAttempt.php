<?php

namespace App\Entity\Security;

use App\Repository\Security\LoginAttemptRepository;
use Doctrine\ORM\Mapping as ORM;

/**
 * Entity to track login attempts for rate limiting and security monitoring
 */
#[ORM\Entity(repositoryClass: LoginAttemptRepository::class)]
#[ORM\Table(name: 'security_login_attempt')]
#[ORM\Index(columns: ['ip_address'], name: 'idx_login_ip')]
#[ORM\Index(columns: ['email'], name: 'idx_login_email')]
#[ORM\Index(columns: ['timestamp'], name: 'idx_login_timestamp')]
class LoginAttempt
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    /**
     * IP address of the login attempt
     */
    #[ORM\Column(type: 'string', length: 45)]
    private string $ipAddress;

    /**
     * Email address used in the login attempt
     */
    #[ORM\Column(type: 'string', length: 180)]
    private string $email;

    /**
     * Timestamp of the login attempt
     */
    #[ORM\Column(type: 'datetime_immutable')]
    private \DateTimeImmutable $timestamp;

    /**
     * Whether the login attempt was successful
     */
    #[ORM\Column(type: 'boolean')]
    private bool $success = false;

    /**
     * User agent of the client making the login attempt
     */
    #[ORM\Column(type: 'text', nullable: true)]
    private ?string $userAgent = null;

    public function __construct(string $ipAddress, string $email, ?string $userAgent = null)
    {
        $this->ipAddress = $ipAddress;
        $this->email = $email;
        $this->userAgent = $userAgent;
        $this->timestamp = new \DateTimeImmutable();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getIpAddress(): string
    {
        return $this->ipAddress;
    }

    public function getEmail(): string
    {
        return $this->email;
    }

    public function getTimestamp(): \DateTimeImmutable
    {
        return $this->timestamp;
    }

    public function isSuccess(): bool
    {
        return $this->success;
    }

    public function setSuccess(bool $success): self
    {
        $this->success = $success;
        return $this;
    }

    public function getUserAgent(): ?string
    {
        return $this->userAgent;
    }
}