<?php

namespace App\Tests\Security;

use App\Domain\Service\JwtTokenService;
use App\Security\ApiTokenHandler;
use Psr\Log\LoggerInterface;
use Symfony\Component\Security\Core\Exception\BadCredentialsException;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\UserBadge;

/**
 * Tests for the ApiTokenHandler
 * 
 * This test verifies that the ApiTokenHandler correctly validates JWT tokens
 * and extracts user information from them.
 * 
 * To run only this test:
 * vendor/bin/pest tests/Security/ApiTokenHandlerTest.php
 */

it('can be instantiated', function () {
    $jwtTokenService = $this->createMock(JwtTokenService::class);
    $logger = $this->createMock(LoggerInterface::class);
    
    $handler = new ApiTokenHandler($jwtTokenService, $logger);
    
    expect($handler)->toBeInstanceOf(ApiTokenHandler::class);
});

it('extracts user badge from valid token', function () {
    // Create mocks
    $jwtTokenService = $this->createMock(JwtTokenService::class);
    $logger = $this->createMock(LoggerInterface::class);
    
    // Configure the JwtTokenService mock to return a valid payload
    $validPayload = [
        'sub' => 'user123',
        'roles' => ['ROLE_USER'],
        'email' => '<EMAIL>'
    ];
    
    $jwtTokenService->method('validate')
        ->with('valid.jwt.token')
        ->willReturn($validPayload);
    
    // Create the handler
    $handler = new ApiTokenHandler($jwtTokenService, $logger);
    
    // Call the method
    $userBadge = $handler->getUserBadgeFrom('valid.jwt.token');
    
    // Assertions
    expect($userBadge)->toBeInstanceOf(UserBadge::class);
    expect($userBadge->getUserIdentifier())->toBe('user123');
});

it('throws exception for invalid token', function () {
    // Create mocks
    $jwtTokenService = $this->createMock(JwtTokenService::class);
    $logger = $this->createMock(LoggerInterface::class);
    
    // Configure the JwtTokenService mock to throw an exception
    $jwtTokenService->method('validate')
        ->with('invalid.jwt.token')
        ->willThrowException(new \Exception('Invalid token'));
    
    // Create the handler
    $handler = new ApiTokenHandler($jwtTokenService, $logger);
    
    // Call the method and expect an exception
    expect(function () use ($handler) {
        $handler->getUserBadgeFrom('invalid.jwt.token');
    })->toThrow(BadCredentialsException::class);
});

it('throws exception for token without sub claim', function () {
    // Create mocks
    $jwtTokenService = $this->createMock(JwtTokenService::class);
    $logger = $this->createMock(LoggerInterface::class);
    
    // Configure the JwtTokenService mock to return a payload without sub
    $invalidPayload = [
        'roles' => ['ROLE_USER'],
        'email' => '<EMAIL>'
    ];
    
    $jwtTokenService->method('validate')
        ->with('token.without.sub')
        ->willReturn($invalidPayload);
    
    // Create the handler
    $handler = new ApiTokenHandler($jwtTokenService, $logger);
    
    // Call the method and expect an exception
    expect(function () use ($handler) {
        $handler->getUserBadgeFrom('token.without.sub');
    })->toThrow(BadCredentialsException::class);
});

/**
 * This test demonstrates how to use the ApiTokenHandler in a real-world scenario:
 * 
 * 1. Login with AD credentials to get a JWT token:
 *    POST /api/login-ad
 *    {
 *      "username": "u074647",
 *      "password": "a moPabios224656156",
 *      "hopitalCode": "EJ-59307"
 *    }
 * 
 * 2. Use the token to access protected endpoints:
 *    GET /api/poles
 *    Headers: Authorization: Bearer {token}
 */