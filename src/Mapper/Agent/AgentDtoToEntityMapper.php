<?php

namespace App\Mapper\Agent;

use App\ApiResource\Praticien\AgentDto;
use App\Entity\Praticien\Agent;
use App\Repository\EntiteJuridiqueRepository;
use App\Repository\AgentRepository;
use Doctrine\DBAL\Driver\PDO\Exception;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfonycasts\MicroMapper\AsMapper;
use Symfonycasts\MicroMapper\MapperInterface;

#[AsMapper(from: AgentDto::class, to: Agent::class)]
class AgentDtoToEntityMapper implements MapperInterface
{
    public function __construct(
        private AgentRepository             $userRepository,
        private EntiteJuridiqueRepository   $hopitalRepository,
        private UserPasswordHasherInterface $userPasswordHasher,
    ) {
    }

    public function load(object $from, string $toClass, array $context): object
    {
        $dto = $from;
        assert($dto instanceof AgentDto);

        $userEntity = $dto->id ? $this->userRepository->find($dto->id) : new Agent();
        if (!$userEntity && $dto->id) {
            throw new Exception('Agent not found');
        }

        return $userEntity;
    }

    public function populate(object $from, object $to, array $context): object
    {
        $dto = $from;
        assert($dto instanceof AgentDto);
        $entity = $to;
        assert($entity instanceof Agent);

        // CollecteurTrait fields
        $this->setIfNotNull($entity, 'setEmail', $dto->email);
        $this->setIfNotNull($entity, 'setNom', $dto->nom);
        $this->setIfNotNull($entity, 'setPrenom', $dto->prenom);
        $this->setIfNotNull($entity, 'setMatricule', $dto->matricule);
        $this->setIfNotNull($entity, 'setHrUser', $dto->hrUser);
        $this->setIfNotNull($entity, 'setTitre', $dto->titre);
        $this->setIfNotNull($entity, 'setCategorie', $dto->categorie);
        $this->setIfNotNull($entity, 'setEtablissement', $dto->etablissement);
        $this->setIfNotNull($entity, 'setDateDepart', $dto->dateDepart);
        $this->setIfNotNull($entity, 'setDateVenue', $dto->dateVenue);
        $this->setIfNotNull($entity, 'setDateMaj', $dto->dateMaj);
        $this->setIfNotNull($entity, 'setCreateurFiche', $dto->createurFiche);
        $this->setIfNotNull($entity, 'setIsAdmin', $dto->isAdmin);
        $this->setIfNotNull($entity, 'setIsAnesthesiste', $dto->isAnesthesiste);

        // NativeFieldTrait fields
        $this->setIfNotNull($entity, 'setFullName', $dto->fullName);
        $this->setIfNotNull($entity, 'setImgUrl', $dto->imgUrl);
        $this->setIfNotNull($entity, 'setBadge', $dto->badge);
        $this->setIfNotNull($entity, 'setRoles', $dto->roles);

        if ($dto->password) {
            $entity->setPassword($this->userPasswordHasher->hashPassword($entity, $dto->password));
        }

        // Relations
        if ($dto->hopital) {
            $hopital = $this->hopitalRepository->find($dto->hopital);
            if (!$hopital) {
                throw new Exception('Hôpital non trouvé');
            }
            $entity->setHopital($hopital);
        }

        return $entity;
    }

    private function setIfNotNull(object $entity, string $setter, mixed $value): void
    {
        if ($value !== null) {
            $entity->$setter($value);
        }
    }

    public function map(AgentDto $dto, string $entityClass): Agent
    {
        /** @var Agent $entity */
        $entity = $this->load($dto, $entityClass, []);
        return $this->populate($dto, $entity, []);
    }
}