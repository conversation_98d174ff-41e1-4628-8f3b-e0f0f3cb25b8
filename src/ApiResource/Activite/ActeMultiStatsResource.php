<?php

namespace App\ApiResource\Activite;

use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\GetCollection;
use App\State\Partials\ActeMultiStatsProvider;

#[ApiResource(
    operations: [
        new GetCollection(
            uriTemplate: '/actes/multi-stats',
            provider: ActeMultiStatsProvider::class
        )
    ]
)]
class ActeMultiStatsResource
{
    public function __construct(
        public readonly array $activiteIds = []
    ) {}
}