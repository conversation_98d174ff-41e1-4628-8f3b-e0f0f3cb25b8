
services:
  # Base de données PostgreSQL
  postgres:
    image: postgres:16
    container_name: supra-postgres
    environment:
      POSTGRES_DB: supra_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      # Persister les données PostgreSQL
      - postgres_data:/var/lib/postgresql/data
      # Scripts d'initialisation si nécessaire
      # - ./docker/postgres/init:/docker-entrypoint-initdb.d
    ports:
      - "5433:5432"  # Port différent pour éviter les conflits avec votre PostgreSQL local
    networks:
      - supra-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d supra_dev"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis pour le cache
  redis:
    image: redis:7-alpine
    container_name: supra-redis
    ports:
      - "6380:6379"  # Port différent pour éviter les conflits avec votre Redis local
    networks:
      - supra-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Application FrankenPHP
  frankenphp:
    build:
      context: .
      dockerfile: docker/frankenphp/Dockerfile
    image: supra-frankenphp:custom
    container_name: supra-frankenphp
    ports:
      - "8000:8080"  # Même port que votre prod
    volumes:
      # Monter tout votre projet local
      - .:/app
    environment:
      # Variables d'environnement pour Docker
      - APP_ENV=dev
      - APP_DEBUG=1
      - DATABASE_USER=postgres
      - DATABASE_PASSWORD=password
      - DATABASE_HOST=postgres
      - DATABASE_PORT=5432
      - DATABASE_NAME=supra_dev
      - DATABASE_URL=********************************************/supra_dev?serverVersion=16&charset=utf8
      - CACHE_DSN=redis://redis:6379
      - PHP_INI_SCAN_DIR=/etc/frankenphp
    working_dir: /app
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - supra-network
    restart: unless-stopped

volumes:
  postgres_data:

networks:
  supra-network:
    driver: bridge
