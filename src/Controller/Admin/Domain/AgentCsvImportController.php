<?php

namespace App\Controller\Admin\Domain;

use App\Controller\Importation\Agent\Dto\AgentImportDto;
use App\Entity\Praticien\Agent;
use App\Entity\Structure\EntiteJuridique;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\Routing\Attribute\Route;

class AgentCsvImportController extends AbstractController
{
    #[Route('/admin/import/agents', name: 'admin_import_agents_csv')]
    public function importAgentsCsv(
        Request $request,
        EntityManagerInterface $entityManager
    ): Response {
        $flashBag = $request->getSession()->getFlashBag();

        // Récupérer toutes les entités juridiques disponibles
        $entitesJuridiques = $entityManager->getRepository(EntiteJuridique::class)->findAll();

        if ($request->isMethod('POST')) {
            /** @var UploadedFile $file */
            $file = $request->files->get('csv_file');
            $entiteJuridiqueId = $request->request->get('entite_juridique_id');

            // Validation de l'entité juridique
            if (empty($entiteJuridiqueId)) {
                $flashBag->add('danger', '❌ Veuillez sélectionner une entité juridique.');
                return $this->render('admin/import_csv.html.twig', [
                    'entity' => 'Agents',
                    'entitesJuridiques' => $entitesJuridiques
                ]);
            }

            $entiteJuridique = $entityManager->getRepository(EntiteJuridique::class)->find($entiteJuridiqueId);
            if (!$entiteJuridique) {
                $flashBag->add('danger', '❌ Entité juridique introuvable.');
                return $this->render('admin/import_csv.html.twig', [
                    'entity' => 'Agents',
                    'entitesJuridiques' => $entitesJuridiques
                ]);
            }

            if (!$file || $file->getClientOriginalExtension() !== 'csv') {
                $flashBag->add('danger', '❌ Veuillez fournir un fichier CSV valide.');
                return $this->render('admin/import_csv.html.twig', [
                    'entity' => 'Agents',
                    'entitesJuridiques' => $entitesJuridiques
                ]);
            }

            try {
                $result = $this->processImport($file, $entiteJuridique, $entityManager);

                if ($result['success']) {
                    $flashBag->add('success', sprintf('✅ Importation réussie des agents pour "%s" avec %d lignes !',
                        $entiteJuridique->getNom(), $result['count']));
                } else {
                    $flashBag->add('danger', '⚠️ Des erreurs sont survenues lors de l\'import.');
                    foreach (array_slice($result['errors'], 0, 10) as $error) {
                        $flashBag->add('danger', $error);
                    }
                    if (count($result['errors']) > 10) {
                        $flashBag->add('danger', sprintf('⚠️ %d autres erreurs non affichées.', count($result['errors']) - 10));
                    }
                }
            } catch (\Exception $e) {
                $flashBag->add('danger', '❌ Erreur lors de l\'import : ' . $e->getMessage());
            }

            return $this->redirectToRoute('admin');
        }

        return $this->render('admin/import_csv.html.twig', [
            'entity' => 'Agents',
            'entitesJuridiques' => $entitesJuridiques
        ]);
    }

    private function processImport(UploadedFile $file, EntiteJuridique $entiteJuridique, EntityManagerInterface $entityManager): array
    {
        $csv = new \SplFileObject($file->getPathname(), 'r');
        $csv->setFlags(\SplFileObject::READ_CSV);
        $csv->setCsvControl(';');

        $batchSize = 1000;
        $errors = [];
        $count = 0;

        // Désactivation temporaire des logs Doctrine
        $entityManager->getConnection()->getConfiguration()->setSQLLogger(null);

        // Lire l'entête
        $header = $csv->fgetcsv();
        if (!$header || count($header) === 1) {
            throw new \InvalidArgumentException('Le fichier CSV est vide ou mal formé.');
        }

        // Nettoyer l'entête
        $header = array_map(fn($col) => strtoupper(trim($col)), $header);

        while (!$csv->eof()) {
            $row = $csv->fgetcsv();

            if (!is_array($row) || count($row) !== count($header)) {
                continue;
            }

            $row = array_map(fn($value) => trim($value), $row);
            $record = array_combine($header, $row);

            try {
                $dto = $this->createDtoFromCsvRecord($record);
                $agent = $this->createAgentFromDto($dto, $entiteJuridique, $entityManager);

                if ($agent) {
                    $entityManager->persist($agent);
                    $count++;

                    if ($count % $batchSize === 0) {
                        $entityManager->flush();
                        $entityManager->clear();
                        // Recharger l'entité après clear
                        $entiteJuridique = $entityManager->getRepository(EntiteJuridique::class)->find($entiteJuridique->getId());
                    }
                }
            } catch (\Exception $e) {
                $errors[] = "⚠️ Erreur ligne " . ($count + 1) . " : " . $e->getMessage();
            }
        }

        $entityManager->flush();
        $entityManager->clear();

        return [
            'success' => empty($errors),
            'count' => $count,
            'errors' => $errors
        ];
    }

    private function createDtoFromCsvRecord(array $record): AgentImportDto
    {
        $dto = new AgentImportDto();
        $reflection = new \ReflectionClass(AgentImportDto::class);

        foreach ($reflection->getProperties(\ReflectionProperty::IS_PUBLIC) as $property) {
            $propertyName = $property->getName();
            $csvColumnName = $this->convertToSnakeCase($propertyName);

            if (isset($record[$csvColumnName])) {
                $value = $record[$csvColumnName];

                if (!empty($value)) {
                    $value = $this->convertValueToPropertyType($value, $property);
                }

                $property->setValue($dto, $value);
            }
        }

        return $dto;
    }

    /**
     * Convertit un nom de propriété camelCase en SNAKE_CASE pour correspondre aux en-têtes CSV
     * Par exemple: hrUser -> HR_USER
     */
    private function convertToSnakeCase(string $input): string
    {
        $result = preg_replace('/([a-z])([A-Z])/', '$1_$2', $input);
        return strtoupper($result);
    }

    private function convertValueToPropertyType($value, \ReflectionProperty $property)
    {
        $type = $property->getType();

        if (!$type) {
            return $value;
        }

        $typeName = $type->getName();

        switch ($typeName) {
            case 'int':
                return (int) $value;
            case 'float':
                return (float) $value;
            case 'bool':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case 'string':
            default:
                return $value;
        }
    }

    private function createAgentFromDto(AgentImportDto $dto, EntiteJuridique $entiteJuridique, EntityManagerInterface $entityManager): Agent
    {
        if (empty($dto->hrUser) || empty($dto->nom) || empty($dto->email)) {
            throw new \InvalidArgumentException('⚠️ hrUser, nom et email sont obligatoires pour les agents');
        }

        // Vérifier si l'agent existe déjà dans cette entité juridique
        $existingAgent = $entityManager->getRepository(Agent::class)->findOneBy([
            'hrUser' => $dto->hrUser,
            'hopital' => $entiteJuridique
        ]);

        if ($existingAgent) {
            throw new \InvalidArgumentException(sprintf('⚠️ L\'agent %s existe déjà dans cette entité juridique', $dto->hrUser));
        }

        $agent = new Agent();
        
        // Champs obligatoires
        $agent->setHrUser($dto->hrUser);
        $agent->setNom($dto->nom);
        $agent->setPrenom($dto->prenom);
        $agent->setEmail($dto->email);
        
        // Champs optionnels
        if ($dto->titre) {
            $agent->setTitre($dto->titre);
        }
        if ($dto->categorie) {
            $agent->setCategorie($dto->categorie);
        }
        if ($dto->etablissement) {
            $agent->setEtablissement($dto->etablissement);
        }
        if ($dto->createurFiche) {
            $agent->setCreateurFiche($dto->createurFiche);
        }
        
        // Conversion des booléens
        $agent->setIsAdmin($dto->isAdmin ?? false);
        $agent->setIsAnesthesiste($dto->isAnesthesiste ?? false);
        
        // Gestion des dates
        $importDate = new \DateTime();
        
        // SCD type 2: dateVenue comme validFrom
        if ($dto->dateVenue) {
            try {
                $dateVenue = \DateTime::createFromFormat('d/m/Y', $dto->dateVenue);
                if ($dateVenue) {
                    $agent->setDateVenue($dateVenue);
                }
            } catch (\Exception $e) {
                // Ignorer les erreurs de format de date
            }
        }
        
        // SCD type 2: dateDepart comme validTo
        if ($dto->dateDepart) {
            try {
                $dateDepart = \DateTime::createFromFormat('d/m/Y', $dto->dateDepart);
                if ($dateDepart) {
                    $agent->setDateDepart($dateDepart);
                }
            } catch (\Exception $e) {
                // Ignorer les erreurs de format de date
            }
        }
        
        // Date de mise à jour
        if ($dto->dateMaj) {
            try {
                $dateMaj = \DateTime::createFromFormat('d/m/Y', $dto->dateMaj);
                if ($dateMaj) {
                    $agent->setDateMaj($dateMaj);
                } else {
                    $agent->setDateMaj($importDate);
                }
            } catch (\Exception $e) {
                $agent->setDateMaj($importDate);
            }
        } else {
            $agent->setDateMaj($importDate);
        }
        
        // Associer l'entité juridique sélectionnée
        $agent->setHopital($entiteJuridique);
        
        // Définir le rôle par défaut (ROLE_USER)
        $agent->setRoles(['ROLE_USER']);
        
        return $agent;
    }
}
