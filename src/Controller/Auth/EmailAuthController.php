<?php

namespace App\Controller\Auth;

use App\Domain\Service\EmailAuthService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Validator\ValidatorInterface;

/**
 * Contrôleur pour l'authentification par email avec code de vérification.
 * 
 * Ce contrôleur expose deux endpoints :
 * - POST /auth/email/login : Initie le processus en envoyant un code par email
 * - POST /auth/email/verify : Vérifie le code et retourne un token JWT
 */
#[Route('/api/auth/email', name: 'auth_email_')]
class EmailAuthController extends AbstractController
{
    public function __construct(
        private readonly EmailAuthService $emailAuthService,
        private readonly ValidatorInterface $validator
    ) {}

    /**
     * Initie le processus d'authentification par email
     *
     * Envoie un code de vérification à l'adresse email fournie
     *
     * @param Request $request
     * @return JsonResponse
     */
    #[Route('/login', name: 'login', methods: ['POST'])]
    public function loginWithEmail(Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                return $this->json([
                    'success' => false,
                    'message' => 'Format JSON invalide'
                ], 400);
            }

            // Validation des données d'entrée
            $constraints = new Assert\Collection([
                'email' => [
                    new Assert\NotBlank(message: 'L\'email est requis'),
                    new Assert\Email(message: 'Format d\'email invalide')
                ]
            ]);

            $violations = $this->validator->validate($data, $constraints);

            if (count($violations) > 0) {
                $errors = [];
                foreach ($violations as $violation) {
                    $errors[] = $violation->getMessage();
                }

                return $this->json([
                    'success' => false,
                    'message' => 'Données invalides',
                    'errors' => $errors
                ], 400);
            }

            // Initier le processus d'authentification
            $result = $this->emailAuthService->initiateEmailAuth($data['email']);

            $statusCode = $result['success'] ? 200 : 400;

            return $this->json($result, $statusCode);

        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'message' => 'Erreur interne du serveur'
            ], 500);
        }
    }

    /**
     * Vérifie le code de vérification et retourne un token JWT
     *
     * @param Request $request
     * @return JsonResponse
     */
    #[Route('/verify', name: 'verify', methods: ['POST'])]
    public function verifyEmailCode(Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                return $this->json([
                    'success' => false,
                    'message' => 'Format JSON invalide'
                ], 400);
            }

            // Validation des données d'entrée
            $constraints = new Assert\Collection([
                'email' => [
                    new Assert\NotBlank(message: 'L\'email est requis'),
                    new Assert\Email(message: 'Format d\'email invalide')
                ],
                'code' => [
                    new Assert\NotBlank(message: 'Le code est requis'),
                    new Assert\Regex(
                        pattern: '/^\d{4}$/',
                        message: 'Le code doit contenir exactement 4 chiffres'
                    )
                ]
            ]);

            $violations = $this->validator->validate($data, $constraints);

            if (count($violations) > 0) {
                $errors = [];
                foreach ($violations as $violation) {
                    $errors[] = $violation->getMessage();
                }

                return $this->json([
                    'success' => false,
                    'message' => 'Données invalides',
                    'errors' => $errors
                ], 400);
            }

            // Vérifier le code et générer le token
            $result = $this->emailAuthService->verifyEmailCode($data['email'], $data['code']);

            $statusCode = $result['success'] ? 200 : 400;

            return $this->json($result, $statusCode);

        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'message' => 'Erreur interne du serveur'
            ], 500);
        }
    }
}
