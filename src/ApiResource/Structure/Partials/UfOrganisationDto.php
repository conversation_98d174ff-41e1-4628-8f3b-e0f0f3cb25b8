<?php

namespace App\ApiResource\Structure\Partials;

/**
 * DTO pour obtenir l'organisation structurelle complète d'une UF sur une période donnée
 *
 * Cette classe représente la hiérarchie complète d'une UF (Unité Fonctionnelle) incluant
 * son service de rattachement, son pôle et son centre de responsabilité.
 *
 * Route API: GET /api/structure/get-organisation-by-ufcode
 *
 * Exemple d'utilisation:
 * GET /api/structure/get-organisation-by-ufcode?code=yz57&p1Start=2024-01-01&p1End=2024-12-31
 *
 * Réponse attendue:
 * {
 *   "ufcode": "yz57",
 *   "ufLibelle": "Cardiologie",
 *   "serviceCode": "CARDIO",
 *   "serviceLibelle": "Service de Cardiologie",
 *   "poleCode": "P01",
 *   "poleLibelle": "Pôle Cardio-vasculaire",
 *   "crCode": "CR123",
 *   "crLibelle": "Centre de Responsabilité Cardio",
 *   "dateDebut": "2024-01-01T00:00:00+00:00",
 *   "dateFin": "2024-12-31T00:00:00+00:00",
 *   "searchStart": "2024-01-01T00:00:00+00:00",
 *   "searchEnd": "2024-12-31T00:00:00+00:00"
 * }
 */
class UfOrganisationDto
{
    public function __construct(
        /** @var string Code unique de l'UF */
        public readonly string $ufcode,

        /** @var string Libellé de l'UF */
        public readonly string $ufLibelle,

        /** @var string Code du pôle */
        public readonly string $poleCode,

        /** @var string Libellé du pôle */
        public readonly string $poleLibelle,

        /** @var string Code du centre de responsabilité */
        public readonly string $crCode,

        /** @var string Libellé du centre de responsabilité */
        public readonly string $crLibelle,

        /** @var \DateTimeInterface Date de début de validité de l'UF */
        public readonly \DateTimeInterface $dateDebut,

        /** @var \DateTimeInterface Date de fin de validité de l'UF */
        public readonly \DateTimeInterface $dateFin,

        /** @var \DateTimeInterface Date de début de la période recherchée */
        public readonly \DateTimeInterface $searchStart,

        /** @var \DateTimeInterface Date de fin de la période recherchée */
        public readonly \DateTimeInterface $searchEnd
    ) {}
}