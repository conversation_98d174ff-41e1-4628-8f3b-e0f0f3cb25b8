<?php

namespace App\Domain\Extension;

use ApiPlatform\Doctrine\Orm\Extension\QueryCollectionExtensionInterface;
use ApiPlatform\Doctrine\Orm\Util\QueryNameGeneratorInterface;
use Doctrine\ORM\QueryBuilder;
use Symfony\Component\HttpFoundation\RequestStack;
use App\Entity\Structure\Ufs;

/**
 * Extension Doctrine pour filtrer la ressource /api/ufs sur plusieurs périodes dynamiques et par entité juridique.
 *
 * Cette extension permet de retourner toutes les UFs :
 *  - dont la période de validité [datdeb, datfin] recouvre au moins l'une des périodes demandées (p1, p2, p3, ...)
 *  - ET optionnellement, appartenant à une entité juridique spécifique (via le paramètre ejcode).
 *
 * Exemple d'appel frontend :
 *   [GET] /api/ufs?p1Start=2024-01-01&p1End=2024-06-30&p2Start=2023-01-01&p2End=2023-06-30&p3Start=2022-01-01&p3End=2022-06-30&ejcode=ej0001
 *
 * Logique :
 *   Pour chaque période, l'UF est retenue si :
 *     datdeb <= periodeEnd
 *     AND (datfin IS NULL OR datfin >= periodeStart)
 *   (on combine toutes les périodes avec OR)
 *
 * Si le paramètre ejcode est présent, l'UF doit également appartenir à l'entité juridique correspondante via son CR et son pôle.
 *
 * Cette extension ne fait rien si aucun paramètre pXStart/pXEnd ou ejcode n'est passé dans la requête.
 */
class UfsExtension implements QueryCollectionExtensionInterface
{
    /**
     * @param RequestStack $requestStack Pour accéder à la requête courante (query params)
     */
    public function __construct(
        private readonly RequestStack $requestStack
    ) {}

    /**
     * Ajoute dynamiquement des conditions WHERE sur le QueryBuilder pour filtrer les UFs sur plusieurs périodes
     * et optionnellement par entité juridique.
     *
     * @param QueryBuilder $qb
     * @param QueryNameGeneratorInterface $queryNameGenerator
     * @param string $resourceClass
     * @param \ApiPlatform\Metadata\Operation|null $operation
     * @param array $context
     */
    public function applyToCollection(
        QueryBuilder $qb,
        QueryNameGeneratorInterface $queryNameGenerator,
        string $resourceClass,
        ?\ApiPlatform\Metadata\Operation $operation = null,
        array $context = []
    ): void {
        // Ne s'applique que sur la ressource Ufs (évite de filtrer sur d'autres entités)
        if ($resourceClass !== Ufs::class) {
            return;
        }

        $request = $this->requestStack->getCurrentRequest();
        if (!$request) {
            return;
        }

        $periods = [];
        $ejcode = $request->query->get('ejcode');

        // Récupère dynamiquement tous les couples pXStart/pXEnd présents dans la requête
        // Exemple : p1Start/p1End, p2Start/p2End, ...
        foreach ($request->query->all() as $key => $value) {
            if (preg_match('/^p\d+Start$/', $key)) {
                $index = substr($key, 1, -5); // extrait le numéro (ex: '1' dans 'p1Start')
                $start = $value;
                $endKey = "p{$index}End";
                $end = $request->query->get($endKey);
                if ($end) {
                    $periods[] = [$start, $end];
                }
            }
        }

        // S'il n'y a pas de période passée et pas d'ejcode, on ne filtre rien
        if (count($periods) === 0 && !$ejcode) {
            return;
        }

        // Si un ejcode est spécifié, on ajoute la condition pour filtrer par entité juridique
        if ($ejcode) {
            // Pour les UFs, la relation avec l'entité juridique passe par le CR et le pôle
            $crAlias = $queryNameGenerator->generateJoinAlias('cr');
            $poleAlias = $queryNameGenerator->generateJoinAlias('pole');
            $hopitalAlias = $queryNameGenerator->generateJoinAlias('hopital');
            
            // Ajouter les jointures nécessaires
            $qb->leftJoin('o.cr', $crAlias)
               ->leftJoin("$crAlias.pole", $poleAlias)
               ->leftJoin("$poleAlias.hopital", $hopitalAlias);
            
            // Ajouter la condition sur le code de l'entité juridique
            $qb->andWhere($qb->expr()->eq("$hopitalAlias.code", ':ejcode'))
               ->setParameter('ejcode', $ejcode);
        }

        // S'il n'y a pas de période passée, on ne continue pas avec le filtrage par période
        if (count($periods) === 0) {
            return;
        }

        // On va construire un WHERE (... OR ... OR ...) pour toutes les périodes
        $orX = $qb->expr()->orX();
        $i = 0;
        foreach ($periods as [$start, $end]) {
            $paramStart = "period_start_$i";
            $paramEnd   = "period_end_$i";

            // Pour chaque période, on ajoute la condition :
            // datdeb <= periodeEnd
            // AND (datfin IS NULL OR datfin >= periodeStart)
            $orX->add(
                $qb->expr()->andX(
                    $qb->expr()->orX(
                        $qb->expr()->isNull('o.datdeb'),
                        $qb->expr()->lte('o.datdeb', ":$paramEnd")
                    ),
                    $qb->expr()->orX(
                        $qb->expr()->isNull('o.datfin'),
                        $qb->expr()->gte('o.datfin', ":$paramStart")
                    )
                )
            );

            $qb->setParameter($paramStart, $start);
            $qb->setParameter($paramEnd, $end);
            $i++;
        }

        // Ajoute la condition globale au QueryBuilder (WHERE (... OR ... OR ...))
        $qb->andWhere($orX);
    }
}