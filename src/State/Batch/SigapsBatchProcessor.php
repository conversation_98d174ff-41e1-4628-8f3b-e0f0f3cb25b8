<?php

namespace App\State\Batch;

use ApiPlatform\Metadata\Operation;
use App\ApiResource\Activite\SigapsDto;
use App\Domain\Service\Batch\BatchOptimizationConfigService;
use App\Domain\Service\Notification\BatchNotificationService;
use App\Entity\Activite\Sigaps;
use App\Entity\Structure\EntiteJuridique;
use App\Mapper\Sigaps\SigapsDtoToEntityMapper;
use App\State\Batch\Base\BatchProcessorOptimized;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Exception\BadRequestException;
use Symfony\Component\HttpFoundation\RequestStack;

class SigapsBatchProcessor extends BatchProcessorOptimized
{
    public function __construct(
        private SigapsDtoToEntityMapper $mapper,
        private RequestStack $requestStack,
        BatchOptimizationConfigService $configService,
        BatchNotificationService $notificationService,
        LoggerInterface $logger,
        EntityManagerInterface $entityManager
    ) {
        parent::__construct($configService, $notificationService, $logger, $entityManager);
    }

    public function process(mixed $data, Operation $operation, array $uriVariables = [], array $context = [])
    {
        // Récupérer le contenu JSON brut de la requête
        $request = $this->requestStack->getCurrentRequest();
        $content = $request->getContent();

        // Décoder le JSON manuellement pour garder la structure de tableau
        $arrayData = json_decode($content, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new BadRequestException('Invalid JSON: ' . json_last_error_msg());
        }

        if (!is_array($arrayData)) {
            throw new BadRequestException('Expected JSON array, got: ' . gettype($arrayData));
        }

        if (empty($arrayData)) {
            throw new BadRequestException('Array cannot be empty');
        }

        // Add ejcode to context if available
        if (isset($uriVariables['ejcode'])) {
            $context['ejcode'] = $uriVariables['ejcode'];
        }

        // Récupérer l'entité juridique si disponible
        $entiteJuridique = null;
        if (isset($uriVariables['ejcode'])) {
            $entiteJuridique = $this->entityManager->getRepository(EntiteJuridique::class)
                ->findOneBy(['code' => $uriVariables['ejcode']]);
        }

        // Utiliser le traitement optimisé
        return $this->processOptimizedBatch($arrayData, 'Sigaps', $entiteJuridique);
    }

    /**
     * Traite un élément individuel du batch
     */
    protected function processItem(array $item, int $index, string $resourceType): ?array
    {
        // Validation des champs requis
        $this->validateRequiredFields($item, ['dateDebut', 'agentHrU'], $index);

        // Créer le DTO à partir des données JSON
        $dto = $this->createDtoFromArray($item);

        // Utiliser le mapper existant
        $entity = $this->mapper->load($dto, Sigaps::class, []);
        $isNew = $entity->getId() === null;
        $entity = $this->mapper->populate($dto, $entity, []);

        $this->entityManager->persist($entity);

        // Créer une réponse simple sans objets DateTime
        $responseData = [
            'id' => $entity->getId(),
            'score' => $entity->getScore(),
            'categorie' => $entity->getCategorie(),
            'nombrePublication' => $entity->getNombrePublication(),
            'dateDebut' => $entity->getDateDebut()?->format('Y-m-d H:i:s'),
            'dateFin' => $entity->getDateFin()?->format('Y-m-d H:i:s'),
            'validFrom' => $entity->getValidFrom()?->format('Y-m-d H:i:s'),
            'validTo' => $entity->getValidTo()?->format('Y-m-d H:i:s'),
            'periodeType' => $entity->getPeriodeType(),
            'source' => $entity->getSource(),
        ];

        return [
            'dto' => $responseData,
            'is_new' => $isNew,
        ];
    }



    private function createDtoFromArray(array $item): SigapsDto
    {
        $dto = new SigapsDto();

        // Champs de base
        $dto->dateDebut = isset($item['dateDebut']) ? new \DateTime($item['dateDebut']) : null;
        $dto->dateFin = isset($item['dateFin']) ? new \DateTime($item['dateFin']) : null;
        $dto->score = $item['score'] ?? null;
        $dto->categorie = $item['categorie'] ?? null;
        $dto->nombrePublication = $item['nombrePublication'] ?? null;
        $dto->agentHrU = $item['agentHrU'] ?? null;

        // Champs de répartition individuels (pour CSV)
        $dto->repartition_A_plus = $item['repartition_A_plus'] ?? null;
        $dto->repartition_A = $item['repartition_A'] ?? null;
        $dto->repartition_B = $item['repartition_B'] ?? null;
        $dto->repartition_C = $item['repartition_C'] ?? null;
        $dto->repartition_D = $item['repartition_D'] ?? null;

        // Répartition globale (si fournie directement)
        // si besoin rendez ce champ en writable dans l'ApiResource (SigapsDto.php)
        if (isset($item['repartitionParCategorie'])) {
            $dto->repartitionParCategorie = $item['repartitionParCategorie'];
        }

        return $dto;
    }


}