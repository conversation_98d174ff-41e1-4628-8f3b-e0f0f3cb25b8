/*! For license information please see app.1ecd6d7a.js.LICENSE.txt */
(()=>{var t={414:function(t){t.exports=function(){"use strict";const t=new Map,e={set(e,i,n){t.has(e)||t.set(e,new Map);const s=t.get(e);s.has(i)||0===s.size?s.set(i,n):console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(s.keys())[0]}.`)},get:(e,i)=>t.has(e)&&t.get(e).get(i)||null,remove(e,i){if(!t.has(e))return;const n=t.get(e);n.delete(i),0===n.size&&t.delete(e)}},i=1e6,n=1e3,s="transitionend",o=t=>(t&&window.CSS&&window.CSS.escape&&(t=t.replace(/#([^\s"#']+)/g,((t,e)=>`#${CSS.escape(e)}`))),t),r=t=>null==t?`${t}`:Object.prototype.toString.call(t).match(/\s([a-z]+)/i)[1].toLowerCase(),a=t=>{do{t+=Math.floor(Math.random()*i)}while(document.getElementById(t));return t},l=t=>{if(!t)return 0;let{transitionDuration:e,transitionDelay:i}=window.getComputedStyle(t);const s=Number.parseFloat(e),o=Number.parseFloat(i);return s||o?(e=e.split(",")[0],i=i.split(",")[0],(Number.parseFloat(e)+Number.parseFloat(i))*n):0},c=t=>{t.dispatchEvent(new Event(s))},d=t=>!(!t||"object"!=typeof t)&&(void 0!==t.jquery&&(t=t[0]),void 0!==t.nodeType),u=t=>d(t)?t.jquery?t[0]:t:"string"==typeof t&&t.length>0?document.querySelector(o(t)):null,h=t=>{if(!d(t)||0===t.getClientRects().length)return!1;const e="visible"===getComputedStyle(t).getPropertyValue("visibility"),i=t.closest("details:not([open])");if(!i)return e;if(i!==t){const e=t.closest("summary");if(e&&e.parentNode!==i)return!1;if(null===e)return!1}return e},p=t=>!t||t.nodeType!==Node.ELEMENT_NODE||!!t.classList.contains("disabled")||(void 0!==t.disabled?t.disabled:t.hasAttribute("disabled")&&"false"!==t.getAttribute("disabled")),f=t=>{if(!document.documentElement.attachShadow)return null;if("function"==typeof t.getRootNode){const e=t.getRootNode();return e instanceof ShadowRoot?e:null}return t instanceof ShadowRoot?t:t.parentNode?f(t.parentNode):null},g=()=>{},m=t=>{t.offsetHeight},v=()=>window.jQuery&&!document.body.hasAttribute("data-bs-no-jquery")?window.jQuery:null,_=[],b=t=>{"loading"===document.readyState?(_.length||document.addEventListener("DOMContentLoaded",(()=>{for(const t of _)t()})),_.push(t)):t()},y=()=>"rtl"===document.documentElement.dir,w=t=>{b((()=>{const e=v();if(e){const i=t.NAME,n=e.fn[i];e.fn[i]=t.jQueryInterface,e.fn[i].Constructor=t,e.fn[i].noConflict=()=>(e.fn[i]=n,t.jQueryInterface)}}))},O=(t,e=[],i=t)=>"function"==typeof t?t(...e):i,A=(t,e,i=!0)=>{if(!i)return void O(t);const n=5,o=l(e)+n;let r=!1;const a=({target:i})=>{i===e&&(r=!0,e.removeEventListener(s,a),O(t))};e.addEventListener(s,a),setTimeout((()=>{r||c(e)}),o)},E=(t,e,i,n)=>{const s=t.length;let o=t.indexOf(e);return-1===o?!i&&n?t[s-1]:t[0]:(o+=i?1:-1,n&&(o=(o+s)%s),t[Math.max(0,Math.min(o,s-1))])},x=/[^.]*(?=\..*)\.|.*/,S=/\..*/,C=/::\d+$/,k={};let I=1;const T={mouseenter:"mouseover",mouseleave:"mouseout"},L=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function N(t,e){return e&&`${e}::${I++}`||t.uidEvent||I++}function $(t){const e=N(t);return t.uidEvent=e,k[e]=k[e]||{},k[e]}function P(t,e){return function i(n){return B(n,{delegateTarget:t}),i.oneOff&&z.off(t,n.type,e),e.apply(t,[n])}}function D(t,e,i){return function n(s){const o=t.querySelectorAll(e);for(let{target:r}=s;r&&r!==this;r=r.parentNode)for(const a of o)if(a===r)return B(s,{delegateTarget:r}),n.oneOff&&z.off(t,s.type,e,i),i.apply(r,[s])}}function j(t,e,i=null){return Object.values(t).find((t=>t.callable===e&&t.delegationSelector===i))}function F(t,e,i){const n="string"==typeof e,s=n?i:e||i;let o=H(t);return L.has(o)||(o=t),[n,s,o]}function M(t,e,i,n,s){if("string"!=typeof e||!t)return;let[o,r,a]=F(e,i,n);if(e in T){const t=t=>function(e){if(!e.relatedTarget||e.relatedTarget!==e.delegateTarget&&!e.delegateTarget.contains(e.relatedTarget))return t.call(this,e)};r=t(r)}const l=$(t),c=l[a]||(l[a]={}),d=j(c,r,o?i:null);if(d)return void(d.oneOff=d.oneOff&&s);const u=N(r,e.replace(x,"")),h=o?D(t,i,r):P(t,r);h.delegationSelector=o?i:null,h.callable=r,h.oneOff=s,h.uidEvent=u,c[u]=h,t.addEventListener(a,h,o)}function q(t,e,i,n,s){const o=j(e[i],n,s);o&&(t.removeEventListener(i,o,Boolean(s)),delete e[i][o.uidEvent])}function R(t,e,i,n){const s=e[i]||{};for(const[o,r]of Object.entries(s))o.includes(n)&&q(t,e,i,r.callable,r.delegationSelector)}function H(t){return t=t.replace(S,""),T[t]||t}const z={on(t,e,i,n){M(t,e,i,n,!1)},one(t,e,i,n){M(t,e,i,n,!0)},off(t,e,i,n){if("string"!=typeof e||!t)return;const[s,o,r]=F(e,i,n),a=r!==e,l=$(t),c=l[r]||{},d=e.startsWith(".");if(void 0===o){if(d)for(const i of Object.keys(l))R(t,l,i,e.slice(1));for(const[i,n]of Object.entries(c)){const s=i.replace(C,"");a&&!e.includes(s)||q(t,l,r,n.callable,n.delegationSelector)}}else{if(!Object.keys(c).length)return;q(t,l,r,o,s?i:null)}},trigger(t,e,i){if("string"!=typeof e||!t)return null;const n=v();let s=null,o=!0,r=!0,a=!1;e!==H(e)&&n&&(s=n.Event(e,i),n(t).trigger(s),o=!s.isPropagationStopped(),r=!s.isImmediatePropagationStopped(),a=s.isDefaultPrevented());const l=B(new Event(e,{bubbles:o,cancelable:!0}),i);return a&&l.preventDefault(),r&&t.dispatchEvent(l),l.defaultPrevented&&s&&s.preventDefault(),l}};function B(t,e={}){for(const[i,n]of Object.entries(e))try{t[i]=n}catch(e){Object.defineProperty(t,i,{configurable:!0,get:()=>n})}return t}function V(t){if("true"===t)return!0;if("false"===t)return!1;if(t===Number(t).toString())return Number(t);if(""===t||"null"===t)return null;if("string"!=typeof t)return t;try{return JSON.parse(decodeURIComponent(t))}catch(e){return t}}function W(t){return t.replace(/[A-Z]/g,(t=>`-${t.toLowerCase()}`))}const K={setDataAttribute(t,e,i){t.setAttribute(`data-bs-${W(e)}`,i)},removeDataAttribute(t,e){t.removeAttribute(`data-bs-${W(e)}`)},getDataAttributes(t){if(!t)return{};const e={},i=Object.keys(t.dataset).filter((t=>t.startsWith("bs")&&!t.startsWith("bsConfig")));for(const n of i){let i=n.replace(/^bs/,"");i=i.charAt(0).toLowerCase()+i.slice(1,i.length),e[i]=V(t.dataset[n])}return e},getDataAttribute:(t,e)=>V(t.getAttribute(`data-bs-${W(e)}`))};class U{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw new Error('You have to implement the static method "NAME", for each component!')}_getConfig(t){return t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t}_mergeConfigObj(t,e){const i=d(e)?K.getDataAttribute(e,"config"):{};return{...this.constructor.Default,..."object"==typeof i?i:{},...d(e)?K.getDataAttributes(e):{},..."object"==typeof t?t:{}}}_typeCheckConfig(t,e=this.constructor.DefaultType){for(const[i,n]of Object.entries(e)){const e=t[i],s=d(e)?"element":r(e);if(!new RegExp(n).test(s))throw new TypeError(`${this.constructor.NAME.toUpperCase()}: Option "${i}" provided type "${s}" but expected type "${n}".`)}}}const Q="5.3.3";class J extends U{constructor(t,i){super(),(t=u(t))&&(this._element=t,this._config=this._getConfig(i),e.set(this._element,this.constructor.DATA_KEY,this))}dispose(){e.remove(this._element,this.constructor.DATA_KEY),z.off(this._element,this.constructor.EVENT_KEY);for(const t of Object.getOwnPropertyNames(this))this[t]=null}_queueCallback(t,e,i=!0){A(t,e,i)}_getConfig(t){return t=this._mergeConfigObj(t,this._element),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}static getInstance(t){return e.get(u(t),this.DATA_KEY)}static getOrCreateInstance(t,e={}){return this.getInstance(t)||new this(t,"object"==typeof e?e:null)}static get VERSION(){return Q}static get DATA_KEY(){return`bs.${this.NAME}`}static get EVENT_KEY(){return`.${this.DATA_KEY}`}static eventName(t){return`${t}${this.EVENT_KEY}`}}const Y=t=>{let e=t.getAttribute("data-bs-target");if(!e||"#"===e){let i=t.getAttribute("href");if(!i||!i.includes("#")&&!i.startsWith("."))return null;i.includes("#")&&!i.startsWith("#")&&(i=`#${i.split("#")[1]}`),e=i&&"#"!==i?i.trim():null}return e?e.split(",").map((t=>o(t))).join(","):null},X={find:(t,e=document.documentElement)=>[].concat(...Element.prototype.querySelectorAll.call(e,t)),findOne:(t,e=document.documentElement)=>Element.prototype.querySelector.call(e,t),children:(t,e)=>[].concat(...t.children).filter((t=>t.matches(e))),parents(t,e){const i=[];let n=t.parentNode.closest(e);for(;n;)i.push(n),n=n.parentNode.closest(e);return i},prev(t,e){let i=t.previousElementSibling;for(;i;){if(i.matches(e))return[i];i=i.previousElementSibling}return[]},next(t,e){let i=t.nextElementSibling;for(;i;){if(i.matches(e))return[i];i=i.nextElementSibling}return[]},focusableChildren(t){const e=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map((t=>`${t}:not([tabindex^="-"])`)).join(",");return this.find(e,t).filter((t=>!p(t)&&h(t)))},getSelectorFromElement(t){const e=Y(t);return e&&X.findOne(e)?e:null},getElementFromSelector(t){const e=Y(t);return e?X.findOne(e):null},getMultipleElementsFromSelector(t){const e=Y(t);return e?X.find(e):[]}},G=(t,e="hide")=>{const i=`click.dismiss${t.EVENT_KEY}`,n=t.NAME;z.on(document,i,`[data-bs-dismiss="${n}"]`,(function(i){if(["A","AREA"].includes(this.tagName)&&i.preventDefault(),p(this))return;const s=X.getElementFromSelector(this)||this.closest(`.${n}`);t.getOrCreateInstance(s)[e]()}))},Z="alert",tt=".bs.alert",et=`close${tt}`,it=`closed${tt}`,nt="fade",st="show";class ot extends J{static get NAME(){return Z}close(){if(z.trigger(this._element,et).defaultPrevented)return;this._element.classList.remove(st);const t=this._element.classList.contains(nt);this._queueCallback((()=>this._destroyElement()),this._element,t)}_destroyElement(){this._element.remove(),z.trigger(this._element,it),this.dispose()}static jQueryInterface(t){return this.each((function(){const e=ot.getOrCreateInstance(this);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t](this)}}))}}G(ot,"close"),w(ot);const rt="button",at="active",lt='[data-bs-toggle="button"]',ct="click.bs.button.data-api";class dt extends J{static get NAME(){return rt}toggle(){this._element.setAttribute("aria-pressed",this._element.classList.toggle(at))}static jQueryInterface(t){return this.each((function(){const e=dt.getOrCreateInstance(this);"toggle"===t&&e[t]()}))}}z.on(document,ct,lt,(t=>{t.preventDefault();const e=t.target.closest(lt);dt.getOrCreateInstance(e).toggle()})),w(dt);const ut="swipe",ht=".bs.swipe",pt=`touchstart${ht}`,ft=`touchmove${ht}`,gt=`touchend${ht}`,mt=`pointerdown${ht}`,vt=`pointerup${ht}`,_t="touch",bt="pen",yt="pointer-event",wt=40,Ot={endCallback:null,leftCallback:null,rightCallback:null},At={endCallback:"(function|null)",leftCallback:"(function|null)",rightCallback:"(function|null)"};class Et extends U{constructor(t,e){super(),this._element=t,t&&Et.isSupported()&&(this._config=this._getConfig(e),this._deltaX=0,this._supportPointerEvents=Boolean(window.PointerEvent),this._initEvents())}static get Default(){return Ot}static get DefaultType(){return At}static get NAME(){return ut}dispose(){z.off(this._element,ht)}_start(t){this._supportPointerEvents?this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX):this._deltaX=t.touches[0].clientX}_end(t){this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX-this._deltaX),this._handleSwipe(),O(this._config.endCallback)}_move(t){this._deltaX=t.touches&&t.touches.length>1?0:t.touches[0].clientX-this._deltaX}_handleSwipe(){const t=Math.abs(this._deltaX);if(t<=wt)return;const e=t/this._deltaX;this._deltaX=0,e&&O(e>0?this._config.rightCallback:this._config.leftCallback)}_initEvents(){this._supportPointerEvents?(z.on(this._element,mt,(t=>this._start(t))),z.on(this._element,vt,(t=>this._end(t))),this._element.classList.add(yt)):(z.on(this._element,pt,(t=>this._start(t))),z.on(this._element,ft,(t=>this._move(t))),z.on(this._element,gt,(t=>this._end(t))))}_eventIsPointerPenTouch(t){return this._supportPointerEvents&&(t.pointerType===bt||t.pointerType===_t)}static isSupported(){return"ontouchstart"in document.documentElement||navigator.maxTouchPoints>0}}const xt="carousel",St=".bs.carousel",Ct=".data-api",kt="ArrowLeft",It="ArrowRight",Tt=500,Lt="next",Nt="prev",$t="left",Pt="right",Dt=`slide${St}`,jt=`slid${St}`,Ft=`keydown${St}`,Mt=`mouseenter${St}`,qt=`mouseleave${St}`,Rt=`dragstart${St}`,Ht=`load${St}${Ct}`,zt=`click${St}${Ct}`,Bt="carousel",Vt="active",Wt="slide",Kt="carousel-item-end",Ut="carousel-item-start",Qt="carousel-item-next",Jt="carousel-item-prev",Yt=".active",Xt=".carousel-item",Gt=Yt+Xt,Zt=".carousel-item img",te=".carousel-indicators",ee="[data-bs-slide], [data-bs-slide-to]",ie='[data-bs-ride="carousel"]',ne={[kt]:Pt,[It]:$t},se={interval:5e3,keyboard:!0,pause:"hover",ride:!1,touch:!0,wrap:!0},oe={interval:"(number|boolean)",keyboard:"boolean",pause:"(string|boolean)",ride:"(boolean|string)",touch:"boolean",wrap:"boolean"};class re extends J{constructor(t,e){super(t,e),this._interval=null,this._activeElement=null,this._isSliding=!1,this.touchTimeout=null,this._swipeHelper=null,this._indicatorsElement=X.findOne(te,this._element),this._addEventListeners(),this._config.ride===Bt&&this.cycle()}static get Default(){return se}static get DefaultType(){return oe}static get NAME(){return xt}next(){this._slide(Lt)}nextWhenVisible(){!document.hidden&&h(this._element)&&this.next()}prev(){this._slide(Nt)}pause(){this._isSliding&&c(this._element),this._clearInterval()}cycle(){this._clearInterval(),this._updateInterval(),this._interval=setInterval((()=>this.nextWhenVisible()),this._config.interval)}_maybeEnableCycle(){this._config.ride&&(this._isSliding?z.one(this._element,jt,(()=>this.cycle())):this.cycle())}to(t){const e=this._getItems();if(t>e.length-1||t<0)return;if(this._isSliding)return void z.one(this._element,jt,(()=>this.to(t)));const i=this._getItemIndex(this._getActive());if(i===t)return;const n=t>i?Lt:Nt;this._slide(n,e[t])}dispose(){this._swipeHelper&&this._swipeHelper.dispose(),super.dispose()}_configAfterMerge(t){return t.defaultInterval=t.interval,t}_addEventListeners(){this._config.keyboard&&z.on(this._element,Ft,(t=>this._keydown(t))),"hover"===this._config.pause&&(z.on(this._element,Mt,(()=>this.pause())),z.on(this._element,qt,(()=>this._maybeEnableCycle()))),this._config.touch&&Et.isSupported()&&this._addTouchEventListeners()}_addTouchEventListeners(){for(const t of X.find(Zt,this._element))z.on(t,Rt,(t=>t.preventDefault()));const t={leftCallback:()=>this._slide(this._directionToOrder($t)),rightCallback:()=>this._slide(this._directionToOrder(Pt)),endCallback:()=>{"hover"===this._config.pause&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout((()=>this._maybeEnableCycle()),Tt+this._config.interval))}};this._swipeHelper=new Et(this._element,t)}_keydown(t){if(/input|textarea/i.test(t.target.tagName))return;const e=ne[t.key];e&&(t.preventDefault(),this._slide(this._directionToOrder(e)))}_getItemIndex(t){return this._getItems().indexOf(t)}_setActiveIndicatorElement(t){if(!this._indicatorsElement)return;const e=X.findOne(Yt,this._indicatorsElement);e.classList.remove(Vt),e.removeAttribute("aria-current");const i=X.findOne(`[data-bs-slide-to="${t}"]`,this._indicatorsElement);i&&(i.classList.add(Vt),i.setAttribute("aria-current","true"))}_updateInterval(){const t=this._activeElement||this._getActive();if(!t)return;const e=Number.parseInt(t.getAttribute("data-bs-interval"),10);this._config.interval=e||this._config.defaultInterval}_slide(t,e=null){if(this._isSliding)return;const i=this._getActive(),n=t===Lt,s=e||E(this._getItems(),i,n,this._config.wrap);if(s===i)return;const o=this._getItemIndex(s),r=e=>z.trigger(this._element,e,{relatedTarget:s,direction:this._orderToDirection(t),from:this._getItemIndex(i),to:o});if(r(Dt).defaultPrevented)return;if(!i||!s)return;const a=Boolean(this._interval);this.pause(),this._isSliding=!0,this._setActiveIndicatorElement(o),this._activeElement=s;const l=n?Ut:Kt,c=n?Qt:Jt;s.classList.add(c),m(s),i.classList.add(l),s.classList.add(l);const d=()=>{s.classList.remove(l,c),s.classList.add(Vt),i.classList.remove(Vt,c,l),this._isSliding=!1,r(jt)};this._queueCallback(d,i,this._isAnimated()),a&&this.cycle()}_isAnimated(){return this._element.classList.contains(Wt)}_getActive(){return X.findOne(Gt,this._element)}_getItems(){return X.find(Xt,this._element)}_clearInterval(){this._interval&&(clearInterval(this._interval),this._interval=null)}_directionToOrder(t){return y()?t===$t?Nt:Lt:t===$t?Lt:Nt}_orderToDirection(t){return y()?t===Nt?$t:Pt:t===Nt?Pt:$t}static jQueryInterface(t){return this.each((function(){const e=re.getOrCreateInstance(this,t);if("number"!=typeof t){if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t]()}}else e.to(t)}))}}z.on(document,zt,ee,(function(t){const e=X.getElementFromSelector(this);if(!e||!e.classList.contains(Bt))return;t.preventDefault();const i=re.getOrCreateInstance(e),n=this.getAttribute("data-bs-slide-to");return n?(i.to(n),void i._maybeEnableCycle()):"next"===K.getDataAttribute(this,"slide")?(i.next(),void i._maybeEnableCycle()):(i.prev(),void i._maybeEnableCycle())})),z.on(window,Ht,(()=>{const t=X.find(ie);for(const e of t)re.getOrCreateInstance(e)})),w(re);const ae="collapse",le=".bs.collapse",ce=`show${le}`,de=`shown${le}`,ue=`hide${le}`,he=`hidden${le}`,pe=`click${le}.data-api`,fe="show",ge="collapse",me="collapsing",ve="collapsed",_e=`:scope .${ge} .${ge}`,be="collapse-horizontal",ye="width",we="height",Oe=".collapse.show, .collapse.collapsing",Ae='[data-bs-toggle="collapse"]',Ee={parent:null,toggle:!0},xe={parent:"(null|element)",toggle:"boolean"};class Se extends J{constructor(t,e){super(t,e),this._isTransitioning=!1,this._triggerArray=[];const i=X.find(Ae);for(const t of i){const e=X.getSelectorFromElement(t),i=X.find(e).filter((t=>t===this._element));null!==e&&i.length&&this._triggerArray.push(t)}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}static get Default(){return Ee}static get DefaultType(){return xe}static get NAME(){return ae}toggle(){this._isShown()?this.hide():this.show()}show(){if(this._isTransitioning||this._isShown())return;let t=[];if(this._config.parent&&(t=this._getFirstLevelChildren(Oe).filter((t=>t!==this._element)).map((t=>Se.getOrCreateInstance(t,{toggle:!1})))),t.length&&t[0]._isTransitioning)return;if(z.trigger(this._element,ce).defaultPrevented)return;for(const e of t)e.hide();const e=this._getDimension();this._element.classList.remove(ge),this._element.classList.add(me),this._element.style[e]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;const i=()=>{this._isTransitioning=!1,this._element.classList.remove(me),this._element.classList.add(ge,fe),this._element.style[e]="",z.trigger(this._element,de)},n=`scroll${e[0].toUpperCase()+e.slice(1)}`;this._queueCallback(i,this._element,!0),this._element.style[e]=`${this._element[n]}px`}hide(){if(this._isTransitioning||!this._isShown())return;if(z.trigger(this._element,ue).defaultPrevented)return;const t=this._getDimension();this._element.style[t]=`${this._element.getBoundingClientRect()[t]}px`,m(this._element),this._element.classList.add(me),this._element.classList.remove(ge,fe);for(const t of this._triggerArray){const e=X.getElementFromSelector(t);e&&!this._isShown(e)&&this._addAriaAndCollapsedClass([t],!1)}this._isTransitioning=!0;const e=()=>{this._isTransitioning=!1,this._element.classList.remove(me),this._element.classList.add(ge),z.trigger(this._element,he)};this._element.style[t]="",this._queueCallback(e,this._element,!0)}_isShown(t=this._element){return t.classList.contains(fe)}_configAfterMerge(t){return t.toggle=Boolean(t.toggle),t.parent=u(t.parent),t}_getDimension(){return this._element.classList.contains(be)?ye:we}_initializeChildren(){if(!this._config.parent)return;const t=this._getFirstLevelChildren(Ae);for(const e of t){const t=X.getElementFromSelector(e);t&&this._addAriaAndCollapsedClass([e],this._isShown(t))}}_getFirstLevelChildren(t){const e=X.find(_e,this._config.parent);return X.find(t,this._config.parent).filter((t=>!e.includes(t)))}_addAriaAndCollapsedClass(t,e){if(t.length)for(const i of t)i.classList.toggle(ve,!e),i.setAttribute("aria-expanded",e)}static jQueryInterface(t){const e={};return"string"==typeof t&&/show|hide/.test(t)&&(e.toggle=!1),this.each((function(){const i=Se.getOrCreateInstance(this,e);if("string"==typeof t){if(void 0===i[t])throw new TypeError(`No method named "${t}"`);i[t]()}}))}}z.on(document,pe,Ae,(function(t){("A"===t.target.tagName||t.delegateTarget&&"A"===t.delegateTarget.tagName)&&t.preventDefault();for(const t of X.getMultipleElementsFromSelector(this))Se.getOrCreateInstance(t,{toggle:!1}).toggle()})),w(Se);var Ce="top",ke="bottom",Ie="right",Te="left",Le="auto",Ne=[Ce,ke,Ie,Te],$e="start",Pe="end",De="clippingParents",je="viewport",Fe="popper",Me="reference",qe=Ne.reduce((function(t,e){return t.concat([e+"-"+$e,e+"-"+Pe])}),[]),Re=[].concat(Ne,[Le]).reduce((function(t,e){return t.concat([e,e+"-"+$e,e+"-"+Pe])}),[]),He="beforeRead",ze="read",Be="afterRead",Ve="beforeMain",We="main",Ke="afterMain",Ue="beforeWrite",Qe="write",Je="afterWrite",Ye=[He,ze,Be,Ve,We,Ke,Ue,Qe,Je];function Xe(t){return t?(t.nodeName||"").toLowerCase():null}function Ge(t){if(null==t)return window;if("[object Window]"!==t.toString()){var e=t.ownerDocument;return e&&e.defaultView||window}return t}function Ze(t){return t instanceof Ge(t).Element||t instanceof Element}function ti(t){return t instanceof Ge(t).HTMLElement||t instanceof HTMLElement}function ei(t){return"undefined"!=typeof ShadowRoot&&(t instanceof Ge(t).ShadowRoot||t instanceof ShadowRoot)}function ii(t){var e=t.state;Object.keys(e.elements).forEach((function(t){var i=e.styles[t]||{},n=e.attributes[t]||{},s=e.elements[t];ti(s)&&Xe(s)&&(Object.assign(s.style,i),Object.keys(n).forEach((function(t){var e=n[t];!1===e?s.removeAttribute(t):s.setAttribute(t,!0===e?"":e)})))}))}function ni(t){var e=t.state,i={popper:{position:e.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(e.elements.popper.style,i.popper),e.styles=i,e.elements.arrow&&Object.assign(e.elements.arrow.style,i.arrow),function(){Object.keys(e.elements).forEach((function(t){var n=e.elements[t],s=e.attributes[t]||{},o=Object.keys(e.styles.hasOwnProperty(t)?e.styles[t]:i[t]).reduce((function(t,e){return t[e]="",t}),{});ti(n)&&Xe(n)&&(Object.assign(n.style,o),Object.keys(s).forEach((function(t){n.removeAttribute(t)})))}))}}const si={name:"applyStyles",enabled:!0,phase:"write",fn:ii,effect:ni,requires:["computeStyles"]};function oi(t){return t.split("-")[0]}var ri=Math.max,ai=Math.min,li=Math.round;function ci(){var t=navigator.userAgentData;return null!=t&&t.brands&&Array.isArray(t.brands)?t.brands.map((function(t){return t.brand+"/"+t.version})).join(" "):navigator.userAgent}function di(){return!/^((?!chrome|android).)*safari/i.test(ci())}function ui(t,e,i){void 0===e&&(e=!1),void 0===i&&(i=!1);var n=t.getBoundingClientRect(),s=1,o=1;e&&ti(t)&&(s=t.offsetWidth>0&&li(n.width)/t.offsetWidth||1,o=t.offsetHeight>0&&li(n.height)/t.offsetHeight||1);var r=(Ze(t)?Ge(t):window).visualViewport,a=!di()&&i,l=(n.left+(a&&r?r.offsetLeft:0))/s,c=(n.top+(a&&r?r.offsetTop:0))/o,d=n.width/s,u=n.height/o;return{width:d,height:u,top:c,right:l+d,bottom:c+u,left:l,x:l,y:c}}function hi(t){var e=ui(t),i=t.offsetWidth,n=t.offsetHeight;return Math.abs(e.width-i)<=1&&(i=e.width),Math.abs(e.height-n)<=1&&(n=e.height),{x:t.offsetLeft,y:t.offsetTop,width:i,height:n}}function pi(t,e){var i=e.getRootNode&&e.getRootNode();if(t.contains(e))return!0;if(i&&ei(i)){var n=e;do{if(n&&t.isSameNode(n))return!0;n=n.parentNode||n.host}while(n)}return!1}function fi(t){return Ge(t).getComputedStyle(t)}function gi(t){return["table","td","th"].indexOf(Xe(t))>=0}function mi(t){return((Ze(t)?t.ownerDocument:t.document)||window.document).documentElement}function vi(t){return"html"===Xe(t)?t:t.assignedSlot||t.parentNode||(ei(t)?t.host:null)||mi(t)}function _i(t){return ti(t)&&"fixed"!==fi(t).position?t.offsetParent:null}function bi(t){var e=/firefox/i.test(ci());if(/Trident/i.test(ci())&&ti(t)&&"fixed"===fi(t).position)return null;var i=vi(t);for(ei(i)&&(i=i.host);ti(i)&&["html","body"].indexOf(Xe(i))<0;){var n=fi(i);if("none"!==n.transform||"none"!==n.perspective||"paint"===n.contain||-1!==["transform","perspective"].indexOf(n.willChange)||e&&"filter"===n.willChange||e&&n.filter&&"none"!==n.filter)return i;i=i.parentNode}return null}function yi(t){for(var e=Ge(t),i=_i(t);i&&gi(i)&&"static"===fi(i).position;)i=_i(i);return i&&("html"===Xe(i)||"body"===Xe(i)&&"static"===fi(i).position)?e:i||bi(t)||e}function wi(t){return["top","bottom"].indexOf(t)>=0?"x":"y"}function Oi(t,e,i){return ri(t,ai(e,i))}function Ai(t,e,i){var n=Oi(t,e,i);return n>i?i:n}function Ei(){return{top:0,right:0,bottom:0,left:0}}function xi(t){return Object.assign({},Ei(),t)}function Si(t,e){return e.reduce((function(e,i){return e[i]=t,e}),{})}var Ci=function(t,e){return xi("number"!=typeof(t="function"==typeof t?t(Object.assign({},e.rects,{placement:e.placement})):t)?t:Si(t,Ne))};function ki(t){var e,i=t.state,n=t.name,s=t.options,o=i.elements.arrow,r=i.modifiersData.popperOffsets,a=oi(i.placement),l=wi(a),c=[Te,Ie].indexOf(a)>=0?"height":"width";if(o&&r){var d=Ci(s.padding,i),u=hi(o),h="y"===l?Ce:Te,p="y"===l?ke:Ie,f=i.rects.reference[c]+i.rects.reference[l]-r[l]-i.rects.popper[c],g=r[l]-i.rects.reference[l],m=yi(o),v=m?"y"===l?m.clientHeight||0:m.clientWidth||0:0,_=f/2-g/2,b=d[h],y=v-u[c]-d[p],w=v/2-u[c]/2+_,O=Oi(b,w,y),A=l;i.modifiersData[n]=((e={})[A]=O,e.centerOffset=O-w,e)}}function Ii(t){var e=t.state,i=t.options.element,n=void 0===i?"[data-popper-arrow]":i;null!=n&&("string"!=typeof n||(n=e.elements.popper.querySelector(n)))&&pi(e.elements.popper,n)&&(e.elements.arrow=n)}const Ti={name:"arrow",enabled:!0,phase:"main",fn:ki,effect:Ii,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Li(t){return t.split("-")[1]}var Ni={top:"auto",right:"auto",bottom:"auto",left:"auto"};function $i(t,e){var i=t.x,n=t.y,s=e.devicePixelRatio||1;return{x:li(i*s)/s||0,y:li(n*s)/s||0}}function Pi(t){var e,i=t.popper,n=t.popperRect,s=t.placement,o=t.variation,r=t.offsets,a=t.position,l=t.gpuAcceleration,c=t.adaptive,d=t.roundOffsets,u=t.isFixed,h=r.x,p=void 0===h?0:h,f=r.y,g=void 0===f?0:f,m="function"==typeof d?d({x:p,y:g}):{x:p,y:g};p=m.x,g=m.y;var v=r.hasOwnProperty("x"),_=r.hasOwnProperty("y"),b=Te,y=Ce,w=window;if(c){var O=yi(i),A="clientHeight",E="clientWidth";O===Ge(i)&&"static"!==fi(O=mi(i)).position&&"absolute"===a&&(A="scrollHeight",E="scrollWidth"),(s===Ce||(s===Te||s===Ie)&&o===Pe)&&(y=ke,g-=(u&&O===w&&w.visualViewport?w.visualViewport.height:O[A])-n.height,g*=l?1:-1),s!==Te&&(s!==Ce&&s!==ke||o!==Pe)||(b=Ie,p-=(u&&O===w&&w.visualViewport?w.visualViewport.width:O[E])-n.width,p*=l?1:-1)}var x,S=Object.assign({position:a},c&&Ni),C=!0===d?$i({x:p,y:g},Ge(i)):{x:p,y:g};return p=C.x,g=C.y,l?Object.assign({},S,((x={})[y]=_?"0":"",x[b]=v?"0":"",x.transform=(w.devicePixelRatio||1)<=1?"translate("+p+"px, "+g+"px)":"translate3d("+p+"px, "+g+"px, 0)",x)):Object.assign({},S,((e={})[y]=_?g+"px":"",e[b]=v?p+"px":"",e.transform="",e))}function Di(t){var e=t.state,i=t.options,n=i.gpuAcceleration,s=void 0===n||n,o=i.adaptive,r=void 0===o||o,a=i.roundOffsets,l=void 0===a||a,c={placement:oi(e.placement),variation:Li(e.placement),popper:e.elements.popper,popperRect:e.rects.popper,gpuAcceleration:s,isFixed:"fixed"===e.options.strategy};null!=e.modifiersData.popperOffsets&&(e.styles.popper=Object.assign({},e.styles.popper,Pi(Object.assign({},c,{offsets:e.modifiersData.popperOffsets,position:e.options.strategy,adaptive:r,roundOffsets:l})))),null!=e.modifiersData.arrow&&(e.styles.arrow=Object.assign({},e.styles.arrow,Pi(Object.assign({},c,{offsets:e.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:l})))),e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-placement":e.placement})}const ji={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:Di,data:{}};var Fi={passive:!0};function Mi(t){var e=t.state,i=t.instance,n=t.options,s=n.scroll,o=void 0===s||s,r=n.resize,a=void 0===r||r,l=Ge(e.elements.popper),c=[].concat(e.scrollParents.reference,e.scrollParents.popper);return o&&c.forEach((function(t){t.addEventListener("scroll",i.update,Fi)})),a&&l.addEventListener("resize",i.update,Fi),function(){o&&c.forEach((function(t){t.removeEventListener("scroll",i.update,Fi)})),a&&l.removeEventListener("resize",i.update,Fi)}}const qi={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:Mi,data:{}};var Ri={left:"right",right:"left",bottom:"top",top:"bottom"};function Hi(t){return t.replace(/left|right|bottom|top/g,(function(t){return Ri[t]}))}var zi={start:"end",end:"start"};function Bi(t){return t.replace(/start|end/g,(function(t){return zi[t]}))}function Vi(t){var e=Ge(t);return{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function Wi(t){return ui(mi(t)).left+Vi(t).scrollLeft}function Ki(t,e){var i=Ge(t),n=mi(t),s=i.visualViewport,o=n.clientWidth,r=n.clientHeight,a=0,l=0;if(s){o=s.width,r=s.height;var c=di();(c||!c&&"fixed"===e)&&(a=s.offsetLeft,l=s.offsetTop)}return{width:o,height:r,x:a+Wi(t),y:l}}function Ui(t){var e,i=mi(t),n=Vi(t),s=null==(e=t.ownerDocument)?void 0:e.body,o=ri(i.scrollWidth,i.clientWidth,s?s.scrollWidth:0,s?s.clientWidth:0),r=ri(i.scrollHeight,i.clientHeight,s?s.scrollHeight:0,s?s.clientHeight:0),a=-n.scrollLeft+Wi(t),l=-n.scrollTop;return"rtl"===fi(s||i).direction&&(a+=ri(i.clientWidth,s?s.clientWidth:0)-o),{width:o,height:r,x:a,y:l}}function Qi(t){var e=fi(t),i=e.overflow,n=e.overflowX,s=e.overflowY;return/auto|scroll|overlay|hidden/.test(i+s+n)}function Ji(t){return["html","body","#document"].indexOf(Xe(t))>=0?t.ownerDocument.body:ti(t)&&Qi(t)?t:Ji(vi(t))}function Yi(t,e){var i;void 0===e&&(e=[]);var n=Ji(t),s=n===(null==(i=t.ownerDocument)?void 0:i.body),o=Ge(n),r=s?[o].concat(o.visualViewport||[],Qi(n)?n:[]):n,a=e.concat(r);return s?a:a.concat(Yi(vi(r)))}function Xi(t){return Object.assign({},t,{left:t.x,top:t.y,right:t.x+t.width,bottom:t.y+t.height})}function Gi(t,e){var i=ui(t,!1,"fixed"===e);return i.top=i.top+t.clientTop,i.left=i.left+t.clientLeft,i.bottom=i.top+t.clientHeight,i.right=i.left+t.clientWidth,i.width=t.clientWidth,i.height=t.clientHeight,i.x=i.left,i.y=i.top,i}function Zi(t,e,i){return e===je?Xi(Ki(t,i)):Ze(e)?Gi(e,i):Xi(Ui(mi(t)))}function tn(t){var e=Yi(vi(t)),i=["absolute","fixed"].indexOf(fi(t).position)>=0&&ti(t)?yi(t):t;return Ze(i)?e.filter((function(t){return Ze(t)&&pi(t,i)&&"body"!==Xe(t)})):[]}function en(t,e,i,n){var s="clippingParents"===e?tn(t):[].concat(e),o=[].concat(s,[i]),r=o[0],a=o.reduce((function(e,i){var s=Zi(t,i,n);return e.top=ri(s.top,e.top),e.right=ai(s.right,e.right),e.bottom=ai(s.bottom,e.bottom),e.left=ri(s.left,e.left),e}),Zi(t,r,n));return a.width=a.right-a.left,a.height=a.bottom-a.top,a.x=a.left,a.y=a.top,a}function nn(t){var e,i=t.reference,n=t.element,s=t.placement,o=s?oi(s):null,r=s?Li(s):null,a=i.x+i.width/2-n.width/2,l=i.y+i.height/2-n.height/2;switch(o){case Ce:e={x:a,y:i.y-n.height};break;case ke:e={x:a,y:i.y+i.height};break;case Ie:e={x:i.x+i.width,y:l};break;case Te:e={x:i.x-n.width,y:l};break;default:e={x:i.x,y:i.y}}var c=o?wi(o):null;if(null!=c){var d="y"===c?"height":"width";switch(r){case $e:e[c]=e[c]-(i[d]/2-n[d]/2);break;case Pe:e[c]=e[c]+(i[d]/2-n[d]/2)}}return e}function sn(t,e){void 0===e&&(e={});var i=e,n=i.placement,s=void 0===n?t.placement:n,o=i.strategy,r=void 0===o?t.strategy:o,a=i.boundary,l=void 0===a?De:a,c=i.rootBoundary,d=void 0===c?je:c,u=i.elementContext,h=void 0===u?Fe:u,p=i.altBoundary,f=void 0!==p&&p,g=i.padding,m=void 0===g?0:g,v=xi("number"!=typeof m?m:Si(m,Ne)),_=h===Fe?Me:Fe,b=t.rects.popper,y=t.elements[f?_:h],w=en(Ze(y)?y:y.contextElement||mi(t.elements.popper),l,d,r),O=ui(t.elements.reference),A=nn({reference:O,element:b,strategy:"absolute",placement:s}),E=Xi(Object.assign({},b,A)),x=h===Fe?E:O,S={top:w.top-x.top+v.top,bottom:x.bottom-w.bottom+v.bottom,left:w.left-x.left+v.left,right:x.right-w.right+v.right},C=t.modifiersData.offset;if(h===Fe&&C){var k=C[s];Object.keys(S).forEach((function(t){var e=[Ie,ke].indexOf(t)>=0?1:-1,i=[Ce,ke].indexOf(t)>=0?"y":"x";S[t]+=k[i]*e}))}return S}function on(t,e){void 0===e&&(e={});var i=e,n=i.placement,s=i.boundary,o=i.rootBoundary,r=i.padding,a=i.flipVariations,l=i.allowedAutoPlacements,c=void 0===l?Re:l,d=Li(n),u=d?a?qe:qe.filter((function(t){return Li(t)===d})):Ne,h=u.filter((function(t){return c.indexOf(t)>=0}));0===h.length&&(h=u);var p=h.reduce((function(e,i){return e[i]=sn(t,{placement:i,boundary:s,rootBoundary:o,padding:r})[oi(i)],e}),{});return Object.keys(p).sort((function(t,e){return p[t]-p[e]}))}function rn(t){if(oi(t)===Le)return[];var e=Hi(t);return[Bi(t),e,Bi(e)]}function an(t){var e=t.state,i=t.options,n=t.name;if(!e.modifiersData[n]._skip){for(var s=i.mainAxis,o=void 0===s||s,r=i.altAxis,a=void 0===r||r,l=i.fallbackPlacements,c=i.padding,d=i.boundary,u=i.rootBoundary,h=i.altBoundary,p=i.flipVariations,f=void 0===p||p,g=i.allowedAutoPlacements,m=e.options.placement,v=oi(m),_=l||(v!==m&&f?rn(m):[Hi(m)]),b=[m].concat(_).reduce((function(t,i){return t.concat(oi(i)===Le?on(e,{placement:i,boundary:d,rootBoundary:u,padding:c,flipVariations:f,allowedAutoPlacements:g}):i)}),[]),y=e.rects.reference,w=e.rects.popper,O=new Map,A=!0,E=b[0],x=0;x<b.length;x++){var S=b[x],C=oi(S),k=Li(S)===$e,I=[Ce,ke].indexOf(C)>=0,T=I?"width":"height",L=sn(e,{placement:S,boundary:d,rootBoundary:u,altBoundary:h,padding:c}),N=I?k?Ie:Te:k?ke:Ce;y[T]>w[T]&&(N=Hi(N));var $=Hi(N),P=[];if(o&&P.push(L[C]<=0),a&&P.push(L[N]<=0,L[$]<=0),P.every((function(t){return t}))){E=S,A=!1;break}O.set(S,P)}if(A)for(var D=function(t){var e=b.find((function(e){var i=O.get(e);if(i)return i.slice(0,t).every((function(t){return t}))}));if(e)return E=e,"break"},j=f?3:1;j>0&&"break"!==D(j);j--);e.placement!==E&&(e.modifiersData[n]._skip=!0,e.placement=E,e.reset=!0)}}const ln={name:"flip",enabled:!0,phase:"main",fn:an,requiresIfExists:["offset"],data:{_skip:!1}};function cn(t,e,i){return void 0===i&&(i={x:0,y:0}),{top:t.top-e.height-i.y,right:t.right-e.width+i.x,bottom:t.bottom-e.height+i.y,left:t.left-e.width-i.x}}function dn(t){return[Ce,Ie,ke,Te].some((function(e){return t[e]>=0}))}function un(t){var e=t.state,i=t.name,n=e.rects.reference,s=e.rects.popper,o=e.modifiersData.preventOverflow,r=sn(e,{elementContext:"reference"}),a=sn(e,{altBoundary:!0}),l=cn(r,n),c=cn(a,s,o),d=dn(l),u=dn(c);e.modifiersData[i]={referenceClippingOffsets:l,popperEscapeOffsets:c,isReferenceHidden:d,hasPopperEscaped:u},e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-reference-hidden":d,"data-popper-escaped":u})}const hn={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:un};function pn(t,e,i){var n=oi(t),s=[Te,Ce].indexOf(n)>=0?-1:1,o="function"==typeof i?i(Object.assign({},e,{placement:t})):i,r=o[0],a=o[1];return r=r||0,a=(a||0)*s,[Te,Ie].indexOf(n)>=0?{x:a,y:r}:{x:r,y:a}}function fn(t){var e=t.state,i=t.options,n=t.name,s=i.offset,o=void 0===s?[0,0]:s,r=Re.reduce((function(t,i){return t[i]=pn(i,e.rects,o),t}),{}),a=r[e.placement],l=a.x,c=a.y;null!=e.modifiersData.popperOffsets&&(e.modifiersData.popperOffsets.x+=l,e.modifiersData.popperOffsets.y+=c),e.modifiersData[n]=r}const gn={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn};function mn(t){var e=t.state,i=t.name;e.modifiersData[i]=nn({reference:e.rects.reference,element:e.rects.popper,strategy:"absolute",placement:e.placement})}const vn={name:"popperOffsets",enabled:!0,phase:"read",fn:mn,data:{}};function _n(t){return"x"===t?"y":"x"}function bn(t){var e=t.state,i=t.options,n=t.name,s=i.mainAxis,o=void 0===s||s,r=i.altAxis,a=void 0!==r&&r,l=i.boundary,c=i.rootBoundary,d=i.altBoundary,u=i.padding,h=i.tether,p=void 0===h||h,f=i.tetherOffset,g=void 0===f?0:f,m=sn(e,{boundary:l,rootBoundary:c,padding:u,altBoundary:d}),v=oi(e.placement),_=Li(e.placement),b=!_,y=wi(v),w=_n(y),O=e.modifiersData.popperOffsets,A=e.rects.reference,E=e.rects.popper,x="function"==typeof g?g(Object.assign({},e.rects,{placement:e.placement})):g,S="number"==typeof x?{mainAxis:x,altAxis:x}:Object.assign({mainAxis:0,altAxis:0},x),C=e.modifiersData.offset?e.modifiersData.offset[e.placement]:null,k={x:0,y:0};if(O){if(o){var I,T="y"===y?Ce:Te,L="y"===y?ke:Ie,N="y"===y?"height":"width",$=O[y],P=$+m[T],D=$-m[L],j=p?-E[N]/2:0,F=_===$e?A[N]:E[N],M=_===$e?-E[N]:-A[N],q=e.elements.arrow,R=p&&q?hi(q):{width:0,height:0},H=e.modifiersData["arrow#persistent"]?e.modifiersData["arrow#persistent"].padding:Ei(),z=H[T],B=H[L],V=Oi(0,A[N],R[N]),W=b?A[N]/2-j-V-z-S.mainAxis:F-V-z-S.mainAxis,K=b?-A[N]/2+j+V+B+S.mainAxis:M+V+B+S.mainAxis,U=e.elements.arrow&&yi(e.elements.arrow),Q=U?"y"===y?U.clientTop||0:U.clientLeft||0:0,J=null!=(I=null==C?void 0:C[y])?I:0,Y=$+K-J,X=Oi(p?ai(P,$+W-J-Q):P,$,p?ri(D,Y):D);O[y]=X,k[y]=X-$}if(a){var G,Z="x"===y?Ce:Te,tt="x"===y?ke:Ie,et=O[w],it="y"===w?"height":"width",nt=et+m[Z],st=et-m[tt],ot=-1!==[Ce,Te].indexOf(v),rt=null!=(G=null==C?void 0:C[w])?G:0,at=ot?nt:et-A[it]-E[it]-rt+S.altAxis,lt=ot?et+A[it]+E[it]-rt-S.altAxis:st,ct=p&&ot?Ai(at,et,lt):Oi(p?at:nt,et,p?lt:st);O[w]=ct,k[w]=ct-et}e.modifiersData[n]=k}}const yn={name:"preventOverflow",enabled:!0,phase:"main",fn:bn,requiresIfExists:["offset"]};function wn(t){return{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}}function On(t){return t!==Ge(t)&&ti(t)?wn(t):Vi(t)}function An(t){var e=t.getBoundingClientRect(),i=li(e.width)/t.offsetWidth||1,n=li(e.height)/t.offsetHeight||1;return 1!==i||1!==n}function En(t,e,i){void 0===i&&(i=!1);var n=ti(e),s=ti(e)&&An(e),o=mi(e),r=ui(t,s,i),a={scrollLeft:0,scrollTop:0},l={x:0,y:0};return(n||!n&&!i)&&(("body"!==Xe(e)||Qi(o))&&(a=On(e)),ti(e)?((l=ui(e,!0)).x+=e.clientLeft,l.y+=e.clientTop):o&&(l.x=Wi(o))),{x:r.left+a.scrollLeft-l.x,y:r.top+a.scrollTop-l.y,width:r.width,height:r.height}}function xn(t){var e=new Map,i=new Set,n=[];function s(t){i.add(t.name),[].concat(t.requires||[],t.requiresIfExists||[]).forEach((function(t){if(!i.has(t)){var n=e.get(t);n&&s(n)}})),n.push(t)}return t.forEach((function(t){e.set(t.name,t)})),t.forEach((function(t){i.has(t.name)||s(t)})),n}function Sn(t){var e=xn(t);return Ye.reduce((function(t,i){return t.concat(e.filter((function(t){return t.phase===i})))}),[])}function Cn(t){var e;return function(){return e||(e=new Promise((function(i){Promise.resolve().then((function(){e=void 0,i(t())}))}))),e}}function kn(t){var e=t.reduce((function(t,e){var i=t[e.name];return t[e.name]=i?Object.assign({},i,e,{options:Object.assign({},i.options,e.options),data:Object.assign({},i.data,e.data)}):e,t}),{});return Object.keys(e).map((function(t){return e[t]}))}var In={placement:"bottom",modifiers:[],strategy:"absolute"};function Tn(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];return!e.some((function(t){return!(t&&"function"==typeof t.getBoundingClientRect)}))}function Ln(t){void 0===t&&(t={});var e=t,i=e.defaultModifiers,n=void 0===i?[]:i,s=e.defaultOptions,o=void 0===s?In:s;return function(t,e,i){void 0===i&&(i=o);var s={placement:"bottom",orderedModifiers:[],options:Object.assign({},In,o),modifiersData:{},elements:{reference:t,popper:e},attributes:{},styles:{}},r=[],a=!1,l={state:s,setOptions:function(i){var r="function"==typeof i?i(s.options):i;d(),s.options=Object.assign({},o,s.options,r),s.scrollParents={reference:Ze(t)?Yi(t):t.contextElement?Yi(t.contextElement):[],popper:Yi(e)};var a=Sn(kn([].concat(n,s.options.modifiers)));return s.orderedModifiers=a.filter((function(t){return t.enabled})),c(),l.update()},forceUpdate:function(){if(!a){var t=s.elements,e=t.reference,i=t.popper;if(Tn(e,i)){s.rects={reference:En(e,yi(i),"fixed"===s.options.strategy),popper:hi(i)},s.reset=!1,s.placement=s.options.placement,s.orderedModifiers.forEach((function(t){return s.modifiersData[t.name]=Object.assign({},t.data)}));for(var n=0;n<s.orderedModifiers.length;n++)if(!0!==s.reset){var o=s.orderedModifiers[n],r=o.fn,c=o.options,d=void 0===c?{}:c,u=o.name;"function"==typeof r&&(s=r({state:s,options:d,name:u,instance:l})||s)}else s.reset=!1,n=-1}}},update:Cn((function(){return new Promise((function(t){l.forceUpdate(),t(s)}))})),destroy:function(){d(),a=!0}};if(!Tn(t,e))return l;function c(){s.orderedModifiers.forEach((function(t){var e=t.name,i=t.options,n=void 0===i?{}:i,o=t.effect;if("function"==typeof o){var a=o({state:s,name:e,instance:l,options:n}),c=function(){};r.push(a||c)}}))}function d(){r.forEach((function(t){return t()})),r=[]}return l.setOptions(i).then((function(t){!a&&i.onFirstUpdate&&i.onFirstUpdate(t)})),l}}var Nn=Ln(),$n=Ln({defaultModifiers:[qi,vn,ji,si]}),Pn=Ln({defaultModifiers:[qi,vn,ji,si,gn,ln,yn,Ti,hn]});const Dn=Object.freeze(Object.defineProperty({__proto__:null,afterMain:Ke,afterRead:Be,afterWrite:Je,applyStyles:si,arrow:Ti,auto:Le,basePlacements:Ne,beforeMain:Ve,beforeRead:He,beforeWrite:Ue,bottom:ke,clippingParents:De,computeStyles:ji,createPopper:Pn,createPopperBase:Nn,createPopperLite:$n,detectOverflow:sn,end:Pe,eventListeners:qi,flip:ln,hide:hn,left:Te,main:We,modifierPhases:Ye,offset:gn,placements:Re,popper:Fe,popperGenerator:Ln,popperOffsets:vn,preventOverflow:yn,read:ze,reference:Me,right:Ie,start:$e,top:Ce,variationPlacements:qe,viewport:je,write:Qe},Symbol.toStringTag,{value:"Module"})),jn="dropdown",Fn=".bs.dropdown",Mn=".data-api",qn="Escape",Rn="Tab",Hn="ArrowUp",zn="ArrowDown",Bn=2,Vn=`hide${Fn}`,Wn=`hidden${Fn}`,Kn=`show${Fn}`,Un=`shown${Fn}`,Qn=`click${Fn}${Mn}`,Jn=`keydown${Fn}${Mn}`,Yn=`keyup${Fn}${Mn}`,Xn="show",Gn="dropup",Zn="dropend",ts="dropstart",es="dropup-center",is="dropdown-center",ns='[data-bs-toggle="dropdown"]:not(.disabled):not(:disabled)',ss=`${ns}.${Xn}`,os=".dropdown-menu",rs=".navbar",as=".navbar-nav",ls=".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",cs=y()?"top-end":"top-start",ds=y()?"top-start":"top-end",us=y()?"bottom-end":"bottom-start",hs=y()?"bottom-start":"bottom-end",ps=y()?"left-start":"right-start",fs=y()?"right-start":"left-start",gs="top",ms="bottom",vs={autoClose:!0,boundary:"clippingParents",display:"dynamic",offset:[0,2],popperConfig:null,reference:"toggle"},_s={autoClose:"(boolean|string)",boundary:"(string|element)",display:"string",offset:"(array|string|function)",popperConfig:"(null|object|function)",reference:"(string|element|object)"};class bs extends J{constructor(t,e){super(t,e),this._popper=null,this._parent=this._element.parentNode,this._menu=X.next(this._element,os)[0]||X.prev(this._element,os)[0]||X.findOne(os,this._parent),this._inNavbar=this._detectNavbar()}static get Default(){return vs}static get DefaultType(){return _s}static get NAME(){return jn}toggle(){return this._isShown()?this.hide():this.show()}show(){if(p(this._element)||this._isShown())return;const t={relatedTarget:this._element};if(!z.trigger(this._element,Kn,t).defaultPrevented){if(this._createPopper(),"ontouchstart"in document.documentElement&&!this._parent.closest(as))for(const t of[].concat(...document.body.children))z.on(t,"mouseover",g);this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.add(Xn),this._element.classList.add(Xn),z.trigger(this._element,Un,t)}}hide(){if(p(this._element)||!this._isShown())return;const t={relatedTarget:this._element};this._completeHide(t)}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_completeHide(t){if(!z.trigger(this._element,Vn,t).defaultPrevented){if("ontouchstart"in document.documentElement)for(const t of[].concat(...document.body.children))z.off(t,"mouseover",g);this._popper&&this._popper.destroy(),this._menu.classList.remove(Xn),this._element.classList.remove(Xn),this._element.setAttribute("aria-expanded","false"),K.removeDataAttribute(this._menu,"popper"),z.trigger(this._element,Wn,t)}}_getConfig(t){if("object"==typeof(t=super._getConfig(t)).reference&&!d(t.reference)&&"function"!=typeof t.reference.getBoundingClientRect)throw new TypeError(`${jn.toUpperCase()}: Option "reference" provided type "object" without a required "getBoundingClientRect" method.`);return t}_createPopper(){if(void 0===Dn)throw new TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org)");let t=this._element;"parent"===this._config.reference?t=this._parent:d(this._config.reference)?t=u(this._config.reference):"object"==typeof this._config.reference&&(t=this._config.reference);const e=this._getPopperConfig();this._popper=Pn(t,this._menu,e)}_isShown(){return this._menu.classList.contains(Xn)}_getPlacement(){const t=this._parent;if(t.classList.contains(Zn))return ps;if(t.classList.contains(ts))return fs;if(t.classList.contains(es))return gs;if(t.classList.contains(is))return ms;const e="end"===getComputedStyle(this._menu).getPropertyValue("--bs-position").trim();return t.classList.contains(Gn)?e?ds:cs:e?hs:us}_detectNavbar(){return null!==this._element.closest(rs)}_getOffset(){const{offset:t}=this._config;return"string"==typeof t?t.split(",").map((t=>Number.parseInt(t,10))):"function"==typeof t?e=>t(e,this._element):t}_getPopperConfig(){const t={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return(this._inNavbar||"static"===this._config.display)&&(K.setDataAttribute(this._menu,"popper","static"),t.modifiers=[{name:"applyStyles",enabled:!1}]),{...t,...O(this._config.popperConfig,[t])}}_selectMenuItem({key:t,target:e}){const i=X.find(ls,this._menu).filter((t=>h(t)));i.length&&E(i,e,t===zn,!i.includes(e)).focus()}static jQueryInterface(t){return this.each((function(){const e=bs.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t]()}}))}static clearMenus(t){if(t.button===Bn||"keyup"===t.type&&t.key!==Rn)return;const e=X.find(ss);for(const i of e){const e=bs.getInstance(i);if(!e||!1===e._config.autoClose)continue;const n=t.composedPath(),s=n.includes(e._menu);if(n.includes(e._element)||"inside"===e._config.autoClose&&!s||"outside"===e._config.autoClose&&s)continue;if(e._menu.contains(t.target)&&("keyup"===t.type&&t.key===Rn||/input|select|option|textarea|form/i.test(t.target.tagName)))continue;const o={relatedTarget:e._element};"click"===t.type&&(o.clickEvent=t),e._completeHide(o)}}static dataApiKeydownHandler(t){const e=/input|textarea/i.test(t.target.tagName),i=t.key===qn,n=[Hn,zn].includes(t.key);if(!n&&!i)return;if(e&&!i)return;t.preventDefault();const s=this.matches(ns)?this:X.prev(this,ns)[0]||X.next(this,ns)[0]||X.findOne(ns,t.delegateTarget.parentNode),o=bs.getOrCreateInstance(s);if(n)return t.stopPropagation(),o.show(),void o._selectMenuItem(t);o._isShown()&&(t.stopPropagation(),o.hide(),s.focus())}}z.on(document,Jn,ns,bs.dataApiKeydownHandler),z.on(document,Jn,os,bs.dataApiKeydownHandler),z.on(document,Qn,bs.clearMenus),z.on(document,Yn,bs.clearMenus),z.on(document,Qn,ns,(function(t){t.preventDefault(),bs.getOrCreateInstance(this).toggle()})),w(bs);const ys="backdrop",ws="fade",Os="show",As=`mousedown.bs.${ys}`,Es={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},xs={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"};class Ss extends U{constructor(t){super(),this._config=this._getConfig(t),this._isAppended=!1,this._element=null}static get Default(){return Es}static get DefaultType(){return xs}static get NAME(){return ys}show(t){if(!this._config.isVisible)return void O(t);this._append();const e=this._getElement();this._config.isAnimated&&m(e),e.classList.add(Os),this._emulateAnimation((()=>{O(t)}))}hide(t){this._config.isVisible?(this._getElement().classList.remove(Os),this._emulateAnimation((()=>{this.dispose(),O(t)}))):O(t)}dispose(){this._isAppended&&(z.off(this._element,As),this._element.remove(),this._isAppended=!1)}_getElement(){if(!this._element){const t=document.createElement("div");t.className=this._config.className,this._config.isAnimated&&t.classList.add(ws),this._element=t}return this._element}_configAfterMerge(t){return t.rootElement=u(t.rootElement),t}_append(){if(this._isAppended)return;const t=this._getElement();this._config.rootElement.append(t),z.on(t,As,(()=>{O(this._config.clickCallback)})),this._isAppended=!0}_emulateAnimation(t){A(t,this._getElement(),this._config.isAnimated)}}const Cs="focustrap",ks=".bs.focustrap",Is=`focusin${ks}`,Ts=`keydown.tab${ks}`,Ls="Tab",Ns="forward",$s="backward",Ps={autofocus:!0,trapElement:null},Ds={autofocus:"boolean",trapElement:"element"};class js extends U{constructor(t){super(),this._config=this._getConfig(t),this._isActive=!1,this._lastTabNavDirection=null}static get Default(){return Ps}static get DefaultType(){return Ds}static get NAME(){return Cs}activate(){this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),z.off(document,ks),z.on(document,Is,(t=>this._handleFocusin(t))),z.on(document,Ts,(t=>this._handleKeydown(t))),this._isActive=!0)}deactivate(){this._isActive&&(this._isActive=!1,z.off(document,ks))}_handleFocusin(t){const{trapElement:e}=this._config;if(t.target===document||t.target===e||e.contains(t.target))return;const i=X.focusableChildren(e);0===i.length?e.focus():this._lastTabNavDirection===$s?i[i.length-1].focus():i[0].focus()}_handleKeydown(t){t.key===Ls&&(this._lastTabNavDirection=t.shiftKey?$s:Ns)}}const Fs=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",Ms=".sticky-top",qs="padding-right",Rs="margin-right";class Hs{constructor(){this._element=document.body}getWidth(){const t=document.documentElement.clientWidth;return Math.abs(window.innerWidth-t)}hide(){const t=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,qs,(e=>e+t)),this._setElementAttributes(Fs,qs,(e=>e+t)),this._setElementAttributes(Ms,Rs,(e=>e-t))}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,qs),this._resetElementAttributes(Fs,qs),this._resetElementAttributes(Ms,Rs)}isOverflowing(){return this.getWidth()>0}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(t,e,i){const n=this.getWidth(),s=t=>{if(t!==this._element&&window.innerWidth>t.clientWidth+n)return;this._saveInitialAttribute(t,e);const s=window.getComputedStyle(t).getPropertyValue(e);t.style.setProperty(e,`${i(Number.parseFloat(s))}px`)};this._applyManipulationCallback(t,s)}_saveInitialAttribute(t,e){const i=t.style.getPropertyValue(e);i&&K.setDataAttribute(t,e,i)}_resetElementAttributes(t,e){const i=t=>{const i=K.getDataAttribute(t,e);null!==i?(K.removeDataAttribute(t,e),t.style.setProperty(e,i)):t.style.removeProperty(e)};this._applyManipulationCallback(t,i)}_applyManipulationCallback(t,e){if(d(t))e(t);else for(const i of X.find(t,this._element))e(i)}}const zs="modal",Bs=".bs.modal",Vs="Escape",Ws=`hide${Bs}`,Ks=`hidePrevented${Bs}`,Us=`hidden${Bs}`,Qs=`show${Bs}`,Js=`shown${Bs}`,Ys=`resize${Bs}`,Xs=`click.dismiss${Bs}`,Gs=`mousedown.dismiss${Bs}`,Zs=`keydown.dismiss${Bs}`,to=`click${Bs}.data-api`,eo="modal-open",io="fade",no="show",so="modal-static",oo=".modal.show",ro=".modal-dialog",ao=".modal-body",lo='[data-bs-toggle="modal"]',co={backdrop:!0,focus:!0,keyboard:!0},uo={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"};class ho extends J{constructor(t,e){super(t,e),this._dialog=X.findOne(ro,this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._isTransitioning=!1,this._scrollBar=new Hs,this._addEventListeners()}static get Default(){return co}static get DefaultType(){return uo}static get NAME(){return zs}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){this._isShown||this._isTransitioning||z.trigger(this._element,Qs,{relatedTarget:t}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add(eo),this._adjustDialog(),this._backdrop.show((()=>this._showElement(t))))}hide(){this._isShown&&!this._isTransitioning&&(z.trigger(this._element,Ws).defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove(no),this._queueCallback((()=>this._hideModal()),this._element,this._isAnimated())))}dispose(){z.off(window,Bs),z.off(this._dialog,Bs),this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new Ss({isVisible:Boolean(this._config.backdrop),isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new js({trapElement:this._element})}_showElement(t){document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0;const e=X.findOne(ao,this._dialog);e&&(e.scrollTop=0),m(this._element),this._element.classList.add(no);const i=()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,z.trigger(this._element,Js,{relatedTarget:t})};this._queueCallback(i,this._dialog,this._isAnimated())}_addEventListeners(){z.on(this._element,Zs,(t=>{t.key===Vs&&(this._config.keyboard?this.hide():this._triggerBackdropTransition())})),z.on(window,Ys,(()=>{this._isShown&&!this._isTransitioning&&this._adjustDialog()})),z.on(this._element,Gs,(t=>{z.one(this._element,Xs,(e=>{this._element===t.target&&this._element===e.target&&("static"!==this._config.backdrop?this._config.backdrop&&this.hide():this._triggerBackdropTransition())}))}))}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide((()=>{document.body.classList.remove(eo),this._resetAdjustments(),this._scrollBar.reset(),z.trigger(this._element,Us)}))}_isAnimated(){return this._element.classList.contains(io)}_triggerBackdropTransition(){if(z.trigger(this._element,Ks).defaultPrevented)return;const t=this._element.scrollHeight>document.documentElement.clientHeight,e=this._element.style.overflowY;"hidden"===e||this._element.classList.contains(so)||(t||(this._element.style.overflowY="hidden"),this._element.classList.add(so),this._queueCallback((()=>{this._element.classList.remove(so),this._queueCallback((()=>{this._element.style.overflowY=e}),this._dialog)}),this._dialog),this._element.focus())}_adjustDialog(){const t=this._element.scrollHeight>document.documentElement.clientHeight,e=this._scrollBar.getWidth(),i=e>0;if(i&&!t){const t=y()?"paddingLeft":"paddingRight";this._element.style[t]=`${e}px`}if(!i&&t){const t=y()?"paddingRight":"paddingLeft";this._element.style[t]=`${e}px`}}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(t,e){return this.each((function(){const i=ho.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===i[t])throw new TypeError(`No method named "${t}"`);i[t](e)}}))}}z.on(document,to,lo,(function(t){const e=X.getElementFromSelector(this);["A","AREA"].includes(this.tagName)&&t.preventDefault(),z.one(e,Qs,(t=>{t.defaultPrevented||z.one(e,Us,(()=>{h(this)&&this.focus()}))}));const i=X.findOne(oo);i&&ho.getInstance(i).hide(),ho.getOrCreateInstance(e).toggle(this)})),G(ho),w(ho);const po="offcanvas",fo=".bs.offcanvas",go=".data-api",mo=`load${fo}${go}`,vo="Escape",_o="show",bo="showing",yo="hiding",wo="offcanvas-backdrop",Oo=".offcanvas.show",Ao=`show${fo}`,Eo=`shown${fo}`,xo=`hide${fo}`,So=`hidePrevented${fo}`,Co=`hidden${fo}`,ko=`resize${fo}`,Io=`click${fo}${go}`,To=`keydown.dismiss${fo}`,Lo='[data-bs-toggle="offcanvas"]',No={backdrop:!0,keyboard:!0,scroll:!1},$o={backdrop:"(boolean|string)",keyboard:"boolean",scroll:"boolean"};class Po extends J{constructor(t,e){super(t,e),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._addEventListeners()}static get Default(){return No}static get DefaultType(){return $o}static get NAME(){return po}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){if(this._isShown)return;if(z.trigger(this._element,Ao,{relatedTarget:t}).defaultPrevented)return;this._isShown=!0,this._backdrop.show(),this._config.scroll||(new Hs).hide(),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add(bo);const e=()=>{this._config.scroll&&!this._config.backdrop||this._focustrap.activate(),this._element.classList.add(_o),this._element.classList.remove(bo),z.trigger(this._element,Eo,{relatedTarget:t})};this._queueCallback(e,this._element,!0)}hide(){if(!this._isShown)return;if(z.trigger(this._element,xo).defaultPrevented)return;this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.add(yo),this._backdrop.hide();const t=()=>{this._element.classList.remove(_o,yo),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._config.scroll||(new Hs).reset(),z.trigger(this._element,Co)};this._queueCallback(t,this._element,!0)}dispose(){this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}_initializeBackDrop(){const t=()=>{"static"!==this._config.backdrop?this.hide():z.trigger(this._element,So)},e=Boolean(this._config.backdrop);return new Ss({className:wo,isVisible:e,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:e?t:null})}_initializeFocusTrap(){return new js({trapElement:this._element})}_addEventListeners(){z.on(this._element,To,(t=>{t.key===vo&&(this._config.keyboard?this.hide():z.trigger(this._element,So))}))}static jQueryInterface(t){return this.each((function(){const e=Po.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t](this)}}))}}z.on(document,Io,Lo,(function(t){const e=X.getElementFromSelector(this);if(["A","AREA"].includes(this.tagName)&&t.preventDefault(),p(this))return;z.one(e,Co,(()=>{h(this)&&this.focus()}));const i=X.findOne(Oo);i&&i!==e&&Po.getInstance(i).hide(),Po.getOrCreateInstance(e).toggle(this)})),z.on(window,mo,(()=>{for(const t of X.find(Oo))Po.getOrCreateInstance(t).show()})),z.on(window,ko,(()=>{for(const t of X.find("[aria-modal][class*=show][class*=offcanvas-]"))"fixed"!==getComputedStyle(t).position&&Po.getOrCreateInstance(t).hide()})),G(Po),w(Po);const Do={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],dd:[],div:[],dl:[],dt:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},jo=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),Fo=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i,Mo=(t,e)=>{const i=t.nodeName.toLowerCase();return e.includes(i)?!jo.has(i)||Boolean(Fo.test(t.nodeValue)):e.filter((t=>t instanceof RegExp)).some((t=>t.test(i)))};function qo(t,e,i){if(!t.length)return t;if(i&&"function"==typeof i)return i(t);const n=(new window.DOMParser).parseFromString(t,"text/html"),s=[].concat(...n.body.querySelectorAll("*"));for(const t of s){const i=t.nodeName.toLowerCase();if(!Object.keys(e).includes(i)){t.remove();continue}const n=[].concat(...t.attributes),s=[].concat(e["*"]||[],e[i]||[]);for(const e of n)Mo(e,s)||t.removeAttribute(e.nodeName)}return n.body.innerHTML}const Ro="TemplateFactory",Ho={allowList:Do,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},zo={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},Bo={entry:"(string|element|function|null)",selector:"(string|element)"};class Vo extends U{constructor(t){super(),this._config=this._getConfig(t)}static get Default(){return Ho}static get DefaultType(){return zo}static get NAME(){return Ro}getContent(){return Object.values(this._config.content).map((t=>this._resolvePossibleFunction(t))).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(t){return this._checkContent(t),this._config.content={...this._config.content,...t},this}toHtml(){const t=document.createElement("div");t.innerHTML=this._maybeSanitize(this._config.template);for(const[e,i]of Object.entries(this._config.content))this._setContent(t,i,e);const e=t.children[0],i=this._resolvePossibleFunction(this._config.extraClass);return i&&e.classList.add(...i.split(" ")),e}_typeCheckConfig(t){super._typeCheckConfig(t),this._checkContent(t.content)}_checkContent(t){for(const[e,i]of Object.entries(t))super._typeCheckConfig({selector:e,entry:i},Bo)}_setContent(t,e,i){const n=X.findOne(i,t);n&&((e=this._resolvePossibleFunction(e))?d(e)?this._putElementInTemplate(u(e),n):this._config.html?n.innerHTML=this._maybeSanitize(e):n.textContent=e:n.remove())}_maybeSanitize(t){return this._config.sanitize?qo(t,this._config.allowList,this._config.sanitizeFn):t}_resolvePossibleFunction(t){return O(t,[this])}_putElementInTemplate(t,e){if(this._config.html)return e.innerHTML="",void e.append(t);e.textContent=t.textContent}}const Wo="tooltip",Ko=new Set(["sanitize","allowList","sanitizeFn"]),Uo="fade",Qo="show",Jo=".tooltip-inner",Yo=".modal",Xo="hide.bs.modal",Go="hover",Zo="focus",tr="click",er="manual",ir="hide",nr="hidden",sr="show",or="shown",rr="inserted",ar="click",lr="focusin",cr="focusout",dr="mouseenter",ur="mouseleave",hr={AUTO:"auto",TOP:"top",RIGHT:y()?"left":"right",BOTTOM:"bottom",LEFT:y()?"right":"left"},pr={allowList:Do,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,6],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},fr={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"};class gr extends J{constructor(t,e){if(void 0===Dn)throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org)");super(t,e),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return pr}static get DefaultType(){return fr}static get NAME(){return Wo}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){this._isEnabled&&(this._activeTrigger.click=!this._activeTrigger.click,this._isShown()?this._leave():this._enter())}dispose(){clearTimeout(this._timeout),z.off(this._element.closest(Yo),Xo,this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),super.dispose()}show(){if("none"===this._element.style.display)throw new Error("Please use show on visible elements");if(!this._isWithContent()||!this._isEnabled)return;const t=z.trigger(this._element,this.constructor.eventName(sr)),e=(f(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(t.defaultPrevented||!e)return;this._disposePopper();const i=this._getTipElement();this._element.setAttribute("aria-describedby",i.getAttribute("id"));const{container:n}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(n.append(i),z.trigger(this._element,this.constructor.eventName(rr))),this._popper=this._createPopper(i),i.classList.add(Qo),"ontouchstart"in document.documentElement)for(const t of[].concat(...document.body.children))z.on(t,"mouseover",g);const s=()=>{z.trigger(this._element,this.constructor.eventName(or)),!1===this._isHovered&&this._leave(),this._isHovered=!1};this._queueCallback(s,this.tip,this._isAnimated())}hide(){if(!this._isShown())return;if(z.trigger(this._element,this.constructor.eventName(ir)).defaultPrevented)return;if(this._getTipElement().classList.remove(Qo),"ontouchstart"in document.documentElement)for(const t of[].concat(...document.body.children))z.off(t,"mouseover",g);this._activeTrigger[tr]=!1,this._activeTrigger[Zo]=!1,this._activeTrigger[Go]=!1,this._isHovered=null;const t=()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),z.trigger(this._element,this.constructor.eventName(nr)))};this._queueCallback(t,this.tip,this._isAnimated())}update(){this._popper&&this._popper.update()}_isWithContent(){return Boolean(this._getTitle())}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(t){const e=this._getTemplateFactory(t).toHtml();if(!e)return null;e.classList.remove(Uo,Qo),e.classList.add(`bs-${this.constructor.NAME}-auto`);const i=a(this.constructor.NAME).toString();return e.setAttribute("id",i),this._isAnimated()&&e.classList.add(Uo),e}setContent(t){this._newContent=t,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(t){return this._templateFactory?this._templateFactory.changeContent(t):this._templateFactory=new Vo({...this._config,content:t,extraClass:this._resolvePossibleFunction(this._config.customClass)}),this._templateFactory}_getContentForTemplate(){return{[Jo]:this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}_initializeOnDelegatedTarget(t){return this.constructor.getOrCreateInstance(t.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains(Uo)}_isShown(){return this.tip&&this.tip.classList.contains(Qo)}_createPopper(t){const e=O(this._config.placement,[this,t,this._element]),i=hr[e.toUpperCase()];return Pn(this._element,t,this._getPopperConfig(i))}_getOffset(){const{offset:t}=this._config;return"string"==typeof t?t.split(",").map((t=>Number.parseInt(t,10))):"function"==typeof t?e=>t(e,this._element):t}_resolvePossibleFunction(t){return O(t,[this._element])}_getPopperConfig(t){const e={placement:t,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:t=>{this._getTipElement().setAttribute("data-popper-placement",t.state.placement)}}]};return{...e,...O(this._config.popperConfig,[e])}}_setListeners(){const t=this._config.trigger.split(" ");for(const e of t)if("click"===e)z.on(this._element,this.constructor.eventName(ar),this._config.selector,(t=>{this._initializeOnDelegatedTarget(t).toggle()}));else if(e!==er){const t=e===Go?this.constructor.eventName(dr):this.constructor.eventName(lr),i=e===Go?this.constructor.eventName(ur):this.constructor.eventName(cr);z.on(this._element,t,this._config.selector,(t=>{const e=this._initializeOnDelegatedTarget(t);e._activeTrigger["focusin"===t.type?Zo:Go]=!0,e._enter()})),z.on(this._element,i,this._config.selector,(t=>{const e=this._initializeOnDelegatedTarget(t);e._activeTrigger["focusout"===t.type?Zo:Go]=e._element.contains(t.relatedTarget),e._leave()}))}this._hideModalHandler=()=>{this._element&&this.hide()},z.on(this._element.closest(Yo),Xo,this._hideModalHandler)}_fixTitle(){const t=this._element.getAttribute("title");t&&(this._element.getAttribute("aria-label")||this._element.textContent.trim()||this._element.setAttribute("aria-label",t),this._element.setAttribute("data-bs-original-title",t),this._element.removeAttribute("title"))}_enter(){this._isShown()||this._isHovered?this._isHovered=!0:(this._isHovered=!0,this._setTimeout((()=>{this._isHovered&&this.show()}),this._config.delay.show))}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout((()=>{this._isHovered||this.hide()}),this._config.delay.hide))}_setTimeout(t,e){clearTimeout(this._timeout),this._timeout=setTimeout(t,e)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(t){const e=K.getDataAttributes(this._element);for(const t of Object.keys(e))Ko.has(t)&&delete e[t];return t={...e,..."object"==typeof t&&t?t:{}},t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t.container=!1===t.container?document.body:u(t.container),"number"==typeof t.delay&&(t.delay={show:t.delay,hide:t.delay}),"number"==typeof t.title&&(t.title=t.title.toString()),"number"==typeof t.content&&(t.content=t.content.toString()),t}_getDelegateConfig(){const t={};for(const[e,i]of Object.entries(this._config))this.constructor.Default[e]!==i&&(t[e]=i);return t.selector=!1,t.trigger="manual",t}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(t){return this.each((function(){const e=gr.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t]()}}))}}w(gr);const mr="popover",vr=".popover-header",_r=".popover-body",br={...gr.Default,content:"",offset:[0,8],placement:"right",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',trigger:"click"},yr={...gr.DefaultType,content:"(null|string|element|function)"};class wr extends gr{static get Default(){return br}static get DefaultType(){return yr}static get NAME(){return mr}_isWithContent(){return this._getTitle()||this._getContent()}_getContentForTemplate(){return{[vr]:this._getTitle(),[_r]:this._getContent()}}_getContent(){return this._resolvePossibleFunction(this._config.content)}static jQueryInterface(t){return this.each((function(){const e=wr.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t]()}}))}}w(wr);const Or="scrollspy",Ar=".bs.scrollspy",Er=`activate${Ar}`,xr=`click${Ar}`,Sr=`load${Ar}.data-api`,Cr="dropdown-item",kr="active",Ir='[data-bs-spy="scroll"]',Tr="[href]",Lr=".nav, .list-group",Nr=".nav-link",$r=`${Nr}, .nav-item > ${Nr}, .list-group-item`,Pr=".dropdown",Dr=".dropdown-toggle",jr={offset:null,rootMargin:"0px 0px -25%",smoothScroll:!1,target:null,threshold:[.1,.5,1]},Fr={offset:"(number|null)",rootMargin:"string",smoothScroll:"boolean",target:"element",threshold:"array"};class Mr extends J{constructor(t,e){super(t,e),this._targetLinks=new Map,this._observableSections=new Map,this._rootElement="visible"===getComputedStyle(this._element).overflowY?null:this._element,this._activeTarget=null,this._observer=null,this._previousScrollData={visibleEntryTop:0,parentScrollTop:0},this.refresh()}static get Default(){return jr}static get DefaultType(){return Fr}static get NAME(){return Or}refresh(){this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver();for(const t of this._observableSections.values())this._observer.observe(t)}dispose(){this._observer.disconnect(),super.dispose()}_configAfterMerge(t){return t.target=u(t.target)||document.body,t.rootMargin=t.offset?`${t.offset}px 0px -30%`:t.rootMargin,"string"==typeof t.threshold&&(t.threshold=t.threshold.split(",").map((t=>Number.parseFloat(t)))),t}_maybeEnableSmoothScroll(){this._config.smoothScroll&&(z.off(this._config.target,xr),z.on(this._config.target,xr,Tr,(t=>{const e=this._observableSections.get(t.target.hash);if(e){t.preventDefault();const i=this._rootElement||window,n=e.offsetTop-this._element.offsetTop;if(i.scrollTo)return void i.scrollTo({top:n,behavior:"smooth"});i.scrollTop=n}})))}_getNewObserver(){const t={root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin};return new IntersectionObserver((t=>this._observerCallback(t)),t)}_observerCallback(t){const e=t=>this._targetLinks.get(`#${t.target.id}`),i=t=>{this._previousScrollData.visibleEntryTop=t.target.offsetTop,this._process(e(t))},n=(this._rootElement||document.documentElement).scrollTop,s=n>=this._previousScrollData.parentScrollTop;this._previousScrollData.parentScrollTop=n;for(const o of t){if(!o.isIntersecting){this._activeTarget=null,this._clearActiveClass(e(o));continue}const t=o.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(s&&t){if(i(o),!n)return}else s||t||i(o)}}_initializeTargetsAndObservables(){this._targetLinks=new Map,this._observableSections=new Map;const t=X.find(Tr,this._config.target);for(const e of t){if(!e.hash||p(e))continue;const t=X.findOne(decodeURI(e.hash),this._element);h(t)&&(this._targetLinks.set(decodeURI(e.hash),e),this._observableSections.set(e.hash,t))}}_process(t){this._activeTarget!==t&&(this._clearActiveClass(this._config.target),this._activeTarget=t,t.classList.add(kr),this._activateParents(t),z.trigger(this._element,Er,{relatedTarget:t}))}_activateParents(t){if(t.classList.contains(Cr))X.findOne(Dr,t.closest(Pr)).classList.add(kr);else for(const e of X.parents(t,Lr))for(const t of X.prev(e,$r))t.classList.add(kr)}_clearActiveClass(t){t.classList.remove(kr);const e=X.find(`${Tr}.${kr}`,t);for(const t of e)t.classList.remove(kr)}static jQueryInterface(t){return this.each((function(){const e=Mr.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t]()}}))}}z.on(window,Sr,(()=>{for(const t of X.find(Ir))Mr.getOrCreateInstance(t)})),w(Mr);const qr="tab",Rr=".bs.tab",Hr=`hide${Rr}`,zr=`hidden${Rr}`,Br=`show${Rr}`,Vr=`shown${Rr}`,Wr=`click${Rr}`,Kr=`keydown${Rr}`,Ur=`load${Rr}`,Qr="ArrowLeft",Jr="ArrowRight",Yr="ArrowUp",Xr="ArrowDown",Gr="Home",Zr="End",ta="active",ea="fade",ia="show",na="dropdown",sa=".dropdown-toggle",oa=".dropdown-menu",ra=`:not(${sa})`,aa='.list-group, .nav, [role="tablist"]',la=".nav-item, .list-group-item",ca='[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',da=`.nav-link${ra}, .list-group-item${ra}, [role="tab"]${ra}, ${ca}`,ua=`.${ta}[data-bs-toggle="tab"], .${ta}[data-bs-toggle="pill"], .${ta}[data-bs-toggle="list"]`;class ha extends J{constructor(t){super(t),this._parent=this._element.closest(aa),this._parent&&(this._setInitialAttributes(this._parent,this._getChildren()),z.on(this._element,Kr,(t=>this._keydown(t))))}static get NAME(){return qr}show(){const t=this._element;if(this._elemIsActive(t))return;const e=this._getActiveElem(),i=e?z.trigger(e,Hr,{relatedTarget:t}):null;z.trigger(t,Br,{relatedTarget:e}).defaultPrevented||i&&i.defaultPrevented||(this._deactivate(e,t),this._activate(t,e))}_activate(t,e){if(!t)return;t.classList.add(ta),this._activate(X.getElementFromSelector(t));const i=()=>{"tab"===t.getAttribute("role")?(t.removeAttribute("tabindex"),t.setAttribute("aria-selected",!0),this._toggleDropDown(t,!0),z.trigger(t,Vr,{relatedTarget:e})):t.classList.add(ia)};this._queueCallback(i,t,t.classList.contains(ea))}_deactivate(t,e){if(!t)return;t.classList.remove(ta),t.blur(),this._deactivate(X.getElementFromSelector(t));const i=()=>{"tab"===t.getAttribute("role")?(t.setAttribute("aria-selected",!1),t.setAttribute("tabindex","-1"),this._toggleDropDown(t,!1),z.trigger(t,zr,{relatedTarget:e})):t.classList.remove(ia)};this._queueCallback(i,t,t.classList.contains(ea))}_keydown(t){if(![Qr,Jr,Yr,Xr,Gr,Zr].includes(t.key))return;t.stopPropagation(),t.preventDefault();const e=this._getChildren().filter((t=>!p(t)));let i;if([Gr,Zr].includes(t.key))i=e[t.key===Gr?0:e.length-1];else{const n=[Jr,Xr].includes(t.key);i=E(e,t.target,n,!0)}i&&(i.focus({preventScroll:!0}),ha.getOrCreateInstance(i).show())}_getChildren(){return X.find(da,this._parent)}_getActiveElem(){return this._getChildren().find((t=>this._elemIsActive(t)))||null}_setInitialAttributes(t,e){this._setAttributeIfNotExists(t,"role","tablist");for(const t of e)this._setInitialAttributesOnChild(t)}_setInitialAttributesOnChild(t){t=this._getInnerElement(t);const e=this._elemIsActive(t),i=this._getOuterElement(t);t.setAttribute("aria-selected",e),i!==t&&this._setAttributeIfNotExists(i,"role","presentation"),e||t.setAttribute("tabindex","-1"),this._setAttributeIfNotExists(t,"role","tab"),this._setInitialAttributesOnTargetPanel(t)}_setInitialAttributesOnTargetPanel(t){const e=X.getElementFromSelector(t);e&&(this._setAttributeIfNotExists(e,"role","tabpanel"),t.id&&this._setAttributeIfNotExists(e,"aria-labelledby",`${t.id}`))}_toggleDropDown(t,e){const i=this._getOuterElement(t);if(!i.classList.contains(na))return;const n=(t,n)=>{const s=X.findOne(t,i);s&&s.classList.toggle(n,e)};n(sa,ta),n(oa,ia),i.setAttribute("aria-expanded",e)}_setAttributeIfNotExists(t,e,i){t.hasAttribute(e)||t.setAttribute(e,i)}_elemIsActive(t){return t.classList.contains(ta)}_getInnerElement(t){return t.matches(da)?t:X.findOne(da,t)}_getOuterElement(t){return t.closest(la)||t}static jQueryInterface(t){return this.each((function(){const e=ha.getOrCreateInstance(this);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t]()}}))}}z.on(document,Wr,ca,(function(t){["A","AREA"].includes(this.tagName)&&t.preventDefault(),p(this)||ha.getOrCreateInstance(this).show()})),z.on(window,Ur,(()=>{for(const t of X.find(ua))ha.getOrCreateInstance(t)})),w(ha);const pa="toast",fa=".bs.toast",ga=`mouseover${fa}`,ma=`mouseout${fa}`,va=`focusin${fa}`,_a=`focusout${fa}`,ba=`hide${fa}`,ya=`hidden${fa}`,wa=`show${fa}`,Oa=`shown${fa}`,Aa="fade",Ea="hide",xa="show",Sa="showing",Ca={animation:"boolean",autohide:"boolean",delay:"number"},ka={animation:!0,autohide:!0,delay:5e3};class Ia extends J{constructor(t,e){super(t,e),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get Default(){return ka}static get DefaultType(){return Ca}static get NAME(){return pa}show(){if(z.trigger(this._element,wa).defaultPrevented)return;this._clearTimeout(),this._config.animation&&this._element.classList.add(Aa);const t=()=>{this._element.classList.remove(Sa),z.trigger(this._element,Oa),this._maybeScheduleHide()};this._element.classList.remove(Ea),m(this._element),this._element.classList.add(xa,Sa),this._queueCallback(t,this._element,this._config.animation)}hide(){if(!this.isShown())return;if(z.trigger(this._element,ba).defaultPrevented)return;const t=()=>{this._element.classList.add(Ea),this._element.classList.remove(Sa,xa),z.trigger(this._element,ya)};this._element.classList.add(Sa),this._queueCallback(t,this._element,this._config.animation)}dispose(){this._clearTimeout(),this.isShown()&&this._element.classList.remove(xa),super.dispose()}isShown(){return this._element.classList.contains(xa)}_maybeScheduleHide(){this._config.autohide&&(this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout((()=>{this.hide()}),this._config.delay)))}_onInteraction(t,e){switch(t.type){case"mouseover":case"mouseout":this._hasMouseInteraction=e;break;case"focusin":case"focusout":this._hasKeyboardInteraction=e}if(e)return void this._clearTimeout();const i=t.relatedTarget;this._element===i||this._element.contains(i)||this._maybeScheduleHide()}_setListeners(){z.on(this._element,ga,(t=>this._onInteraction(t,!0))),z.on(this._element,ma,(t=>this._onInteraction(t,!1))),z.on(this._element,va,(t=>this._onInteraction(t,!0))),z.on(this._element,_a,(t=>this._onInteraction(t,!1)))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(t){return this.each((function(){const e=Ia.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t](this)}}))}}return G(Ia),w(Ia),{Alert:ot,Button:dt,Carousel:re,Collapse:Se,Dropdown:bs,Modal:ho,Offcanvas:Po,Popover:wr,ScrollSpy:Mr,Tab:ha,Toast:Ia,Tooltip:gr}}()},538:(t,e,i)=>{"use strict";i.r(e)},766:function(t){t.exports=function(){"use strict";function t(t,e){t.split(/\s+/).forEach((t=>{e(t)}))}class e{constructor(){this._events={}}on(e,i){t(e,(t=>{const e=this._events[t]||[];e.push(i),this._events[t]=e}))}off(e,i){var n=arguments.length;0!==n?t(e,(t=>{if(1===n)return void delete this._events[t];const e=this._events[t];void 0!==e&&(e.splice(e.indexOf(i),1),this._events[t]=e)})):this._events={}}trigger(e,...i){var n=this;t(e,(t=>{const e=n._events[t];void 0!==e&&e.forEach((t=>{t.apply(n,i)}))}))}}const i=t=>(t=t.filter(Boolean)).length<2?t[0]||"":1==a(t)?"["+t.join("")+"]":"(?:"+t.join("|")+")",n=t=>{if(!o(t))return t.join("");let e="",i=0;const n=()=>{i>1&&(e+="{"+i+"}")};return t.forEach(((s,o)=>{s!==t[o-1]?(n(),e+=s,i=1):i++})),n(),e},s=t=>{let e=Array.from(t);return i(e)},o=t=>new Set(t).size!==t.length,r=t=>(t+"").replace(/([\$\(\)\*\+\.\?\[\]\^\{\|\}\\])/gu,"\\$1"),a=t=>t.reduce(((t,e)=>Math.max(t,l(e))),0),l=t=>Array.from(t).length,c=t=>{if(1===t.length)return[[t]];let e=[];const i=t.substring(1);return c(i).forEach((function(i){let n=i.slice(0);n[0]=t.charAt(0)+n[0],e.push(n),n=i.slice(0),n.unshift(t.charAt(0)),e.push(n)})),e},d=[[0,65535]];let u,h;const p={},f={"/":"⁄∕",0:"߀",a:"ⱥɐɑ",aa:"ꜳ",ae:"æǽǣ",ao:"ꜵ",au:"ꜷ",av:"ꜹꜻ",ay:"ꜽ",b:"ƀɓƃ",c:"ꜿƈȼↄ",d:"đɗɖᴅƌꮷԁɦ",e:"ɛǝᴇɇ",f:"ꝼƒ",g:"ǥɠꞡᵹꝿɢ",h:"ħⱨⱶɥ",i:"ɨı",j:"ɉȷ",k:"ƙⱪꝁꝃꝅꞣ",l:"łƚɫⱡꝉꝇꞁɭ",m:"ɱɯϻ",n:"ꞥƞɲꞑᴎлԉ",o:"øǿɔɵꝋꝍᴑ",oe:"œ",oi:"ƣ",oo:"ꝏ",ou:"ȣ",p:"ƥᵽꝑꝓꝕρ",q:"ꝗꝙɋ",r:"ɍɽꝛꞧꞃ",s:"ßȿꞩꞅʂ",t:"ŧƭʈⱦꞇ",th:"þ",tz:"ꜩ",u:"ʉ",v:"ʋꝟʌ",vy:"ꝡ",w:"ⱳ",y:"ƴɏỿ",z:"ƶȥɀⱬꝣ",hv:"ƕ"};for(let t in f){let e=f[t]||"";for(let i=0;i<e.length;i++){let n=e.substring(i,i+1);p[n]=t}}const g=new RegExp(Object.keys(p).join("|")+"|[̀-ͯ·ʾʼ]","gu"),m=(t,e="NFKD")=>t.normalize(e),v=t=>Array.from(t).reduce(((t,e)=>t+_(e)),""),_=t=>(t=m(t).toLowerCase().replace(g,(t=>p[t]||"")),m(t,"NFC")),b=t=>{const e={},i=(t,i)=>{const n=e[t]||new Set,o=new RegExp("^"+s(n)+"$","iu");i.match(o)||(n.add(r(i)),e[t]=n)};for(let e of function*(t){for(const[e,i]of t)for(let t=e;t<=i;t++){let e=String.fromCharCode(t),i=v(e);i!=e.toLowerCase()&&(i.length>3||0!=i.length&&(yield{folded:i,composed:e,code_point:t}))}}(t))i(e.folded,e.folded),i(e.folded,e.composed);return e},y=t=>{const e=b(t),n={};let o=[];for(let t in e){let i=e[t];i&&(n[t]=s(i)),t.length>1&&o.push(r(t))}o.sort(((t,e)=>e.length-t.length));const a=i(o);return h=new RegExp("^"+a,"u"),n},w=(t,e=1)=>(e=Math.max(e,t.length-1),i(c(t).map((t=>((t,e=1)=>{let i=0;return t=t.map((t=>(u[t]&&(i+=t.length),u[t]||t))),i>=e?n(t):""})(t,e))))),O=(t,e=!0)=>{let s=t.length>1?1:0;return i(t.map((t=>{let i=[];const o=e?t.length():t.length()-1;for(let e=0;e<o;e++)i.push(w(t.substrs[e]||"",s));return n(i)})))},A=(t,e)=>{for(const i of e){if(i.start!=t.start||i.end!=t.end)continue;if(i.substrs.join("")!==t.substrs.join(""))continue;let e=t.parts;const n=t=>{for(const i of e){if(i.start===t.start&&i.substr===t.substr)return!1;if(1!=t.length&&1!=i.length){if(t.start<i.start&&t.end>i.start)return!0;if(i.start<t.start&&i.end>t.start)return!0}}return!1};if(!(i.parts.filter(n).length>0))return!0}return!1};class E{parts;substrs;start;end;constructor(){this.parts=[],this.substrs=[],this.start=0,this.end=0}add(t){t&&(this.parts.push(t),this.substrs.push(t.substr),this.start=Math.min(t.start,this.start),this.end=Math.max(t.end,this.end))}last(){return this.parts[this.parts.length-1]}length(){return this.parts.length}clone(t,e){let i=new E,n=JSON.parse(JSON.stringify(this.parts)),s=n.pop();for(const t of n)i.add(t);let o=e.substr.substring(0,t-s.start),r=o.length;return i.add({start:s.start,end:s.start+r,length:r,substr:o}),i}}const x=t=>{void 0===u&&(u=y(d)),t=v(t);let e="",i=[new E];for(let n=0;n<t.length;n++){let s=t.substring(n).match(h);const o=t.substring(n,n+1),r=s?s[0]:null;let a=[],l=new Set;for(const t of i){const e=t.last();if(!e||1==e.length||e.end<=n)if(r){const e=r.length;t.add({start:n,end:n+e,length:e,substr:r}),l.add("1")}else t.add({start:n,end:n+1,length:1,substr:o}),l.add("2");else if(r){let i=t.clone(n,e);const s=r.length;i.add({start:n,end:n+s,length:s,substr:r}),a.push(i)}else l.add("3")}if(a.length>0){a=a.sort(((t,e)=>t.length()-e.length()));for(let t of a)A(t,i)||i.push(t)}else if(n>0&&1==l.size&&!l.has("3")){e+=O(i,!1);let t=new E;const n=i[0];n&&t.add(n.last()),i=[t]}}return e+=O(i,!0),e},S=(t,e)=>{if(t)return t[e]},C=(t,e)=>{if(t){for(var i,n=e.split(".");(i=n.shift())&&(t=t[i]););return t}},k=(t,e,i)=>{var n,s;return t?(t+="",null==e.regex||-1===(s=t.search(e.regex))?0:(n=e.string.length/t.length,0===s&&(n+=.5),n*i)):0},I=(t,e)=>{var i=t[e];if("function"==typeof i)return i;i&&!Array.isArray(i)&&(t[e]=[i])},T=(t,e)=>{if(Array.isArray(t))t.forEach(e);else for(var i in t)t.hasOwnProperty(i)&&e(t[i],i)},L=(t,e)=>"number"==typeof t&&"number"==typeof e?t>e?1:t<e?-1:0:(t=v(t+"").toLowerCase())>(e=v(e+"").toLowerCase())?1:e>t?-1:0;class N{items;settings;constructor(t,e){this.items=t,this.settings=e||{diacritics:!0}}tokenize(t,e,i){if(!t||!t.length)return[];const n=[],s=t.split(/\s+/);var o;return i&&(o=new RegExp("^("+Object.keys(i).map(r).join("|")+"):(.*)$")),s.forEach((t=>{let i,s=null,a=null;o&&(i=t.match(o))&&(s=i[1],t=i[2]),t.length>0&&(a=this.settings.diacritics?x(t)||null:r(t),a&&e&&(a="\\b"+a)),n.push({string:t,regex:a?new RegExp(a,"iu"):null,field:s})})),n}getScoreFunction(t,e){var i=this.prepareSearch(t,e);return this._getScoreFunction(i)}_getScoreFunction(t){const e=t.tokens,i=e.length;if(!i)return function(){return 0};const n=t.options.fields,s=t.weights,o=n.length,r=t.getAttrFn;if(!o)return function(){return 1};const a=1===o?function(t,e){const i=n[0].field;return k(r(e,i),t,s[i]||1)}:function(t,e){var i=0;if(t.field){const n=r(e,t.field);!t.regex&&n?i+=1/o:i+=k(n,t,1)}else T(s,((n,s)=>{i+=k(r(e,s),t,n)}));return i/o};return 1===i?function(t){return a(e[0],t)}:"and"===t.options.conjunction?function(t){var n,s=0;for(let i of e){if((n=a(i,t))<=0)return 0;s+=n}return s/i}:function(t){var n=0;return T(e,(e=>{n+=a(e,t)})),n/i}}getSortFunction(t,e){var i=this.prepareSearch(t,e);return this._getSortFunction(i)}_getSortFunction(t){var e,i=[];const n=this,s=t.options,o=!t.query&&s.sort_empty?s.sort_empty:s.sort;if("function"==typeof o)return o.bind(this);const r=function(e,i){return"$score"===e?i.score:t.getAttrFn(n.items[i.id],e)};if(o)for(let e of o)(t.query||"$score"!==e.field)&&i.push(e);if(t.query){e=!0;for(let t of i)if("$score"===t.field){e=!1;break}e&&i.unshift({field:"$score",direction:"desc"})}else i=i.filter((t=>"$score"!==t.field));return i.length?function(t,e){var n,s;for(let o of i)if(s=o.field,n=("desc"===o.direction?-1:1)*L(r(s,t),r(s,e)))return n;return 0}:null}prepareSearch(t,e){const i={};var n=Object.assign({},e);if(I(n,"sort"),I(n,"sort_empty"),n.fields){I(n,"fields");const t=[];n.fields.forEach((e=>{"string"==typeof e&&(e={field:e,weight:1}),t.push(e),i[e.field]="weight"in e?e.weight:1})),n.fields=t}return{options:n,query:t.toLowerCase().trim(),tokens:this.tokenize(t,n.respect_word_boundaries,i),total:0,items:[],weights:i,getAttrFn:n.nesting?C:S}}search(t,e){var i,n,s=this;n=this.prepareSearch(t,e),e=n.options,t=n.query;const o=e.score||s._getScoreFunction(n);t.length?T(s.items,((t,s)=>{i=o(t),(!1===e.filter||i>0)&&n.items.push({score:i,id:s})})):T(s.items,((t,e)=>{n.items.push({score:1,id:e})}));const r=s._getSortFunction(n);return r&&n.items.sort(r),n.total=n.items.length,"number"==typeof e.limit&&(n.items=n.items.slice(0,e.limit)),n}}const $=t=>null==t?null:P(t),P=t=>"boolean"==typeof t?t?"1":"0":t+"",D=t=>(t+"").replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;"),j=(t,e)=>{var i;return function(n,s){var o=this;i&&(o.loading=Math.max(o.loading-1,0),clearTimeout(i)),i=setTimeout((function(){i=null,o.loadedSearches[n]=!0,t.call(o,n,s)}),e)}},F=(t,e,i)=>{var n,s=t.trigger,o={};for(n of(t.trigger=function(){var i=arguments[0];if(-1===e.indexOf(i))return s.apply(t,arguments);o[i]=arguments},i.apply(t,[]),t.trigger=s,e))n in o&&s.apply(t,o[n])},M=(t,e=!1)=>{t&&(t.preventDefault(),e&&t.stopPropagation())},q=(t,e,i,n)=>{t.addEventListener(e,i,n)},R=(t,e)=>!!e&&!!e[t]&&1==(e.altKey?1:0)+(e.ctrlKey?1:0)+(e.shiftKey?1:0)+(e.metaKey?1:0),H=(t,e)=>t.getAttribute("id")||(t.setAttribute("id",e),e),z=t=>t.replace(/[\\"']/g,"\\$&"),B=(t,e)=>{e&&t.append(e)},V=(t,e)=>{if(Array.isArray(t))t.forEach(e);else for(var i in t)t.hasOwnProperty(i)&&e(t[i],i)},W=t=>{if(t.jquery)return t[0];if(t instanceof HTMLElement)return t;if(K(t)){var e=document.createElement("template");return e.innerHTML=t.trim(),e.content.firstChild}return document.querySelector(t)},K=t=>"string"==typeof t&&t.indexOf("<")>-1,U=(t,e)=>{var i=document.createEvent("HTMLEvents");i.initEvent(e,!0,!1),t.dispatchEvent(i)},Q=(t,e)=>{Object.assign(t.style,e)},J=(t,...e)=>{var i=X(e);(t=G(t)).map((t=>{i.map((e=>{t.classList.add(e)}))}))},Y=(t,...e)=>{var i=X(e);(t=G(t)).map((t=>{i.map((e=>{t.classList.remove(e)}))}))},X=t=>{var e=[];return V(t,(t=>{"string"==typeof t&&(t=t.trim().split(/[\t\n\f\r\s]/)),Array.isArray(t)&&(e=e.concat(t))})),e.filter(Boolean)},G=t=>(Array.isArray(t)||(t=[t]),t),Z=(t,e,i)=>{if(!i||i.contains(t))for(;t&&t.matches;){if(t.matches(e))return t;t=t.parentNode}},tt=(t,e=0)=>e>0?t[t.length-1]:t[0],et=(t,e)=>{if(!t)return-1;e=e||t.nodeName;for(var i=0;t=t.previousElementSibling;)t.matches(e)&&i++;return i},it=(t,e)=>{V(e,((e,i)=>{null==e?t.removeAttribute(i):t.setAttribute(i,""+e)}))},nt=(t,e)=>{t.parentNode&&t.parentNode.replaceChild(e,t)},st=(t,e)=>{if(null===e)return;if("string"==typeof e){if(!e.length)return;e=new RegExp(e,"i")}const i=t=>3===t.nodeType?(t=>{var i=t.data.match(e);if(i&&t.data.length>0){var n=document.createElement("span");n.className="highlight";var s=t.splitText(i.index);s.splitText(i[0].length);var o=s.cloneNode(!0);return n.appendChild(o),nt(s,n),1}return 0})(t):((t=>{1!==t.nodeType||!t.childNodes||/(script|style)/i.test(t.tagName)||"highlight"===t.className&&"SPAN"===t.tagName||Array.from(t.childNodes).forEach((t=>{i(t)}))})(t),0);i(t)},ot="undefined"!=typeof navigator&&/Mac/.test(navigator.userAgent)?"metaKey":"ctrlKey";var rt={options:[],optgroups:[],plugins:[],delimiter:",",splitOn:null,persist:!0,diacritics:!0,create:null,createOnBlur:!1,createFilter:null,highlight:!0,openOnFocus:!0,shouldOpen:null,maxOptions:50,maxItems:null,hideSelected:null,duplicates:!1,addPrecedence:!1,selectOnTab:!1,preload:null,allowEmptyOption:!1,refreshThrottle:300,loadThrottle:300,loadingClass:"loading",dataAttr:null,optgroupField:"optgroup",valueField:"value",labelField:"text",disabledField:"disabled",optgroupLabelField:"label",optgroupValueField:"value",lockOptgroupOrder:!1,sortField:"$order",searchField:["text"],searchConjunction:"and",mode:null,wrapperClass:"ts-wrapper",controlClass:"ts-control",dropdownClass:"ts-dropdown",dropdownContentClass:"ts-dropdown-content",itemClass:"item",optionClass:"option",dropdownParent:null,controlInput:'<input type="text" autocomplete="off" size="1" />',copyClassesToDropdown:!1,placeholder:null,hidePlaceholder:null,shouldLoad:function(t){return t.length>0},render:{}};function at(t,e){var i=Object.assign({},rt,e),n=i.dataAttr,s=i.labelField,o=i.valueField,r=i.disabledField,a=i.optgroupField,l=i.optgroupLabelField,c=i.optgroupValueField,d=t.tagName.toLowerCase(),u=t.getAttribute("placeholder")||t.getAttribute("data-placeholder");if(!u&&!i.allowEmptyOption){let e=t.querySelector('option[value=""]');e&&(u=e.textContent)}var h={placeholder:u,options:[],optgroups:[],items:[],maxItems:null};return"select"===d?(()=>{var e,d=h.options,u={},p=1;let f=0;var g=t=>{var e=Object.assign({},t.dataset),i=n&&e[n];return"string"==typeof i&&i.length&&(e=Object.assign(e,JSON.parse(i))),e},m=(t,e)=>{var n=$(t.value);if(null!=n&&(n||i.allowEmptyOption)){if(u.hasOwnProperty(n)){if(e){var l=u[n][a];l?Array.isArray(l)?l.push(e):u[n][a]=[l,e]:u[n][a]=e}}else{var c=g(t);c[s]=c[s]||t.textContent,c[o]=c[o]||n,c[r]=c[r]||t.disabled,c[a]=c[a]||e,c.$option=t,c.$order=c.$order||++f,u[n]=c,d.push(c)}t.selected&&h.items.push(n)}};h.maxItems=t.hasAttribute("multiple")?null:1,V(t.children,(t=>{var i,n,s;"optgroup"===(e=t.tagName.toLowerCase())?((s=g(i=t))[l]=s[l]||i.getAttribute("label")||"",s[c]=s[c]||p++,s[r]=s[r]||i.disabled,s.$order=s.$order||++f,h.optgroups.push(s),n=s[c],V(i.children,(t=>{m(t,n)}))):"option"===e&&m(t)}))})():(()=>{const e=t.getAttribute(n);if(e)h.options=JSON.parse(e),V(h.options,(t=>{h.items.push(t[o])}));else{var r=t.value.trim()||"";if(!i.allowEmptyOption&&!r.length)return;const e=r.split(i.delimiter);V(e,(t=>{const e={};e[s]=t,e[o]=t,h.options.push(e)})),h.items=e}})(),Object.assign({},rt,h,e)}var lt=0;class ct extends(function(t){return t.plugins={},class extends t{constructor(...t){super(...t),this.plugins={names:[],settings:{},requested:{},loaded:{}}}static define(e,i){t.plugins[e]={name:e,fn:i}}initializePlugins(t){var e,i;const n=this,s=[];if(Array.isArray(t))t.forEach((t=>{"string"==typeof t?s.push(t):(n.plugins.settings[t.name]=t.options,s.push(t.name))}));else if(t)for(e in t)t.hasOwnProperty(e)&&(n.plugins.settings[e]=t[e],s.push(e));for(;i=s.shift();)n.require(i)}loadPlugin(e){var i=this,n=i.plugins,s=t.plugins[e];if(!t.plugins.hasOwnProperty(e))throw new Error('Unable to find "'+e+'" plugin');n.requested[e]=!0,n.loaded[e]=s.fn.apply(i,[i.plugins.settings[e]||{}]),n.names.push(e)}require(t){var e=this,i=e.plugins;if(!e.plugins.loaded.hasOwnProperty(t)){if(i.requested[t])throw new Error('Plugin has circular dependency ("'+t+'")');e.loadPlugin(t)}return i.loaded[t]}}}(e)){constructor(t,e){var i;super(),this.order=0,this.isOpen=!1,this.isDisabled=!1,this.isReadOnly=!1,this.isInvalid=!1,this.isValid=!0,this.isLocked=!1,this.isFocused=!1,this.isInputHidden=!1,this.isSetup=!1,this.ignoreFocus=!1,this.ignoreHover=!1,this.hasOptions=!1,this.lastValue="",this.caretPos=0,this.loading=0,this.loadedSearches={},this.activeOption=null,this.activeItems=[],this.optgroups={},this.options={},this.userOptions={},this.items=[],this.refreshTimeout=null,lt++;var n=W(t);if(n.tomselect)throw new Error("Tom Select already initialized on this element");n.tomselect=this,i=(window.getComputedStyle&&window.getComputedStyle(n,null)).getPropertyValue("direction");const s=at(n,e);this.settings=s,this.input=n,this.tabIndex=n.tabIndex||0,this.is_select_tag="select"===n.tagName.toLowerCase(),this.rtl=/rtl/i.test(i),this.inputId=H(n,"tomselect-"+lt),this.isRequired=n.required,this.sifter=new N(this.options,{diacritics:s.diacritics}),s.mode=s.mode||(1===s.maxItems?"single":"multi"),"boolean"!=typeof s.hideSelected&&(s.hideSelected="multi"===s.mode),"boolean"!=typeof s.hidePlaceholder&&(s.hidePlaceholder="multi"!==s.mode);var o=s.createFilter;"function"!=typeof o&&("string"==typeof o&&(o=new RegExp(o)),o instanceof RegExp?s.createFilter=t=>o.test(t):s.createFilter=t=>this.settings.duplicates||!this.options[t]),this.initializePlugins(s.plugins),this.setupCallbacks(),this.setupTemplates();const r=W("<div>"),a=W("<div>"),l=this._render("dropdown"),c=W('<div role="listbox" tabindex="-1">'),d=this.input.getAttribute("class")||"",u=s.mode;var h;J(r,s.wrapperClass,d,u),J(a,s.controlClass),B(r,a),J(l,s.dropdownClass,u),s.copyClassesToDropdown&&J(l,d),J(c,s.dropdownContentClass),B(l,c),W(s.dropdownParent||r).appendChild(l),K(s.controlInput)?(h=W(s.controlInput),V(["autocorrect","autocapitalize","autocomplete","spellcheck"],(t=>{n.getAttribute(t)&&it(h,{[t]:n.getAttribute(t)})})),h.tabIndex=-1,a.appendChild(h),this.focus_node=h):s.controlInput?(h=W(s.controlInput),this.focus_node=h):(h=W("<input/>"),this.focus_node=a),this.wrapper=r,this.dropdown=l,this.dropdown_content=c,this.control=a,this.control_input=h,this.setup()}setup(){const t=this,e=t.settings,i=t.control_input,n=t.dropdown,s=t.dropdown_content,o=t.wrapper,a=t.control,l=t.input,c=t.focus_node,d={passive:!0},u=t.inputId+"-ts-dropdown";it(s,{id:u}),it(c,{role:"combobox","aria-haspopup":"listbox","aria-expanded":"false","aria-controls":u});const h=H(c,t.inputId+"-ts-control"),p="label[for='"+(t=>t.replace(/['"\\]/g,"\\$&"))(t.inputId)+"']",f=document.querySelector(p),g=t.focus.bind(t);if(f){q(f,"click",g),it(f,{for:h});const e=H(f,t.inputId+"-ts-label");it(c,{"aria-labelledby":e}),it(s,{"aria-labelledby":e})}if(o.style.width=l.style.width,t.plugins.names.length){const e="plugin-"+t.plugins.names.join(" plugin-");J([o,n],e)}(null===e.maxItems||e.maxItems>1)&&t.is_select_tag&&it(l,{multiple:"multiple"}),e.placeholder&&it(i,{placeholder:e.placeholder}),!e.splitOn&&e.delimiter&&(e.splitOn=new RegExp("\\s*"+r(e.delimiter)+"+\\s*")),e.load&&e.loadThrottle&&(e.load=j(e.load,e.loadThrottle)),q(n,"mousemove",(()=>{t.ignoreHover=!1})),q(n,"mouseenter",(e=>{var i=Z(e.target,"[data-selectable]",n);i&&t.onOptionHover(e,i)}),{capture:!0}),q(n,"click",(e=>{const i=Z(e.target,"[data-selectable]");i&&(t.onOptionSelect(e,i),M(e,!0))})),q(a,"click",(e=>{var n=Z(e.target,"[data-ts-item]",a);n&&t.onItemSelect(e,n)?M(e,!0):""==i.value&&(t.onClick(),M(e,!0))})),q(c,"keydown",(e=>t.onKeyDown(e))),q(i,"keypress",(e=>t.onKeyPress(e))),q(i,"input",(e=>t.onInput(e))),q(c,"blur",(e=>t.onBlur(e))),q(c,"focus",(e=>t.onFocus(e))),q(i,"paste",(e=>t.onPaste(e)));const m=e=>{const s=e.composedPath()[0];if(!o.contains(s)&&!n.contains(s))return t.isFocused&&t.blur(),void t.inputState();s==i&&t.isOpen?e.stopPropagation():M(e,!0)},v=()=>{t.isOpen&&t.positionDropdown()};q(document,"mousedown",m),q(window,"scroll",v,d),q(window,"resize",v,d),this._destroy=()=>{document.removeEventListener("mousedown",m),window.removeEventListener("scroll",v),window.removeEventListener("resize",v),f&&f.removeEventListener("click",g)},this.revertSettings={innerHTML:l.innerHTML,tabIndex:l.tabIndex},l.tabIndex=-1,l.insertAdjacentElement("afterend",t.wrapper),t.sync(!1),e.items=[],delete e.optgroups,delete e.options,q(l,"invalid",(()=>{t.isValid&&(t.isValid=!1,t.isInvalid=!0,t.refreshState())})),t.updateOriginalInput(),t.refreshItems(),t.close(!1),t.inputState(),t.isSetup=!0,l.disabled?t.disable():l.readOnly?t.setReadOnly(!0):t.enable(),t.on("change",this.onChange),J(l,"tomselected","ts-hidden-accessible"),t.trigger("initialize"),!0===e.preload&&t.preload()}setupOptions(t=[],e=[]){this.addOptions(t),V(e,(t=>{this.registerOptionGroup(t)}))}setupTemplates(){var t=this,e=t.settings.labelField,i=t.settings.optgroupLabelField,n={optgroup:t=>{let e=document.createElement("div");return e.className="optgroup",e.appendChild(t.options),e},optgroup_header:(t,e)=>'<div class="optgroup-header">'+e(t[i])+"</div>",option:(t,i)=>"<div>"+i(t[e])+"</div>",item:(t,i)=>"<div>"+i(t[e])+"</div>",option_create:(t,e)=>'<div class="create">Add <strong>'+e(t.input)+"</strong>&hellip;</div>",no_results:()=>'<div class="no-results">No results found</div>',loading:()=>'<div class="spinner"></div>',not_loading:()=>{},dropdown:()=>"<div></div>"};t.settings.render=Object.assign({},n,t.settings.render)}setupCallbacks(){var t,e,i={initialize:"onInitialize",change:"onChange",item_add:"onItemAdd",item_remove:"onItemRemove",item_select:"onItemSelect",clear:"onClear",option_add:"onOptionAdd",option_remove:"onOptionRemove",option_clear:"onOptionClear",optgroup_add:"onOptionGroupAdd",optgroup_remove:"onOptionGroupRemove",optgroup_clear:"onOptionGroupClear",dropdown_open:"onDropdownOpen",dropdown_close:"onDropdownClose",type:"onType",load:"onLoad",focus:"onFocus",blur:"onBlur"};for(t in i)(e=this.settings[i[t]])&&this.on(t,e)}sync(t=!0){const e=this,i=t?at(e.input,{delimiter:e.settings.delimiter}):e.settings;e.setupOptions(i.options,i.optgroups),e.setValue(i.items||[],!0),e.lastQuery=null}onClick(){var t=this;if(t.activeItems.length>0)return t.clearActiveItems(),void t.focus();t.isFocused&&t.isOpen?t.blur():t.focus()}onMouseDown(){}onChange(){U(this.input,"input"),U(this.input,"change")}onPaste(t){var e=this;e.isInputHidden||e.isLocked?M(t):e.settings.splitOn&&setTimeout((()=>{var t=e.inputValue();if(t.match(e.settings.splitOn)){var i=t.trim().split(e.settings.splitOn);V(i,(t=>{$(t)&&(this.options[t]?e.addItem(t):e.createItem(t))}))}}),0)}onKeyPress(t){var e=this;if(!e.isLocked){var i=String.fromCharCode(t.keyCode||t.which);return e.settings.create&&"multi"===e.settings.mode&&i===e.settings.delimiter?(e.createItem(),void M(t)):void 0}M(t)}onKeyDown(t){var e=this;if(e.ignoreHover=!0,e.isLocked)9!==t.keyCode&&M(t);else{switch(t.keyCode){case 65:if(R(ot,t)&&""==e.control_input.value)return M(t),void e.selectAll();break;case 27:return e.isOpen&&(M(t,!0),e.close()),void e.clearActiveItems();case 40:if(!e.isOpen&&e.hasOptions)e.open();else if(e.activeOption){let t=e.getAdjacent(e.activeOption,1);t&&e.setActiveOption(t)}return void M(t);case 38:if(e.activeOption){let t=e.getAdjacent(e.activeOption,-1);t&&e.setActiveOption(t)}return void M(t);case 13:return void(e.canSelect(e.activeOption)?(e.onOptionSelect(t,e.activeOption),M(t)):(e.settings.create&&e.createItem()||document.activeElement==e.control_input&&e.isOpen)&&M(t));case 37:return void e.advanceSelection(-1,t);case 39:return void e.advanceSelection(1,t);case 9:return void(e.settings.selectOnTab&&(e.canSelect(e.activeOption)&&(e.onOptionSelect(t,e.activeOption),M(t)),e.settings.create&&e.createItem()&&M(t)));case 8:case 46:return void e.deleteSelection(t)}e.isInputHidden&&!R(ot,t)&&M(t)}}onInput(t){if(this.isLocked)return;const e=this.inputValue();this.lastValue!==e&&(this.lastValue=e,""!=e?(this.refreshTimeout&&window.clearTimeout(this.refreshTimeout),this.refreshTimeout=((t,e)=>e>0?window.setTimeout(t,e):(t.call(null),null))((()=>{this.refreshTimeout=null,this._onInput()}),this.settings.refreshThrottle)):this._onInput())}_onInput(){const t=this.lastValue;this.settings.shouldLoad.call(this,t)&&this.load(t),this.refreshOptions(),this.trigger("type",t)}onOptionHover(t,e){this.ignoreHover||this.setActiveOption(e,!1)}onFocus(t){var e=this,i=e.isFocused;if(e.isDisabled||e.isReadOnly)return e.blur(),void M(t);e.ignoreFocus||(e.isFocused=!0,"focus"===e.settings.preload&&e.preload(),i||e.trigger("focus"),e.activeItems.length||(e.inputState(),e.refreshOptions(!!e.settings.openOnFocus)),e.refreshState())}onBlur(t){if(!1!==document.hasFocus()){var e=this;if(e.isFocused){e.isFocused=!1,e.ignoreFocus=!1;var i=()=>{e.close(),e.setActiveItem(),e.setCaret(e.items.length),e.trigger("blur")};e.settings.create&&e.settings.createOnBlur?e.createItem(null,i):i()}}}onOptionSelect(t,e){var i,n=this;e.parentElement&&e.parentElement.matches("[data-disabled]")||(e.classList.contains("create")?n.createItem(null,(()=>{n.settings.closeAfterSelect&&n.close()})):void 0!==(i=e.dataset.value)&&(n.lastQuery=null,n.addItem(i),n.settings.closeAfterSelect&&n.close(),!n.settings.hideSelected&&t.type&&/click/.test(t.type)&&n.setActiveOption(e)))}canSelect(t){return!!(this.isOpen&&t&&this.dropdown_content.contains(t))}onItemSelect(t,e){var i=this;return!i.isLocked&&"multi"===i.settings.mode&&(M(t),i.setActiveItem(e,t),!0)}canLoad(t){return!!this.settings.load&&!this.loadedSearches.hasOwnProperty(t)}load(t){const e=this;if(!e.canLoad(t))return;J(e.wrapper,e.settings.loadingClass),e.loading++;const i=e.loadCallback.bind(e);e.settings.load.call(e,t,i)}loadCallback(t,e){const i=this;i.loading=Math.max(i.loading-1,0),i.lastQuery=null,i.clearActiveOption(),i.setupOptions(t,e),i.refreshOptions(i.isFocused&&!i.isInputHidden),i.loading||Y(i.wrapper,i.settings.loadingClass),i.trigger("load",t,e)}preload(){var t=this.wrapper.classList;t.contains("preloaded")||(t.add("preloaded"),this.load(""))}setTextboxValue(t=""){var e=this.control_input;e.value!==t&&(e.value=t,U(e,"update"),this.lastValue=t)}getValue(){return this.is_select_tag&&this.input.hasAttribute("multiple")?this.items:this.items.join(this.settings.delimiter)}setValue(t,e){F(this,e?[]:["change"],(()=>{this.clear(e),this.addItems(t,e)}))}setMaxItems(t){0===t&&(t=null),this.settings.maxItems=t,this.refreshState()}setActiveItem(t,e){var i,n,s,o,r,a,l=this;if("single"!==l.settings.mode){if(!t)return l.clearActiveItems(),void(l.isFocused&&l.inputState());if("click"===(i=e&&e.type.toLowerCase())&&R("shiftKey",e)&&l.activeItems.length){for(a=l.getLastActive(),(s=Array.prototype.indexOf.call(l.control.children,a))>(o=Array.prototype.indexOf.call(l.control.children,t))&&(r=s,s=o,o=r),n=s;n<=o;n++)t=l.control.children[n],-1===l.activeItems.indexOf(t)&&l.setActiveItemClass(t);M(e)}else"click"===i&&R(ot,e)||"keydown"===i&&R("shiftKey",e)?t.classList.contains("active")?l.removeActiveItem(t):l.setActiveItemClass(t):(l.clearActiveItems(),l.setActiveItemClass(t));l.inputState(),l.isFocused||l.focus()}}setActiveItemClass(t){const e=this,i=e.control.querySelector(".last-active");i&&Y(i,"last-active"),J(t,"active last-active"),e.trigger("item_select",t),-1==e.activeItems.indexOf(t)&&e.activeItems.push(t)}removeActiveItem(t){var e=this.activeItems.indexOf(t);this.activeItems.splice(e,1),Y(t,"active")}clearActiveItems(){Y(this.activeItems,"active"),this.activeItems=[]}setActiveOption(t,e=!0){t!==this.activeOption&&(this.clearActiveOption(),t&&(this.activeOption=t,it(this.focus_node,{"aria-activedescendant":t.getAttribute("id")}),it(t,{"aria-selected":"true"}),J(t,"active"),e&&this.scrollToOption(t)))}scrollToOption(t,e){if(!t)return;const i=this.dropdown_content,n=i.clientHeight,s=i.scrollTop||0,o=t.offsetHeight,r=t.getBoundingClientRect().top-i.getBoundingClientRect().top+s;r+o>n+s?this.scroll(r-n+o,e):r<s&&this.scroll(r,e)}scroll(t,e){const i=this.dropdown_content;e&&(i.style.scrollBehavior=e),i.scrollTop=t,i.style.scrollBehavior=""}clearActiveOption(){this.activeOption&&(Y(this.activeOption,"active"),it(this.activeOption,{"aria-selected":null})),this.activeOption=null,it(this.focus_node,{"aria-activedescendant":null})}selectAll(){const t=this;if("single"===t.settings.mode)return;const e=t.controlChildren();e.length&&(t.inputState(),t.close(),t.activeItems=e,V(e,(e=>{t.setActiveItemClass(e)})))}inputState(){var t=this;t.control.contains(t.control_input)&&(it(t.control_input,{placeholder:t.settings.placeholder}),t.activeItems.length>0||!t.isFocused&&t.settings.hidePlaceholder&&t.items.length>0?(t.setTextboxValue(),t.isInputHidden=!0):(t.settings.hidePlaceholder&&t.items.length>0&&it(t.control_input,{placeholder:""}),t.isInputHidden=!1),t.wrapper.classList.toggle("input-hidden",t.isInputHidden))}inputValue(){return this.control_input.value.trim()}focus(){var t=this;t.isDisabled||t.isReadOnly||(t.ignoreFocus=!0,t.control_input.offsetWidth?t.control_input.focus():t.focus_node.focus(),setTimeout((()=>{t.ignoreFocus=!1,t.onFocus()}),0))}blur(){this.focus_node.blur(),this.onBlur()}getScoreFunction(t){return this.sifter.getScoreFunction(t,this.getSearchOptions())}getSearchOptions(){var t=this.settings,e=t.sortField;return"string"==typeof t.sortField&&(e=[{field:t.sortField}]),{fields:t.searchField,conjunction:t.searchConjunction,sort:e,nesting:t.nesting}}search(t){var e,i,n=this,s=this.getSearchOptions();if(n.settings.score&&"function"!=typeof(i=n.settings.score.call(n,t)))throw new Error('Tom Select "score" setting must be a function that returns a function');return t!==n.lastQuery?(n.lastQuery=t,e=n.sifter.search(t,Object.assign(s,{score:i})),n.currentResults=e):e=Object.assign({},n.currentResults),n.settings.hideSelected&&(e.items=e.items.filter((t=>{let e=$(t.id);return!(e&&-1!==n.items.indexOf(e))}))),e}refreshOptions(t=!0){var e,i,n,s,o,r,a,l,c,d;const u={},h=[];var p=this,f=p.inputValue();const g=f===p.lastQuery||""==f&&null==p.lastQuery;var m=p.search(f),v=null,_=p.settings.shouldOpen||!1,b=p.dropdown_content;g&&(v=p.activeOption)&&(c=v.closest("[data-group]")),s=m.items.length,"number"==typeof p.settings.maxOptions&&(s=Math.min(s,p.settings.maxOptions)),s>0&&(_=!0);const y=(t,e)=>{let i=u[t];if(void 0!==i){let t=h[i];if(void 0!==t)return[i,t.fragment]}let n=document.createDocumentFragment();return i=h.length,h.push({fragment:n,order:e,optgroup:t}),[i,n]};for(e=0;e<s;e++){let t=m.items[e];if(!t)continue;let s=t.id,a=p.options[s];if(void 0===a)continue;let l=P(s),d=p.getOption(l,!0);for(p.settings.hideSelected||d.classList.toggle("selected",p.items.includes(l)),o=a[p.settings.optgroupField]||"",i=0,n=(r=Array.isArray(o)?o:[o])&&r.length;i<n;i++){o=r[i];let t=a.$order,e=p.optgroups[o];void 0===e?o="":t=e.$order;const[n,l]=y(o,t);i>0&&(d=d.cloneNode(!0),it(d,{id:a.$id+"-clone-"+i,"aria-selected":null}),d.classList.add("ts-cloned"),Y(d,"active"),p.activeOption&&p.activeOption.dataset.value==s&&c&&c.dataset.group===o.toString()&&(v=d)),l.appendChild(d),""!=o&&(u[o]=n)}}var w;p.settings.lockOptgroupOrder&&h.sort(((t,e)=>t.order-e.order)),a=document.createDocumentFragment(),V(h,(t=>{let e=t.fragment,i=t.optgroup;if(!e||!e.children.length)return;let n=p.optgroups[i];if(void 0!==n){let t=document.createDocumentFragment(),i=p.render("optgroup_header",n);B(t,i),B(t,e);let s=p.render("optgroup",{group:n,options:t});B(a,s)}else B(a,e)})),b.innerHTML="",B(b,a),p.settings.highlight&&(w=b.querySelectorAll("span.highlight"),Array.prototype.forEach.call(w,(function(t){var e=t.parentNode;e.replaceChild(t.firstChild,t),e.normalize()})),m.query.length&&m.tokens.length&&V(m.tokens,(t=>{st(b,t.regex)})));var O=t=>{let e=p.render(t,{input:f});return e&&(_=!0,b.insertBefore(e,b.firstChild)),e};if(p.loading?O("loading"):p.settings.shouldLoad.call(p,f)?0===m.items.length&&O("no_results"):O("not_loading"),(l=p.canCreate(f))&&(d=O("option_create")),p.hasOptions=m.items.length>0||l,_){if(m.items.length>0){if(v||"single"!==p.settings.mode||null==p.items[0]||(v=p.getOption(p.items[0])),!b.contains(v)){let t=0;d&&!p.settings.addPrecedence&&(t=1),v=p.selectable()[t]}}else d&&(v=d);t&&!p.isOpen&&(p.open(),p.scrollToOption(v,"auto")),p.setActiveOption(v)}else p.clearActiveOption(),t&&p.isOpen&&p.close(!1)}selectable(){return this.dropdown_content.querySelectorAll("[data-selectable]")}addOption(t,e=!1){const i=this;if(Array.isArray(t))return i.addOptions(t,e),!1;const n=$(t[i.settings.valueField]);return null!==n&&!i.options.hasOwnProperty(n)&&(t.$order=t.$order||++i.order,t.$id=i.inputId+"-opt-"+t.$order,i.options[n]=t,i.lastQuery=null,e&&(i.userOptions[n]=e,i.trigger("option_add",n,t)),n)}addOptions(t,e=!1){V(t,(t=>{this.addOption(t,e)}))}registerOption(t){return this.addOption(t)}registerOptionGroup(t){var e=$(t[this.settings.optgroupValueField]);return null!==e&&(t.$order=t.$order||++this.order,this.optgroups[e]=t,e)}addOptionGroup(t,e){var i;e[this.settings.optgroupValueField]=t,(i=this.registerOptionGroup(e))&&this.trigger("optgroup_add",i,e)}removeOptionGroup(t){this.optgroups.hasOwnProperty(t)&&(delete this.optgroups[t],this.clearCache(),this.trigger("optgroup_remove",t))}clearOptionGroups(){this.optgroups={},this.clearCache(),this.trigger("optgroup_clear")}updateOption(t,e){const i=this;var n,s;const o=$(t),r=$(e[i.settings.valueField]);if(null===o)return;const a=i.options[o];if(null==a)return;if("string"!=typeof r)throw new Error("Value must be set in option data");const l=i.getOption(o),c=i.getItem(o);if(e.$order=e.$order||a.$order,delete i.options[o],i.uncacheValue(r),i.options[r]=e,l){if(i.dropdown_content.contains(l)){const t=i._render("option",e);nt(l,t),i.activeOption===l&&i.setActiveOption(t)}l.remove()}c&&(-1!==(s=i.items.indexOf(o))&&i.items.splice(s,1,r),n=i._render("item",e),c.classList.contains("active")&&J(n,"active"),nt(c,n)),i.lastQuery=null}removeOption(t,e){const i=this;t=P(t),i.uncacheValue(t),delete i.userOptions[t],delete i.options[t],i.lastQuery=null,i.trigger("option_remove",t),i.removeItem(t,e)}clearOptions(t){const e=(t||this.clearFilter).bind(this);this.loadedSearches={},this.userOptions={},this.clearCache();const i={};V(this.options,((t,n)=>{e(t,n)&&(i[n]=t)})),this.options=this.sifter.items=i,this.lastQuery=null,this.trigger("option_clear")}clearFilter(t,e){return this.items.indexOf(e)>=0}getOption(t,e=!1){const i=$(t);if(null===i)return null;const n=this.options[i];if(null!=n){if(n.$div)return n.$div;if(e)return this._render("option",n)}return null}getAdjacent(t,e,i="option"){var n;if(!t)return null;n="item"==i?this.controlChildren():this.dropdown_content.querySelectorAll("[data-selectable]");for(let i=0;i<n.length;i++)if(n[i]==t)return e>0?n[i+1]:n[i-1];return null}getItem(t){if("object"==typeof t)return t;var e=$(t);return null!==e?this.control.querySelector(`[data-value="${z(e)}"]`):null}addItems(t,e){var i=this,n=Array.isArray(t)?t:[t];const s=(n=n.filter((t=>-1===i.items.indexOf(t))))[n.length-1];n.forEach((t=>{i.isPending=t!==s,i.addItem(t,e)}))}addItem(t,e){F(this,e?[]:["change","dropdown_close"],(()=>{var i,n;const s=this,o=s.settings.mode,r=$(t);if((!r||-1===s.items.indexOf(r)||("single"===o&&s.close(),"single"!==o&&s.settings.duplicates))&&null!==r&&s.options.hasOwnProperty(r)&&("single"===o&&s.clear(e),"multi"!==o||!s.isFull())){if(i=s._render("item",s.options[r]),s.control.contains(i)&&(i=i.cloneNode(!0)),n=s.isFull(),s.items.splice(s.caretPos,0,r),s.insertAtCaret(i),s.isSetup){if(!s.isPending&&s.settings.hideSelected){let t=s.getOption(r),e=s.getAdjacent(t,1);e&&s.setActiveOption(e)}s.isPending||s.settings.closeAfterSelect||s.refreshOptions(s.isFocused&&"single"!==o),0!=s.settings.closeAfterSelect&&s.isFull()?s.close():s.isPending||s.positionDropdown(),s.trigger("item_add",r,i),s.isPending||s.updateOriginalInput({silent:e})}(!s.isPending||!n&&s.isFull())&&(s.inputState(),s.refreshState())}}))}removeItem(t=null,e){const i=this;if(!(t=i.getItem(t)))return;var n,s;const o=t.dataset.value;n=et(t),t.remove(),t.classList.contains("active")&&(s=i.activeItems.indexOf(t),i.activeItems.splice(s,1),Y(t,"active")),i.items.splice(n,1),i.lastQuery=null,!i.settings.persist&&i.userOptions.hasOwnProperty(o)&&i.removeOption(o,e),n<i.caretPos&&i.setCaret(i.caretPos-1),i.updateOriginalInput({silent:e}),i.refreshState(),i.positionDropdown(),i.trigger("item_remove",o,t)}createItem(t=null,e=()=>{}){3===arguments.length&&(e=arguments[2]),"function"!=typeof e&&(e=()=>{});var i,n=this,s=n.caretPos;if(t=t||n.inputValue(),!n.canCreate(t))return e(),!1;n.lock();var o=!1,r=t=>{if(n.unlock(),!t||"object"!=typeof t)return e();var i=$(t[n.settings.valueField]);if("string"!=typeof i)return e();n.setTextboxValue(),n.addOption(t,!0),n.setCaret(s),n.addItem(i),e(t),o=!0};return i="function"==typeof n.settings.create?n.settings.create.call(this,t,r):{[n.settings.labelField]:t,[n.settings.valueField]:t},o||r(i),!0}refreshItems(){var t=this;t.lastQuery=null,t.isSetup&&t.addItems(t.items),t.updateOriginalInput(),t.refreshState()}refreshState(){const t=this;t.refreshValidityState();const e=t.isFull(),i=t.isLocked;t.wrapper.classList.toggle("rtl",t.rtl);const n=t.wrapper.classList;var s;n.toggle("focus",t.isFocused),n.toggle("disabled",t.isDisabled),n.toggle("readonly",t.isReadOnly),n.toggle("required",t.isRequired),n.toggle("invalid",!t.isValid),n.toggle("locked",i),n.toggle("full",e),n.toggle("input-active",t.isFocused&&!t.isInputHidden),n.toggle("dropdown-active",t.isOpen),n.toggle("has-options",(s=t.options,0===Object.keys(s).length)),n.toggle("has-items",t.items.length>0)}refreshValidityState(){var t=this;t.input.validity&&(t.isValid=t.input.validity.valid,t.isInvalid=!t.isValid)}isFull(){return null!==this.settings.maxItems&&this.items.length>=this.settings.maxItems}updateOriginalInput(t={}){const e=this;var i,n;const s=e.input.querySelector('option[value=""]');if(e.is_select_tag){const o=[],r=e.input.querySelectorAll("option:checked").length;function a(t,i,n){return t||(t=W('<option value="'+D(i)+'">'+D(n)+"</option>")),t!=s&&e.input.append(t),o.push(t),(t!=s||r>0)&&(t.selected=!0),t}e.input.querySelectorAll("option:checked").forEach((t=>{t.selected=!1})),0==e.items.length&&"single"==e.settings.mode?a(s,"",""):e.items.forEach((t=>{i=e.options[t],n=i[e.settings.labelField]||"",o.includes(i.$option)?a(e.input.querySelector(`option[value="${z(t)}"]:not(:checked)`),t,n):i.$option=a(i.$option,t,n)}))}else e.input.value=e.getValue();e.isSetup&&(t.silent||e.trigger("change",e.getValue()))}open(){var t=this;t.isLocked||t.isOpen||"multi"===t.settings.mode&&t.isFull()||(t.isOpen=!0,it(t.focus_node,{"aria-expanded":"true"}),t.refreshState(),Q(t.dropdown,{visibility:"hidden",display:"block"}),t.positionDropdown(),Q(t.dropdown,{visibility:"visible",display:"block"}),t.focus(),t.trigger("dropdown_open",t.dropdown))}close(t=!0){var e=this,i=e.isOpen;t&&(e.setTextboxValue(),"single"===e.settings.mode&&e.items.length&&e.inputState()),e.isOpen=!1,it(e.focus_node,{"aria-expanded":"false"}),Q(e.dropdown,{display:"none"}),e.settings.hideSelected&&e.clearActiveOption(),e.refreshState(),i&&e.trigger("dropdown_close",e.dropdown)}positionDropdown(){if("body"===this.settings.dropdownParent){var t=this.control,e=t.getBoundingClientRect(),i=t.offsetHeight+e.top+window.scrollY,n=e.left+window.scrollX;Q(this.dropdown,{width:e.width+"px",top:i+"px",left:n+"px"})}}clear(t){var e=this;if(e.items.length){var i=e.controlChildren();V(i,(t=>{e.removeItem(t,!0)})),e.inputState(),t||e.updateOriginalInput(),e.trigger("clear")}}insertAtCaret(t){const e=this,i=e.caretPos,n=e.control;n.insertBefore(t,n.children[i]||null),e.setCaret(i+1)}deleteSelection(t){var e,i,n,s,o,r=this;e=t&&8===t.keyCode?-1:1,i={start:(o=r.control_input).selectionStart||0,length:(o.selectionEnd||0)-(o.selectionStart||0)};const a=[];if(r.activeItems.length)s=tt(r.activeItems,e),n=et(s),e>0&&n++,V(r.activeItems,(t=>a.push(t)));else if((r.isFocused||"single"===r.settings.mode)&&r.items.length){const t=r.controlChildren();let n;e<0&&0===i.start&&0===i.length?n=t[r.caretPos-1]:e>0&&i.start===r.inputValue().length&&(n=t[r.caretPos]),void 0!==n&&a.push(n)}if(!r.shouldDelete(a,t))return!1;for(M(t,!0),void 0!==n&&r.setCaret(n);a.length;)r.removeItem(a.pop());return r.inputState(),r.positionDropdown(),r.refreshOptions(!1),!0}shouldDelete(t,e){const i=t.map((t=>t.dataset.value));return!(!i.length||"function"==typeof this.settings.onDelete&&!1===this.settings.onDelete(i,e))}advanceSelection(t,e){var i,n,s=this;s.rtl&&(t*=-1),s.inputValue().length||(R(ot,e)||R("shiftKey",e)?(n=(i=s.getLastActive(t))?i.classList.contains("active")?s.getAdjacent(i,t,"item"):i:t>0?s.control_input.nextElementSibling:s.control_input.previousElementSibling)&&(n.classList.contains("active")&&s.removeActiveItem(i),s.setActiveItemClass(n)):s.moveCaret(t))}moveCaret(t){}getLastActive(t){let e=this.control.querySelector(".last-active");if(e)return e;var i=this.control.querySelectorAll(".active");return i?tt(i,t):void 0}setCaret(t){this.caretPos=this.items.length}controlChildren(){return Array.from(this.control.querySelectorAll("[data-ts-item]"))}lock(){this.setLocked(!0)}unlock(){this.setLocked(!1)}setLocked(t=this.isReadOnly||this.isDisabled){this.isLocked=t,this.refreshState()}disable(){this.setDisabled(!0),this.close()}enable(){this.setDisabled(!1)}setDisabled(t){this.focus_node.tabIndex=t?-1:this.tabIndex,this.isDisabled=t,this.input.disabled=t,this.control_input.disabled=t,this.setLocked()}setReadOnly(t){this.isReadOnly=t,this.input.readOnly=t,this.control_input.readOnly=t,this.setLocked()}destroy(){var t=this,e=t.revertSettings;t.trigger("destroy"),t.off(),t.wrapper.remove(),t.dropdown.remove(),t.input.innerHTML=e.innerHTML,t.input.tabIndex=e.tabIndex,Y(t.input,"tomselected","ts-hidden-accessible"),t._destroy(),delete t.input.tomselect}render(t,e){var i,n;const s=this;if("function"!=typeof this.settings.render[t])return null;if(!(n=s.settings.render[t].call(this,e,D)))return null;if(n=W(n),"option"===t||"option_create"===t?e[s.settings.disabledField]?it(n,{"aria-disabled":"true"}):it(n,{"data-selectable":""}):"optgroup"===t&&(i=e.group[s.settings.optgroupValueField],it(n,{"data-group":i}),e.group[s.settings.disabledField]&&it(n,{"data-disabled":""})),"option"===t||"item"===t){const i=P(e[s.settings.valueField]);it(n,{"data-value":i}),"item"===t?(J(n,s.settings.itemClass),it(n,{"data-ts-item":""})):(J(n,s.settings.optionClass),it(n,{role:"option",id:e.$id}),e.$div=n,s.options[i]=e)}return n}_render(t,e){const i=this.render(t,e);if(null==i)throw"HTMLElement expected";return i}clearCache(){V(this.options,(t=>{t.$div&&(t.$div.remove(),delete t.$div)}))}uncacheValue(t){const e=this.getOption(t);e&&e.remove()}canCreate(t){return this.settings.create&&t.length>0&&this.settings.createFilter.call(this,t)}hook(t,e,i){var n=this,s=n[e];n[e]=function(){var e,o;return"after"===t&&(e=s.apply(n,arguments)),o=i.apply(n,arguments),"instead"===t?o:("before"===t&&(e=s.apply(n,arguments)),e)}}}return ct.define("change_listener",(function(){q(this.input,"change",(()=>{this.sync()}))})),ct.define("checkbox_options",(function(t){var e=this,i=e.onOptionSelect;e.settings.hideSelected=!1;const n=Object.assign({className:"tomselect-checkbox",checkedClassNames:void 0,uncheckedClassNames:void 0},t);var s=function(t,e){e?(t.checked=!0,n.uncheckedClassNames&&t.classList.remove(...n.uncheckedClassNames),n.checkedClassNames&&t.classList.add(...n.checkedClassNames)):(t.checked=!1,n.checkedClassNames&&t.classList.remove(...n.checkedClassNames),n.uncheckedClassNames&&t.classList.add(...n.uncheckedClassNames))},o=function(t){setTimeout((()=>{var e=t.querySelector("input."+n.className);e instanceof HTMLInputElement&&s(e,t.classList.contains("selected"))}),1)};e.hook("after","setupTemplates",(()=>{var t=e.settings.render.option;e.settings.render.option=(i,o)=>{var r=W(t.call(e,i,o)),a=document.createElement("input");n.className&&a.classList.add(n.className),a.addEventListener("click",(function(t){M(t)})),a.type="checkbox";const l=$(i[e.settings.valueField]);return s(a,!!(l&&e.items.indexOf(l)>-1)),r.prepend(a),r}})),e.on("item_remove",(t=>{var i=e.getOption(t);i&&(i.classList.remove("selected"),o(i))})),e.on("item_add",(t=>{var i=e.getOption(t);i&&o(i)})),e.hook("instead","onOptionSelect",((t,n)=>{if(n.classList.contains("selected"))return n.classList.remove("selected"),e.removeItem(n.dataset.value),e.refreshOptions(),void M(t,!0);i.call(e,t,n),o(n)}))})),ct.define("clear_button",(function(t){const e=this,i=Object.assign({className:"clear-button",title:"Clear All",html:t=>`<div class="${t.className}" title="${t.title}">&#10799;</div>`},t);e.on("initialize",(()=>{var t=W(i.html(i));t.addEventListener("click",(t=>{e.isLocked||(e.clear(),"single"===e.settings.mode&&e.settings.allowEmptyOption&&e.addItem(""),t.preventDefault(),t.stopPropagation())})),e.control.appendChild(t)}))})),ct.define("drag_drop",(function(){var t=this;if("multi"!==t.settings.mode)return;var e=t.lock,i=t.unlock;let n,s=!0;t.hook("after","setupTemplates",(()=>{var e=t.settings.render.item;t.settings.render.item=(i,o)=>{const r=W(e.call(t,i,o));it(r,{draggable:"true"});const a=t=>{t.preventDefault(),r.classList.add("ts-drag-over"),l(r,n)},l=(t,e)=>{var i,n,s;void 0!==e&&(((t,e)=>{do{var i;if(t==(e=null==(i=e)?void 0:i.previousElementSibling))return!0}while(e&&e.previousElementSibling);return!1})(e,r)?(n=e,null==(s=(i=t).parentNode)||s.insertBefore(n,i.nextSibling)):((t,e)=>{var i;null==(i=t.parentNode)||i.insertBefore(e,t)})(t,e))};return q(r,"mousedown",(t=>{s||M(t),t.stopPropagation()})),q(r,"dragstart",(t=>{n=r,setTimeout((()=>{r.classList.add("ts-dragging")}),0)})),q(r,"dragenter",a),q(r,"dragover",a),q(r,"dragleave",(()=>{r.classList.remove("ts-drag-over")})),q(r,"dragend",(()=>{var e;document.querySelectorAll(".ts-drag-over").forEach((t=>t.classList.remove("ts-drag-over"))),null==(e=n)||e.classList.remove("ts-dragging"),n=void 0;var i=[];t.control.querySelectorAll("[data-value]").forEach((t=>{if(t.dataset.value){let e=t.dataset.value;e&&i.push(e)}})),t.setValue(i)})),r}})),t.hook("instead","lock",(()=>(s=!1,e.call(t)))),t.hook("instead","unlock",(()=>(s=!0,i.call(t))))})),ct.define("dropdown_header",(function(t){const e=this,i=Object.assign({title:"Untitled",headerClass:"dropdown-header",titleRowClass:"dropdown-header-title",labelClass:"dropdown-header-label",closeClass:"dropdown-header-close",html:t=>'<div class="'+t.headerClass+'"><div class="'+t.titleRowClass+'"><span class="'+t.labelClass+'">'+t.title+'</span><a class="'+t.closeClass+'">&times;</a></div></div>'},t);e.on("initialize",(()=>{var t=W(i.html(i)),n=t.querySelector("."+i.closeClass);n&&n.addEventListener("click",(t=>{M(t,!0),e.close()})),e.dropdown.insertBefore(t,e.dropdown.firstChild)}))})),ct.define("caret_position",(function(){var t=this;t.hook("instead","setCaret",(e=>{"single"!==t.settings.mode&&t.control.contains(t.control_input)?(e=Math.max(0,Math.min(t.items.length,e)))==t.caretPos||t.isPending||t.controlChildren().forEach(((i,n)=>{n<e?t.control_input.insertAdjacentElement("beforebegin",i):t.control.appendChild(i)})):e=t.items.length,t.caretPos=e})),t.hook("instead","moveCaret",(e=>{if(!t.isFocused)return;const i=t.getLastActive(e);if(i){const n=et(i);t.setCaret(e>0?n+1:n),t.setActiveItem(),Y(i,"last-active")}else t.setCaret(t.caretPos+e)}))})),ct.define("dropdown_input",(function(){const t=this;t.settings.shouldOpen=!0,t.hook("before","setup",(()=>{t.focus_node=t.control,J(t.control_input,"dropdown-input");const e=W('<div class="dropdown-input-wrap">');e.append(t.control_input),t.dropdown.insertBefore(e,t.dropdown.firstChild);const i=W('<input class="items-placeholder" tabindex="-1" />');i.placeholder=t.settings.placeholder||"",t.control.append(i)})),t.on("initialize",(()=>{t.control_input.addEventListener("keydown",(e=>{switch(e.keyCode){case 27:return t.isOpen&&(M(e,!0),t.close()),void t.clearActiveItems();case 9:t.focus_node.tabIndex=-1}return t.onKeyDown.call(t,e)})),t.on("blur",(()=>{t.focus_node.tabIndex=t.isDisabled?-1:t.tabIndex})),t.on("dropdown_open",(()=>{t.control_input.focus()}));const e=t.onBlur;t.hook("instead","onBlur",(i=>{if(!i||i.relatedTarget!=t.control_input)return e.call(t)})),q(t.control_input,"blur",(()=>t.onBlur())),t.hook("before","close",(()=>{t.isOpen&&t.focus_node.focus({preventScroll:!0})}))}))})),ct.define("input_autogrow",(function(){var t=this;t.on("initialize",(()=>{var e=document.createElement("span"),i=t.control_input;e.style.cssText="position:absolute; top:-99999px; left:-99999px; width:auto; padding:0; white-space:pre; ",t.wrapper.appendChild(e);for(const t of["letterSpacing","fontSize","fontFamily","fontWeight","textTransform"])e.style[t]=i.style[t];var n=()=>{e.textContent=i.value,i.style.width=e.clientWidth+"px"};n(),t.on("update item_add item_remove",n),q(i,"input",n),q(i,"keyup",n),q(i,"blur",n),q(i,"update",n)}))})),ct.define("no_backspace_delete",(function(){var t=this,e=t.deleteSelection;this.hook("instead","deleteSelection",(i=>!!t.activeItems.length&&e.call(t,i)))})),ct.define("no_active_items",(function(){this.hook("instead","setActiveItem",(()=>{})),this.hook("instead","selectAll",(()=>{}))})),ct.define("optgroup_columns",(function(){var t=this,e=t.onKeyDown;t.hook("instead","onKeyDown",(i=>{var n,s,o,r;if(!t.isOpen||37!==i.keyCode&&39!==i.keyCode)return e.call(t,i);t.ignoreHover=!0,r=Z(t.activeOption,"[data-group]"),n=et(t.activeOption,"[data-selectable]"),r&&(r=37===i.keyCode?r.previousSibling:r.nextSibling)&&(s=(o=r.querySelectorAll("[data-selectable]"))[Math.min(o.length-1,n)])&&t.setActiveOption(s)}))})),ct.define("remove_button",(function(t){const e=Object.assign({label:"&times;",title:"Remove",className:"remove",append:!0},t);var i=this;if(e.append){var n='<a href="javascript:void(0)" class="'+e.className+'" tabindex="-1" title="'+D(e.title)+'">'+e.label+"</a>";i.hook("after","setupTemplates",(()=>{var t=i.settings.render.item;i.settings.render.item=(e,s)=>{var o=W(t.call(i,e,s)),r=W(n);return o.appendChild(r),q(r,"mousedown",(t=>{M(t,!0)})),q(r,"click",(t=>{i.isLocked||(M(t,!0),i.isLocked||i.shouldDelete([o],t)&&(i.removeItem(o),i.refreshOptions(!1),i.inputState()))})),o}}))}})),ct.define("restore_on_backspace",(function(t){const e=this,i=Object.assign({text:t=>t[e.settings.labelField]},t);e.on("item_remove",(function(t){if(e.isFocused&&""===e.control_input.value.trim()){var n=e.options[t];n&&e.setTextboxValue(i.text.call(e,n))}}))})),ct.define("virtual_scroll",(function(){const t=this,e=t.canLoad,i=t.clearActiveOption,n=t.loadCallback;var s,o,r={},a=!1,l=[];if(t.settings.shouldLoadMore||(t.settings.shouldLoadMore=()=>{if(s.clientHeight/(s.scrollHeight-s.scrollTop)>.9)return!0;if(t.activeOption){var e=t.selectable();if(Array.from(e).indexOf(t.activeOption)>=e.length-2)return!0}return!1}),!t.settings.firstUrl)throw"virtual_scroll plugin requires a firstUrl() method";t.settings.sortField=[{field:"$order"},{field:"$score"}];const c=e=>!("number"==typeof t.settings.maxOptions&&s.children.length>=t.settings.maxOptions||!(e in r)||!r[e]),d=(e,i)=>t.items.indexOf(i)>=0||l.indexOf(i)>=0;t.setNextUrl=(t,e)=>{r[t]=e},t.getUrl=e=>{if(e in r){const t=r[e];return r[e]=!1,t}return t.clearPagination(),t.settings.firstUrl.call(t,e)},t.clearPagination=()=>{r={}},t.hook("instead","clearActiveOption",(()=>{if(!a)return i.call(t)})),t.hook("instead","canLoad",(i=>i in r?c(i):e.call(t,i))),t.hook("instead","loadCallback",((e,i)=>{if(a){if(o){const i=e[0];void 0!==i&&(o.dataset.value=i[t.settings.valueField])}}else t.clearOptions(d);n.call(t,e,i),a=!1})),t.hook("after","refreshOptions",(()=>{const e=t.lastValue;var i;c(e)?(i=t.render("loading_more",{query:e}))&&(i.setAttribute("data-selectable",""),o=i):e in r&&!s.querySelector(".no-results")&&(i=t.render("no_more_results",{query:e})),i&&(J(i,t.settings.optionClass),s.append(i))})),t.on("initialize",(()=>{l=Object.keys(t.options),s=t.dropdown_content,t.settings.render=Object.assign({},{loading_more:()=>'<div class="loading-more-results">Loading more results ... </div>',no_more_results:()=>'<div class="no-more-results">No more results</div>'},t.settings.render),s.addEventListener("scroll",(()=>{t.settings.shouldLoadMore.call(t)&&c(t.lastValue)&&(a||(a=!0,t.load.call(t,t.lastValue)))}))}))})),ct}()}},e={};function i(n){var s=e[n];if(void 0!==s)return s.exports;var o=e[n]={exports:{}};return t[n].call(o.exports,o,o.exports,i),o.exports}i.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return i.d(e,{a:e}),e},i.d=(t,e)=>{for(var n in e)i.o(e,n)&&!i.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},i.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),i.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},(()=>{"use strict";var t=i(414),e=i.n(t);class n{constructor(t,e=!0,i=[],n=5e3){this.ctx=t,this.iframes=e,this.exclude=i,this.iframesTimeout=n}static matches(t,e){const i="string"==typeof e?[e]:e,n=t.matches||t.matchesSelector||t.msMatchesSelector||t.mozMatchesSelector||t.oMatchesSelector||t.webkitMatchesSelector;if(n){let e=!1;return i.every((i=>!n.call(t,i)||(e=!0,!1))),e}return!1}getContexts(){let t,e=[];return t=void 0!==this.ctx&&this.ctx?NodeList.prototype.isPrototypeOf(this.ctx)?Array.prototype.slice.call(this.ctx):Array.isArray(this.ctx)?this.ctx:"string"==typeof this.ctx?Array.prototype.slice.call(document.querySelectorAll(this.ctx)):[this.ctx]:[],t.forEach((t=>{const i=e.filter((e=>e.contains(t))).length>0;-1!==e.indexOf(t)||i||e.push(t)})),e}getIframeContents(t,e,i=()=>{}){let n;try{const e=t.contentWindow;if(n=e.document,!e||!n)throw new Error("iframe inaccessible")}catch(t){i()}n&&e(n)}isIframeBlank(t){const e="about:blank",i=t.getAttribute("src").trim();return t.contentWindow.location.href===e&&i!==e&&i}observeIframeLoad(t,e,i){let n=!1,s=null;const o=()=>{if(!n){n=!0,clearTimeout(s);try{this.isIframeBlank(t)||(t.removeEventListener("load",o),this.getIframeContents(t,e,i))}catch(t){i()}}};t.addEventListener("load",o),s=setTimeout(o,this.iframesTimeout)}onIframeReady(t,e,i){try{"complete"===t.contentWindow.document.readyState?this.isIframeBlank(t)?this.observeIframeLoad(t,e,i):this.getIframeContents(t,e,i):this.observeIframeLoad(t,e,i)}catch(t){i()}}waitForIframes(t,e){let i=0;this.forEachIframe(t,(()=>!0),(t=>{i++,this.waitForIframes(t.querySelector("html"),(()=>{--i||e()}))}),(t=>{t||e()}))}forEachIframe(t,e,i,s=()=>{}){let o=t.querySelectorAll("iframe"),r=o.length,a=0;o=Array.prototype.slice.call(o);const l=()=>{--r<=0&&s(a)};r||l(),o.forEach((t=>{n.matches(t,this.exclude)?l():this.onIframeReady(t,(n=>{e(t)&&(a++,i(n)),l()}),l)}))}createIterator(t,e,i){return document.createNodeIterator(t,e,i,!1)}createInstanceOnIframe(t){return new n(t.querySelector("html"),this.iframes)}compareNodeIframe(t,e,i){if(t.compareDocumentPosition(i)&Node.DOCUMENT_POSITION_PRECEDING){if(null===e)return!0;if(e.compareDocumentPosition(i)&Node.DOCUMENT_POSITION_FOLLOWING)return!0}return!1}getIteratorNode(t){const e=t.previousNode();let i;return i=(null===e||t.nextNode())&&t.nextNode(),{prevNode:e,node:i}}checkIframeFilter(t,e,i,n){let s=!1,o=!1;return n.forEach(((t,e)=>{t.val===i&&(s=e,o=t.handled)})),this.compareNodeIframe(t,e,i)?(!1!==s||o?!1===s||o||(n[s].handled=!0):n.push({val:i,handled:!0}),!0):(!1===s&&n.push({val:i,handled:!1}),!1)}handleOpenIframes(t,e,i,n){t.forEach((t=>{t.handled||this.getIframeContents(t.val,(t=>{this.createInstanceOnIframe(t).forEachNode(e,i,n)}))}))}iterateThroughNodes(t,e,i,n,s){const o=this.createIterator(e,t,n);let r,a,l=[],c=[],d=()=>(({prevNode:a,node:r}=this.getIteratorNode(o)),r);for(;d();)this.iframes&&this.forEachIframe(e,(t=>this.checkIframeFilter(r,a,t,l)),(e=>{this.createInstanceOnIframe(e).forEachNode(t,(t=>c.push(t)),n)})),c.push(r);c.forEach((t=>{i(t)})),this.iframes&&this.handleOpenIframes(l,t,i,n),s()}forEachNode(t,e,i,n=()=>{}){const s=this.getContexts();let o=s.length;o||n(),s.forEach((s=>{const r=()=>{this.iterateThroughNodes(t,s,e,i,(()=>{--o<=0&&n()}))};this.iframes?this.waitForIframes(s,r):r()}))}}class s{constructor(t){this.ctx=t,this.ie=!1;const e=window.navigator.userAgent;(e.indexOf("MSIE")>-1||e.indexOf("Trident")>-1)&&(this.ie=!0)}set opt(t){this._opt=Object.assign({},{element:"",className:"",exclude:[],iframes:!1,iframesTimeout:5e3,separateWordSearch:!0,diacritics:!0,synonyms:{},accuracy:"partially",acrossElements:!1,caseSensitive:!1,ignoreJoiners:!1,ignoreGroups:0,ignorePunctuation:[],wildcards:"disabled",each:()=>{},noMatch:()=>{},filter:()=>!0,done:()=>{},debug:!1,log:window.console},t)}get opt(){return this._opt}get iterator(){return new n(this.ctx,this.opt.iframes,this.opt.exclude,this.opt.iframesTimeout)}log(t,e="debug"){const i=this.opt.log;this.opt.debug&&"object"==typeof i&&"function"==typeof i[e]&&i[e](`mark.js: ${t}`)}escapeStr(t){return t.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&")}createRegExp(t){return"disabled"!==this.opt.wildcards&&(t=this.setupWildcardsRegExp(t)),t=this.escapeStr(t),Object.keys(this.opt.synonyms).length&&(t=this.createSynonymsRegExp(t)),(this.opt.ignoreJoiners||this.opt.ignorePunctuation.length)&&(t=this.setupIgnoreJoinersRegExp(t)),this.opt.diacritics&&(t=this.createDiacriticsRegExp(t)),t=this.createMergedBlanksRegExp(t),(this.opt.ignoreJoiners||this.opt.ignorePunctuation.length)&&(t=this.createJoinersRegExp(t)),"disabled"!==this.opt.wildcards&&(t=this.createWildcardsRegExp(t)),t=this.createAccuracyRegExp(t)}createSynonymsRegExp(t){const e=this.opt.synonyms,i=this.opt.caseSensitive?"":"i",n=this.opt.ignoreJoiners||this.opt.ignorePunctuation.length?"\0":"";for(let s in e)if(e.hasOwnProperty(s)){const o=e[s],r="disabled"!==this.opt.wildcards?this.setupWildcardsRegExp(s):this.escapeStr(s),a="disabled"!==this.opt.wildcards?this.setupWildcardsRegExp(o):this.escapeStr(o);""!==r&&""!==a&&(t=t.replace(new RegExp(`(${this.escapeStr(r)}|${this.escapeStr(a)})`,`gm${i}`),n+`(${this.processSynomyms(r)}|`+`${this.processSynomyms(a)})`+n))}return t}processSynomyms(t){return(this.opt.ignoreJoiners||this.opt.ignorePunctuation.length)&&(t=this.setupIgnoreJoinersRegExp(t)),t}setupWildcardsRegExp(t){return(t=t.replace(/(?:\\)*\?/g,(t=>"\\"===t.charAt(0)?"?":""))).replace(/(?:\\)*\*/g,(t=>"\\"===t.charAt(0)?"*":""))}createWildcardsRegExp(t){let e="withSpaces"===this.opt.wildcards;return t.replace(/\u0001/g,e?"[\\S\\s]?":"\\S?").replace(/\u0002/g,e?"[\\S\\s]*?":"\\S*")}setupIgnoreJoinersRegExp(t){return t.replace(/[^(|)\\]/g,((t,e,i)=>{let n=i.charAt(e+1);return/[(|)\\]/.test(n)||""===n?t:t+"\0"}))}createJoinersRegExp(t){let e=[];const i=this.opt.ignorePunctuation;return Array.isArray(i)&&i.length&&e.push(this.escapeStr(i.join(""))),this.opt.ignoreJoiners&&e.push("\\u00ad\\u200b\\u200c\\u200d"),e.length?t.split(/\u0000+/).join(`[${e.join("")}]*`):t}createDiacriticsRegExp(t){const e=this.opt.caseSensitive?"":"i",i=this.opt.caseSensitive?["aàáảãạăằắẳẵặâầấẩẫậäåāą","AÀÁẢÃẠĂẰẮẲẴẶÂẦẤẨẪẬÄÅĀĄ","cçćč","CÇĆČ","dđď","DĐĎ","eèéẻẽẹêềếểễệëěēę","EÈÉẺẼẸÊỀẾỂỄỆËĚĒĘ","iìíỉĩịîïī","IÌÍỈĨỊÎÏĪ","lł","LŁ","nñňń","NÑŇŃ","oòóỏõọôồốổỗộơởỡớờợöøō","OÒÓỎÕỌÔỒỐỔỖỘƠỞỠỚỜỢÖØŌ","rř","RŘ","sšśșş","SŠŚȘŞ","tťțţ","TŤȚŢ","uùúủũụưừứửữựûüůū","UÙÚỦŨỤƯỪỨỬỮỰÛÜŮŪ","yýỳỷỹỵÿ","YÝỲỶỸỴŸ","zžżź","ZŽŻŹ"]:["aàáảãạăằắẳẵặâầấẩẫậäåāąAÀÁẢÃẠĂẰẮẲẴẶÂẦẤẨẪẬÄÅĀĄ","cçćčCÇĆČ","dđďDĐĎ","eèéẻẽẹêềếểễệëěēęEÈÉẺẼẸÊỀẾỂỄỆËĚĒĘ","iìíỉĩịîïīIÌÍỈĨỊÎÏĪ","lłLŁ","nñňńNÑŇŃ","oòóỏõọôồốổỗộơởỡớờợöøōOÒÓỎÕỌÔỒỐỔỖỘƠỞỠỚỜỢÖØŌ","rřRŘ","sšśșşSŠŚȘŞ","tťțţTŤȚŢ","uùúủũụưừứửữựûüůūUÙÚỦŨỤƯỪỨỬỮỰÛÜŮŪ","yýỳỷỹỵÿYÝỲỶỸỴŸ","zžżźZŽŻŹ"];let n=[];return t.split("").forEach((s=>{i.every((i=>{if(-1!==i.indexOf(s)){if(n.indexOf(i)>-1)return!1;t=t.replace(new RegExp(`[${i}]`,`gm${e}`),`[${i}]`),n.push(i)}return!0}))})),t}createMergedBlanksRegExp(t){return t.replace(/[\s]+/gim,"[\\s]+")}createAccuracyRegExp(t){let e=this.opt.accuracy,i="string"==typeof e?e:e.value,n="string"==typeof e?[]:e.limiters,s="";switch(n.forEach((t=>{s+=`|${this.escapeStr(t)}`})),i){case"partially":default:return`()(${t})`;case"complementary":return s="\\s"+(s||this.escapeStr("!\"#$%&'()*+,-./:;<=>?@[\\]^_`{|}~¡¿")),`()([^${s}]*${t}[^${s}]*)`;case"exactly":return`(^|\\s${s})(${t})(?=$|\\s${s})`}}getSeparatedKeywords(t){let e=[];return t.forEach((t=>{this.opt.separateWordSearch?t.split(" ").forEach((t=>{t.trim()&&-1===e.indexOf(t)&&e.push(t)})):t.trim()&&-1===e.indexOf(t)&&e.push(t)})),{keywords:e.sort(((t,e)=>e.length-t.length)),length:e.length}}isNumeric(t){return Number(parseFloat(t))==t}checkRanges(t){if(!Array.isArray(t)||"[object Object]"!==Object.prototype.toString.call(t[0]))return this.log("markRanges() will only accept an array of objects"),this.opt.noMatch(t),[];const e=[];let i=0;return t.sort(((t,e)=>t.start-e.start)).forEach((t=>{let{start:n,end:s,valid:o}=this.callNoMatchOnInvalidRanges(t,i);o&&(t.start=n,t.length=s-n,e.push(t),i=s)})),e}callNoMatchOnInvalidRanges(t,e){let i,n,s=!1;return t&&void 0!==t.start?(i=parseInt(t.start,10),n=i+parseInt(t.length,10),this.isNumeric(t.start)&&this.isNumeric(t.length)&&n-e>0&&n-i>0?s=!0:(this.log(`Ignoring invalid or overlapping range: ${JSON.stringify(t)}`),this.opt.noMatch(t))):(this.log(`Ignoring invalid range: ${JSON.stringify(t)}`),this.opt.noMatch(t)),{start:i,end:n,valid:s}}checkWhitespaceRanges(t,e,i){let n,s=!0,o=i.length,r=e-o,a=parseInt(t.start,10)-r;return a=a>o?o:a,n=a+parseInt(t.length,10),n>o&&(n=o,this.log(`End range automatically set to the max value of ${o}`)),a<0||n-a<0||a>o||n>o?(s=!1,this.log(`Invalid range: ${JSON.stringify(t)}`),this.opt.noMatch(t)):""===i.substring(a,n).replace(/\s+/g,"")&&(s=!1,this.log("Skipping whitespace only range: "+JSON.stringify(t)),this.opt.noMatch(t)),{start:a,end:n,valid:s}}getTextNodes(t){let e="",i=[];this.iterator.forEachNode(NodeFilter.SHOW_TEXT,(t=>{i.push({start:e.length,end:(e+=t.textContent).length,node:t})}),(t=>this.matchesExclude(t.parentNode)?NodeFilter.FILTER_REJECT:NodeFilter.FILTER_ACCEPT),(()=>{t({value:e,nodes:i})}))}matchesExclude(t){return n.matches(t,this.opt.exclude.concat(["script","style","title","head","html"]))}wrapRangeInTextNode(t,e,i){const n=this.opt.element?this.opt.element:"mark",s=t.splitText(e),o=s.splitText(i-e);let r=document.createElement(n);return r.setAttribute("data-markjs","true"),this.opt.className&&r.setAttribute("class",this.opt.className),r.textContent=s.textContent,s.parentNode.replaceChild(r,s),o}wrapRangeInMappedTextNode(t,e,i,n,s){t.nodes.every(((o,r)=>{const a=t.nodes[r+1];if(void 0===a||a.start>e){if(!n(o.node))return!1;const a=e-o.start,l=(i>o.end?o.end:i)-o.start,c=t.value.substr(0,o.start),d=t.value.substr(l+o.start);if(o.node=this.wrapRangeInTextNode(o.node,a,l),t.value=c+d,t.nodes.forEach(((e,i)=>{i>=r&&(t.nodes[i].start>0&&i!==r&&(t.nodes[i].start-=l),t.nodes[i].end-=l)})),i-=l,s(o.node.previousSibling,o.start),!(i>o.end))return!1;e=o.end}return!0}))}wrapMatches(t,e,i,n,s){const o=0===e?0:e+1;this.getTextNodes((e=>{e.nodes.forEach((e=>{let s;for(e=e.node;null!==(s=t.exec(e.textContent))&&""!==s[o];){if(!i(s[o],e))continue;let r=s.index;if(0!==o)for(let t=1;t<o;t++)r+=s[t].length;e=this.wrapRangeInTextNode(e,r,r+s[o].length),n(e.previousSibling),t.lastIndex=0}})),s()}))}wrapMatchesAcrossElements(t,e,i,n,s){const o=0===e?0:e+1;this.getTextNodes((e=>{let r;for(;null!==(r=t.exec(e.value))&&""!==r[o];){let s=r.index;if(0!==o)for(let t=1;t<o;t++)s+=r[t].length;const a=s+r[o].length;this.wrapRangeInMappedTextNode(e,s,a,(t=>i(r[o],t)),((e,i)=>{t.lastIndex=i,n(e)}))}s()}))}wrapRangeFromIndex(t,e,i,n){this.getTextNodes((s=>{const o=s.value.length;t.forEach(((t,n)=>{let{start:r,end:a,valid:l}=this.checkWhitespaceRanges(t,o,s.value);l&&this.wrapRangeInMappedTextNode(s,r,a,(i=>e(i,t,s.value.substring(r,a),n)),(e=>{i(e,t)}))})),n()}))}unwrapMatches(t){const e=t.parentNode;let i=document.createDocumentFragment();for(;t.firstChild;)i.appendChild(t.removeChild(t.firstChild));e.replaceChild(i,t),this.ie?this.normalizeTextNode(e):e.normalize()}normalizeTextNode(t){if(t){if(3===t.nodeType)for(;t.nextSibling&&3===t.nextSibling.nodeType;)t.nodeValue+=t.nextSibling.nodeValue,t.parentNode.removeChild(t.nextSibling);else this.normalizeTextNode(t.firstChild);this.normalizeTextNode(t.nextSibling)}}markRegExp(t,e){this.opt=e,this.log(`Searching with expression "${t}"`);let i=0,n="wrapMatches";this.opt.acrossElements&&(n="wrapMatchesAcrossElements"),this[n](t,this.opt.ignoreGroups,((t,e)=>this.opt.filter(e,t,i)),(t=>{i++,this.opt.each(t)}),(()=>{0===i&&this.opt.noMatch(t),this.opt.done(i)}))}mark(t,e){this.opt=e;let i=0,n="wrapMatches";const{keywords:s,length:o}=this.getSeparatedKeywords("string"==typeof t?[t]:t),r=this.opt.caseSensitive?"":"i",a=t=>{let e=new RegExp(this.createRegExp(t),`gm${r}`),l=0;this.log(`Searching with expression "${e}"`),this[n](e,1,((e,n)=>this.opt.filter(n,t,i,l)),(t=>{l++,i++,this.opt.each(t)}),(()=>{0===l&&this.opt.noMatch(t),s[o-1]===t?this.opt.done(i):a(s[s.indexOf(t)+1])}))};this.opt.acrossElements&&(n="wrapMatchesAcrossElements"),0===o?this.opt.done(i):a(s[0])}markRanges(t,e){this.opt=e;let i=0,n=this.checkRanges(t);n&&n.length?(this.log("Starting to mark with the following ranges: "+JSON.stringify(n)),this.wrapRangeFromIndex(n,((t,e,i,n)=>this.opt.filter(t,e,i,n)),((t,e)=>{i++,this.opt.each(t,e)}),(()=>{this.opt.done(i)}))):this.opt.done(i)}unmark(t){this.opt=t;let e=this.opt.element?this.opt.element:"*";e+="[data-markjs]",this.opt.className&&(e+=`.${this.opt.className}`),this.log(`Removal selector "${e}"`),this.iterator.forEachNode(NodeFilter.SHOW_ELEMENT,(t=>{this.unwrapMatches(t)}),(t=>{const i=n.matches(t,e),s=this.matchesExclude(t);return!i||s?NodeFilter.FILTER_REJECT:NodeFilter.FILTER_ACCEPT}),this.opt.done)}}function o(t){const e=new s(t);return this.mark=(t,i)=>(e.mark(t,i),this),this.markRegExp=(t,i)=>(e.markRegExp(t,i),this),this.markRanges=(t,i)=>(e.markRanges(t,i),this),this.unmark=t=>(e.unmark(t),this),this}var r=i(766),a=i.n(r);function l(t){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},l(t)}function c(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,n)}return i}function d(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?c(Object(i),!0).forEach((function(e){u(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):c(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}function u(t,e,i){return(e=p(e))in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function h(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,p(n.key),n)}}function p(t){var e=function(t,e){if("object"!=l(t)||!t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var n=i.call(t,e||"default");if("object"!=l(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==l(e)?e:e+""}function f(t,e){(function(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")})(t,e),e.add(t)}function g(t,e,i){if("function"==typeof t?t===e:t.has(e))return arguments.length<3?e:i;throw new TypeError("Private element is not present on this object")}var m=new WeakSet,v=function(){return t=function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),f(this,m)},(e=[{key:"create",value:function(t){if(!t.classList.contains("tomselected")){var e=t.getAttribute("data-ea-autocomplete-endpoint-url");return null!==e?g(m,this,w).call(this,t,e):"true"===t.getAttribute("data-ea-autocomplete-render-items-as-html")?g(m,this,y).call(this,t):g(m,this,b).call(this,t)}}}])&&h(t.prototype,e),i&&h(t,i),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e,i}();function _(t){var e={render:{no_results:function(e,i){return'<div class="no-results">'.concat(t.getAttribute("data-ea-i18n-no-results-found"),"</div>")}},plugins:{dropdown_input:{}}};return null===t.getAttribute("required")&&null===t.getAttribute("disabled")&&(e.plugins.clear_button={title:""}),null!==t.getAttribute("multiple")&&(e.plugins.remove_button={title:""}),null!==t.getAttribute("data-ea-autocomplete-endpoint-url")&&(e.plugins.virtual_scroll={}),"true"===t.getAttribute("data-ea-autocomplete-allow-item-create")&&(e.create=!0),e}function b(t){var e=g(m,this,A).call(this,g(m,this,_).call(this,t),{maxOptions:null});return new(a())(t,e)}function y(t){for(var e=[],i=0;i<t.options.length;i++){var n=t.options[i].text,s=t.options[i].value;e.push({label_text:g(m,this,O).call(this,n),label_raw:n,value:s})}var o=g(m,this,A).call(this,g(m,this,_).call(this,t),{valueField:"value",labelField:"label_raw",searchField:["label_text"],options:e,maxOptions:null,render:{item:function(t,e){return"<div>".concat(t.label_raw,"</div>")},option:function(t,e){return"<div>".concat(t.label_raw,"</div>")}}});return new(a())(t,o)}function w(t,e){var i="true"===t.getAttribute("data-ea-autocomplete-render-items-as-html"),n=g(m,this,A).call(this,g(m,this,_).call(this,t),{valueField:"entityId",labelField:"entityAsString",searchField:["entityAsString"],firstUrl:function(t){return e+"&query="+encodeURIComponent(t)},load:function(t,e){var i=this,n=this.getUrl(t);fetch(n).then((function(t){return t.json()})).then((function(n){i.setNextUrl(t,n.next_page),e(n.results)})).catch((function(){return e()}))},preload:"focus",maxOptions:null,score:function(t){return function(t){return 1}},render:{option:function(t,e){return"<div>".concat(i?t.entityAsString:e(t.entityAsString),"</div>")},item:function(t,e){return"<div>".concat(i?t.entityAsString:e(t.entityAsString),"</div>")},loading_more:function(e,i){return'<div class="loading-more-results">'.concat(t.getAttribute("data-ea-i18n-loading-more-results"),"</div>")},no_more_results:function(e,i){return'<div class="no-more-results">'.concat(t.getAttribute("data-ea-i18n-no-more-results"),"</div>")},no_results:function(e,i){return'<div class="no-results">'.concat(t.getAttribute("data-ea-i18n-no-results-found"),"</div>")}}});return new(a())(t,n)}function O(t){return t.replace(/(<([^>]+)>)/gi,"")}function A(t,e){return d(d({},t),e)}function E(t,e){e?(t.classList.remove("d-block"),t.classList.add("d-none")):(t.classList.remove("d-none"),t.classList.add("d-block"))}function x(t){return x="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},x(t)}function S(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,k(n.key),n)}}function C(t,e,i){return e&&S(t.prototype,e),i&&S(t,i),Object.defineProperty(t,"prototype",{writable:!1}),t}function k(t){var e=function(t,e){if("object"!=x(t)||!t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var n=i.call(t,e||"default");if("object"!=x(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==x(e)?e:e+""}function I(t,e,i){T(t,e),e.set(t,i)}function T(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}function L(t,e){return t.get($(t,e))}function N(t,e,i){return t.set($(t,e),i),i}function $(t,e,i){if("function"==typeof t?t===e:t.has(e))return arguments.length<3?e:i;throw new TypeError("Private element is not present on this object")}i(538),window.bootstrap=e(),document.addEventListener("DOMContentLoaded",(function(){window.EasyAdminApp=new F}));var P=new WeakMap,D=new WeakMap,j=new WeakSet,F=C((function t(){var e,i,n=this;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),T(e=this,i=j),i.add(e),I(this,P,void 0),I(this,D,void 0),N(P,this,"ea/sidebar/width"),N(D,this,"ea/content/width"),$(j,this,M).call(this),$(j,this,q).call(this),$(j,this,R).call(this),$(j,this,H).call(this),$(j,this,z).call(this),$(j,this,B).call(this),$(j,this,W).call(this),$(j,this,V).call(this),$(j,this,K).call(this),$(j,this,U).call(this),$(j,this,Q).call(this),document.addEventListener("ea.collection.item-added",(function(){return $(j,n,W).call(n)}))}));function M(){if(window.location.href.includes("#")&&document.querySelector("body").classList.contains("ea-index")){var t=window.location.href.split("#")[0];window.history.replaceState({},"",t)}}function q(){var t=document.querySelectorAll("#main-menu .menu-item.has-submenu");t.forEach((function(e){var i=e.querySelector(".submenu");null!==i&&(e.classList.contains("expanded")&&(i.style.maxHeight=i.scrollHeight+"px"),e.querySelector(".submenu-toggle").addEventListener("click",(function(n){n.preventDefault(),t.forEach((function(t){if(e!==t){var i=t.querySelector(".submenu");t.classList.contains("expanded")&&(i.style.maxHeight="0px",t.classList.remove("expanded"))}})),e.classList.contains("expanded")?(i.style.maxHeight="0px",e.classList.remove("expanded")):(i.style.maxHeight=i.scrollHeight+"px",e.classList.add("expanded"))})))}))}function R(){var t=this,e=document.querySelector("#sidebar-resizer-handler");null!==e&&e.addEventListener("click",(function(){var e=localStorage.getItem(L(P,t))||"normal",i="normal"===e?"compact":"normal";document.querySelector("body").classList.remove("ea-sidebar-width-".concat(e)),document.querySelector("body").classList.add("ea-sidebar-width-".concat(i)),localStorage.setItem(L(P,t),i)}));var i=document.querySelector("#content-resizer-handler");null!==i&&i.addEventListener("click",(function(){var e=localStorage.getItem(L(D,t))||"normal",i="normal"===e?"full":"normal";document.querySelector("body").classList.remove("ea-content-width-".concat(e)),document.querySelector("body").classList.add("ea-content-width-".concat(i)),localStorage.setItem(L(D,t),i)}))}function H(){var t,e=document.querySelector("#navigation-toggler"),i="ea-mobile-sidebar-visible";null!==e&&e.addEventListener("click",(function(){document.querySelector("body").classList.toggle(i),document.querySelector("body").classList.contains(i)?((t=document.createElement("div")).classList.add("modal-backdrop","fade","show"),t.onclick=function(){document.querySelector("body").classList.remove(i),document.body.removeChild(t),t=null},document.body.appendChild(t)):t&&(document.body.removeChild(t),t=null)}))}function z(){var t=document.querySelector('.form-action-search [name="query"]');if(null!==t&&""!==t.value.trim()){var e=function(t){for(var e,i=/"([^"\\]*(\\.[^"\\]*)*)"|\S+/g,n=[];null!==(e=i.exec(t));)n.push(e[0].replaceAll('"',"").trim());return n}(t.value),i=new RegExp(e.map((function(t){return t.replace(/[|\\{}()[\]^$+*?.-]/g,"\\$&")})).join("|"),"i");new o(document.querySelectorAll("table tbody td.searchable")).markRegExp(i)}}function B(){var t=this,e=document.querySelector(".datagrid-filters .action-filters-button");if(null!==e){var i=document.querySelector(e.getAttribute("data-bs-target"));e.setAttribute("href",e.getAttribute("data-href")),e.removeAttribute("data-href"),e.classList.remove("disabled"),e.addEventListener("click",(function(n){var s=i.querySelector(".modal-body");s.innerHTML='<div class="fa-3x px-3 py-3 text-muted text-center"><i class="fas fa-circle-notch fa-spin"></i></div>',fetch(e.getAttribute("href")).then((function(t){return t.text()})).then((function(e){s.innerHTML=e,$(j,t,W).call(t),$(j,t,J).call(t)})).catch((function(t){console.error(t)})),n.preventDefault()}));var n=function(t){t.closest("form").querySelectorAll('input[name^="filters['.concat(t.dataset.filterProperty,']"]')).forEach((function(t){t.remove()})),t.remove()};document.querySelector("#modal-clear-button").addEventListener("click",(function(){i.querySelectorAll(".filter-field").forEach((function(t){n(t)})),i.querySelector("form").submit()})),document.querySelector("#modal-apply-button").addEventListener("click",(function(){i.querySelectorAll(".filter-checkbox:not(:checked)").forEach((function(t){n(t.closest(".filter-field"))})),i.querySelector("form").submit()}))}}function V(){var t=null,e=document.querySelector(".form-batch-checkbox-all");if(null!==e){var i=document.querySelectorAll('input[type="checkbox"].form-batch-checkbox');e.addEventListener("change",(function(){i.forEach((function(t){t.checked=e.checked,t.dispatchEvent(new Event("change"))}))}));var n=document.querySelector(".deselect-batch-button");null!==n&&n.addEventListener("click",(function(){e.checked=!1,e.dispatchEvent(new Event("change"))})),i.forEach((function(n,s){n.dataset.rowIndex=s,n.addEventListener("click",(function(e){if(t&&e.shiftKey){var n=parseInt(t.dataset.rowIndex),s=parseInt(e.target.dataset.rowIndex),o=e.target.checked,r=Math.min(n,s),a=Math.max(n,s);i.forEach((function(t,e){r<=e&&e<=a&&(t.checked=o,t.dispatchEvent(new Event("change")))}))}t=e.target})),n.addEventListener("change",(function(){var t=document.querySelectorAll('input[type="checkbox"].form-batch-checkbox:checked'),i=n.closest("tr"),s=n.closest(".content");n.checked?i.classList.add("selected-row"):(i.classList.remove("selected-row"),e.checked=!1);var o=0!==t.length,r=document.querySelector(".content-header-title > .title"),a=s.querySelector(".datagrid-filters"),l=s.querySelector(".global-actions"),c=s.querySelector(".batch-actions");null!==r&&E(r,o),null!==a&&E(a,o),null!==l&&E(l,o),null!==c&&E(c,!o)}))}));var s=document.querySelector("#batch-action-confirmation-title"),o=s.textContent;document.querySelectorAll("[data-action-batch]").forEach((function(t){t.addEventListener("click",(function(t){t.preventDefault();var e=t.currentTarget,i=e.textContent.trim()||e.getAttribute("title"),n=document.querySelectorAll('input[type="checkbox"].form-batch-checkbox:checked');s.textContent=o.replace("%action_name%",i).replace("%num_items%",n.length.toString()),document.querySelector("#modal-batch-action-button").addEventListener("click",(function(){e.setAttribute("disabled","disabled");var t={batchActionName:e.getAttribute("data-action-name"),entityFqcn:e.getAttribute("data-entity-fqcn"),batchActionUrl:e.getAttribute("data-action-url"),batchActionCsrfToken:e.getAttribute("data-action-csrf-token")};n.forEach((function(e,i){t["batchActionEntityIds[".concat(i,"]")]=e.value}));var i=document.createElement("form");for(var s in i.setAttribute("method","POST"),i.setAttribute("action",e.getAttribute("data-action-url")),t){var o=document.createElement("input");o.setAttribute("type","hidden"),o.setAttribute("name",s),o.setAttribute("value",t[s]),i.appendChild(o)}document.body.appendChild(i),i.submit()}))}))}))}}function W(){var t=new v;document.querySelectorAll('[data-ea-widget="ea-autocomplete"]').forEach((function(e){t.create(e)}))}function K(){document.querySelectorAll(".action-delete").forEach((function(t){t.addEventListener("click",(function(e){e.preventDefault(),document.querySelector("#modal-delete-button").addEventListener("click",(function(){var e=t.getAttribute("formaction"),i=document.querySelector("#delete-form");i.setAttribute("action",e),i.submit()}))}))}))}function U(){document.querySelectorAll('[data-bs-toggle="popover"]').forEach((function(t){new(e().Popover)(t)}))}function Q(){document.querySelectorAll('[data-bs-toggle="tooltip"]').forEach((function(t){new(e().Tooltip)(t)}))}function J(){document.querySelectorAll(".filter-checkbox").forEach((function(t){t.addEventListener("change",(function(){var e=t.nextElementSibling,i=t.nextElementSibling.getAttribute("aria-expanded");(t.checked&&"false"===i||!t.checked&&"true"===i)&&e.click()}))})),document.querySelectorAll("form[data-ea-filters-form-id]").forEach((function(t){t.addEventListener("change",(function(t){if(!t.target.classList.contains("filter-checkbox")){var e=t.target.closest(".filter-field").querySelector(".filter-checkbox");e.checked||(e.checked=!0)}}))})),document.querySelectorAll("[data-ea-comparison-id]").forEach((function(t){t.addEventListener("change",(function(t){var e=t.currentTarget,i=e.dataset.eaComparisonId;if(void 0!==i){var n=document.querySelector('[data-ea-value2-of-comparison-id="'.concat(i,'"]'));null!==n&&E(n,"between"!==e.value)}}))}))}})()})();