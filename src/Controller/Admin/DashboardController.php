<?php

namespace App\Controller\Admin;

use App\Entity\Praticien\AgentUfs;
use App\Entity\Structure\Service;
use App\Entity\Structure\EntiteJuridique;
use App\Entity\Structure\Pole;
use App\Entity\Structure\Cr;
use App\Entity\Structure\Tenant;
use App\Entity\Activite\Actes;
use App\Entity\Activite\GardesAstreintes;
use App\Entity\Activite\Liberal;
use App\Entity\Activite\Sigaps;
use App\Entity\Praticien\Agent;
use App\Entity\Praticien\AgentHopitalRole;
use App\Entity\Structure\Ufs;
use App\Entity\Importation\Importation;
use App\Domain\Enum\AgentRoleType;
use Symfony\Component\HttpFoundation\Response;
use EasyCorp\Bundle\EasyAdminBundle\Config\Dashboard;
use EasyCorp\Bundle\EasyAdminBundle\Config\MenuItem;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractDashboardController;
use Symfony\Component\Routing\Attribute\Route;
use Doctrine\ORM\Query\Expr\Join;

class DashboardController extends AbstractDashboardController
{
    public function __construct(
        private EntityManagerInterface $entityManager
    ) {
    }

    #[Route('/manager/dtnib/admin/tutorial/business', name: 'admin_tutorial_business')]
    public function tutorialBusiness(): Response
    {
        // Récupérer les informations de l'utilisateur connecté
        $user = $this->getUser();
        $userName = $user ? $user->getUserIdentifier() : 'Administrateur';
        
        return $this->render('admin/tutorial_business.html.twig', [
            'user_name' => $userName
        ]);
    }
    
    #[Route('/manager/dtnib/admin/tutorial/developer', name: 'admin_tutorial_developer')]
    public function tutorialDeveloper(): Response
    {
        // Récupérer les informations de l'utilisateur connecté
        $user = $this->getUser();
        $userName = $user ? $user->getUserIdentifier() : 'Administrateur';
        
        return $this->render('admin/tutorial_developer.html.twig', [
            'user_name' => $userName
        ]);
    }
    
    #[Route('/manager/dtnib/admin/tutorial', name: 'admin_tutorial')]
    public function tutorial(): Response
    {
        // Rediriger vers le guide métier par défaut
        return $this->redirectToRoute('admin_tutorial_business');
    }

    #[Route('/manager/dtnib/admin', name: 'admin')]
    public function index(): Response
    {
        // Récupérer tous les tenants
        $tenants = $this->entityManager->getRepository(Tenant::class)->findAll();
        
        // Statistiques générales
        $stats = [
            'total_tenants' => count($tenants),
            'total_hopitaux' => $this->entityManager->getRepository(EntiteJuridique::class)->count([]),
            'total_agents' => $this->countActiveAgents(),
            'total_pole_actifs' => $this->countActivePoles(),
        ];
        
        // Statistiques par tenant
        $tenant_stats = [];
        foreach ($tenants as $tenant) {
            $tenant_stats[] = [
                'nom' => $tenant->getNom(),
                'hopitaux' => $this->countByTenant($tenant, EntiteJuridique::class),
                'poles' => $this->countPolesByTenant($tenant),
                'services' => $this->countServicesByTenant($tenant),
                'agents' => $this->countAgentsByTenant($tenant),
            ];
        }
        
        // Statistiques d'activité
        $activity_stats = [
            'actes' => $this->entityManager->getRepository(Actes::class)->count([]),
            'gardes' => $this->entityManager->getRepository(GardesAstreintes::class)->count([]),
            'liberal' => $this->entityManager->getRepository(Liberal::class)->count([]),
            'sigaps' => $this->entityManager->getRepository(Sigaps::class)->count([]),
        ];
        
        // Statistiques de rôle
        $role_stats = $this->getRoleStatistics();
        
        // Statistiques de connexion par hôpital
        $connection_stats = $this->getConnectionStatsByHopital();
        
        // Top 5 des utilisateurs les plus connectés
        $top_connected_users = $this->getTopConnectedUsers();
        
        // Statistiques d'importation
        $import_stats = [
            'last_import_date' => $this->getLastImportDate(),
            'imports_this_week' => $this->getImportsThisWeek(),
            'imports_this_month' => $this->getImportsThisMonth(),
        ];
        
        // Rendre le template avec toutes les données
        return $this->render('admin/dashboard.html.twig', [
            'stats' => $stats,
            'tenant_stats' => $tenant_stats,
            'activity_stats' => $activity_stats,
            'role_stats' => $role_stats,
            'connection_stats' => $connection_stats,
            'top_connected_users' => $top_connected_users,
            'import_stats' => $import_stats,
        ]);
    }

    public function configureDashboard(): Dashboard
    {
        return Dashboard::new()
            ->setTitle('
            <div style="display: flex; align-items: center; gap: 8px;">
                <div style="background-color: white; padding: 3px; border-radius: 4px; display: flex; align-items: center; justify-content: center; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                    <img src="/assets/images/chru-nancy-logo.png" 
                         height="18" 
                         width="18" 
                         style="filter: contrast(1.2) brightness(1.1); object-fit: contain;" 
                         alt="CHRU Logo">
                </div>
                <div style="display: flex; flex-direction: column; line-height: 1;">
                    <span style="font-size: 1.1rem; font-weight: 600; color: #2563eb;">GHT Supra Admin</span>
                    <span style="font-size: 0.7rem; color: #64748b; margin-top: 1px;">1 Tenant pour plusieurs EJ</span>
                </div>
            </div>
        ');

    }

    public function configureMenuItems(): iterable
    {
        // 🏠 Dashboard
        yield MenuItem::linkToDashboard('Dashboard', 'fa fa-home');
        
        // 📚 Guide
        yield MenuItem::section('Guide');
        yield MenuItem::linkToRoute('Guide Métier', 'fas fa-briefcase', 'admin_tutorial_business');
        yield MenuItem::linkToRoute('Guide Intégrateur', 'fas fa-cogs', 'admin_tutorial_developer');

        // 🏗️ Structure
        yield MenuItem::section('Structure');
        yield MenuItem::linkToCrud('Tenants', 'fas fa-building', Tenant::class);
        yield MenuItem::linkToCrud('Entité Juridique', 'fas fa-hospital', EntiteJuridique::class);
        yield MenuItem::linkToCrud('Pôles', 'fas fa-project-diagram', Pole::class);
        yield MenuItem::linkToCrud('Services', 'fas fa-clinic-medical', Service::class);
        yield MenuItem::linkToCrud('Centres de Responsabilité', 'fas fa-layer-group', Cr::class);
        yield MenuItem::linkToCrud('Ufs', 'fas fa-network-wired', Ufs::class);

        // 👥 Personnel
        yield MenuItem::section('Personnel');
        yield MenuItem::linkToCrud('Agents', 'fas fa-user', Agent::class);
        yield MenuItem::linkToCrud('Affectations UF', 'fas fa-users-between-lines', AgentUfs::class); // Nouvelle ligne
        yield MenuItem::linkToCrud('Affectations Rôle', 'fas fa-users-cog', AgentHopitalRole::class);

        // 📊 Activités
        yield MenuItem::section('Activités');
        yield MenuItem::linkToCrud('Actes', 'fas fa-file-medical', Actes::class);
        yield MenuItem::linkToCrud('Gardes & Astreintes', 'fas fa-clock', GardesAstreintes::class);
        yield MenuItem::linkToCrud('Liberal', 'fas fa-hand-holding-usd', Liberal::class);
        yield MenuItem::linkToCrud('Sigaps', 'fas fa-chart-line', Sigaps::class);
        
        // ⚙️ Système
        yield MenuItem::section('Système');
        yield MenuItem::linkToCrud('Historique des importations', 'fas fa-sync', Importation::class);
    }

    private function countByTenant(Tenant $tenant, string $entityClass): int
    {
        return $this->entityManager->getRepository($entityClass)->count(['tenant' => $tenant]);
    }

    private function countPolesByTenant(Tenant $tenant): int
    {
        $qb = $this->entityManager->createQueryBuilder();
        return $qb->select('COUNT(p.id)')
            ->from(Pole::class, 'p')
            ->join('p.hopital', 'h')
            ->where('h.tenant = :tenant')
            ->setParameter('tenant', $tenant)
            ->getQuery()
            ->getSingleScalarResult();
    }

    private function countServicesByTenant(Tenant $tenant): int
    {
        $qb = $this->entityManager->createQueryBuilder();
        return $qb->select('COUNT(s.id)')
            ->from(Service::class, 's')
            ->join('s.hopital', 'h')
            ->where('h.tenant = :tenant')
            ->setParameter('tenant', $tenant)
            ->getQuery()
            ->getSingleScalarResult();
    }

    private function countAgentsByTenant(Tenant $tenant): int
    {
        $qb = $this->entityManager->createQueryBuilder();
        return $qb->select('COUNT(DISTINCT a.id)')
            ->from(Agent::class, 'a')
            ->join('a.hopital', 'h')
            ->where('h.tenant = :tenant')
            ->setParameter('tenant', $tenant)
            ->getQuery()
            ->getSingleScalarResult();
    }
    
    private function countActivePoles(): int
    {
        $qb = $this->entityManager->createQueryBuilder();
        return $qb->select('COUNT(p.id)')
            ->from(Pole::class, 'p')
            ->where('p.datfin < :today')
            ->setParameter('today', new \DateTime())
            ->getQuery()
            ->getSingleScalarResult();
    }
    
    private function countActiveAgents(): int
    {
        $qb = $this->entityManager->createQueryBuilder();
        return $qb->select('COUNT(a.id)')
            ->from(Agent::class, 'a')
            ->where('a.dateDepart IS NULL OR a.dateDepart > :today')
            ->setParameter('today', new \DateTime())
            ->getQuery()
            ->getSingleScalarResult();
    }

    private function getRoleStatistics(): array
    {
        $qb = $this->entityManager->createQueryBuilder();
        $results = $qb->select('ahr.role, COUNT(ahr.id) as count')
            ->from(AgentHopitalRole::class, 'ahr')
            ->groupBy('ahr.role')
            ->orderBy('count', 'DESC')
            ->getQuery()
            ->getResult();

        $roleStats = array_map(function($result) {
            $role = $result['role'];
            $level = AgentRoleType::getRoleLevel($role);

            return [
                'role' => $role,
                'count' => (int) $result['count'],
                'level' => $level,
                'hierarchy_badge' => $this->getRoleHierarchyBadge($level),
                'role_description' => $this->getRoleDescription($role)
            ];
        }, $results);

        // Trier par niveau hiérarchique (décroissant) pour avoir les rôles les plus élevés en premier
        usort($roleStats, function($a, $b) {
            return $b['level'] <=> $a['level'];
        });

        return $roleStats;
    }

    private function getRoleHierarchyBadge(int $level): string
    {
        return match($level) {
            5 => '5', // FIFAP/CME - Niveau maximum
            4 => '4', // MANAGER
            3 => '3', // CHEF_DE_POLE
            2 => '2', // ADMIN
            1 => '1', // USER
            default => '0' // Rôle inconnu
        };
    }

    private function getRoleDescription(string $role): string
    {
        return match($role) {
            AgentRoleType::FIFAP => 'Directeur FIFAP',
            AgentRoleType::CME => 'Directeur CME',
            AgentRoleType::MANAGER => 'Manager',
            AgentRoleType::CHEF_DE_POLE => 'Chef de Pôle',
            AgentRoleType::ADMIN => 'Administrateur',
            AgentRoleType::USER => 'Utilisateur',
            default => 'Rôle personnalisé'
        };
    }

    private function getConnectionStatsByHopital(): array
    {
        // Récupérer toutes les entités juridiques avec leurs agents
        $qb = $this->entityManager->createQueryBuilder();
        $results = $qb->select('ej.nom as hopital_nom, ej.code as hopital_code')
            ->addSelect('COUNT(a.id) as total_agents')
            ->from(EntiteJuridique::class, 'ej')
            ->leftJoin('ej.users', 'a')
            ->groupBy('ej.id, ej.nom, ej.code')
            ->orderBy('ej.nom', 'ASC')
            ->getQuery()
            ->getResult();

        // Pour chaque hôpital, calculer les statistiques de connexion
        $connectionStats = [];
        foreach ($results as $result) {
            $hopitalNom = $result['hopital_nom'];
            $hopitalCode = $result['hopital_code'];
            $totalAgents = (int) $result['total_agents'];

            // Récupérer les agents de cet hôpital avec leurs stats de connexion
            $agentsQb = $this->entityManager->createQueryBuilder();
            $agents = $agentsQb->select('a')
                ->from(Agent::class, 'a')
                ->join('a.hopital', 'h')
                ->where('h.nom = :hopital_nom')
                ->setParameter('hopital_nom', $hopitalNom)
                ->getQuery()
                ->getResult();

            $totalConnections = 0;
            $agentsConnected = 0;

            // Parcourir chaque agent pour récupérer ses statistiques de connexion
            foreach ($agents as $agent) {
                $agentConnectionStats = $agent->getConnectionStats();
                if ($agentConnectionStats && isset($agentConnectionStats['total_connections'])) {
                    $totalConnections += (int) $agentConnectionStats['total_connections'];

                    // Si l'agent a une dernière connexion, il est considéré comme "connecté"
                    if (isset($agentConnectionStats['last_connection']) && $agentConnectionStats['last_connection']) {
                        $agentsConnected++;
                    }
                }
            }

            $avgConnections = $totalAgents > 0 ? round($totalConnections / $totalAgents, 1) : 0;

            $connectionStats[] = [
                'hopital_nom' => $hopitalNom,
                'hopital_code' => $hopitalCode,
                'total_agents' => $totalAgents,
                'total_connections' => $totalConnections,
                'agents_connected' => $agentsConnected,
                'avg_connections' => $avgConnections
            ];
        }

        // Trier par nombre total de connexions (décroissant)
        usort($connectionStats, function($a, $b) {
            // Vérification de sécurité pour éviter les erreurs sur null
            if (!is_array($a) || !is_array($b)) {
                return 0;
            }
            return $b['total_connections'] <=> $a['total_connections'];
        });

        return $connectionStats;
    }

    private function getTopConnectedUsers(): array
    {
        // Récupérer tous les agents avec leurs statistiques de connexion
        $agents = $this->entityManager->getRepository(Agent::class)->findAll();

        $userStats = [];

        foreach ($agents as $agent) {
            $connectionStats = $agent->getConnectionStats();

            // Vérifier que l'agent a des statistiques de connexion
            if ($connectionStats && isset($connectionStats['total_connections']) && $connectionStats['total_connections'] > 0) {
                $userStats[] = [
                    'agent_nom' => $agent->getNom() ?? 'N/A',
                    'agent_prenom' => $agent->getPrenom() ?? 'N/A',
                    'agent_email' => $agent->getEmail() ?? 'N/A',
                    'hopital_nom' => $agent->getHopital() ? $agent->getHopital()->getNom() : 'N/A',
                    'hopital_code' => $agent->getHopital() ? $agent->getHopital()->getCode() : 'N/A',
                    'total_connections' => (int) $connectionStats['total_connections'],
                    'last_connection' => $connectionStats['last_connection'] ?? null,
                    'first_connection' => $connectionStats['first_connection'] ?? null
                ];
            }
        }

        // Trier par nombre total de connexions (décroissant) et prendre les 5 premiers
        usort($userStats, function($a, $b) {
            return $b['total_connections'] <=> $a['total_connections'];
        });

        return array_slice($userStats, 0, 5);
    }
    
    /**
     * Récupère la date de la dernière importation
     * 
     * @return \DateTime|null La date de la dernière importation ou null si aucune importation n'existe
     */
    private function getLastImportDate(): ?\DateTime
    {
        try {
            $qb = $this->entityManager->createQueryBuilder();
            $result = $qb->select('i.dateImportation')
                ->from(Importation::class, 'i')
                ->orderBy('i.dateImportation', 'DESC')
                ->setMaxResults(1)
                ->getQuery()
                ->getOneOrNullResult();
                
            return $result['dateImportation'] ?? new \DateTime();
        } catch (\Exception $e) {
            // En cas d'erreur, retourner la date actuelle
            return new \DateTime();
        }
    }
    
    /**
     * Compte le nombre d'importations effectuées cette semaine
     * 
     * @return int Le nombre d'importations cette semaine
     */
    private function getImportsThisWeek(): int
    {
        try {
            $startOfWeek = new \DateTime('monday this week');
            $startOfWeek->setTime(0, 0, 0);
            
            $endOfWeek = new \DateTime('sunday this week');
            $endOfWeek->setTime(23, 59, 59);
            
            $qb = $this->entityManager->createQueryBuilder();
            return $qb->select('COUNT(i.id)')
                ->from(Importation::class, 'i')
                ->where('i.dateImportation BETWEEN :start AND :end')
                ->setParameter('start', $startOfWeek)
                ->setParameter('end', $endOfWeek)
                ->getQuery()
                ->getSingleScalarResult();
        } catch (\Exception $e) {
            // En cas d'erreur, retourner 0
            return 0;
        }
    }
    
    /**
     * Compte le nombre d'importations effectuées ce mois
     * 
     * @return int Le nombre d'importations ce mois
     */
    private function getImportsThisMonth(): int
    {
        try {
            $startOfMonth = new \DateTime('first day of this month');
            $startOfMonth->setTime(0, 0, 0);
            
            $endOfMonth = new \DateTime('last day of this month');
            $endOfMonth->setTime(23, 59, 59);
            
            $qb = $this->entityManager->createQueryBuilder();
            return $qb->select('COUNT(i.id)')
                ->from(Importation::class, 'i')
                ->where('i.dateImportation BETWEEN :start AND :end')
                ->setParameter('start', $startOfMonth)
                ->setParameter('end', $endOfMonth)
                ->getQuery()
                ->getSingleScalarResult();
        } catch (\Exception $e) {
            // En cas d'erreur, retourner 0
            return 0;
        }
    }
}
