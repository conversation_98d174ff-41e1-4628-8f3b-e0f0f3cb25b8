<?php

namespace App\Mapper\GaredeAstreinte;

use App\ApiResource\Activite\GardesAstreintesDto;
use App\ApiResource\Praticien\AgentDto;
use App\Entity\Activite\GardesAstreintes;
use App\Repository\AgentUfsRepository;
use App\Repository\CrRepository;
use Symfonycasts\MicroMapper\AsMapper;
use Symfonycasts\MicroMapper\MapperInterface;
use Symfonycasts\MicroMapper\MicroMapperInterface;

#[AsMapper(from: GardesAstreintes::class, to: GardesAstreintesDto::class)]
class GardesAstreintesEntityToDtoMapper implements MapperInterface
{
    public function __construct(
        private MicroMapperInterface $microMapper,
        private AgentUfsRepository $agentUfsRepository,
        private CrRepository $crRepository
    ) {}

    public function load(object $from, string $toClass, array $context): object
    {
        $garde = $from;
        assert($garde instanceof GardesAstreintes);

        $dto = new GardesAstreintesDto();
        $dto->id = $garde->getId(); // On charge uniquement l'ID ici

        return $dto;
    }

    public function populate(object $from, object $to, array $context): object
    {
        $garde = $from;
        $dto = $to;
        assert($garde instanceof GardesAstreintes);
        assert($dto instanceof GardesAstreintesDto);

        $dto->dateDebut = $garde->getDateDebut();
        $dto->dateFin = $garde->getDateFin();
        $dto->totalGarde = $garde->getTotalGarde();
        $dto->totalAstreinte = $garde->getTotalAstreinte();
        $dto->dateGarde = $garde->getDateGarde();
        $dto->typeGarde = $garde->getTypeGarde();

        // Champs du trait HorodatageTrait
        $dto->validFrom = $garde->getValidFrom();
        $dto->validTo = $garde->getValidTo();
        $dto->periodeType = $garde->getPeriodeType();
        $dto->source = $garde->getSource();

        // Informations de l'agent
        $agent = $garde->getAgent();
        $dto->agentHrU = $agent?->getHrUser();
        
        // Récupération de la dernière affectation de l'agent
        if ($dto->agentHrU) {
            $lastAffectation = $this->agentUfsRepository->findLastAffectationByHrUser($dto->agentHrU);
            if ($lastAffectation && $lastAffectation->getUfs()) {
                $ufs = $lastAffectation->getUfs();
                $ufCode = $ufs->getUfcode();
                $ufLibelle = $ufs->getLibelle();
                
                if ($ufCode && $ufLibelle) {
                    $dto->derniereAffectionDeAgent = $ufCode . ' - ' . $ufLibelle;
                } elseif ($ufCode) {
                    $dto->derniereAffectionDeAgent = $ufCode;
                } elseif ($ufLibelle) {
                    $dto->derniereAffectionDeAgent = $ufLibelle;
                }
            }
        }
        
        // Récupération du nom, prénom et titre de l'agent
        if ($agent) {
            $titre = $agent->getTitre();
            $prenom = $agent->getPrenom();
            $nom = $agent->getNom();
            
            $nomPrenomTitre = '';
            if ($titre) {
                $nomPrenomTitre .= $titre . ' ';
            }
            if ($prenom) {
                $nomPrenomTitre .= $prenom . ' ';
            }
            if ($nom) {
                $nomPrenomTitre .= $nom;
            }
            
            $dto->agentNomPrenomTitre = trim($nomPrenomTitre) ?: null;
        }

        return $dto;
    }
}
