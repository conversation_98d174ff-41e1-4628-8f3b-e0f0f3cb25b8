<?php
namespace App\Entity\Structure\Trait;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

/**
 * Trait pour la gestion des paramètres généraux d'une EntiteJuridique
 *
 * Ce trait fournit des valeurs par défaut pour tous les paramètres lors de la création.
 * Ces valeurs doivent être personnalisées via l'interface d'administration (/admin)
 * après la création de l'EntiteJuridique.
 *
 * Les chemins, URLs et autres configurations présents ici sont des exemples de valeurs par défaut
 * vous pouvez ne pas leur toucher.
 */
trait ParametresTrait
{
    #[ORM\Column(type: Types::JSON, nullable: true)]
    private ?array $parametre = [
        "addresses" => [
            "affectationAgentFilesPaths" => "/mnt/bo/files/agents",
            "chargementAutomatiqueDesAffectationEstActif" => false
        ],
        "notifications" => [
            "adminEmail" => "<EMAIL>",
            "damResponssableDataAffectionEmail" => "<EMAIL>",
            "notifierErreurAuResponssableDamAffectation" => false,
            "notifierErreurAuAdminPourAffectation" => false,
            "NotifierAdminDesErreurProduite" => false
        ],
        "apiEndpoints" => [
            "dataIntegratorApiInfo" => "https://data-integrator.chru-nancy.fr/api/nancy/collecteur",
            "backendUrlInfo" => "https://supra-backend.chru-nancy.fr/api/nancy"
        ]
    ];

    public function getParametre(): ?array
    {
        return $this->parametre;
    }

    public function setParametre(array $parametre): void
    {
        $this->parametre = array_merge($this->parametre ?? [], $parametre);
    }

    // Addresses getters and setters
    public function getAffectationAgentFilesPaths(): string
    {
        return $this->parametre['addresses']['affectationAgentFilesPaths'] ?? "/mnt/bo/files/agents";
    }

    public function setAffectationAgentFilesPaths(string $path): void
    {
        if ($this->parametre === null) {
            $this->initializeDefaultParametre();
        }
        $this->parametre['addresses']['affectationAgentFilesPaths'] = $path;
    }

    public function isChargementAutomatiqueDesAffectationEstActif(): bool
    {
        return $this->parametre['addresses']['chargementAutomatiqueDesAffectationEstActif'] ?? false;
    }

    public function setChargementAutomatiqueDesAffectationEstActif(bool $actif): void
    {
        if ($this->parametre === null) {
            $this->initializeDefaultParametre();
        }
        $this->parametre['addresses']['chargementAutomatiqueDesAffectationEstActif'] = $actif;
    }

    // Notifications getters and setters
    public function getAdminEmail(): string
    {
        return $this->parametre['notifications']['adminEmail'] ?? "<EMAIL>";
    }

    public function setAdminEmail(string $email): void
    {
        if ($this->parametre === null) {
            $this->initializeDefaultParametre();
        }
        $this->parametre['notifications']['adminEmail'] = $email;
    }

    public function getDamResponssableDataAffectionEmail(): string
    {
        return $this->parametre['notifications']['damResponssableDataAffectionEmail'] ?? "<EMAIL>";
    }

    public function setDamResponssableDataAffectionEmail(string $email): void
    {
        if ($this->parametre === null) {
            $this->initializeDefaultParametre();
        }
        $this->parametre['notifications']['damResponssableDataAffectionEmail'] = $email;
    }

    public function isNotifierErreurAuResponssableDamAffectation(): bool
    {
        return $this->parametre['notifications']['notifierErreurAuResponssableDamAffectation'] ?? false;
    }

    public function setNotifierErreurAuResponssableDamAffectation(bool $notifier): void
    {
        if ($this->parametre === null) {
            $this->initializeDefaultParametre();
        }
        $this->parametre['notifications']['notifierErreurAuResponssableDamAffectation'] = $notifier;
    }

    public function isNotifierErreurAuAdminPourAffectation(): bool
    {
        return $this->parametre['notifications']['notifierErreurAuAdminPourAffectation'] ?? false;
    }

    public function setNotifierErreurAuAdminPourAffectation(bool $notifier): void
    {
        if ($this->parametre === null) {
            $this->initializeDefaultParametre();
        }
        $this->parametre['notifications']['notifierErreurAuAdminPourAffectation'] = $notifier;
    }

    public function isNotifierAdminDesErreurProduite(): bool
    {
        return $this->parametre['notifications']['NotifierAdminDesErreurProduite'] ?? false;
    }

    public function setNotifierAdminDesErreurProduite(bool $notifier): void
    {
        if ($this->parametre === null) {
            $this->initializeDefaultParametre();
        }
        $this->parametre['notifications']['NotifierAdminDesErreurProduite'] = $notifier;
    }

    // API endpoints getters and setters
    public function getDataIntegratorApiEndpoint(): string
    {
        return $this->parametre['apiEndpoints']['dataIntegratorApiInfo'] ?? "https://data-integrator.chru-nancy.fr/api/nancy/collecteur";
    }

    public function setDataIntegratorApiEndpoint(string $endpoint): void
    {
        if ($this->parametre === null) {
            $this->initializeDefaultParametre();
        }
        $this->parametre['apiEndpoints']['dataIntegratorApiInfo'] = $endpoint;
    }

    public function getBackendUrlEndpoint(): string
    {
        return $this->parametre['apiEndpoints']['backendUrlInfo'] ?? "https://supra-backend.chru-nancy.fr/api/nancy";
    }

    public function setBackendUrlEndpoint(string $endpoint): void
    {
        if ($this->parametre === null) {
            $this->initializeDefaultParametre();
        }
        $this->parametre['apiEndpoints']['backendUrlInfo'] = $endpoint;
    }

    // Helper method to initialize default values
    private function initializeDefaultParametre(): void
    {
        $this->parametre = [
            "addresses" => [
                "affectationAgentFilesPaths" => "/mnt/bo/files/agents",
                "chargementAutomatiqueDesAffectationEstActif" => false
            ],
            "notifications" => [
                "adminEmail" => "<EMAIL>",
                "damResponssableDataAffectionEmail" => "<EMAIL>",
                "notifierErreurAuResponssableDamAffectation" => false,
                "notifierErreurAuAdminPourAffectation" => false,
                "NotifierAdminDesErreurProduite" => false
            ],
            "apiEndpoints" => [
                "dataIntegratorApiInfo" => "https://data-integrator.chru-nancy.fr/api/nancy",
                "backendUrlInfo" => "https://supra-backend.chru-nancy.fr/api/nancy"
            ]
        ];
    }
}
