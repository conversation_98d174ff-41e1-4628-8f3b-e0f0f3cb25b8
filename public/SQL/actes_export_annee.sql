--------------------------------------------------------------------+
---- EXPORT ACTES AVEC CHAMPS STATIQUE
---------------------------------------------------------------------
SELECT
    CODE_ACTE,
    COALESCE(LIBELLEC_A, LIBELLEL_A) AS description_acte,
    TYPEACTE,
    HIERARCHIE2 AS categorie_principale,
    HIERARCHIE2LIB AS categorie_principale_libelle,
    ANNEE_ACTE,
    MOIS_ACTE,
    TO_DATE(ANNEE_ACTE || '-' || MOIS_ACTE || '-01', 'YYYY-MM-DD') AS validFrom,
    CASE
        WHEN ANNEE_ACTE < EXTRACT(YEAR FROM CURRENT_DATE)
            THEN TO_DATE(ANNEE_ACTE || '-12-31', 'YYYY-MM-DD')
        ELSE NULL
        END AS validTo,
    'Annuel' AS periodeType,  -- Période par défaut (modifiable plus tard)
    NB AS nombre_actes,
    MATPAIEPRINC AS praticien_MATRICULE,
    UFPRINCIPAL AS uf_code,
    'Collecteur' AS source
FROM homere.EX_MOIS_ACTES2
WHERE TYPEACTE IN ('CCAM', 'NGAP', 'LABO')
  AND ANNEE_ACTE = 2024  -- Pour récupérer les données depuis 2023 (ou ajustable)
ORDER BY ANNEE_ACTE DESC, MOIS_ACTE DESC;

--------------------------------  OPTIMISATION --------------------------------------------
SELECT
    CODE_ACTE,
    COALESCE(LIBELLEC_A, LIBELLEL_A) AS description_acte,
    TYPEACTE,
    HIERARCHIE2 AS categorie_principale,
    HIERARCHIE2LIB AS categorie_principale_libelle,
    ANNEE_ACTE,
    MOIS_ACTE,
    NVL(TYPEVENUE, 0) AS typeVenue,
    NVL(ICR_A, 0) AS icrA
    -- 🔥 Assure que `validFrom` est toujours au format `YYYY-MM-DD`
    TO_CHAR(TO_DATE(ANNEE_ACTE || '-' || LPAD(MOIS_ACTE, 2, '0') || '-01', 'YYYY-MM-DD'), 'YYYY-MM-DD') AS validFrom,
    -- 🔥 Si l'année est inférieure à l'année en cours, mettre `31/12/ANNEE`
    -- Sinon NULL proprement géré
    CASE
        WHEN ANNEE_ACTE < EXTRACT(YEAR FROM CURRENT_DATE)
            THEN TO_CHAR(TO_DATE(ANNEE_ACTE || '-12-31', 'YYYY-MM-DD'), 'YYYY-MM-DD')
        ELSE NULL
        END AS validTo,
    'Annuel' AS periodeType,  -- Période par défaut (modifiable plus tard)
    NB AS nombre_actes,
    MATPAIEPRINC AS praticien_MATRICULE,
    UFPRINCIPAL AS uf_code,
    'Collecteur' AS source
FROM homere.EX_MOIS_ACTES2
WHERE TYPEACTE IN ('CCAM', 'NGAP', 'LABO')
  AND ANNEE_ACTE = 2024  -- Pour récupérer les données de 2024
ORDER BY ANNEE_ACTE DESC, MOIS_ACTE DESC;


------------------------------------------------------------
SELECT COUNT(*) AS nb_actes
FROM homere.EX_MOIS_ACTES2
WHERE MATPAIEPRINC = 'U608650' and ANNEE_ACTE = '2024' ;