<?php
/**
 * Configuration de la limite de mémoire PHP
 * 
 * PROBLÈME DE MÉMOIRE APRÈS IMPORTATION DES ACTES:
 * ---------------------------------------------
 * Un problème de mémoire a été identifié après l'exécution de la commande app:import:actes.
 * Lorsqu'un utilisateur tente d'accéder à l'interface d'administration après l'importation,
 * une erreur "Fatal error: Allowed memory size exhausted" peut se produire dans:
 * - ContextListener.php (lors de la sérialisation du token de sécurité)
 * - logs.html.php (lors de l'affichage des logs d'erreur)
 * 
 * SOLUTION MISE EN PLACE:
 * --------------------
 * 1. Augmentation de la limite mémoire PHP à 2048M (temporaire)
 * 2. Optimisation de la commande app:import:actes:
 *    - Fermeture explicite des connexions à la base de données
 *    - Nettoyage amélioré du cache et des références en mémoire
 *    - Utilisation de gc_mem_caches() pour libérer la mémoire de l'interpréteur
 * 3. Création d'un signal de redémarrage après l'importation
 *    - Fichier créé dans var/restart_needed.txt
 *    - À utiliser avec un cron pour redémarrer frankenPHP
 * 
 * SOLUTION RECOMMANDÉE À LONG TERME:
 * -------------------------------
 * 1. Déplacer cette configuration dans un fichier .env ou php.ini
 * 2. Implémenter un redémarrage automatique de PHP-FPM après les importations volumineuses
 * 3. Optimiser davantage la gestion mémoire dans ActeImportService
 * 4. Envisager de diviser les importations en lots plus petits (par jour)
 */
ini_set('memory_limit', '2048M'); // Probleme deja resolu voir le script cd supra/supra-infra/setup-frankenphp-config.sh (je laisse cette ligne pour savoir pourquoi ce fichier vous pouvez la retirez:) )
use App\Kernel;

require_once dirname(__DIR__).'/vendor/autoload_runtime.php';

return function (array $context) {
    return new Kernel($context['APP_ENV'], (bool) $context['APP_DEBUG']);
};
