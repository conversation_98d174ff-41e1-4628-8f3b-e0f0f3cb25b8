<?php

namespace App\Domain\Extension\CoreSupraFilter\Cr;

use ApiPlatform\Doctrine\Orm\Extension\QueryCollectionExtensionInterface;
use ApiPlatform\Doctrine\Orm\Util\QueryNameGeneratorInterface;
use ApiPlatform\Metadata\Operation;
use App\Domain\Extension\CoreSupraFilter\BaseCustomFilterExtension;
use App\Domain\Extension\CoreSupraFilter\Pole\PoleCategoryExcludeFilter;
use App\Entity\Structure\Cr;
use Doctrine\ORM\QueryBuilder;

/**
 * Pôle exclu → ses CRs  sont automatiquement exclus
 * CR exclue → exclusion directe
 */
class CrCategoryFilterExtension extends BaseCustomFilterExtension implements QueryCollectionExtensionInterface
{
    public function applyToCollection(
        QueryBuilder $queryBuilder,
        QueryNameGeneratorInterface $queryNameGenerator,
        string $resourceClass,
        ?Operation $operation = null,
        array $context = []
    ): void {
        if ($resourceClass !== Cr::class) {
            return;
        }

        if (!$this->isApiPlatformContext($context)) {
            return;
        }

        $this->addCrcodeFilter($queryBuilder, $queryNameGenerator);
    }

    private function addCrcodeFilter(QueryBuilder $queryBuilder, QueryNameGeneratorInterface $queryNameGenerator): void
    {
        $rootAlias = $queryBuilder->getRootAliases()[0];
        $excludedCrcodes = CrCategoryExcludeFilter::getExcludedCrcodes();
        $excludedPolecodes = PoleCategoryExcludeFilter::getExcludedPolecodes();

        $conditions = [];

        // 1. Exclusion directe des CRs par crcode
        if (!empty($excludedCrcodes)) {
            $parameterName = $queryNameGenerator->generateParameterName('excludedCrcodes');
            $conditions[] = sprintf(
                '(UPPER(%s.crcode) NOT IN (:%s) OR %s.crcode IS NULL)',
                $rootAlias,
                $parameterName,
                $rootAlias
            );
            $queryBuilder->setParameter($parameterName, array_map('strtoupper', $excludedCrcodes));
        }

        // 2. Exclusion en cascade : CRs dont le pôle parent est exclu
        if (!empty($excludedPolecodes)) {
            // Assurer que la jointure avec pole existe
            $this->ensurePoleJoin($queryBuilder, $rootAlias);

            $parameterName = $queryNameGenerator->generateParameterName('excludedPolecodes');
            $conditions[] = sprintf(
                '(UPPER(pole.polecode) NOT IN (:%s) OR pole.polecode IS NULL)',
                $parameterName
            );
            $queryBuilder->setParameter($parameterName, array_map('strtoupper', $excludedPolecodes));
        }

        // Appliquer toutes les conditions
        if (!empty($conditions)) {
            $queryBuilder->andWhere(implode(' AND ', $conditions));
        }
    }

    private function ensurePoleJoin(QueryBuilder $queryBuilder, string $rootAlias): void
    {
        $aliases = $queryBuilder->getAllAliases();

        // Vérifier si la jointure 'pole' existe déjà
        if (!in_array('pole', $aliases)) {
            $queryBuilder->leftJoin($rootAlias . '.pole', 'pole');
        }
    }

    /**
     * Version alternative excluant aussi les CRs avec crcode NULL
     */
    /*
    private function addCrcodeFilter_SI_TU_VEUX_IGNORER_LES_CRS_AVEC_CRCODE_NULL(
        QueryBuilder $queryBuilder,
        QueryNameGeneratorInterface $queryNameGenerator
    ): void {
        $rootAlias = $queryBuilder->getRootAliases()[0];
        $excludedCrcodes = CrCategoryExcludeFilter::getExcludedCrcodes();
        $excludedPolecodes = PoleCategoryExcludeFilter::getExcludedPolecodes();

        $conditions = [];

        // Force crcode NOT NULL
        $conditions[] = sprintf('%s.crcode IS NOT NULL', $rootAlias);

        // Exclusion directe des CRs
        if (!empty($excludedCrcodes)) {
            $parameterName = $queryNameGenerator->generateParameterName('excludedCrcodes');
            $conditions[] = sprintf('UPPER(%s.crcode) NOT IN (:%s)', $rootAlias, $parameterName);
            $queryBuilder->setParameter($parameterName, array_map('strtoupper', $excludedCrcodes));
        }

        // Exclusion cascade des pôles
        if (!empty($excludedPolecodes)) {
            $this->ensurePoleJoin($queryBuilder, $rootAlias);
            $parameterName = $queryNameGenerator->generateParameterName('excludedPolecodes');
            $conditions[] = sprintf(
                '(UPPER(pole.polecode) NOT IN (:%s) OR pole.polecode IS NULL)',
                $parameterName
            );
            $queryBuilder->setParameter($parameterName, array_map('strtoupper', $excludedPolecodes));
        }

        if (!empty($conditions)) {
            $queryBuilder->andWhere(implode(' AND ', $conditions));
        }
    }
    */
}