<?php

namespace App\Domain\Extension;

use ApiPlatform\Doctrine\Orm\Extension\QueryCollectionExtensionInterface;
use ApiPlatform\Doctrine\Orm\Util\QueryNameGeneratorInterface;
use ApiPlatform\Metadata\Operation;
use App\Entity\Activite\Actes;
use Doctrine\ORM\QueryBuilder;
use Symfony\Component\HttpFoundation\RequestStack;

/**
 * Extension pour filtrer les actes selon les filtres en cascade du subheader
 * 
 * Cette extension gère les filtres suivants :
 * - practitioner : Filtre par praticien (agent)
 * - pole : Filtre par pôle (via ufIntervention -> cr -> pole)
 * - cr : Filtre par centre de responsabilité (via ufIntervention -> cr)
 * - typeVenue : Filtre par type de venue (1=Consultation, 2=Hospitalisation, 3=Urgence)
 * 
 * Relations:
 * - actes -> ufIntervention -> cr -> pole
 * - actes -> agent (direct)
 * - actes -> typeVenue (direct)
 */
class ActesCascadeFiltersExtension implements QueryCollectionExtensionInterface
{
    public function __construct(
        private RequestStack $requestStack
    ) {}

    public function applyToCollection(
        QueryBuilder $queryBuilder,
        QueryNameGeneratorInterface $queryNameGenerator,
        string $resourceClass,
        ?Operation $operation = null,
        array $context = []
    ): void {
        // Appliquer seulement sur l'entité Actes
        if ($resourceClass !== Actes::class) {
            return;
        }

        $request = $this->requestStack->getCurrentRequest();
        if (!$request) {
            return;
        }

        // Appliquer les filtres en cascade
        $this->applyCascadeFilters($queryBuilder, $queryNameGenerator, $request);
    }

    /**
     * Applique les filtres en cascade selon les paramètres de la requête
     */
    private function applyCascadeFilters(
        QueryBuilder $queryBuilder,
        QueryNameGeneratorInterface $queryNameGenerator,
        $request
    ): void {
        $rootAlias = $queryBuilder->getRootAliases()[0];

        // Filtre par praticien
        $practitioner = $request->query->get('practitioner');
        if ($practitioner) {
            $this->applyPractitionerFilter($queryBuilder, $queryNameGenerator, $rootAlias, $practitioner);
        }

        // Filtre par pôle
        $pole = $request->query->get('pole');
        if ($pole) {
            $this->applyPoleFilter($queryBuilder, $queryNameGenerator, $rootAlias, $pole);
        }

        // Filtre par CR
        $cr = $request->query->get('cr');
        if ($cr) {
            $this->applyCrFilter($queryBuilder, $queryNameGenerator, $rootAlias, $cr);
        }

        // Filtre par type de venue
        $typeVenue = $request->query->get('typeVenue');
        if ($typeVenue && is_numeric($typeVenue)) {
            $this->applyTypeVenueFilter($queryBuilder, $queryNameGenerator, $rootAlias, (int)$typeVenue);
        }
    }

    /**
     * Filtre par praticien (agent)
     * Paramètre: practitioner=/api/agent/123 ou practitioner=123
     * Relation: actes -> agent (direct)
     */
    private function applyPractitionerFilter(
        QueryBuilder $queryBuilder,
        QueryNameGeneratorInterface $queryNameGenerator,
        string $rootAlias,
        string $practitioner
    ): void {
        // Extraire l'ID depuis l'IRI si nécessaire
        $practitionerId = $this->extractIdFromIri($practitioner);
        
        if ($practitionerId) {
            $parameterName = $queryNameGenerator->generateParameterName('practitioner');
            
            // Joindre avec l'entité Agent si pas déjà fait
            if (!$this->hasJoin($queryBuilder, 'agent')) {
                $queryBuilder->leftJoin($rootAlias . '.agent', 'agent');
            }
            
            $queryBuilder
                ->andWhere('agent.id = :' . $parameterName)
                ->setParameter($parameterName, $practitionerId);
        }
    }

    /**
     * Filtre par pôle
     * Paramètre: pole=/api/poles/456 ou pole=456
     * Relation: actes -> ufIntervention -> cr -> pole
     */
    private function applyPoleFilter(
        QueryBuilder $queryBuilder,
        QueryNameGeneratorInterface $queryNameGenerator,
        string $rootAlias,
        string $pole
    ): void {
        $poleId = $this->extractIdFromIri($pole);
        
        if ($poleId) {
            $parameterName = $queryNameGenerator->generateParameterName('pole');
            
            // Joindre avec UF intervention, puis CR, puis pôle (actes -> ufIntervention -> cr -> pole)
            if (!$this->hasJoin($queryBuilder, 'ufIntervention')) {
                $queryBuilder->leftJoin($rootAlias . '.ufIntervention', 'ufIntervention');
            }
            if (!$this->hasJoin($queryBuilder, 'ufCr')) {
                $queryBuilder->leftJoin('ufIntervention.cr', 'ufCr');
            }
            if (!$this->hasJoin($queryBuilder, 'pole')) {
                $queryBuilder->leftJoin('ufCr.pole', 'pole');
            }
            
            $queryBuilder
                ->andWhere('pole.id = :' . $parameterName)
                ->setParameter($parameterName, $poleId);
        }
    }

    /**
     * Filtre par centre de responsabilité (CR)
     * Paramètre: cr=/api/crs/789 ou cr=789
     * Relation: actes -> ufIntervention -> cr
     */
    private function applyCrFilter(
        QueryBuilder $queryBuilder,
        QueryNameGeneratorInterface $queryNameGenerator,
        string $rootAlias,
        string $cr
    ): void {
        $crId = $this->extractIdFromIri($cr);
        
        if ($crId) {
            $parameterName = $queryNameGenerator->generateParameterName('cr');
            
            // Joindre avec UF intervention puis avec le CR (actes -> ufIntervention -> cr)
            if (!$this->hasJoin($queryBuilder, 'ufIntervention')) {
                $queryBuilder->leftJoin($rootAlias . '.ufIntervention', 'ufIntervention');
            }
            if (!$this->hasJoin($queryBuilder, 'ufCr')) {
                $queryBuilder->leftJoin('ufIntervention.cr', 'ufCr');
            }
            
            $queryBuilder
                ->andWhere('ufCr.id = :' . $parameterName)
                ->setParameter($parameterName, $crId);
        }
    }

    /**
     * Filtre par type de venue
     * Paramètre: typeVenue=1 (1=Consultation, 2=Hospitalisation, 3=Urgence)
     * Relation: actes -> typeVenue (direct)
     */
    private function applyTypeVenueFilter(
        QueryBuilder $queryBuilder,
        QueryNameGeneratorInterface $queryNameGenerator,
        string $rootAlias,
        int $typeVenue
    ): void {
        // Valider que le type de venue est valide
        if (!in_array($typeVenue, [1, 2, 3])) {
            return;
        }

        $parameterName = $queryNameGenerator->generateParameterName('typeVenue');
        
        $queryBuilder
            ->andWhere($rootAlias . '.typeVenue = :' . $parameterName)
            ->setParameter($parameterName, $typeVenue);
    }

    /**
     * Extrait l'ID depuis une IRI API Platform
     * Exemple: "/api/agent/123" -> "123"
     */
    private function extractIdFromIri(string $iri): ?string
    {
        // Si c'est déjà un ID numérique, le retourner
        if (is_numeric($iri)) {
            return $iri;
        }

        // Extraire l'ID depuis l'IRI
        if (preg_match('/\/([^\/]+)$/', $iri, $matches)) {
            return $matches[1];
        }

        return null;
    }

    /**
     * Vérifie si une jointure existe déjà dans la requête
     */
    private function hasJoin(QueryBuilder $queryBuilder, string $alias): bool
    {
        $joins = $queryBuilder->getDQLPart('join');
        
        foreach ($joins as $rootAlias => $joinArray) {
            foreach ($joinArray as $join) {
                if ($join->getAlias() === $alias) {
                    return true;
                }
            }
        }
        
        return false;
    }
}
