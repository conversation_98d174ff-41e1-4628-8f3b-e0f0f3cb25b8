<?php

namespace App\State\Partials;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use App\ApiResource\Activite\Partials\ActeMultiStatsDto;
use App\Repository\ActesRepository;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Contracts\Cache\CacheInterface;
use Symfony\Contracts\Cache\ItemInterface;

/**
 * StateProvider pour les statistiques multi-actes
 */
class ActeMultiStatsProvider implements ProviderInterface
{
    public function __construct(
        private ActesRepository $actesRepository,
        private RequestStack $requestStack,
        private CacheInterface $cache,
        #[Autowire('%env(int:API_CACHE_TTL)%')] private int $apiCacheTtl = 3600
    ) {}

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): object|array|null
    {
        $request = $this->requestStack->getCurrentRequest();
        if (!$request) {
            return [];
        }

        // Recuperation des codes d'actes depuis les query parameters
        $acteCodesParam = $request->query->get('acteCodes');
        if (!$acteCodesParam) {
            return [];
        }
        
        // Les codes peuvent etre passes comme "YYYY001,ZZZZ002,AAAA003" ou comme array
        if (is_string($acteCodesParam)) {
            $acteCodes = explode(',', $acteCodesParam);
        } else {
            $acteCodes = (array) $acteCodesParam;
        }
        
        // Nettoyer et valider les codes
        $acteCodes = array_filter(array_map('trim', $acteCodes));
        
        if (empty($acteCodes)) {
            return [];
        }

        // Recuperation des parametres de periodes depuis la query string
        $p1Start = $request->query->get('p1Start');
        $p1End = $request->query->get('p1End');
        $p2Start = $request->query->get('p2Start');
        $p2End = $request->query->get('p2End');
        $p3Start = $request->query->get('p3Start');
        $p3End = $request->query->get('p3End');

        // Creation de la cle de cache unique
        $cacheKey = sprintf(
            'acte_multi_stats_%s_%s_%s_%s_%s_%s_%s',
            md5(implode(',', $acteCodes)),
            $p1Start ?? 'null',
            $p1End ?? 'null',
            $p2Start ?? 'null',
            $p2End ?? 'null',
            $p3Start ?? 'null',
            $p3End ?? 'null'
        );

        return $this->cache->get($cacheKey, function (ItemInterface $item) use (
            $acteCodes, $p1Start, $p1End, $p2Start, $p2End, $p3Start, $p3End
        ) {
            $item->expiresAfter($this->apiCacheTtl);

            // Conversion des dates string en objets DateTime
            $p1StartDate = $p1Start ? new \DateTime($p1Start) : null;
            $p1EndDate = $p1End ? new \DateTime($p1End) : null;
            $p2StartDate = $p2Start ? new \DateTime($p2Start) : null;
            $p2EndDate = $p2End ? new \DateTime($p2End) : null;
            $p3StartDate = $p3Start ? new \DateTime($p3Start) : null;
            $p3EndDate = $p3End ? new \DateTime($p3End) : null;

            // Recuperation des statistiques agregees par acte
            $acteStatsData = $this->actesRepository->getMultiActeStatsByActeCodes(
                $acteCodes,
                $p1StartDate,
                $p1EndDate,
                $p2StartDate,
                $p2EndDate,
                $p3StartDate,
                $p3EndDate
            );

            $acteStats = [];

            foreach ($acteStatsData as $acteData) {
                $totalRealisations = $acteData['p1_count'] + $acteData['p2_count'] + $acteData['p3_count'];

                // Inclusion uniquement des actes qui ont au moins une realisation
                if ($totalRealisations > 0) {
                    $acteStats[] = new ActeMultiStatsDto(
                        acteCode: $acteData['code'],
                        acteDescription: $acteData['description'] ?? 'Description non disponible',
                        p1Count: (int)$acteData['p1_count'],
                        p2Count: (int)$acteData['p2_count'],
                        p3Count: (int)$acteData['p3_count'],
                        totalActivites: (int)$acteData['total_activites'],
                        totalRealisations: $totalRealisations
                    );
                }
            }

            // Tri par total de realisations decroissant
            usort($acteStats, fn($a, $b) => $b->totalRealisations <=> $a->totalRealisations);

            return $acteStats;
        });
    }
}