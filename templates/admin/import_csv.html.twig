{% extends '@EasyAdmin/page/content.html.twig' %}

{% block content %}
    {% for flash in app.flashes('success') %}
        <div class="alert alert-success">
            ✅ {{ flash|raw }}
        </div>
    {% endfor %}

    {% for flash in app.flashes('danger') %}
        <div class="alert alert-danger">
            ❌ {{ flash|raw }}
        </div>
    {% endfor %}

    <div class="container">
        <h2>📥 Importation de données pour {{ entity|split('\\')|last }}</h2>

        <form action="" method="post" enctype="multipart/form-data">
            <div class="mb-3">
                <label for="entite_juridique_id" class="form-label">
                    <span class="text-danger">*</span> Sélectionnez l'entité juridique
                </label>
                <select class="form-control" id="entite_juridique_id" name="entite_juridique_id" required>
                    <option value="">-- Choisir une entité juridique --</option>
                    {% for entiteJuridique in entitesJuridiques %}
                        <option value="{{ entiteJuridique.id }}">{{ entiteJuridique.nom }}</option>
                    {% endfor %}
                </select>
                <small class="form-text text-muted">
                    Les données seront importées dans l'entité juridique sélectionnée
                </small>
            </div>

            <div class="mb-3">
                <label for="csv_file" class="form-label">
                    <span class="text-danger">*</span> Sélectionnez un fichier CSV
                </label>
                <input type="file" class="form-control" id="csv_file" name="csv_file" accept=".csv" required>
                <small class="form-text text-muted">
                    Format accepté : CSV uniquement
                </small>
            </div>

            <div class="d-flex justify-content-between">
                <button type="submit" class="btn btn-success">
                    📤 Importer les données
                </button>

                {% set mock_csv_filename = '/data_models_mocks/' ~ entity|split('\\')|last|lower ~ '_mock.csv' %}
                <a href="{{ asset(mock_csv_filename) }}" class="btn btn-info" download>
                    📥 Télécharger un mock CSV
                </a>
            </div>

            <div class="mt-3">
                <small class="text-muted">
                    <span class="text-danger">*</span> Champs obligatoires
                </small>
            </div>
        </form>
    </div>
{% endblock %}
