security:
    # https://symfony.com/doc/current/security.html#registering-the-user-hashing-passwords
    password_hashers:
        Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface: 'auto'

    # Role hierarchy based on App\Domain\Enum\AgentRoleType
    role_hierarchy:
        ROLE_ADMIN: ROLE_USER
        ROLE_CHEF_DE_POLE: ROLE_ADMIN
        ROLE_MANAGER: ROLE_CHEF_DE_POLE
        ROLE_CME: ROLE_MANAGER
        ROLE_FIFAP: ROLE_MANAGER

    # https://symfony.com/doc/current/security.html#loading-the-user-the-user-provider
    providers:
        # used to reload user from session & other features (e.g. switch_user)
        app_user_provider: # DB
            entity:
                class: App\Entity\Praticien\Agent
                property: email

        # https://symfony.com/doc/current/security.html#using-ldap
        ldap_provider:
            ldap:
                service: Symfony\Component\Ldap\Ldap
                base_dn: '%env(LDAP_BASE_DN)%'
                default_roles: ROLE_USER
                uid_key: sAMAccountName
    firewalls:
        dev:
            pattern: ^/(_(profiler|wdt)|css|images|js)/
            security: false

        # Authentification EasyAdmin avec formulaire et session
        admin:
            pattern: ^/manager/dtnib/admin
            provider: app_user_provider
            entry_point: App\Domain\Service\AdminFormAuthenticator
            # Protection contre la fixation de session
            logout:
                path: /manager/dtnib/admin/logout
                target: /manager/dtnib/admin/login
                # Invalider la session lors de la déconnexion
                invalidate_session: true
                # Supprimer le cookie de session
                delete_cookies: ['PHPSESSID']
            remember_me:
                secret: '%kernel.secret%'
                lifetime: 604800 # 1 semaine
                path: /manager/dtnib/admin
                # Utiliser des cookies sécurisés pour remember_me
                secure: true
                httponly: true
                samesite: 'strict'
            custom_authenticator: App\Domain\Service\AdminFormAuthenticator
            # Gestionnaire d'accès refusé personnalisé
            access_denied_handler: App\Security\AdminAccessDeniedHandler
            # Configuration de la session
            # Régénérer la session à chaque requête
            login_throttling:
                max_attempts: 5
                interval: '15 minutes'

        # API: Authentification LDAP (Active Directory)
        login_ad:
            pattern: ^/api/login-ad$
            stateless: true
            provider: ldap_provider
            custom_authenticator: App\Domain\Service\AuthLdap

        # API: Authentification base de données (fallback)
        login_db:
            pattern: ^/api/login$
            stateless: true
            provider: app_user_provider
            custom_authenticator: App\Domain\Service\AuthLdap

        # API: Toutes les autres routes API (protégées par JWT)
        api:
            pattern: ^/api
            stateless: true
            provider: app_user_provider
            access_token:
                token_handler: App\Security\ApiTokenHandler
                failure_handler: App\Security\ApiAuthenticationFailureHandler

    access_control:
        - { path: ^/manager/dtnib/admin/login$, role: PUBLIC_ACCESS }
        - { path: ^/api/auth/email/login, role: PUBLIC_ACCESS }
        - { path: ^/api/auth/email/verify, role: PUBLIC_ACCESS }
        - { path: ^/manager/dtnib/admin, roles: ROLE_ADMIN }
        - { path: ^/api/login(-ad)?$, role: PUBLIC_ACCESS }
        - { path: ^/api/ejs, role: PUBLIC_ACCESS }
        - { path: ^/api, roles: IS_AUTHENTICATED_FULLY }


when@test:
    security:
        password_hashers:
            # By default, password hashers are resource intensive and take time. This is
            # important to generate secure password hashes. In tests however, secure hashes
            # are not important, waste resources and increase test times. The following
            # reduces the work factor to the lowest possible values.
            Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface:
                algorithm: auto
                cost: 4 # Lowest possible value for bcrypt
                time_cost: 3 # Lowest possible value for argon
                memory_cost: 10 # Lowest possible value for argon
