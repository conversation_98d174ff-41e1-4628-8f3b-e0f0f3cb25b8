<?php
// Test script for the /api/actes/filters-simple endpoint
// This script tests the functionality of the ActesFiltersSimpleController

// Function to make a GET request and display the results
// Run with: php tests/RapideTest/test_actes_filters_simple.php

function testEndpoint($url, $bearerToken) {
    echo "Testing endpoint: $url\n";
    
    // Use cURL to make the request
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HEADER, true);
    
    // Add Authorization header with bearer token
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $bearerToken
    ]);
    
    $response = curl_exec($ch);
    
    // Check for cURL errors
    if ($response === false) {
        echo "cURL Error: " . curl_error($ch) . "\n";
        curl_close($ch);
        return;
    }
    
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
    $headers = substr($response, 0, $headerSize);
    $body = substr($response, $headerSize);
    
    curl_close($ch);
    
    echo "HTTP Status Code: $httpCode\n";
    
    // Display important headers
    $headerLines = explode("\n", $headers);
    echo "Response Headers:\n";
    foreach ($headerLines as $line) {
        if (preg_match('/^(Content-Type|Content-Length|X-Debug-Token|X-Debug-Token-Link):/i', $line)) {
            echo "  $line\n";
        }
    }
    
    // Parse the JSON response
    $data = json_decode($body, true);
    
    if ($data) {
        echo "JSON Structure Keys: " . implode(", ", array_keys($data)) . "\n";
        
        // Display some details about the response
        if (isset($data['practitioners']) && is_array($data['practitioners'])) {
            echo "Number of practitioners: " . count($data['practitioners']) . "\n";
            if (count($data['practitioners']) > 0) {
                echo "First practitioner: " . json_encode($data['practitioners'][0]) . "\n";
            }
        }
        
        if (isset($data['poles']) && is_array($data['poles'])) {
            echo "Number of poles: " . count($data['poles']) . "\n";
            if (count($data['poles']) > 0) {
                echo "First pole: " . json_encode($data['poles'][0]) . "\n";
            }
        }
        
        if (isset($data['crs']) && is_array($data['crs'])) {
            echo "Number of CRs: " . count($data['crs']) . "\n";
            if (count($data['crs']) > 0) {
                echo "First CR: " . json_encode($data['crs'][0]) . "\n";
            }
        }
        
        if (isset($data['totalActes'])) {
            echo "Total actes: " . $data['totalActes'] . "\n";
        }
        
        if (isset($data['appliedFilters']) && is_array($data['appliedFilters'])) {
            echo "Applied filters: " . json_encode($data['appliedFilters']) . "\n";
        }
        
        if (isset($data['appliedPeriods']) && is_array($data['appliedPeriods'])) {
            echo "Applied periods: " . json_encode($data['appliedPeriods']) . "\n";
        }
        
        if (isset($data['generationTimeMs'])) {
            echo "Generation time (ms): " . $data['generationTimeMs'] . "\n";
        }
    } else {
        echo "Failed to parse JSON response.\n";
        echo "Raw response body:\n";
        echo substr($body, 0, 500) . (strlen($body) > 500 ? "...[truncated]" : "") . "\n";
    }
    
    echo "\n";
}

// Base URL - replace with your actual API base URL if needed
$baseUrl = 'http://localhost:8000';

// Define period parameters for January to June for 2025, 2024, 2023
$periodParams = "p1Start=2025-01-01&p1End=2025-06-30&p2Start=2024-01-01&p2End=2024-06-30&p3Start=2023-01-01&p3End=2023-06-30";

// Ejcode parameter
$ejcode = "ej0001";

// Bearer token for authentication (from the issue description)
$bearerToken = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ToYfqp5fsxBUHcC0dXXsr-Js-ylY-U62FEbYSBF2a4g';

echo "=== Testing ActesFiltersSimpleController endpoint ===\n\n";

// Test 1: Basic test without parameters
echo "Test 1: Basic test without parameters\n";
echo "------------------------------------\n";
testEndpoint("$baseUrl/api/actes/filters-simple", $bearerToken);

// Test 2: With period parameters
echo "Test 2: With period parameters\n";
echo "--------------------------\n";
testEndpoint("$baseUrl/api/actes/filters-simple?$periodParams", $bearerToken);

// Test 3: With ejcode parameter
echo "Test 3: With ejcode parameter\n";
echo "-------------------------\n";
testEndpoint("$baseUrl/api/actes/filters-simple?ejcode=$ejcode", $bearerToken);

// Test 4: With both period and ejcode parameters
echo "Test 4: With both period and ejcode parameters\n";
echo "----------------------------------------\n";
testEndpoint("$baseUrl/api/actes/filters-simple?$periodParams&ejcode=$ejcode", $bearerToken);

echo "Tests completed.\n";