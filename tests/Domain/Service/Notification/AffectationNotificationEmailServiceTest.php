<?php

namespace App\Tests\Domain\Service\Notification;

use App\Domain\Service\Notification\AffectationNotificationEmailService;
use App\Entity\Structure\EntiteJuridique;
use Psr\Log\LoggerInterface;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Email;
use Twig\Environment;

it('can be instantiated', function () {
    $mailer = $this->createMock(MailerInterface::class);
    $twig = $this->createMock(Environment::class);
    $logger = $this->createMock(LoggerInterface::class);
    
    $service = new AffectationNotificationEmailService($mailer, $twig, $logger);
    
    expect($service)->toBeInstanceOf(AffectationNotificationEmailService::class);
});

it('sends auto import success notification', function () {
    // Create mocks
    $mailer = $this->createMock(MailerInterface::class);
    $twig = $this->createMock(Environment::class);
    $logger = $this->createMock(LoggerInterface::class);
    $entiteJuridique = $this->createMock(EntiteJuridique::class);
    
    // Configure mocks
    $entiteJuridique->method('isNotifierErreurAuAdminPourAffectation')
        ->willReturn(true);
    
    $entiteJuridique->method('isNotifierErreurAuResponssableDamAffectation')
        ->willReturn(false);
    
    $entiteJuridique->method('getAdminEmail')
        ->willReturn('<EMAIL>');
    
    $entiteJuridique->method('getCode')
        ->willReturn('EJ001');
    
    $twig->method('render')
        ->willReturn('<html>Email content</html>');
    
    // Import result data
    $importResult = [
        'filesProcessed' => 2,
        'totalCreated' => 10,
        'totalUpdated' => 5,
        'errors' => []
    ];
    
    // Expect mailer to be called
    $mailer->expects($this->once())
        ->method('send')
        ->with($this->callback(function ($email) {
            return $email instanceof Email &&
                   $email->getSubject() === '[EJ001] Importation automatique d\'affectations réussie';
        }));
    
    // Create service and call method
    $service = new AffectationNotificationEmailService($mailer, $twig, $logger);
    $service->sendAffectationAutoImportSuccessNotification($entiteJuridique, $importResult);
});

it('does not send auto import success notification when notifications are disabled', function () {
    // Create mocks
    $mailer = $this->createMock(MailerInterface::class);
    $twig = $this->createMock(Environment::class);
    $logger = $this->createMock(LoggerInterface::class);
    $entiteJuridique = $this->createMock(EntiteJuridique::class);
    
    // Configure mocks
    $entiteJuridique->method('isNotifierErreurAuAdminPourAffectation')
        ->willReturn(false);
    
    $entiteJuridique->method('isNotifierErreurAuResponssableDamAffectation')
        ->willReturn(false);
    
    $entiteJuridique->method('getCode')
        ->willReturn('EJ001');
    
    // Import result data
    $importResult = [
        'filesProcessed' => 2,
        'totalCreated' => 10,
        'totalUpdated' => 5,
        'errors' => []
    ];
    
    // Expect logger to be called
    $logger->expects($this->once())
        ->method('info')
        ->with(
            'Notifications désactivées pour l\'entité juridique, aucun email de succès envoyé',
            $this->callback(function ($context) {
                return isset($context['entiteJuridique']) && $context['entiteJuridique'] === 'EJ001';
            })
        );
    
    // Expect mailer not to be called
    $mailer->expects($this->never())
        ->method('send');
    
    // Create service and call method
    $service = new AffectationNotificationEmailService($mailer, $twig, $logger);
    $service->sendAffectationAutoImportSuccessNotification($entiteJuridique, $importResult);
});

it('sends auto import error notification', function () {
    // Create mocks
    $mailer = $this->createMock(MailerInterface::class);
    $twig = $this->createMock(Environment::class);
    $logger = $this->createMock(LoggerInterface::class);
    $entiteJuridique = $this->createMock(EntiteJuridique::class);
    
    // Configure mocks
    $entiteJuridique->method('isNotifierErreurAuAdminPourAffectation')
        ->willReturn(true);
    
    $entiteJuridique->method('isNotifierErreurAuResponssableDamAffectation')
        ->willReturn(false);
    
    $entiteJuridique->method('getAdminEmail')
        ->willReturn('<EMAIL>');
    
    $entiteJuridique->method('getCode')
        ->willReturn('EJ001');
    
    $twig->method('render')
        ->willReturn('<html>Email content</html>');
    
    // Error data
    $errors = [
        'Error 1',
        'Error 2'
    ];
    
    // Expect mailer to be called
    $mailer->expects($this->once())
        ->method('send')
        ->with($this->callback(function ($email) {
            return $email instanceof Email &&
                   $email->getSubject() === '[EJ001] ERREUR - Importation automatique d\'affectations';
        }));
    
    // Create service and call method
    $service = new AffectationNotificationEmailService($mailer, $twig, $logger);
    $service->sendAffectationAutoImportErrorNotification($entiteJuridique, $errors, '/path/to/file.csv');
});

it('sends invalid CSV file notification', function () {
    // Create mocks
    $mailer = $this->createMock(MailerInterface::class);
    $twig = $this->createMock(Environment::class);
    $logger = $this->createMock(LoggerInterface::class);
    $entiteJuridique = $this->createMock(EntiteJuridique::class);
    
    // Configure mocks
    $entiteJuridique->method('isNotifierErreurAuResponssableDamAffectation')
        ->willReturn(true);
    
    $entiteJuridique->method('getDamResponssableDataAffectionEmail')
        ->willReturn('<EMAIL>');
    
    $entiteJuridique->method('getCode')
        ->willReturn('EJ001');
    
    $twig->method('render')
        ->willReturn('<html>Email content</html>');
    
    // Expect mailer to be called
    $mailer->expects($this->once())
        ->method('send')
        ->with($this->callback(function ($email) {
            return $email instanceof Email &&
                   $email->getSubject() === '[EJ001] Fichier CSV d\'affectation invalide';
        }));
    
    // Create service and call method
    $service = new AffectationNotificationEmailService($mailer, $twig, $logger);
    $service->sendInvalidCsvFileNotification($entiteJuridique, '/path/to/file.csv', 'Invalid CSV format');
});

it('sends weekly affectation report', function () {
    // Create mocks
    $mailer = $this->createMock(MailerInterface::class);
    $twig = $this->createMock(Environment::class);
    $logger = $this->createMock(LoggerInterface::class);
    $entiteJuridique = $this->createMock(EntiteJuridique::class);
    
    // Configure mocks
    $entiteJuridique->method('isNotifierErreurAuAdminPourAffectation')
        ->willReturn(true);
    
    $entiteJuridique->method('getAdminEmail')
        ->willReturn('<EMAIL>');
    
    $entiteJuridique->method('getCode')
        ->willReturn('EJ001');
    
    $twig->method('render')
        ->willReturn('<html>Email content</html>');
    
    // Weekly stats data
    $weeklyStats = [
        'totalAffectations' => 100,
        'newAffectations' => 20,
        'updatedAffectations' => 10,
        'topUfs' => [
            ['ufCode' => 'UF001', 'count' => 15],
            ['ufCode' => 'UF002', 'count' => 10]
        ]
    ];
    
    // Expect mailer to be called
    $mailer->expects($this->once())
        ->method('send')
        ->with($this->callback(function ($email) {
            return $email instanceof Email &&
                   $email->getSubject() === '[EJ001] Rapport hebdomadaire - Importations d\'affectations';
        }));
    
    // Create service and call method
    $service = new AffectationNotificationEmailService($mailer, $twig, $logger);
    $service->sendWeeklyAffectationReport($entiteJuridique, $weeklyStats);
});

it('handles empty recipient list gracefully', function () {
    // Create mocks
    $mailer = $this->createMock(MailerInterface::class);
    $twig = $this->createMock(Environment::class);
    $logger = $this->createMock(LoggerInterface::class);
    $entiteJuridique = $this->createMock(EntiteJuridique::class);
    
    // Configure mocks
    $entiteJuridique->method('isNotifierErreurAuAdminPourAffectation')
        ->willReturn(true);
    
    $entiteJuridique->method('isNotifierErreurAuResponssableDamAffectation')
        ->willReturn(false);
    
    $entiteJuridique->method('getAdminEmail')
        ->willReturn('invalid-email'); // Invalid email to trigger empty valid recipients
    
    $entiteJuridique->method('getCode')
        ->willReturn('EJ001');
    
    $twig->method('render')
        ->willReturn('<html>Email content</html>');
    
    // Expect logger to be called
    $logger->expects($this->once())
        ->method('warning')
        ->with(
            'Aucun destinataire valide pour l\'envoi d\'email',
            $this->callback(function ($context) {
                return isset($context['subject']) && 
                       $context['subject'] === '[EJ001] Importation automatique d\'affectations réussie';
            })
        );
    
    // Expect mailer not to be called
    $mailer->expects($this->never())
        ->method('send');
    
    // Create service and call method
    $service = new AffectationNotificationEmailService($mailer, $twig, $logger);
    $service->sendAffectationAutoImportSuccessNotification($entiteJuridique, []);
});

it('handles mailer exceptions gracefully', function () {
    // Create mocks
    $mailer = $this->createMock(MailerInterface::class);
    $twig = $this->createMock(Environment::class);
    $logger = $this->createMock(LoggerInterface::class);
    $entiteJuridique = $this->createMock(EntiteJuridique::class);
    
    // Configure mocks
    $entiteJuridique->method('isNotifierErreurAuAdminPourAffectation')
        ->willReturn(true);
    
    $entiteJuridique->method('isNotifierErreurAuResponssableDamAffectation')
        ->willReturn(false);
    
    $entiteJuridique->method('getAdminEmail')
        ->willReturn('<EMAIL>');
    
    $entiteJuridique->method('getCode')
        ->willReturn('EJ001');
    
    $twig->method('render')
        ->willReturn('<html>Email content</html>');
    
    // Mailer throws exception
    $mailer->method('send')
        ->willThrowException(new \Exception('Mailer error'));
    
    // Expect logger to be called for error
    $logger->expects($this->once())
        ->method('error')
        ->with(
            'Erreur lors de l\'envoi d\'email de notification',
            $this->callback(function ($context) {
                return isset($context['error']) && 
                       $context['error'] === 'Mailer error';
            })
        );
    
    // Create service and call method
    $service = new AffectationNotificationEmailService($mailer, $twig, $logger);
    
    // This should not throw an exception
    $service->sendAffectationAutoImportSuccessNotification($entiteJuridique, []);
});