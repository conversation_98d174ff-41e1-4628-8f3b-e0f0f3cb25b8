<?php

namespace App\Command;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Contracts\HttpClient\HttpClientInterface;

/**
 * Commande pour rafraîchir les agents via le data-integrator.
 * 
 * Cette commande effectue deux opérations principales :
 * 1. Nettoie le cache global du data-integrator
 * 2. Déclenche un rafraîchissement des agents
 *  Commande:
 *  app:data-integrator:global-cache-clear
 * Utile pour forcer une synchronisation des données sans effectuer une importation complète.
 */
#[AsCommand(
    name: 'app:data-integrator:global-cache-clear',
    description: 'Nettoie le cache global du data-integrator et rafraîchit les agents',
)]
class DataIntegratorGlobalCacheClearCommand extends Command
{
    public function __construct(
        private readonly HttpClientInterface $client,
        #[Autowire('%data_integrator_base_uri%')] private readonly string $baseUri,
    ) {
        parent::__construct();
    }
    
    /**
     * Nettoie le cache global du data-integrator et retourne la réponse de l'API
     * 
     * @return array La réponse de l'API contenant success et ejcode
     * @throws \Exception Si une erreur survient lors de l'appel à l'API
     */
    public function clearGlobalCacheAndGetResponse(): array
    {
        // Étape 1: Nettoyage du cache global
        $clearCache = $this->client->request('GET', $this->baseUri . '/global-cache/clear')->toArray();
        if (($clearCache['success'] ?? null) !== true) {
            throw new \Exception('Erreur lors du clear du cache global [data-integrator] : ' . ($clearCache['message'] ?? ''));
        }
        
        return $clearCache;
    }

    protected function configure(): void
    {
        $this
            ->setHelp('Cette commande nettoie le cache global du data-integrator et rafraîchit les agents sans effectuer une importation complète.');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->title('Nettoyage du cache global du data-integrator');

        try {
            // Cache pour les entités juridiques (déclaration pour compatibilité avec le code existant)
            $ejCache = [];
            
            // Étape 1: Nettoyage du cache global
            $io->section('Nettoyage du cache global du data-integrator');
            $clearCache = $this->client->request('GET', $this->baseUri . '/global-cache/clear')->toArray();
            if (($clearCache['success'] ?? null) !== true) {
                throw new \Exception('Erreur lors du clear du cache global [data-integrator] : ' . ($clearCache['message'] ?? ''));
            }
            $io->success('Cache global nettoyé avec succès');
            
            // Étape 2: Rafraîchissement des agents
//            $io->section('Rafraîchissement des agents');
//            $refresh = $this->client->request('GET', $this->baseUri . '/agents/refresh')->toArray();
//            if (($refresh['success'] ?? null) !== true) {
//                throw new \Exception('Erreur lors du refresh des agents : ' . ($refresh['message'] ?? ''));
//            }
//            $io->success('Agents rafraîchis avec succès');

            //$io->success('Opération terminée avec succès');
            return Command::SUCCESS;
        } catch (\Throwable $e) {
            $io->error('Une erreur est survenue : ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}