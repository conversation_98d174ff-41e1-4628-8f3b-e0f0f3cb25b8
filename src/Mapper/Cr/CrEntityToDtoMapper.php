<?php

namespace App\Mapper\Cr;

use App\ApiResource\Structure\CrDto;
use App\ApiResource\Structure\PoleDto;
use App\Entity\Structure\Cr;
use Symfonycasts\MicroMapper\AsMapper;
use Symfonycasts\MicroMapper\MapperInterface;
use Symfonycasts\MicroMapper\MicroMapperInterface;

#[AsMapper(from: Cr::class, to: CrDto::class)]
class CrEntityToDtoMapper implements MapperInterface
{
    public function __construct(private MicroMapperInterface $microMapper) {}

    public function load(object $from, string $toClass, array $context): object
    {
        $cr = $from;
        assert($cr instanceof Cr);

        $dto = new CrDto();
        $dto->id = $cr->getId(); // On charge uniquement l'ID ici

        return $dto;
    }

    public function populate(object $from, object $to, array $context): object
    {
        $cr = $from;
        $dto = $to;
        assert($cr instanceof Cr);
        assert($dto instanceof CrDto);

        $dto->dateCreation = $cr->getDateCreation();
        $dto->etab = $cr->getEtab();
        $dto->crcode = $cr->getCrcode();
        $dto->datdeb = $cr->getDatdeb();
        $dto->datfin = $cr->getDatfin();
        $dto->libelle = $cr->getLibelle();
        $dto->nomresp = $cr->getNomresp();
        $dto->polecode = $cr->getPolecode();
        $dto->pfuser = $cr->getPfuser();
        $dto->dmdacre = $cr->getDmdacre();
        $dto->dmdamaj = $cr->getDmdamaj();

        // Champs d'horodatage
        $dto->isActif = $cr->isActive();
        $dto->validFrom = $cr->getValidFrom();
        $dto->validTo = $cr->getValidTo();
        $dto->periodeType = $cr->getPeriodeType();
        $dto->source = $cr->getSource();

        // Convertir Pole en URI API Platform
        if ($cr->getPole()) {
            $dto->pole = $this->microMapper->map($cr->getPole(), PoleDto::class, [
                MicroMapperInterface::MAX_DEPTH => 0, // On ne charge que l'ID
            ]);
        }

        return $dto;
    }
}