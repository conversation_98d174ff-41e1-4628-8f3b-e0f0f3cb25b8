<?php

namespace App\ApiResource\Activite;

use ApiPlatform\Doctrine\Orm\Filter\SearchFilter;
use ApiPlatform\Doctrine\Orm\State\Options;
use ApiPlatform\Metadata\ApiFilter;
use ApiPlatform\Metadata\ApiProperty;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\GetCollection;
use ApiPlatform\Metadata\Link;
use ApiPlatform\Metadata\Post;
use App\ApiResource\Base\BaseDto;
use App\ApiResource\Praticien\AgentDto;
use App\Domain\Enum\PeriodeType;
use App\Entity\Activite\GardesAstreintes;
use App\State\Batch\GardesAstreintesBatchProcessor;
use App\State\EntityClassDtoStateProcessor;
use App\State\EntityToDtoStateProvider;

#[ApiResource(
    shortName: 'gardes-astreintes',
    operations: [
        new Get(),
        new GetCollection(
            uriTemplate: '/gardes-astreintes',
        ),
        new GetCollection(
            uriTemplate: '/{ejcode}/gardes-astreintes',
            uriVariables: [
                'ejcode' => new Link(
                    schema: ['type' => 'string'],
                    description: 'Code de l\'entité juridique',
                    required: true
                )
            ]
        ),
        new Post(
            formats: ['json' => ['application/json']]
        ),
        new Post(
            uriTemplate: '/gardes-astreintes/batch',
            formats: ['json' => ['application/json']],
            processor: GardesAstreintesBatchProcessor::class // pour le traitement en batch CSV (juste pour accepter un tableau d'objets)
        )
    ],
    paginationItemsPerPage: 10,
    provider: EntityToDtoStateProvider::class,
    processor: EntityClassDtoStateProcessor::class,
    stateOptions: new Options(entityClass: GardesAstreintes::class),
)]
#[ApiFilter(SearchFilter::class, properties: [
    'typeGrade' => 'partial',
    'agent.hrUser' => 'exact'  // Filtre sur le hrUser de l'agent lié
])]
class GardesAstreintesDto extends BaseDto
{
    #[ApiProperty(description: "Date de début de la période", example: "2024-03-01T00:00:00")]
    public ?\DateTimeInterface $dateDebut = null;

    #[ApiProperty(description: "Date de fin de la période", example: "2024-03-31T23:59:59")]
    public ?\DateTimeInterface $dateFin = null;

    #[ApiProperty(description: "Total des gardes", example: 10)]
    public ?int $totalGarde = null;

    #[ApiProperty(description: "Total des astreintes", example: 5)]
    public ?int $totalAstreinte = null;

    #[ApiProperty(description: "Date de la garde", example: "2024-03-15T18:00:00")]
    public ?\DateTimeInterface $dateGarde = null;

    #[ApiProperty(description: "Type de garde", example: "Nuit")]
    public string $typeGarde;

    #[ApiProperty(description: "Agent lié aux gardes/astreintes, transmis par hrUser", example: "u12345")]
    public ?string $agentHrU = null;

    #[ApiProperty(description: "Dernière affectation connue de l'agent", readable: true, writable: false, example: "Service de cardiologie")]
    public ?string $derniereAffectionDeAgent = null;

    #[ApiProperty(description: "Nom, prénom et titre de l'agent", readable: true, writable: false, example: "Dr. John Doe")]
    public ?string $agentNomPrenomTitre = null;


    // Champs du trait HorodatageTrait (générés automatiquement)
    #[ApiProperty(description: "Date de début de validité", readable: true, writable: false, example: "2024-01-01T00:00:00")]
    public ?\DateTimeInterface $validFrom = null;

    #[ApiProperty(description: "Date de fin de validité", readable: true, writable: false, example: "2024-12-31T23:59:59")]
    public ?\DateTimeInterface $validTo = null;

    #[ApiProperty(description: "Type de période", readable: true, writable: false, example: "MENSUEL")]
    public string $periodeType = PeriodeType::MENSUEL;

    #[ApiProperty(description: "Source des données", readable: true, writable: false, example: "Hôpital")]
    public ?string $source = null;
}
