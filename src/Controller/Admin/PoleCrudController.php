<?php

namespace App\Controller\Admin;

use App\Entity\Structure\Pole;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\BooleanField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateTimeField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ChoiceField;
use EasyCorp\Bundle\EasyAdminBundle\Field\FormField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;

class PoleCrudController extends AbstractCrudController
{
    public static function getEntityFqcn(): string
    {
        return Pole::class;
    }

    public function configureActions(Actions $actions): Actions
    {
        $importCsv = Action::new('importCsv', '📥 Importer CSV')
            ->linkToRoute('admin_import_structures_csv', ['entity' => $this->getEntityFqcn()])
            ->setCssClass('btn btn-primary');

        return $actions
            ->add(Crud::PAGE_NEW, $importCsv)
            ->add(Crud::PAGE_INDEX, Action::DETAIL);
    }

    public function configureFields(string $pageName): iterable
    {
        $fields = [
            TextField::new('id')->hideOnForm(),

            TextField::new('etab')
                ->setLabel('Établissement')
                ->setHelp('Code de l\'établissement (max 3 caractères)')
                ->setFormTypeOption('attr', ['maxlength' => 3])
                ->hideOnIndex(),

            TextField::new('polecode')
                ->setLabel('Code du Pôle')
                ->setHelp('Code unique du pôle (max 3 caractères)')
                ->setFormTypeOption('attr', ['maxlength' => 3]),

            TextField::new('libelle')
                ->setLabel('Libellé du Pôle')
                ->setHelp('Nom du pôle hospitalier'),

            FormField::addPanel('Période de validité (SCD TYPE2)')
                ->setIcon('fas fa-history')
                ->setHelp('SCD TYPE2: 
                Conserve l\'historique complet des changements en créant de nouveaux enregistrements 
                avec des périodes de validité définies par une date de début et une date de fin. 
                Ces champs déterminent quand cet enregistrement est valide dans le système.'),

            DateTimeField::new('datdeb')
                ->setLabel('Date de début')
                ->setFormat('d/M/Y')
                ->setHelp('Date à partir de laquelle cet enregistrement est valide')
                ->hideOnIndex(),

            DateTimeField::new('datfin')
                ->setLabel('Date de fin')
                ->setFormat('d/M/Y')
                ->setHelp('Date jusqu\'à laquelle cet enregistrement est valide. Laissez vide si l\'enregistrement est toujours actif.'),

            TextField::new('pfuser')
                ->setLabel('Utilisateur PF')
                ->setHelp('Ex: Utilisateur ayant créé/modifié l\'enregistrement. Vous pouvez utiliser ce champ pour garder une information pertinente pour le pôle, comme le chef de pôle pour cette période.')
                ->hideOnIndex(),

            DateTimeField::new('dmdacre')
                ->setLabel('Date de création')
                ->setFormat('d/M/Y')
                ->setFormTypeOption('mapped', false)
                ->setValue(new \DateTime('now'))
                ->hideOnForm()
                ->hideOnIndex(),

            BooleanField::new('is_actif')
                ->setLabel('Est actif')
                ->hideOnIndex(),

            ChoiceField::new('periodeType')
                ->setLabel('Type de période')
                ->setChoices([
                    'Hebdomadaire' => 'HEBDOMADAIRE',
                    'Mensuel' => 'MENSUEL',
                    'Annuel' => 'ANNUEL',
                ])
                ->setHelp('Période à laquelle cet enregistrement est amené à changer.')
                ->hideOnIndex(),

            TextField::new('source')
                ->setLabel('Source')
                ->setHelp('Origine de la donnée')
                ->hideOnIndex(),

            AssociationField::new('hopital')
                ->setLabel('Hôpital')
                ->setHelp('Hôpital auquel appartient ce pôle'),

            AssociationField::new('crs')
                ->setLabel('CRs associés')
                ->setHelp('Centres de Responsabilité rattachés à ce pôle')
                ->setFormTypeOption('by_reference', false)
                ->hideOnIndex(),
        ];

        // Add validFrom, validTo, and dmdamaj fields only for edit and detail pages
        if ($pageName !== Crud::PAGE_NEW) {
            $fields[] = DateTimeField::new('dmdamaj')
                ->setLabel('Date de mise à jour')
                ->setFormat('d/M/Y')
                ->setValue(new \DateTime('now'))
                ->hideOnIndex();
        }

        return $fields;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setEntityLabelInSingular('Pôle')
            ->setEntityLabelInPlural('Pôles')
            ->setPageTitle('index', 'Gestion des Pôles')
            ->setPageTitle('detail', 'Détails du Pôle')
            ->setPageTitle('new', 'Créer un nouveau Pôle')
            ->setPageTitle('edit', 'Modifier le Pôle')
            ->setHelp('index', '<strong>Astuce :</strong> Pour gagner de la place, double-cliquez sur la barre de séparation pour masquer ou afficher le menu latéral.')
            ->showEntityActionsInlined();
    }
    
    /**
     * Custom logic to handle entity creation
     * 
     * Cette méthode génère automatiquement les champs validFrom et validTo
     * à partir des champs datdeb et datfin lors de la création d'un pôle.
     */
    public function persistEntity(\Doctrine\ORM\EntityManagerInterface $entityManager, $entityInstance): void
    {
        if ($entityInstance instanceof \App\Entity\Structure\Pole) {
            // Set dmdacre (date de création) to current date if not already set
            if ($entityInstance->getDmdacre() === null) {
                $entityInstance->setDmdacre(new \DateTime('now'));
            }
            
            // Generate validFrom from datdeb
            if ($entityInstance->getDatdeb() !== null) {
                $entityInstance->setValidFrom($entityInstance->getDatdeb());
            }
            
            // Generate validTo from datfin
            if ($entityInstance->getDatfin() !== null) {
                $entityInstance->setValidTo($entityInstance->getDatfin());
            }
        }
        
        // Call parent method to persist the entity
        parent::persistEntity($entityManager, $entityInstance);
    }
    
    /**
     * Custom logic to handle entity updates
     * 
     * Cette méthode met à jour les champs validFrom et validTo
     * à partir des champs datdeb et datfin lors de la modification d'un pôle.
     */
    public function updateEntity(\Doctrine\ORM\EntityManagerInterface $entityManager, $entityInstance): void
    {
        if ($entityInstance instanceof \App\Entity\Structure\Pole) {
            // Generate validFrom from datdeb
            if ($entityInstance->getDatdeb() !== null) {
                $entityInstance->setValidFrom($entityInstance->getDatdeb());
            }
            
            // Generate validTo from datfin
            if ($entityInstance->getDatfin() !== null) {
                $entityInstance->setValidTo($entityInstance->getDatfin());
            }
        }
        
        // Call parent method to update the entity
        parent::updateEntity($entityManager, $entityInstance);
    }
}
