<?php

namespace App\Mapper\EntiteJuridique;

use App\ApiResource\Structure\EntiteJuridiqueDto;
use App\Entity\Structure\EntiteJuridique;
use App\Repository\EntiteJuridiqueRepository;
use App\Repository\TenantRepository;
use Doctrine\DBAL\Driver\PDO\Exception;
use Symfonycasts\MicroMapper\AsMapper;
use Symfonycasts\MicroMapper\MapperInterface;

#[AsMapper(from: EntiteJuridiqueDto::class, to: EntiteJuridique::class)]
class EntiteJuridiqueDtoToEntityMapper implements MapperInterface
{
    public function __construct(
        private EntiteJuridiqueRepository $hopitalRepository,
        private TenantRepository          $tenantRepository
    ) {}

    public function load(object $from, string $toClass, array $context): object
    {
        $dto = $from;
        assert($dto instanceof EntiteJuridiqueDto);

        // On récupère l'entité existante si un ID est fourni, sinon on en crée une nouvelle
        $hopital = $dto->id ? $this->hopitalRepository->find($dto->id) : new EntiteJuridique();
        if (!$hopital) {
            throw new Exception('EntiteJuridique non trouvé');
        }

        return $hopital;
    }

    public function populate(object $from, object $to, array $context): object
    {
        $dto = $from;
        $hopital = $to;
        assert($dto instanceof EntiteJuridiqueDto);
        assert($hopital instanceof EntiteJuridique);

        $hopital->setCode($dto->code);
        $hopital->setNom($dto->nom);
        $hopital->setAdresse($dto->adresse);
        $hopital->setTelephone($dto->telephone);
        $hopital->setEmail($dto->email);
        $hopital->setDateCreation($dto->dateCreation);

        if ($dto->parametre !== null) {
            $hopital->setParametre($dto->parametre);
        }

        // Convertir l'IRI API Platform en entité Tenant
        if ($dto->tenant) {
            // Extraire l'ID depuis l'URI "/api/tenants/{id}"
            $tenantId = basename($dto->tenant);
            $tenant = $this->tenantRepository->find($tenantId);
            if (!$tenant) {
                throw new Exception('Tenant non trouvé');
            }
            $hopital->setTenant($tenant);
        }

        return $hopital;
    }
}
