# In all environments, the following files are loaded if they exist,
# the latter taking precedence over the former:
#
#  * .env                contains default values for the environment variables needed by the app
#  * .env.dev.local          uncommitted file with local overrides
#  * .env.$APP_ENV       committed environment-specific defaults
#  * .env.$APP_ENV.local uncommitted environment-specific overrides
#
# Real environment variables win over .env files.
#
# DO NOT DEFINE PRODUCTION SECRETS IN THIS FILE NOR IN ANY OTHER COMMITTED FILES.

###> symfony/framework-bundle ###
APP_ENV=dev
APP_SECRET=changeme_in_production ## genere moi par `composer dump-env prod` ou `openssl rand -hex 64``
###< symfony/framework-bundle ###

###> doctrine/doctrine-bundle ###
# Variables pour construire DATABASE_URL
DATABASE_USER=postgres
DATABASE_PASSWORD=changeme
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=supra_db

DATABASE_URL="postgresql://${DATABASE_USER}:${DATABASE_PASSWORD}@${DATABASE_HOST}:${DATABASE_PORT}/${DATABASE_NAME}?serverVersion=15&charset=utf8"
###< doctrine/doctrine-bundle ###

###> nelmio/cors-bundle ###
CORS_ALLOW_ORIGIN='^https?://(localhost|127\.0\.0\.1)(:[0-9]+)?$'
###< nelmio/cors-bundle ###

###> lexik/jwt-authentication-bundle ###
JWT_SECRET_KEY=%kernel.project_dir%/config/jwt/private.pem
JWT_PUBLIC_KEY=%kernel.project_dir%/config/jwt/public.pem
JWT_PASSPHRASE=changeme_in_production
###< lexik/jwt-authentication-bundle ###

###> JWT Firebase (legacy) ###
# JWT_SECRET should be defined in a local environment file (.env.*.local) and not committed to version control
###< JWT Firebase ###

###> symfony/messenger ###
MESSENGER_TRANSPORT_DSN=doctrine://default?auto_setup=0
###< symfony/messenger ###

###> symfony/cache ###
CACHE_DSN=redis://localhost:6379
API_CACHE_TTL=3600
###< symfony/cache ###

###> symfony/mailer ###
MAILER_DSN=sendmail://default
MAILER_FROM_EMAIL=<EMAIL>
###< symfony/mailer ###

###> LDAP Configuration ###
# LDAP (Active Directory) - Non bloquant pour les futures EJ
# Voir Structure/Trait/LdapConfigTrait.php pour plus d'infos
LDAP_HOST=ldap://changeme:389
LDAP_DOMAIN=changeme
LDAP_BASE_DN=DC=changeme,DC=local
###< LDAP Configuration ###

###> SUPRA Services ###
# Base URI for the Data Integrator API
DATA_INTEGRATOR_BASE_URI=http://localhost:3000/api
###< SUPRA Services ###