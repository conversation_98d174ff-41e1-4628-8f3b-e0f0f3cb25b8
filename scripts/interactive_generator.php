<?php
/**
 * Générateur interactif de données de test CSV
 * Usage: php interactive_generator.php
 */

require_once 'generate_mock_data.php';

class InteractiveGenerator
{
    private MockDataGenerator $generator;

    public function __construct()
    {
        $this->generator = new MockDataGenerator();
    }

    public function run(): void
    {
        $this->showBanner();
        
        do {
            $this->showMenu();
            $choice = $this->getInput("Votre choix (0-8): ");
            
            switch ($choice) {
                case '1':
                    $this->generateType('affectations');
                    break;
                case '2':
                    $this->generateType('sigaps');
                    break;
                case '3':
                    $this->generateType('liberal');
                    break;
                case '4':
                    $this->generateType('actes');
                    break;
                case '5':
                    $this->generateType('gardes');
                    break;
                case '6':
                    $this->generateAllNormal();
                    break;
                case '7':
                    $this->generateAllLarge();
                    break;
                case '8':
                    $this->quickTestSuite();
                    break;
                case '0':
                    echo "\n👋 Au revoir !\n\n";
                    return;
                default:
                    echo "\n❌ Choix invalide, veuillez réessayer.\n\n";
            }
            
            if ($choice !== '0') {
                $continue = $this->getInput("\nGénérer d'autres fichiers ? (o/n): ");
                if (!in_array(strtolower($continue), ['o', 'oui', 'y', 'yes'])) {
                    break;
                }
                echo "\n";
            }
        } while ($choice !== '0');
        
        $this->showFinalMessage();
    }

    private function showBanner(): void
    {
        echo "\n";
        echo "🎯 ========================================\n";
        echo "   GÉNÉRATEUR INTERACTIF DE DONNÉES CSV  \n";
        echo "   Format: UTF-8 (délimité par virgules)  \n";
        echo "   ========================================\n";
        echo "\n";
    }

    private function showMenu(): void
    {
        echo "Choisissez le type de données à générer :\n\n";
        echo "1. 📋 Affectations (uConnexion, dates, UF, etc.)\n";
        echo "2. 📊 SIGAPS (scores, catégories, publications)\n";
        echo "3. 💰 Liberal (actes libéraux, tarifs)\n";
        echo "4. 🏥 Actes (CCAM, NGAP, coefficients)\n";
        echo "5. 🌙 Gardes/Astreintes (planning, heures)\n";
        echo "6. 🎯 TOUT générer (4000 lignes chacun)\n";
        echo "7. 🚀 GROS VOLUME (10000 lignes chacun)\n";
        echo "8. ⚡ SUITE DE TESTS (volumes progressifs)\n";
        echo "0. ❌ Quitter\n\n";
    }

    private function generateType(string $type): void
    {
        $lines = $this->getInput("Nombre de lignes (défaut 4000): ");
        $lines = empty($lines) ? 4000 : (int)$lines;
        
        echo "\n🚀 Génération de {$lines} lignes pour {$type}...\n";
        
        $startTime = microtime(true);
        $method = 'generate' . ucfirst($type);
        $filename = $this->generator->$method($lines);
        $duration = round(microtime(true) - $startTime, 2);
        
        echo "✅ Fichier '{$filename}' généré en {$duration}s\n";
        echo "📄 Format: UTF-8 (délimité par virgules) - Compatible front\n";
        $this->showOptimizationTips($lines);
    }

    private function generateAllNormal(): void
    {
        echo "\n🎯 Génération de tous les types (4000 lignes chacun)...\n\n";

        $startTime = microtime(true);
        $files = $this->generator->generateAll(4000);
        $duration = round(microtime(true) - $startTime, 2);
        
        echo "\n✅ Tous les fichiers générés en {$duration}s :\n";
        foreach ($files as $type => $filename) {
            echo "   • {$type}: {$filename}\n";
        }

        echo "\n📄 Format: UTF-8 (délimité par virgules) - Compatible front\n";
        echo "💡 Parfait pour tester les optimisations batch !\n";
    }

    private function generateAllLarge(): void
    {
        echo "\n🚀 Génération de tous les types (10000 lignes chacun)...\n";
        echo "⚠️  Cela peut prendre quelques minutes...\n\n";
        
        $startTime = microtime(true);
        $files = $this->generator->generateAll(10000);
        $duration = round(microtime(true) - $startTime, 2);
        
        echo "\n✅ Tous les gros fichiers générés en {$duration}s :\n";
        foreach ($files as $type => $filename) {
            echo "   • {$type}: {$filename}\n";
        }

        echo "\n📄 Format: UTF-8 (délimité par virgules) - Compatible front\n";
        echo "🔥 Parfait pour tester les performances sur gros volumes !\n";
    }

    private function quickTestSuite(): void
    {
        echo "\n⚡ SUITE DE TESTS - Volumes progressifs\n";
        echo "=====================================\n\n";
        
        $testSizes = [
            50 => "Tests fonctionnels",
            500 => "Tests d'optimisation", 
            2000 => "Tests de performance"
        ];
        
        foreach ($testSizes as $size => $description) {
            echo "📊 {$description} ({$size} lignes)...\n";
            
            $startTime = microtime(true);
            $filename = $this->generator->generateAffectations($size);
            $duration = round(microtime(true) - $startTime, 2);
            
            echo "   ✅ {$filename} généré en {$duration}s\n";
            $this->showOptimizationTips($size);
            echo "\n";
        }
        
        echo "🎉 Suite de tests complète ! Testez dans cet ordre :\n";
        echo "   1. 50 lignes → Emails normaux attendus\n";
        echo "   2. 500 lignes → Emails désactivés + synthèse\n";
        echo "   3. 2000 lignes → Optimisations complètes\n";
    }

    private function showOptimizationTips(int $lines): void
    {
        if ($lines < 100) {
            echo "💡 Volume petit → Emails normaux, pas d'optimisations\n";
        } elseif ($lines < 1000) {
            echo "💡 Volume moyen → Emails désactivés, micro-transactions\n";
        } else {
            echo "💡 Gros volume → Optimisations complètes activées\n";
        }
    }

    private function showFinalMessage(): void
    {
        echo "\n🎉 Terminé ! Vos fichiers CSV sont prêts pour les tests.\n\n";
        echo "💡 CONSEILS D'UTILISATION :\n";
        echo "   • Petits volumes (<100) : Tests fonctionnels\n";
        echo "   • Volumes moyens (1000) : Tests de performance\n";
        echo "   • Gros volumes (10k+) : Tests d'optimisation\n\n";
        echo "📊 SURVEILLANCE :\n";
        echo "   • Logs : tail -f var/log/batch.log\n";
        echo "   • Emails : 1 seul email de synthèse pour gros volumes\n";
        echo "   • Mémoire : Utilisation stable même sur gros volumes\n\n";
        echo "🚀 Bon test de vos optimisations batch !\n\n";
    }

    private function getInput(string $prompt): string
    {
        echo $prompt;
        return trim(fgets(STDIN));
    }
}

// Exécution du générateur interactif
if (php_sapi_name() === 'cli') {
    $interactive = new InteractiveGenerator();
    $interactive->run();
} else {
    echo "Ce script doit être exécuté en ligne de commande.\n";
}
