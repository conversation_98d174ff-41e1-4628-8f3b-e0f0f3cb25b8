# Deployment Guide for SUPRA Application

This document provides guidance for deploying the SUPRA application in different environments (development, qualification, and production).

## Environment Configuration

The application uses environment-specific configuration files to adapt its behavior to different deployment environments:

- `.env`: Default configuration with development settings
- `.env.dev`: Development environment-specific settings
- `.env.qualif`: Qualification environment-specific settings
- `.env.prod`: Production environment-specific settings
- `.env.local`: Local development overrides (not committed to version control)

## Environment Variables

The following environment variables are used to configure the application:

- `APP_ENV`: The current environment (`dev`, `qualif`, or `prod`)
- `APP_DEBUG`: Whether to enable debug mode (`true` or `false`)
- `APP_SECRET`: Secret key used for security purposes ``php bin/console secrets:generate-keys``
- Database configuration (`DATABASE_URL`, etc.)
- LDAP configuration (`LDAP_HOST`, etc.)
- JWT configuration (`JWT_SECRET`, etc.)
- Cache configuration (`CACHE_DSN`, etc.)
- Mail configuration (`MAILER_DSN`, etc.)
- Service endpoints (`DATA_INTEGRATOR_BASE_URI`, etc.)
- CORS configuration (`CORS_ALLOW_ORIGIN`, etc.)

## Deployment Process

### 1. Development VM

For deployment to the development VM:

1. Ensure the `.env` file has `APP_ENV=dev`
2. Copy the `.env.dev` file to the server
3. Create a `.env.dev.local` file if needed for server-specific overrides
4. Run `composer install --no-dev` to install dependencies
5. Run `php bin/console cache:clear` to clear the cache
6. Run `php bin/console doctrine:migrations:migrate` to update the database schema

### 2. Qualification VM

For deployment to the qualification VM:

1. Ensure the `.env.qualif` file has `APP_ENV=qualif`
2. Copy the `.env.qualif` file to the server
3. Create a `.env.qualif.local` file if needed for server-specific overrides
4. Run `composer install --no-dev` to install dependencies
5. Run `APP_ENV=qualif php bin/console cache:clear` to clear the cache
6. Run `APP_ENV=qualif php bin/console doctrine:migrations:migrate` to update the database schema

### 3. Production VM

For deployment to the production VM:

1. Ensure the `.env.prod` file has `APP_ENV=prod`
2. Copy the `.env.prod` file to the server
3. Create a `.env.prod.local` file if needed for server-specific overrides
4. Run `composer install --no-dev` to install dependencies
5. Run `APP_ENV=prod php bin/console cache:clear` to clear the cache
6. Run `APP_ENV=prod php bin/console doctrine:migrations:migrate` to update the database schema

## Security Considerations

- Never commit sensitive information (passwords, API keys, etc.) to version control
- Use environment-specific secrets for production
- Consider using Symfony's secrets management for sensitive information
- Ensure that `APP_DEBUG` is set to `false` in production and qualification environments
- Use HTTPS in production and qualification environments
- Enable secure cookies in production and qualification environments

## Testing Environment Configuration

To test if the environment configuration is working correctly:

1. Run `php bin/console about` to see the current environment
2. Check the logs for any configuration-related errors
3. Test specific features that depend on environment-specific configuration
4. Verify that error pages are displayed correctly in each environment

## Troubleshooting

If you encounter issues with the environment configuration:

1. Check that the correct `.env` files are being loaded
2. Verify that environment variables are set correctly
3. Clear the cache after making changes to the environment configuration
4. Check the logs for any errors related to missing or invalid configuration
5. Ensure that the server has the necessary permissions to read the configuration files