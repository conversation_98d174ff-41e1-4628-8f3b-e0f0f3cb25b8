{
    frankenphp {
        # Configuration globale de FrankenPHP
        num_threads 4
    }
    auto_https off
    log {
        level INFO
        output file var/log/caddy_access.log
    }
}

:8080 {
    root * public

    # Configuration PHP avec FrankenPHP
    php_server {
        # Utiliser resolve_root_symlink si nécessaire pour WSL
        resolve_root_symlink

        # Variables PHP pour augmenter les limites
        env PHP_INI_MAX_EXECUTION_TIME 300
        env PHP_INI_MEMORY_LIMIT 1024M
        env PHP_INI_POST_MAX_SIZE 100M
        env PHP_INI_UPLOAD_MAX_FILESIZE 100M
        env PHP_INI_ERROR_REPORTING "E_ALL & ~E_DEPRECATED & ~E_STRICT"

        # Variables Symfony
        env APP_ENV dev
        env APP_DEBUG 1
        env SYMFONY_DEPRECATIONS_HELPER weak
    }

    # Gestion des fichiers statiques
    encode gzip

    # Headers de sécurité
    header {
        X-Frame-Options "DENY"
        X-Content-Type-Options "nosniff"
        X-XSS-Protection "1; mode=block"
    }
}