<?php

namespace App\Entity\Praticien;

use App\Entity\Base\BaseEntity;
use App\Entity\Structure\Ufs;
use App\Entity\Trait\HorodatageTrait;
use App\Entity\Praticien\Trait\DamAffectationTrait;
use App\Repository\AgentUfsRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

/**
 *  AgentUfs alias les affectations des agents aux UFS
 */
#[ORM\Entity(repositoryClass: AgentUfsRepository::class)]
class AgentUfs extends BaseEntity
{
    use HorodatageTrait;
    use DamAffectationTrait;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $date_debut = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $date_fin = null;

    #[ORM\ManyToOne(targetEntity: Agent::class, inversedBy: "agentUfs")]
    #[ORM\JoinColumn(nullable: false, onDelete: "CASCADE")]
    private ?Agent $agent = null;

    #[ORM\ManyToOne(targetEntity: Ufs::class, inversedBy: "praticiensUfs")]
    #[ORM\JoinColumn(nullable: false, onDelete: "CASCADE")]
    private ?Ufs $ufs = null;

    public function __construct()
    {
        parent::__construct();
    }
    public function getDateDebut(): ?\DateTimeInterface
    {
        return $this->date_debut;
    }

    public function setDateDebut(?\DateTimeInterface $date_debut): static
    {
        $this->date_debut = $date_debut;
        return $this;
    }

    public function getDateFin(): ?\DateTimeInterface
    {
        return $this->date_fin;
    }

    public function setDateFin(?\DateTimeInterface $date_fin): static
    {
        $this->date_fin = $date_fin;
        return $this;
    }

    public function getAgent(): ?Agent
    {
        return $this->agent;
    }

    public function setAgent(?Agent $agent): static
    {
        $this->agent = $agent;
        return $this;
    }

    public function getUfs(): ?Ufs
    {
        return $this->ufs;
    }

    public function setUfs(?Ufs $ufs): static
    {
        $this->ufs = $ufs;
        return $this;
    }
}