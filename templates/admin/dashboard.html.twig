{% extends '@EasyAdmin/page/content.html.twig' %}

{% block main %}
    <div class="content-wrapper">
        <div class="content">
            <h1 class="title"> Tableau de Bord Administrateur GHT</h1>

            <!-- Statistiques générales -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title">{{ stats.total_tenants }}</h4>
                                    <p class="card-text">Tenants</p>
                                </div>
                                <div>
                                    <i class="fas fa-building fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title">{{ stats.total_hopitaux }}</h4>
                                    <p class="card-text">Entités Juridiques</p>
                                </div>
                                <div>
                                    <i class="fas fa-hospital fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title">{{ stats.total_agents }}</h4>
                                    <p class="card-text">Agents Présents</p>
                                </div>
                                <div>
                                    <i class="fas fa-users fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title">{{ stats.total_pole_actifs }}</h4>
                                    <p class="card-text">Pôles Actifs</p>
                                </div>
                                <div>
                                    <i class="fas fa-project-diagram fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tableau des tenants et leurs statistiques -->
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Répartition par Tenant</h3>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>Tenant</th>
                                            <th>Hôpitaux</th>
                                            <th>Pôles</th>
                                            <th>Services</th>
                                            <th>Agents</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for tenant_stat in tenant_stats %}
                                        <tr>
                                            <td><strong>{{ tenant_stat.nom }}</strong></td>
                                            <td><span class="badge bg-primary">{{ tenant_stat.hopitaux }}</span></td>
                                            <td><span class="badge bg-info">{{ tenant_stat.poles }}</span></td>
                                            <td><span class="badge bg-success">{{ tenant_stat.services }}</span></td>
                                            <td><span class="badge bg-warning">{{ tenant_stat.agents }}</span></td>
                                            <td>
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check-circle"></i> Actif
                                                </span>
                                            </td>
                                        </tr>
                                        {% else %}
                                        <tr>
                                            <td colspan="6" class="text-center text-muted">
                                                <i class="fas fa-info-circle"></i> Aucun tenant trouvé
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Activités récentes -->
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Dernières Importations</h3>
                        </div>
                        <div class="card-body">
                            <table class="table table-sm">
                                <tr>
                                    <td><i class="fas fa-upload text-success"></i> Dernière importation</td>
                                    <td class="text-end"><span class="badge bg-success">{{ import_stats.last_import_date|date('d/m/Y') }}</span></td>
                                </tr>
                                <tr>
                                    <td><i class="fas fa-calendar text-blue"></i> Cette semaine</td>
                                    <td class="text-end"><span class="badge bg-blue">{{ import_stats.imports_this_week }}</span></td>
                                </tr>
                                <tr>
                                    <td><i class="fas fa-chart-line text-warning"></i> Ce mois</td>
                                    <td class="text-end"><span class="badge bg-warning">{{ import_stats.imports_this_month }}</span></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-user-tag"></i> Agents par Rôle
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Niveau</th>
                                            <th>Rôle</th>
                                            <th class="text-end">Nombre</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for role_stat in role_stats %}
                                        <tr>
                                            <td class="text-center">
                                                {% if role_stat.level == 5 %}
                                                    <span class="badge bg-danger">{{ role_stat.level }}</span>
                                                {% elseif role_stat.level == 4 %}
                                                    <span class="badge bg-warning">{{ role_stat.level }}</span>
                                                {% elseif role_stat.level == 3 %}
                                                    <span class="badge bg-info">{{ role_stat.level }}</span>
                                                {% elseif role_stat.level == 2 %}
                                                    <span class="badge bg-primary">{{ role_stat.level }}</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">{{ role_stat.level }}</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <i class="fas fa-user-tag"></i> {{ role_stat.role }}
                                            </td>
                                            <td class="text-end">
                                                <span class="badge bg-secondary">{{ role_stat.count }}</span>
                                            </td>
                                        </tr>
                                        {% else %}
                                        <tr>
                                            <td colspan="3" class="text-center text-muted">
                                                <i class="fas fa-info-circle"></i> Aucune donnée disponible
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            <div class="mt-3">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle"></i>
                                    <strong>Hiérarchie :</strong> 5 > 4 > 3 > 2 > 1
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistiques de connexion par hôpital -->
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-wifi text-success"></i> Statistiques de Connexion par Hôpital (EJ)
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>Hôpital (EJ)</th>
                                            <th>Code</th>
                                            <th class="text-center">Agents Total</th>
                                            <th class="text-center">Agents Connectés</th>
                                            <th class="text-center">Total Connexions</th>
                                            <th class="text-center">Moy. Connexions/Agent</th>
                                            <th class="text-center">Taux d'Activité</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for conn_stat in connection_stats %}
                                        <tr>
                                            <td>
                                                <strong>{{ conn_stat.hopital_nom }}</strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">{{ conn_stat.hopital_code }}</span>
                                            </td>
                                            <td class="text-center">
                                                <span class="badge bg-info">{{ conn_stat.total_agents }}</span>
                                            </td>
                                            <td class="text-center">
                                                <span class="badge bg-success">{{ conn_stat.agents_connected }}</span>
                                            </td>
                                            <td class="text-center">
                                                <span class="badge bg-primary">{{ conn_stat.total_connections }}</span>
                                            </td>
                                            <td class="text-center">
                                                <span class="badge bg-warning">{{ conn_stat.avg_connections }}</span>
                                            </td>
                                            <td class="text-center">
                                                {% set activity_rate = conn_stat.total_agents > 0 ? (conn_stat.agents_connected / conn_stat.total_agents * 100) : 0 %}
                                                {% if activity_rate >= 75 %}
                                                    <span class="badge bg-success">{{ activity_rate|round }}%</span>
                                                {% elseif activity_rate >= 50 %}
                                                    <span class="badge bg-warning">{{ activity_rate|round }}%</span>
                                                {% else %}
                                                    <span class="badge bg-danger">{{ activity_rate|round }}%</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% else %}
                                        <tr>
                                            <td colspan="7" class="text-center text-muted">
                                                <i class="fas fa-info-circle"></i> Aucune donnée de connexion disponible
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            <div class="mt-3">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle"></i>
                                    <strong>Taux d'activité :</strong>
                                    <span class="badge bg-success">≥75%</span> Excellent
                                    <span class="badge bg-warning">50-74%</span> Moyen
                                    <span class="badge bg-danger">&lt;50%</span> Faible
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Top 5 des utilisateurs les plus connectés -->
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-trophy text-warning"></i> Top 5 - Utilisateurs les Plus Connectés
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>Rang</th>
                                            <th>Utilisateur</th>
                                            <th>Email</th>
                                            <th>Entité Juridique</th>
                                            <th class="text-center">Total Connexions</th>
                                            <th class="text-center">Première Connexion</th>
                                            <th class="text-center">Dernière Connexion</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for index, user in top_connected_users %}
                                        <tr>
                                            <td class="text-center">
                                                {% if loop.index == 1 %}
                                                    <span class="badge bg-warning text-dark"><i class="fas fa-trophy"></i> #{{ loop.index }}</span>
                                                {% elseif loop.index == 2 %}
                                                    <span class="badge bg-secondary"><i class="fas fa-medal"></i> #{{ loop.index }}</span>
                                                {% elseif loop.index == 3 %}
                                                    <span class="badge bg-warning"><i class="fas fa-medal"></i> #{{ loop.index }}</span>
                                                {% else %}
                                                    <span class="badge bg-primary">#{{ loop.index }}</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <strong>{{ user.agent_prenom }} {{ user.agent_nom }}</strong>
                                            </td>
                                            <td>
                                                <small class="text-muted">{{ user.agent_email }}</small>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">{{ user.hopital_code }}</span>
                                                <small class="text-muted d-block">{{ user.hopital_nom }}</small>
                                            </td>
                                            <td class="text-center">
                                                <span class="badge bg-success fs-6">{{ user.total_connections }}</span>
                                            </td>
                                            <td class="text-center">
                                                {% if user.first_connection %}
                                                    <small class="text-muted">{{ user.first_connection|date('d/m/Y H:i') }}</small>
                                                {% else %}
                                                    <small class="text-muted">N/A</small>
                                                {% endif %}
                                            </td>
                                            <td class="text-center">
                                                {% if user.last_connection %}
                                                    <small class="text-muted">{{ user.last_connection|date('d/m/Y H:i') }}</small>
                                                {% else %}
                                                    <small class="text-muted">N/A</small>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% else %}
                                        <tr>
                                            <td colspan="7" class="text-center text-muted">
                                                <i class="fas fa-info-circle"></i> Aucun utilisateur avec des connexions trouvé
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            <div class="mt-3">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle"></i>
                                    Ce classement montre les 5 utilisateurs les plus actifs sur la plateforme Supra.
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
