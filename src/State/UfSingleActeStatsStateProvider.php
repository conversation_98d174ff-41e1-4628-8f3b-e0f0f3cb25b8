<?php

namespace App\State;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use App\ApiResource\Activite\UfSingleActeStatsDto;
use App\Repository\ActesRepository;
use App\Repository\UfsRepository;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Contracts\Cache\CacheInterface;
use Symfony\Contracts\Cache\ItemInterface;

/**
 * StateProvider pour les statistiques UF d'un acte spécifique
 * 
 * Calcule la répartition d'un acte médical par Unité Fonctionnelle (UF) 
 * sur 3 périodes temporelles avec cache Redis.
 * 
 * Route: GET /api/actes/{id}/uf-single-stats
 * Paramètres: p1Start, p1End, p2Start, p2End, p3Start, p3End
 * 
 * Exemple Postman:
 * GET http://localhost:8000/api/actes/46C2DXI8VA/uf-single-stats?p1Start=2024-01-01&p1End=2024-09-31&p2Start=2023-01-01&p2End=2023-09-31&p3Start=2022-01-01&p3End=2022-09-31
 */
class UfSingleActeStatsStateProvider implements ProviderInterface
{
    public function __construct(
        private ActesRepository $actesRepository,
        private UfsRepository $ufsRepository,
        private RequestStack $requestStack,
        private CacheInterface $cache,
        #[Autowire('%env(int:API_CACHE_TTL)%')] private int $apiCacheTtl = 3600
    ) {}

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): object|array|null
    {
        $acteId = $uriVariables['id'] ?? null;
        if (!$acteId) {
            return [];
        }

        $request = $this->requestStack->getCurrentRequest();
        if (!$request) {
            return [];
        }

        // Récupération des paramètres de périodes depuis la query string
        $p1Start = $request->query->get('p1Start');
        $p1End = $request->query->get('p1End');
        $p2Start = $request->query->get('p2Start');
        $p2End = $request->query->get('p2End');
        $p3Start = $request->query->get('p3Start');
        $p3End = $request->query->get('p3End');

        // Création de la clé de cache unique
        $cacheKey = sprintf(
            'uf_single_acte_stats_%s_%s_%s_%s_%s_%s_%s',
            $acteId,
            $p1Start ?? 'null',
            $p1End ?? 'null',
            $p2Start ?? 'null',
            $p2End ?? 'null',
            $p3Start ?? 'null',
            $p3End ?? 'null'
        );

        return $this->cache->get($cacheKey, function (ItemInterface $item) use (
            $acteId, $p1Start, $p1End, $p2Start, $p2End, $p3Start, $p3End
        ) {
            $item->expiresAfter($this->apiCacheTtl);

            // Conversion des dates string en objets DateTime
            $p1StartDate = $p1Start ? new \DateTime($p1Start) : null;
            $p1EndDate = $p1End ? new \DateTime($p1End) : null;
            $p2StartDate = $p2Start ? new \DateTime($p2Start) : null;
            $p2EndDate = $p2End ? new \DateTime($p2End) : null;
            $p3StartDate = $p3Start ? new \DateTime($p3Start) : null;
            $p3EndDate = $p3End ? new \DateTime($p3End) : null;

            // Récupération de toutes les UF qui ont réalisé cet acte
            $ufsData = $this->actesRepository->getUfsForActe($acteId);

            if (empty($ufsData)) {
                return [];
            }

            // Calcul des totaux pour chaque période (pour les fréquences)
            $totalP1 = $p1StartDate && $p1EndDate ? 
                $this->actesRepository->getTotalRealisationsForActeInPeriod($acteId, $p1StartDate, $p1EndDate) : 0;
            $totalP2 = $p2StartDate && $p2EndDate ? 
                $this->actesRepository->getTotalRealisationsForActeInPeriod($acteId, $p2StartDate, $p2EndDate) : 0;
            $totalP3 = $p3StartDate && $p3EndDate ? 
                $this->actesRepository->getTotalRealisationsForActeInPeriod($acteId, $p3StartDate, $p3EndDate) : 0;

            $ufStats = [];

            foreach ($ufsData as $ufData) {
                // Calcul des nombres de réalisations pour chaque période
                $p1Count = $p1StartDate && $p1EndDate ? 
                    $this->actesRepository->sumRealisationsByActeAndUfForPeriod($acteId, $ufData['id'], $p1StartDate, $p1EndDate) : 0;
                $p2Count = $p2StartDate && $p2EndDate ? 
                    $this->actesRepository->sumRealisationsByActeAndUfForPeriod($acteId, $ufData['id'], $p2StartDate, $p2EndDate) : 0;
                $p3Count = $p3StartDate && $p3EndDate ? 
                    $this->actesRepository->sumRealisationsByActeAndUfForPeriod($acteId, $ufData['id'], $p3StartDate, $p3EndDate) : 0;

                $totalCount = $p1Count + $p2Count + $p3Count;
                $totalGlobal = $totalP1 + $totalP2 + $totalP3;

                // Calcul des fréquences (pourcentages)
                $p1Frequency = $totalP1 > 0 ? ($p1Count / $totalP1) * 100 : 0;
                $p2Frequency = $totalP2 > 0 ? ($p2Count / $totalP2) * 100 : 0;
                $p3Frequency = $totalP3 > 0 ? ($p3Count / $totalP3) * 100 : 0;
                $globalFrequency = $totalGlobal > 0 ? ($totalCount / $totalGlobal) * 100 : 0;

                // Inclusion uniquement des UF qui ont au moins une réalisation
                if ($totalCount > 0) {
                    $ufStats[] = new UfSingleActeStatsDto(
                        ufId: $ufData['id'],
                        ufCode: $ufData['ufcode'],
                        ufLibelle: $ufData['libelle'],
                        p1Count: $p1Count,
                        p1Frequency: round($p1Frequency, 1),
                        p2Count: $p2Count,
                        p2Frequency: round($p2Frequency, 1),
                        p3Count: $p3Count,
                        p3Frequency: round($p3Frequency, 1),
                        totalCount: $totalCount,
                        globalFrequency: round($globalFrequency, 1)
                    );
                }
            }

            // Tri par fréquence globale décroissante
            usort($ufStats, fn($a, $b) => $b->globalFrequency <=> $a->globalFrequency);

            return $ufStats;
        });
    }
}
