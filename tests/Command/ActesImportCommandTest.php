<?php

namespace App\Tests\Command;

use App\Command\ActesImportCommand;
use App\Domain\Service\Importation\ActeImportService;
use App\Domain\Service\Notification\ErrorNotificationEmailService;
use App\Entity\Structure\EntiteJuridique;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Doctrine\ORM\QueryBuilder;
use Doctrine\ORM\AbstractQuery;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Tester\CommandTester;

it('can be instantiated', function () {
    $acteImportService = $this->createMock(ActeImportService::class);
    $errorNotificationService = $this->createMock(ErrorNotificationEmailService::class);
    $entityManager = $this->createMock(EntityManagerInterface::class);
    
    $command = new ActesImportCommand($acteImportService, $errorNotificationService, $entityManager);
    
    expect($command)->toBeInstanceOf(ActesImportCommand::class);
});

it('configures the command correctly', function () {
    $acteImportService = $this->createMock(ActeImportService::class);
    $errorNotificationService = $this->createMock(ErrorNotificationEmailService::class);
    $entityManager = $this->createMock(EntityManagerInterface::class);
    
    $command = new ActesImportCommand($acteImportService, $errorNotificationService, $entityManager);
    
    // Use getName() method to get the command name
    expect($command->getName())->toBe('app:import:actes');
    
    // Test that the command has a 'date' option
    $definition = $command->getDefinition();
    expect($definition->hasOption('date'))->toBeTrue();
    expect($definition->getOption('date')->getShortcut())->toBe('d');
    expect($definition->getOption('date')->isValueOptional())->toBeTrue();
});

it('executes successfully with default parameters', function () {
    $acteImportService = $this->createMock(ActeImportService::class);
    $errorNotificationService = $this->createMock(ErrorNotificationEmailService::class);
    $entityManager = $this->createMock(EntityManagerInterface::class);
    
    // Mock importActes to return a success result
    $acteImportService->method('importActes')
        ->willReturn([
            'processed' => 10,
            'created' => 5,
            'updated' => 3,
            'errors' => []
        ]);
    
    $command = new ActesImportCommand($acteImportService, $errorNotificationService, $entityManager);
    $commandTester = new CommandTester($command);
    
    $exitCode = $commandTester->execute([]);
    
    expect($exitCode)->toBe(0);
    expect($commandTester->getDisplay())->toContain('Import terminé avec succès');
    expect($commandTester->getDisplay())->toContain('Traités');
    expect($commandTester->getDisplay())->toContain('10');
    expect($commandTester->getDisplay())->toContain('Créés');
    expect($commandTester->getDisplay())->toContain('5');
    expect($commandTester->getDisplay())->toContain('Mis à jour');
    expect($commandTester->getDisplay())->toContain('3');
    expect($commandTester->getDisplay())->toContain('Erreurs');
    expect($commandTester->getDisplay())->toContain('0');
});

it('executes successfully with date parameter', function () {
    $acteImportService = $this->createMock(ActeImportService::class);
    $errorNotificationService = $this->createMock(ErrorNotificationEmailService::class);
    $entityManager = $this->createMock(EntityManagerInterface::class);
    
    // Mock importActes to return a success result
    $acteImportService->method('importActes')
        ->with('2025-08-09')
        ->willReturn([
            'processed' => 10,
            'created' => 5,
            'updated' => 3,
            'errors' => []
        ]);
    
    $command = new ActesImportCommand($acteImportService, $errorNotificationService, $entityManager);
    $commandTester = new CommandTester($command);
    
    $exitCode = $commandTester->execute([
        '--date' => '2025-08-09'
    ]);
    
    expect($exitCode)->toBe(0);
    expect($commandTester->getDisplay())->toContain('Import pour la date : 2025-08-09');
    expect($commandTester->getDisplay())->toContain('Import terminé avec succès');
});

it('handles invalid date parameter', function () {
    $acteImportService = $this->createMock(ActeImportService::class);
    $errorNotificationService = $this->createMock(ErrorNotificationEmailService::class);
    $entityManager = $this->createMock(EntityManagerInterface::class);
    
    $command = new ActesImportCommand($acteImportService, $errorNotificationService, $entityManager);
    $commandTester = new CommandTester($command);
    
    $exitCode = $commandTester->execute([
        '--date' => 'invalid-date'
    ]);
    
    expect($exitCode)->toBe(1);
    expect($commandTester->getDisplay())->toContain('Format de date invalide');
});

it('handles errors from import service', function () {
    $acteImportService = $this->createMock(ActeImportService::class);
    $errorNotificationService = $this->createMock(ErrorNotificationEmailService::class);
    $entityManager = $this->createMock(EntityManagerInterface::class);
    
    // Mock importActes to throw an exception
    $acteImportService->method('importActes')
        ->willThrowException(new \Exception('Test error'));
    
    // Instead of overriding the protected method, let's mock the dependencies it uses
    // Mock the repository to return an empty array to avoid the foreach issue
    $repository = $this->createMock(EntityRepository::class);
    $entityManager->method('getRepository')
        ->with(EntiteJuridique::class)
        ->willReturn($repository);
    
    // Create a mock query builder
    $queryBuilder = $this->createMock(QueryBuilder::class);
    $repository->method('createQueryBuilder')
        ->willReturn($queryBuilder);
    
    // Configure query builder to return itself for where() method
    $queryBuilder->method('where')
        ->willReturnSelf();
    
    // Create a mock query
    $query = $this->getMockBuilder(\Doctrine\ORM\Query::class)
        ->disableOriginalConstructor()
        ->getMock();
    
    // Configure query builder to return the mock query
    $queryBuilder->method('getQuery')
        ->willReturn($query);
    
    // Configure query to return an empty array (no entities to notify)
    $query->method('getResult')
        ->willReturn([]);
    
    $command = new ActesImportCommand($acteImportService, $errorNotificationService, $entityManager);
    $commandTester = new CommandTester($command);
    
    $exitCode = $commandTester->execute([]);
    
    expect($exitCode)->toBe(1);
    expect($commandTester->getDisplay())->toContain('Erreur lors de l\'import : Test error');
});

it('notifies admins of errors', function () {
    $acteImportService = $this->createMock(ActeImportService::class);
    $errorNotificationService = $this->createMock(ErrorNotificationEmailService::class);
    $entityManager = $this->createMock(EntityManagerInterface::class);
    
    // Create a mock EntiteJuridique
    $entiteJuridique = $this->createMock(EntiteJuridique::class);
    
    // Create a mock repository
    $repository = $this->createMock(EntityRepository::class);
    $entityManager->method('getRepository')
        ->with(EntiteJuridique::class)
        ->willReturn($repository);
    
    // Create a mock query builder
    $queryBuilder = $this->createMock(QueryBuilder::class);
    $repository->method('createQueryBuilder')
        ->willReturn($queryBuilder);
    
    // Configure query builder to return itself for where() method
    $queryBuilder->method('where')
        ->willReturnSelf();
    
    // Create a mock query
    $query = $this->getMockBuilder(\Doctrine\ORM\Query::class)
        ->disableOriginalConstructor()
        ->getMock();
    
    // Configure query builder to return the mock query
    $queryBuilder->method('getQuery')
        ->willReturn($query);
    
    // Configure query to return our mock entity
    $query->method('getResult')
        ->willReturn([$entiteJuridique]);
    
    // Expect sendErrorNotificationToAdmin to be called
    $errorNotificationService->expects($this->once())
        ->method('sendErrorNotificationToAdmin')
        ->with(
            $this->equalTo($entiteJuridique),
            $this->equalTo('Import des actes'),
            $this->equalTo('Test error'),
            $this->callback(function ($context) {
                return isset($context['type']) && $context['type'] === 'Commande console' &&
                       isset($context['command']) && $context['command'] === 'app:import:actes';
            })
        );
    
    $command = new ActesImportCommand($acteImportService, $errorNotificationService, $entityManager);
    
    // Use reflection to call the protected method
    $reflectionMethod = new \ReflectionMethod(ActesImportCommand::class, 'notifyAdminsOfError');
    $reflectionMethod->setAccessible(true);
    $reflectionMethod->invoke($command, new \Exception('Test error'), null);
});

it('handles errors with date parameter', function () {
    $acteImportService = $this->createMock(ActeImportService::class);
    $errorNotificationService = $this->createMock(ErrorNotificationEmailService::class);
    $entityManager = $this->createMock(EntityManagerInterface::class);
    
    // Create a mock EntiteJuridique
    $entiteJuridique = $this->createMock(EntiteJuridique::class);
    
    // Create a mock repository
    $repository = $this->createMock(EntityRepository::class);
    $entityManager->method('getRepository')
        ->with(EntiteJuridique::class)
        ->willReturn($repository);
    
    // Create a mock query builder
    $queryBuilder = $this->createMock(QueryBuilder::class);
    $repository->method('createQueryBuilder')
        ->willReturn($queryBuilder);
    
    // Configure query builder to return itself for where() method
    $queryBuilder->method('where')
        ->willReturnSelf();
    
    // Create a mock query
    $query = $this->getMockBuilder(\Doctrine\ORM\Query::class)
        ->disableOriginalConstructor()
        ->getMock();
    
    // Configure query builder to return the mock query
    $queryBuilder->method('getQuery')
        ->willReturn($query);
    
    // Configure query to return our mock entity
    $query->method('getResult')
        ->willReturn([$entiteJuridique]);
    
    // Expect sendErrorNotificationToAdmin to be called with date parameter
    $errorNotificationService->expects($this->once())
        ->method('sendErrorNotificationToAdmin')
        ->with(
            $this->equalTo($entiteJuridique),
            $this->equalTo('Import des actes'),
            $this->equalTo('Test error'),
            $this->callback(function ($context) {
                return isset($context['date']) && $context['date'] === '2025-08-09';
            })
        );
    
    $command = new ActesImportCommand($acteImportService, $errorNotificationService, $entityManager);
    
    // Use reflection to call the protected method
    $reflectionMethod = new \ReflectionMethod(ActesImportCommand::class, 'notifyAdminsOfError');
    $reflectionMethod->setAccessible(true);
    $reflectionMethod->invoke($command, new \Exception('Test error'), '2025-08-09');
});

it('handles errors in notification service', function () {
    $acteImportService = $this->createMock(ActeImportService::class);
    $errorNotificationService = $this->createMock(ErrorNotificationEmailService::class);
    $entityManager = $this->createMock(EntityManagerInterface::class);
    
    // Create a mock EntiteJuridique
    $entiteJuridique = $this->createMock(EntiteJuridique::class);
    
    // Create a mock repository
    $repository = $this->createMock(EntityRepository::class);
    $entityManager->method('getRepository')
        ->with(EntiteJuridique::class)
        ->willReturn($repository);
    
    // Create a mock query builder
    $queryBuilder = $this->createMock(QueryBuilder::class);
    $repository->method('createQueryBuilder')
        ->willReturn($queryBuilder);
    
    // Configure query builder to return itself for where() method
    $queryBuilder->method('where')
        ->willReturnSelf();
    
    // Create a mock query
    $query = $this->getMockBuilder(\Doctrine\ORM\Query::class)
        ->disableOriginalConstructor()
        ->getMock();
    
    // Configure query builder to return the mock query
    $queryBuilder->method('getQuery')
        ->willReturn($query);
    
    // Configure query to return our mock entity
    $query->method('getResult')
        ->willReturn([$entiteJuridique]);
    
    // Mock sendErrorNotificationToAdmin to throw an exception
    $errorNotificationService->method('sendErrorNotificationToAdmin')
        ->willThrowException(new \Exception('Notification error'));
    
    $command = new ActesImportCommand($acteImportService, $errorNotificationService, $entityManager);
    
    // Use reflection to call the protected method
    $reflectionMethod = new \ReflectionMethod(ActesImportCommand::class, 'notifyAdminsOfError');
    $reflectionMethod->setAccessible(true);
    
    // This should not throw an exception
    $reflectionMethod->invoke($command, new \Exception('Test error'), null);
    
    // We can't easily test that error_log was called, but we can verify that the method completes without throwing an exception
    expect(true)->toBeTrue();
});