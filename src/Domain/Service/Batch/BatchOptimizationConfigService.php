<?php

namespace App\Domain\Service\Batch;

/**
 * Service centralisé pour gérer les paramètres d'optimisation des imports batch
 * 
 * Ce service permet de configurer :
 * - La taille des micro-batch pour les transactions
 * - La désactivation conditionnelle des emails
 * - Les paramètres de gestion mémoire
 * - Les seuils de performance
 */
class BatchOptimizationConfigService
{
    // Tailles de batch par défaut
    public const DEFAULT_MICRO_BATCH_SIZE = 150;
    public const DEFAULT_MEMORY_CLEAR_INTERVAL = 100;
    public const DEFAULT_BULK_INSERT_THRESHOLD = 50;
    
    // Seuils pour activation automatique des optimisations - PRODUCTION READY
    public const AUTO_OPTIMIZATION_THRESHOLD = 50; // Activé dès 50 enregistrements
    public const LARGE_BATCH_THRESHOLD = 1000; // Gros batch dès 1000
    
    // Configuration par type de ressource - OPTIMISÉE POUR PRODUCTION
    private array $resourceConfigs = [
        'Sigaps' => [
            'micro_batch_size' => 150,
            'memory_clear_interval' => 100,
            'bulk_insert_threshold' => 50,
            'disable_emails_threshold' => 100, // Emails désactivés dès 100
        ],
        'Liberal' => [
            'micro_batch_size' => 100,
            'memory_clear_interval' => 75,
            'bulk_insert_threshold' => 50,
            'disable_emails_threshold' => 100,
        ],
        'GardesAstreintes' => [
            'micro_batch_size' => 75,
            'memory_clear_interval' => 50,
            'bulk_insert_threshold' => 25,
            'disable_emails_threshold' => 50,
        ],
        'Actes' => [
            'micro_batch_size' => 200,
            'memory_clear_interval' => 150,
            'bulk_insert_threshold' => 100,
            'disable_emails_threshold' => 200,
        ],
        'Affectations' => [
            'micro_batch_size' => 75,
            'memory_clear_interval' => 50,
            'bulk_insert_threshold' => 25,
            'disable_emails_threshold' => 50,
        ],
    ];

    /**
     * Détermine si les optimisations doivent être activées automatiquement
     */
    public function shouldEnableOptimizations(int $totalRecords, string $resourceType = null): bool
    {
        if ($totalRecords >= self::LARGE_BATCH_THRESHOLD) {
            return true;
        }
        
        if ($resourceType && isset($this->resourceConfigs[$resourceType])) {
            $config = $this->resourceConfigs[$resourceType];
            return $totalRecords >= ($config['disable_emails_threshold'] ?? self::AUTO_OPTIMIZATION_THRESHOLD);
        }
        
        return $totalRecords >= self::AUTO_OPTIMIZATION_THRESHOLD;
    }

    /**
     * Détermine si les emails doivent être désactivés pendant l'import
     */
    public function shouldDisableEmails(int $totalRecords, string $resourceType = null): bool
    {
        if ($resourceType && isset($this->resourceConfigs[$resourceType])) {
            $threshold = $this->resourceConfigs[$resourceType]['disable_emails_threshold'];
            return $totalRecords >= $threshold;
        }
        
        return $totalRecords >= self::AUTO_OPTIMIZATION_THRESHOLD;
    }

    /**
     * Retourne la taille optimale des micro-batch pour un type de ressource
     */
    public function getMicroBatchSize(string $resourceType = null, int $totalRecords = 0): int
    {
        $baseSize = self::DEFAULT_MICRO_BATCH_SIZE;
        
        if ($resourceType && isset($this->resourceConfigs[$resourceType])) {
            $baseSize = $this->resourceConfigs[$resourceType]['micro_batch_size'];
        }
        
        // Ajuster la taille selon le volume total
        if ($totalRecords >= self::LARGE_BATCH_THRESHOLD) {
            return min($baseSize * 2, 500); // Augmenter mais limiter à 500
        }
        
        return $baseSize;
    }

    /**
     * Retourne l'intervalle pour clear() la mémoire
     */
    public function getMemoryClearInterval(string $resourceType = null): int
    {
        if ($resourceType && isset($this->resourceConfigs[$resourceType])) {
            return $this->resourceConfigs[$resourceType]['memory_clear_interval'];
        }
        
        return self::DEFAULT_MEMORY_CLEAR_INTERVAL;
    }

    /**
     * Retourne le seuil pour utiliser les BULK operations
     */
    public function getBulkInsertThreshold(string $resourceType = null): int
    {
        if ($resourceType && isset($this->resourceConfigs[$resourceType])) {
            return $this->resourceConfigs[$resourceType]['bulk_insert_threshold'];
        }
        
        return self::DEFAULT_BULK_INSERT_THRESHOLD;
    }

    /**
     * Retourne la configuration complète pour un type de ressource
     */
    public function getResourceConfig(string $resourceType, int $totalRecords = 0): array
    {
        $config = $this->resourceConfigs[$resourceType] ?? [];
        
        return [
            'micro_batch_size' => $this->getMicroBatchSize($resourceType, $totalRecords),
            'memory_clear_interval' => $this->getMemoryClearInterval($resourceType),
            'bulk_insert_threshold' => $this->getBulkInsertThreshold($resourceType),
            'disable_emails' => $this->shouldDisableEmails($totalRecords, $resourceType),
            'enable_optimizations' => $this->shouldEnableOptimizations($totalRecords, $resourceType),
            'is_large_batch' => $totalRecords >= self::LARGE_BATCH_THRESHOLD,
        ];
    }

    /**
     * Calcule le nombre optimal de transactions pour un batch
     */
    public function calculateOptimalTransactionCount(int $totalRecords, string $resourceType = null): int
    {
        $microBatchSize = $this->getMicroBatchSize($resourceType, $totalRecords);
        return max(1, ceil($totalRecords / $microBatchSize));
    }

    /**
     * Retourne les statistiques de configuration pour le logging
     */
    public function getConfigStats(string $resourceType, int $totalRecords): array
    {
        $config = $this->getResourceConfig($resourceType, $totalRecords);
        
        return [
            'resource_type' => $resourceType,
            'total_records' => $totalRecords,
            'estimated_transactions' => $this->calculateOptimalTransactionCount($totalRecords, $resourceType),
            'micro_batch_size' => $config['micro_batch_size'],
            'memory_clear_interval' => $config['memory_clear_interval'],
            'optimizations_enabled' => $config['enable_optimizations'],
            'emails_disabled' => $config['disable_emails'],
            'is_large_batch' => $config['is_large_batch'],
        ];
    }

    /**
     * Valide et ajuste la configuration selon les contraintes système
     */
    public function validateAndAdjustConfig(array $config, int $availableMemoryMB = null): array
    {
        // Ajuster selon la mémoire disponible
        if ($availableMemoryMB && $availableMemoryMB < 512) {
            $config['micro_batch_size'] = min($config['micro_batch_size'], 50);
            $config['memory_clear_interval'] = min($config['memory_clear_interval'], 25);
        }
        
        // Assurer des valeurs minimales
        $config['micro_batch_size'] = max($config['micro_batch_size'], 10);
        $config['memory_clear_interval'] = max($config['memory_clear_interval'], 5);
        
        return $config;
    }
}
