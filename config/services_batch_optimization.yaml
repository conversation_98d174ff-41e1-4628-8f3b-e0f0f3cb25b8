# ========================================
# CONFIGURATION OPTIMISATIONS BATCH - PRODUCTION READY
# ========================================
# Toutes les optimisations sont ACTIVÉES par défaut
# Emails désactivés automatiquement dès 50+ enregistrements
# Micro-transactions pour éviter les timeouts
# Gestion mémoire automatique

services:
    # Service de configuration des optimisations batch
    App\Domain\Service\Batch\BatchOptimizationConfigService:
        public: true

    # Service de notifications batch optimisées
    App\Domain\Service\Notification\BatchNotificationService:
        arguments:
            $mailer: '@mailer'
            $twig: '@twig'
            $logger: '@logger'
            $errorNotificationService: '@App\Domain\Service\Notification\ErrorNotificationEmailService'
        tags:
            - { name: 'monolog.logger', channel: 'batch' }

    # Service d'import d'affectations optimisé
    App\Domain\Service\Importation\AffectationImportOptimizedService:
        arguments:
            $em: '@doctrine.orm.entity_manager'
            $ufsFinderService: '@App\Domain\Service\Core\UfsFinderService'
            $agentFinderService: '@App\Domain\Service\Core\AgentFinderService'
            $configService: '@App\Domain\Service\Batch\BatchOptimizationConfigService'
            $notificationService: '@App\Domain\Service\Notification\BatchNotificationService'
            $logger: '@logger'
        tags:
            - { name: 'monolog.logger', channel: 'batch' }

    # BatchProcessor optimisés avec injection des dépendances
    App\State\Batch\SigapsBatchProcessor:
        arguments:
            $mapper: '@App\Mapper\Sigaps\SigapsDtoToEntityMapper'
            $requestStack: '@request_stack'
            $configService: '@App\Domain\Service\Batch\BatchOptimizationConfigService'
            $notificationService: '@App\Domain\Service\Notification\BatchNotificationService'
            $logger: '@logger'
            $entityManager: '@doctrine.orm.entity_manager'
        tags:
            - { name: 'monolog.logger', channel: 'batch' }

    # Configuration pour les autres BatchProcessor (à adapter selon vos mappers)
    App\State\Batch\LiberalBatchProcessor:
        arguments:
            $mapper: '@App\Mapper\Liberal\LiberalDtoToEntityMapper'
            $requestStack: '@request_stack'
            $configService: '@App\Domain\Service\Batch\BatchOptimizationConfigService'
            $notificationService: '@App\Domain\Service\Notification\BatchNotificationService'
            $logger: '@logger'
            $entityManager: '@doctrine.orm.entity_manager'
        tags:
            - { name: 'monolog.logger', channel: 'batch' }

    App\State\Batch\GardesAstreintesBatchProcessor:
        arguments:
            $mapper: '@App\Mapper\GaredeAstreinte\GardesAstreintesDtoToEntityMapper'
            $requestStack: '@request_stack'
            $agentRepository: '@App\Repository\AgentRepository'
            $configService: '@App\Domain\Service\Batch\BatchOptimizationConfigService'
            $notificationService: '@App\Domain\Service\Notification\BatchNotificationService'
            $logger: '@logger'
            $entityManager: '@doctrine.orm.entity_manager'
        tags:
            - { name: 'monolog.logger', channel: 'batch' }

    App\State\Batch\ActesBatchProcessor:
        arguments:
            $mapper: '@App\Mapper\Actes\ActesDtoToEntityMapper'
            $requestStack: '@request_stack'
            $ufsRepository: '@App\Repository\UfsRepository'
            $agentRepository: '@App\Repository\AgentRepository'
            $configService: '@App\Domain\Service\Batch\BatchOptimizationConfigService'
            $notificationService: '@App\Domain\Service\Notification\BatchNotificationService'
            $logger: '@logger'
            $entityManager: '@doctrine.orm.entity_manager'
        tags:
            - { name: 'monolog.logger', channel: 'batch' }

# Configuration du logger pour les batch
monolog:
    channels: ['batch']
    handlers:
        batch:
            type: rotating_file
            path: '%kernel.logs_dir%/batch.log'
            level: info
            max_files: 10
            channels: ['batch']
