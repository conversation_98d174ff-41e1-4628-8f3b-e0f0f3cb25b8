<?php

namespace App\Repository;

use App\Entity\Praticien\Agent;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Component\Security\Core\Exception\UnsupportedUserException;
use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;
use Symfony\Component\Security\Core\User\PasswordUpgraderInterface;

/**
 * @extends ServiceEntityRepository<Agent>
 */
class AgentRepository extends ServiceEntityRepository implements PasswordUpgraderInterface
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Agent::class);
    }

    /**
     * Used to upgrade (rehash) the user's password automatically over time.
     */
    public function upgradePassword(PasswordAuthenticatedUserInterface $user, string $newHashedPassword): void
    {
        if (!$user instanceof Agent) {
            throw new UnsupportedUserException(sprintf('Instances of "%s" are not supported.', $user::class));
        }

        $user->setPassword($newHashedPassword);
        $this->getEntityManager()->persist($user);
        $this->getEntityManager()->flush();
    }

    /**
     * Find an agent by identifier (email or hrUser)
     * 
     * @param string $identifier The identifier (email or hrUser)
     * @return Agent|null The agent if found, null otherwise
     */
    public function findByIdentifier(string $identifier): ?Agent
    {
        $identifier = strtolower($identifier);

        // If identifier contains @, it's an email
        if (strpos($identifier, '@') !== false) {
            // First try to find by email
            $agent = $this->createQueryBuilder('a')
                ->where('LOWER(a.email) = LOWER(:email)')
                ->setParameter('email', $identifier)
                ->getQuery()
                ->getOneOrNullResult();

            if ($agent) {
                return $agent;
            }
        }

        // If not found by email or identifier is not an email, try to find by hrUser
        return $this->createQueryBuilder('a')
            ->where('LOWER(a.hrUser) = LOWER(:identifier)')
            ->setParameter('identifier', $identifier)
            ->getQuery()
            ->getOneOrNullResult();
    }

    //    /**
    //     * @return Agent[] Returns an array of Agent objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('u')
    //            ->andWhere('u.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('u.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    //    public function findOneBySomeField($value): ?Agent
    //    {
    //        return $this->createQueryBuilder('u')
    //            ->andWhere('u.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->getQuery()
    //            ->getOneOrNullResult()
    //        ;
    //    }
}
