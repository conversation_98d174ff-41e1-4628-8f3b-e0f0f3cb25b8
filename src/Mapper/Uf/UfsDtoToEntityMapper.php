<?php

namespace App\Mapper\Uf;

use App\ApiResource\Structure\UfsDto;
use App\Entity\Structure\Ufs;
use App\Repository\CrRepository;
use App\Repository\ServiceRepository;
use App\Repository\UfsRepository;
use Doctrine\DBAL\Exception;
use Symfonycasts\MicroMapper\AsMapper;
use Symfonycasts\MicroMapper\MapperInterface;

#[AsMapper(from: UfsDto::class, to: Ufs::class)]
class UfsDtoToEntityMapper implements MapperInterface
{
    public function __construct(
        private UfsRepository $ufsRepository,
        private CrRepository $crRepository,
        private ServiceRepository $serviceRepository
    ) {}

    public function load(object $from, string $toClass, array $context): object
    {
        $dto = $from;
        assert($dto instanceof UfsDto);

        // On récupère l'entité existante si un ID est fourni, sinon on en crée une nouvelle
        $ufs = $dto->id ? $this->ufsRepository->find($dto->id) : new Ufs();
        if (!$ufs) {
            throw new Exception('UFS non trouvé');
        }

        return $ufs;
    }

    public function populate(object $from, object $to, array $context): object
    {
        $dto = $from;
        $ufs = $to;
        assert($dto instanceof UfsDto);
        assert($ufs instanceof Ufs);

        $ufs->setEtab($dto->etab);
        $ufs->setUfcode($dto->ufcode);
        $ufs->setDatdeb($dto->datdeb);
        $ufs->setDatfin($dto->datfin);
        $ufs->setDatclos($dto->datclos);
        $ufs->setLibelle($dto->libelle);
        $ufs->setTacode($dto->tacode);
        $ufs->setCdcode($dto->cdcode);
        $ufs->setLettrebudg($dto->lettrebudg);
        $ufs->setSecode($dto->secode);
        $ufs->setCgcode($dto->cgcode);
        $ufs->setUmcode($dto->umcode);
        $ufs->setPfuser($dto->pfuser);
        $ufs->setDmdacre($dto->dmdacre);
        $ufs->setDmdamaj($dto->dmdamaj);
        $ufs->setTopmedical($dto->topmedical);
        $ufs->setCrcode($dto->crcode);
        $ufs->setSacode($dto->sacode);

        // Champs d'horodatage
        $ufs->setIsActif($dto->isActif);
        $ufs->setValidFrom($dto->validFrom);
        $ufs->setValidTo($dto->validTo);
        $ufs->setPeriodeType($dto->periodeType);
        $ufs->setSource($dto->source);

        // Convertir l'IRI API Platform en entité Cr
        if ($dto->cr) {
            // Extraire l'ID depuis l'URI "/api/crs/{id}"
            $crId = basename($dto->cr->id);
            $cr = $this->crRepository->find($crId);
            if (!$cr) {
                throw new Exception('CR non trouvé');
            }
            $ufs->setCr($cr);
        }

        // Convertir l'IRI API Platform en entité Service
        if ($dto->service) {
            // Extraire l'ID depuis l'URI "/api/services/{id}"
            $serviceId = basename($dto->service->id);
            $service = $this->serviceRepository->find($serviceId);
            if (!$service) {
                throw new Exception('Service non trouvé');
            }
            $ufs->setService($service);
        }

        return $ufs;
    }
}
