<?php

namespace App\Mapper\Cr;

use App\ApiResource\Structure\CrDto;
use App\Entity\Structure\Cr;
use App\Repository\CrRepository;
use App\Repository\PoleRepository;
use Doctrine\DBAL\Exception;
use Symfonycasts\MicroMapper\AsMapper;
use Symfonycasts\MicroMapper\MapperInterface;

#[AsMapper(from: CrDto::class, to: Cr::class)]
class CrDtoToEntityMapper implements MapperInterface
{
    public function __construct(
        private CrRepository $crRepository,
        private PoleRepository $poleRepository
    ) {}

    public function load(object $from, string $toClass, array $context): object
    {
        $dto = $from;
        assert($dto instanceof CrDto);

        // On récupère l'entité existante si un ID est fourni, sinon on en crée une nouvelle
        $cr = $dto->id ? $this->crRepository->find($dto->id) : new Cr();
        if (!$cr) {
            throw new Exception('CR non trouvé');
        }

        return $cr;
    }

    public function populate(object $from, object $to, array $context): object
    {
        $dto = $from;
        $cr = $to;
        assert($dto instanceof CrDto);
        assert($cr instanceof Cr);

        $cr->setEtab($dto->etab);
        $cr->setCrcode($dto->crcode);
        $cr->setDatdeb($dto->datdeb);
        $cr->setDatfin($dto->datfin);
        $cr->setLibelle($dto->libelle);
        $cr->setNomresp($dto->nomresp);
        $cr->setPolecode($dto->polecode);
        $cr->setPfuser($dto->pfuser);
        $cr->setDmdacre($dto->dmdacre);
        $cr->setDmdamaj($dto->dmdamaj);

        // Champs d'horodatage
        $cr->setIsActif($dto->isActif);
        $cr->setValidFrom($dto->validFrom);
        $cr->setValidTo($dto->validTo);
        $cr->setPeriodeType($dto->periodeType);
        $cr->setSource($dto->source);

        // Convertir l'IRI API Platform en entité Pole
        if ($dto->pole) {
            // Extraire l'ID depuis l'URI "/api/poles/{id}"
            $poleId = basename($dto->pole);
            $pole = $this->poleRepository->find($poleId);
            if (!$pole) {
                throw new Exception('Pôle non trouvé');
            }
            $cr->setPole($pole);
        }

        return $cr;
    }
}