<?php

namespace App\Command;

use App\Command\DataIntegratorGlobalCacheClearCommand;
use App\Domain\Service\Importation\ActeImportService;
use App\Domain\Service\Notification\ErrorNotificationEmailService;
use App\Entity\Importation\Importation;
use App\Entity\Structure\EntiteJuridique;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Input\ArrayInput;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\Console\Application;

/**
 * Commande pour l'importation automatique des actes depuis le data_integrator avec optimisation mémoire.
 * 
 * Cette commande récupère et synchronise tous les actes médicaux depuis le service
 * data_integrator avec support UPSERT (création/mise à jour automatique).
 * 
 * OPTIMISATIONS MÉMOIRE :
 * - Cache d'entités pour réduire les requêtes redondantes
 * - Taille de batch dynamique qui s'ajuste selon l'utilisation mémoire
 * - Transactions plus petites pour éviter les problèmes de mémoire
 * - Nettoyage régulier du cache Doctrine et garbage collection
 * - Requêtes optimisées pour éviter de charger des données inutiles
 * - Mesures d'urgence en cas de consommation mémoire excessive
 * 
 * Par défaut, importe les actes des 7 derniers jours.
 * Optionnellement, permet de spécifier une date précise.
 * 
 * En cas d'erreur critique, notifie automatiquement tous les admins des entités
 * juridiques ayant activé les notifications d'erreur (NotifierAdminDesErreurProduite = true).
 * 
 * Usage complet avec toutes les options :
 * php bin/console app:import:actes [options]
 * 
 * Options disponibles :
 * --date=YYYY-MM-DD (-d)       : Date spécifique pour l'importation (format: Y-m-d)
 * --page-size=N (-p)           : Nombre d'actes à récupérer par page API (défaut: 50, recommandé: 20-25 pour économiser la mémoire)
 * --batch-size=N (-b)          : Nombre d'actes à traiter avant de flush/clear (défaut: 50, recommandé: 20 pour économiser la mémoire)
 * --memory-warning=N           : Limite de mémoire en MB avant d'émettre un avertissement (défaut: 1500)
 * --memory-critical=N          : Limite de mémoire en MB avant d'arrêter l'importation (défaut: 2500)
 * --skip-cache-clear           : Ne pas vider le cache avant/après l'importation (non recommandé)
 * 
 * Exemples d'utilisation :
 * - Import par défaut (J-7) : 
 *   php bin/console app:import:actes
 * 
 * - Import pour une date spécifique : 
 *   php bin/console app:import:actes --date=2024-01-15
 * 
 * - Import optimisé pour économiser la mémoire (recommandé pour environnements contraints) : 
 *   php bin/console app:import:actes --page-size=20 --batch-size=20 --memory-warning=1000 --memory-critical=1500
 * 
 * - Import optimisé pour les performances avec grands volumes de données (si mémoire suffisante) : 
 *   php bin/console app:import:actes --page-size=100 --batch-size=50 --memory-warning=2000 --memory-critical=3000
 * 
 * - Import par jour pour éviter les timeouts et problèmes de mémoire : 
 *   php bin/console app:import:actes --date=2024-01-15
 * 
 * - Import par jour avec paramètres optimisés pour la mémoire :
 *   php bin/console app:import:actes --date=2024-01-15 --page-size=20 --batch-size=20
 *
 * Exemple de cron Windows (PowerShell) :
 * & 'C:\path\to\php.exe' 'C:\path\to\supra\bin\console' app:import:actes --page-size=20 --batch-size=20 >> C:\path\to\logs\supra-actes-import.log 2>&1
 * 
 * Exemple de cron Linux (tous les jours à 2h00) :
 * 0 2 * * * /usr/bin/php /path/to/supra/bin/console app:import:actes --date=$(date -d '7 days ago' +\%Y-\%m-\%d) --page-size=20 --batch-size=20 >> /var/log/supra-actes-import.log 2>&1
 *
 * Alternative sans paramètre date (utilise J-7 par défaut dans le code) :
 * 0 2 * * * /usr/bin/php /path/to/supra/bin/console app:import:actes --page-size=20 --batch-size=20 >> /var/log/supra-actes-import.log 2>&1
 */
#[AsCommand(
    name: 'app:import:actes',
    description: 'Importe tous les actes depuis le data_integrator',
)]
class ActesImportCommand extends Command
{
    // Limites de mémoire par défaut (en MB)
    private const MEMORY_WARNING_LIMIT = 1500;
    private const MEMORY_CRITICAL_LIMIT = 2500;
    
    private ?Application $application = null;
    
    public function __construct(
        private readonly ActeImportService $acteImportService,
        private readonly ErrorNotificationEmailService $errorNotificationService,
        private readonly EntityManagerInterface $em
    ) {
        parent::__construct();
    }
    
    protected function initialize(InputInterface $input, OutputInterface $output): void
    {
        $this->application = $this->getApplication();
    }
    
    /**
     * Execute la commande de nettoyage du cache global du data integrator
     * 
     * @return array|null La réponse de l'API contenant success et ejcode, ou null en cas d'erreur
     */
    private function executeDataIntegratorCacheClearCommand(OutputInterface $output): ?array
    {
        if (!$this->application) {
            return null;
        }
        
        try {
            $command = $this->application->find('app:data-integrator:global-cache-clear');
            
            // Exécuter la commande pour l'affichage dans la console
            $arguments = new ArrayInput([]);
            $command->run($arguments, $output);
            
            // Appeler directement la méthode pour récupérer la réponse de l'API
            if ($command instanceof DataIntegratorGlobalCacheClearCommand) {
                return $command->clearGlobalCacheAndGetResponse();
            }
        } catch (\Exception $e) {
            // Log l'erreur mais ne pas interrompre le processus
            $output->writeln('<error>Erreur lors de la récupération de la réponse API: ' . $e->getMessage() . '</error>');
        }
        
        return null;
    }

    protected function configure(): void
    {
        $this
            ->addOption(
                'date',
                'd',
                InputOption::VALUE_OPTIONAL,
                'Date spécifique pour l\'importation (format: Y-m-d)'
            )
            ->addOption(
                'page-size',
                'p',
                InputOption::VALUE_OPTIONAL,
                'Nombre d\'actes à récupérer par page API',
                50
            )
            ->addOption(
                'batch-size',
                'b',
                InputOption::VALUE_OPTIONAL,
                'Nombre d\'actes à traiter avant de flush/clear',
                50
            )
            ->addOption(
                'memory-warning',
                null,
                InputOption::VALUE_OPTIONAL,
                'Limite de mémoire en MB avant d\'émettre un avertissement',
                self::MEMORY_WARNING_LIMIT
            )
            ->addOption(
                'memory-critical',
                null,
                InputOption::VALUE_OPTIONAL,
                'Limite de mémoire en MB avant d\'arrêter l\'importation',
                self::MEMORY_CRITICAL_LIMIT
            )
            ->addOption(
                'skip-cache-clear',
                null,
                InputOption::VALUE_NONE,
                'Ne pas vider le cache avant l\'importation'
            )
            ->setHelp('Cette commande importe les actes depuis le data_integrator avec support UPSERT');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $result = Command::FAILURE;
        
        try {
            // Récupérer les options de la ligne de commande
            $date = $input->getOption('date');
            $pageSize = (int) $input->getOption('page-size');
            $batchSize = (int) $input->getOption('batch-size');
            $memoryWarningLimit = (int) $input->getOption('memory-warning');
            $memoryCriticalLimit = (int) $input->getOption('memory-critical');
            $skipCacheClear = (bool) $input->getOption('skip-cache-clear');
            
            // Afficher les paramètres d'importation
            $io->section('Début de l\'importation des actes');
            $io->table(
                ['Paramètre', 'Valeur'],
                [
                    ['Date d\'import', $date ?: 'J-7 (par défaut)'],
                    ['Taille de page API', $pageSize],
                    ['Taille de batch', $batchSize],
                    ['Limite mémoire avertissement', $memoryWarningLimit . ' MB'],
                    ['Limite mémoire critique', $memoryCriticalLimit . ' MB'],
                    ['Skip cache clear', $skipCacheClear ? 'Oui' : 'Non'],
                ]
            );
            
            // Nettoyer le cache du data integrator avant l'importation
            if (!$skipCacheClear) {
                $io->section('Nettoyage du cache du data integrator avant importation');
                $this->executeDataIntegratorCacheClearCommand($output);
                
                $io->text('Nettoyage du cache Doctrine avant importation...');
                $this->clearCache($io);
            }
            
            // Vérifier la mémoire disponible avant de commencer
            $this->checkMemoryLimits($io, $memoryWarningLimit, $memoryCriticalLimit);
            
            $io->title('Debut: Import des actes');

            if ($date) {
                $io->info("Import pour la date : {$date}");
                // Validation du format de date
                if (!\DateTime::createFromFormat('Y-m-d', $date)) {
                    $io->error('Format de date invalide. Utilisez le format Y-m-d (ex: 2024-01-15)');
                    return Command::FAILURE;
                }
            } else {
                $io->info('Import complet (toutes les données) date = Date actuelle - 7 jours');
            }

            // IMPORTANT: Nous ne gérons pas les transactions au niveau de la commande.
            // Le service ActeImportService gère ses propres transactions de manière optimisée
            // en découpant l'importation en plusieurs petites transactions pour limiter l'utilisation mémoire.
            // Cela évite les problèmes de transactions imbriquées avec Doctrine où seule la transaction
            // la plus externe serait réellement commitée.
            
            try {
                $startTime = microtime(true);
                $memoryWarningIssued = false;
                $memoryReadings = [];
                
                // Afficher l'état initial de la mémoire
                $initialMemory = memory_get_usage(true) / 1024 / 1024;
                $memoryReadings[] = $initialMemory;
                $io->text(sprintf('Mémoire initiale: %.2f MB', $initialMemory));
                
                // Vérifier les limites de mémoire avant de commencer
                if ($initialMemory >= $memoryWarningLimit) {
                    $io->warning(sprintf(
                        'Utilisation élevée de la mémoire avant importation: %.1f MB (limite d\'avertissement: %d MB)',
                        $initialMemory,
                        $memoryWarningLimit
                    ));
                }
                
                // Initialiser les variables pour la barre de progression
                $progressBar = null;
                $memoryWarningIssued = false;
                
                // Fonction de callback pour mettre à jour la barre de progression
                $progressCallback = function (
                    int $currentPage, 
                    int $totalPages, 
                    int $processedItems, 
                    int $totalItems, 
                    float $currentMemory = 0, 
                    float $peakMemory = 0
                ) use (
                    $io, 
                    &$progressBar, 
                    $memoryWarningLimit, 
                    $memoryCriticalLimit, 
                    &$memoryWarningIssued,
                    &$memoryReadings
                ) {
                    // Initialiser la barre de progression si nécessaire
                    if ($progressBar === null) {
                        $io->text(sprintf('Importation des actes - %d pages au total, environ %d actes...', $totalPages, $totalItems));
                        $progressBar = $io->createProgressBar($totalPages);
                        $progressBar->setFormat(
                            ' Page %current%/%max% [%bar%] %percent:3s%% | %elapsed:6s% | Actes: %processedItems% | Mémoire: %memory:6s% | Page actuelle: %currentPage%'
                        );
                        $progressBar->setMessage($currentPage, 'currentPage');
                        $progressBar->setMessage($processedItems, 'processedItems');
                        $progressBar->start();
                    } else {
                        $progressBar->setProgress($currentPage);
                        $progressBar->setMessage($currentPage, 'currentPage');
                        $progressBar->setMessage($processedItems, 'processedItems');
                    }
                    
                    // Afficher la mémoire utilisée
                    $memoryFormatted = number_format($currentMemory, 1) . ' MB';
                    $progressBar->setMessage($memoryFormatted, 'memory');
                    
                    // Enregistrer la mesure de mémoire pour l'analyse de tendance
                    $memoryReadings[] = $currentMemory;
                    
                    // Vérifier les limites de mémoire
                    if ($currentMemory >= $memoryCriticalLimit) {
                        throw new \RuntimeException(sprintf(
                            'Limite de mémoire critique atteinte: %.1f MB (limite: %d MB). Arrêt de l\'importation pour éviter un crash.',
                            $currentMemory,
                            $memoryCriticalLimit
                        ));
                    } elseif ($currentMemory >= $memoryWarningLimit && !$memoryWarningIssued) {
                        $memoryWarningIssued = true;
                        $progressBar->clear();
                        $io->warning(sprintf(
                            'Utilisation élevée de la mémoire: %.1f MB (limite d\'avertissement: %d MB)',
                            $currentMemory,
                            $memoryWarningLimit
                        ));
                        $progressBar->display();
                    }
                };
                
                // Lancer l'importation avec le callback de progression et les paramètres optimisés
                $stats = $this->acteImportService->importActes(
                    $date, 
                    $progressCallback,
                    $batchSize,  // Utiliser la taille de batch configurée
                    $pageSize    // Utiliser la taille de page configurée
                );
                
                // Finaliser la barre de progression
                if ($progressBar !== null) {
                    $progressBar->finish();
                    $io->newLine(2);
                }
                
                // No need to commit transaction here as the service handles transactions
                
                // Afficher l'état final de la mémoire
                $finalMemory = memory_get_usage(true) / 1024 / 1024;
                $memoryReadings[] = $finalMemory;
                $io->text(sprintf('Mémoire finale: %.2f MB', $finalMemory));
                $io->text(sprintf('Différence de mémoire: %.2f MB', $finalMemory - $initialMemory));
                
                // Vérifier si la mémoire a dépassé les limites
                $peakMemory = memory_get_peak_usage(true) / 1024 / 1024;
                if ($peakMemory >= $memoryCriticalLimit) {
                    $io->error(sprintf(
                        'Pic de mémoire critique atteint: %.1f MB (limite: %d MB). Considérez augmenter la limite de mémoire PHP.',
                        $peakMemory,
                        $memoryCriticalLimit
                    ));
                } elseif ($peakMemory >= $memoryWarningLimit) {
                    $io->warning(sprintf(
                        'Pic de mémoire élevé: %.1f MB (limite d\'avertissement: %d MB)',
                        $peakMemory,
                        $memoryWarningLimit
                    ));
                }
                
                // Calculer et afficher le temps total d'exécution
                $totalTime = microtime(true) - $startTime;
                $io->text(sprintf('Temps total d\'exécution: %s', $this->formatTime($totalTime)));
                
                // Afficher les statistiques de mémoire
                if (!empty($memoryReadings)) {
                    $avgMemory = array_sum($memoryReadings) / count($memoryReadings);
                    $maxMemory = max($memoryReadings);
                    $io->text([
                        sprintf('Mémoire moyenne utilisée: %.1f MB', $avgMemory),
                        sprintf('Mémoire maximale utilisée: %.1f MB', $maxMemory)
                    ]);
                }
                
                // Recommandations pour optimiser les performances
                $this->showPerformanceRecommendations($io);
                
                $io->success('✅ Import terminé avec succès');
                $io->table(
                    ['Métrique', 'Valeur'],
                    [
                        ['Traités', $stats['processed']],
                        ['Créés', $stats['created']],
                        ['Mis à jour', $stats['updated']],
                        ['Erreurs', count($stats['errors'])]
                    ]
                );
    
                if (!empty($stats['errors'])) {
                    $io->warning(' Erreurs détectées :');
                    foreach ($stats['errors'] as $error) {
                        $io->text(sprintf(
                            '- Code: %s, Matricule: %s, Erreur: %s',
                            $error['codeActe'],
                            $error['praticienMatricule'],
                            $error['error']
                        ));
                    }
                }
                
                $result = Command::SUCCESS;
                
                // Enregistrer le résultat de l'importation avec la nouvelle méthode
                $this->saveImportationResult($io, $output, 'Actes', true, $date, $stats);
                
            } catch (\Throwable $e) {
                // Rollback any active transactions that might have been started by the service but not yet committed
                if ($this->em->getConnection()->isTransactionActive()) {
                    $this->em->rollback();
                }
                
                $io->error('❌ Erreur lors de l\'importation : ' . $e->getMessage());
    
                // Notifier toutes les entités juridiques qui ont activé les notifications d'erreur
                $this->notifyAdminsOfError($e, $date);
                
                // In case of error, we don't have stats from a successful import
                // We'll create a minimal stats array with just the refresh information if available
                $errorStats = null;
                if ($e instanceof \Exception && method_exists($e, 'getRefreshInfo')) {
                    $errorStats = ['refresh' => $e->getRefreshInfo()];
                }
                
                // Enregistrer le résultat de l'importation (échec) avec la nouvelle méthode
                $this->saveImportationResult($io, $output, 'Actes', false, $date, $errorStats);
                
                $result = Command::FAILURE;
            }
            
            // Nettoyer le cache du data integrator après l'importation (même en cas d'erreur)
            if (!$skipCacheClear) {
                $io->section('Nettoyage du cache du data integrator après importation');
                $this->executeDataIntegratorCacheClearCommand($output);
                
                $io->text('Nettoyage du cache Doctrine après importation...');
                $this->clearCache($io);
            }
            
            // Créer un signal pour redémarrer le serveur web si nécessaire
            $this->createRestartSignal($io);

            return $result;

        } catch (\Throwable $e) {
            $io->error('❌ Erreur lors de l\'initialisation : ' . $e->getMessage());

            // Notifier toutes les entités juridiques qui ont activé les notifications d'erreur
            $this->notifyAdminsOfError($e, $date ?? null);
            
            return Command::FAILURE;
        }
    }

    /**
     * Enregistre le résultat de l'importation avec gestion des erreurs EntityManager
     * 
     * @param SymfonyStyle $io Interface de sortie
     * @param OutputInterface $output Interface de sortie brute
     * @param string $resourceName Nom de la ressource importée
     * @param bool $isSuccess Indique si l'importation s'est bien passée
     * @param string|null $date Date de l'importation (format Y-m-d)
     * @param array|null $stats Statistiques de l'importation, incluant les informations de refresh
     */
    private function saveImportationResult(SymfonyStyle $io, OutputInterface $output, string $resourceName, bool $isSuccess, ?string $date = null, ?array $stats = null): void
    {
        $io->section('Enregistrement du résultat de l\'importation');
        
        try {
            // Récupérer la réponse de l'API pour obtenir l'ejcode
            $apiResponse = $this->executeDataIntegratorCacheClearCommand($output);
            $ejcode = $apiResponse['ejCode'] ?? "ej0001";
            
            $importation = new Importation();
            $importation->setNomRessource($resourceName);
            $importation->setEstReussie($isSuccess);
            
            // Store refresh information if available
            if ($stats !== null && isset($stats['refresh']) && is_array($stats['refresh'])) {
                $importation->setRefreshDebugInfo($stats['refresh']);
                $io->info('Informations de refresh stockées dans l\'importation');
            }
            
            // Associer l'EntiteJuridique si l'ejcode est disponible
            if ($ejcode) {
                try {
                    $entiteJuridique = $this->em->getRepository(EntiteJuridique::class)->findOneBy(['code' => $ejcode]);
                    
                    // Si l'entité juridique n'est pas trouvée avec le code fourni, essayer avec "ej0001"
                    if (!$entiteJuridique && $ejcode !== "ej0001") {
                        $io->warning(sprintf('Aucune EntiteJuridique trouvée avec le code: %s. Essai avec le code par défaut "ej0001"', $ejcode));
                        $ejcode = "ej0001";
                        $entiteJuridique = $this->em->getRepository(EntiteJuridique::class)->findOneBy(['code' => $ejcode]);
                    }
                    
                    if ($entiteJuridique) {
                        $importation->setEntiteJuridique($entiteJuridique);
                        $io->info(sprintf('EntiteJuridique associée: %s (code: %s)', $entiteJuridique->getNom(), $ejcode));
                    } else {
                        $io->warning(sprintf('Aucune EntiteJuridique trouvée, même avec le code par défaut "ej0001"'));
                    }
                } catch (\Throwable $ejError) {
                    $io->warning('Erreur lors de la recherche de l\'EntiteJuridique : ' . $ejError->getMessage());
                }
            } else {
                $io->warning('Aucun code d\'EntiteJuridique trouvé dans la réponse API, utilisation du code par défaut "ej0001"');
            }
            
            try {
                // Persister l'entité
                $this->em->persist($importation);
                $this->em->flush();
                
                $io->success('Résultat de l\'importation enregistré avec succès');
            } catch (\Throwable $persistError) {
                // Si l'EntityManager est fermé, on log l'erreur mais on ne bloque pas le processus
                $io->warning('Impossible de persister le résultat de l\'importation : ' . $persistError->getMessage());
                
                // Log plus de détails pour le débogage
                error_log('Erreur lors de la persistance du résultat d\'importation : ' . $persistError->getMessage());
                error_log('Trace : ' . $persistError->getTraceAsString());
            }
        } catch (\Throwable $e) {
            $io->warning('Impossible d\'enregistrer le résultat de l\'importation : ' . $e->getMessage());
            // Ne pas modifier le résultat de la commande si l'enregistrement échoue
        }
    }

    /**
     * Nettoie le cache avant l'importation pour libérer de la mémoire
     * 
     * @param SymfonyStyle $io Interface de sortie
     * @return void
     */
    private function clearCache(SymfonyStyle $io): void
    {
        try {
            // Vider le cache Doctrine pour libérer de la mémoire
            $this->em->clear();
            
            // Fermer et réinitialiser la connexion à la base de données
            // Cela libère les ressources de connexion et évite les fuites mémoire
            $connection = $this->em->getConnection();
            if ($connection->isConnected()) {
                $connection->close();
            }
            
            // Forcer le garbage collector
            if (gc_enabled()) {
                $collectedCycles = gc_collect_cycles();
                $io->text(sprintf('Garbage collector: %d cycles collectés', $collectedCycles));
                
                // Libérer la mémoire allouée à l'interpréteur PHP
                if (function_exists('gc_mem_caches')) {
                    gc_mem_caches();
                    $io->text('Mémoire de l\'interpréteur PHP libérée');
                }
            }
            
            // Libérer la mémoire des variables statiques
            $this->clearStaticProperties();
            
            // Afficher la mémoire disponible après nettoyage
            $memoryUsage = round(memory_get_usage(true) / 1024 / 1024, 2);
            $io->text(sprintf('Mémoire utilisée après nettoyage: %.2f MB', $memoryUsage));
            
            $io->success('Cache nettoyé avec succès');
        } catch (\Exception $e) {
            $io->warning('Erreur lors du nettoyage du cache : ' . $e->getMessage());
        }
    }
    
    /**
     * Nettoie les propriétés statiques des classes principales pour libérer la mémoire
     * 
     * @return void
     */
    private function clearStaticProperties(): void
    {
        // Réinitialiser les propriétés statiques des classes qui pourraient retenir de la mémoire
        // Cette méthode est expérimentale et doit être utilisée avec précaution
        
        // Réinitialiser les caches statiques de Doctrine
        $reflectionClass = new \ReflectionClass(\Doctrine\ORM\EntityManager::class);
        foreach ($reflectionClass->getStaticProperties() as $name => $value) {
            $property = $reflectionClass->getProperty($name);
            if ($property->isStatic() && $property->isPublic()) {
                $property->setValue(null);
            }
        }
    }
    
    /**
     * Crée un fichier signal pour indiquer qu'un redémarrage du serveur web est nécessaire
     * 
     * Ce fichier peut être détecté par un processus externe (comme un cron) qui a les permissions
     * nécessaires pour redémarrer le serveur web ou PHP-FPM.
     * 
     * @param SymfonyStyle $io Interface de sortie
     * @return void
     */
    private function createRestartSignal(SymfonyStyle $io): void
    {
        try {
            // Définir le chemin du fichier signal
            $varDir = dirname(__DIR__, 2) . '/var';
            $signalFile = $varDir . '/restart_needed.txt';
            
            // Créer le contenu du fichier avec des informations utiles
            $content = sprintf(
                "Redémarrage nécessaire après importation des actes\n" .
                "Date: %s\n" .
                "Raison: Libération de la mémoire après importation massive de données\n" .
                "Pic mémoire: %.2f MB\n",
                (new \DateTime())->format('Y-m-d H:i:s'),
                memory_get_peak_usage(true) / 1024 / 1024
            );
            
            // Écrire le fichier
            if (file_put_contents($signalFile, $content) !== false) {
                $io->info([
                    'Signal de redémarrage créé: ' . $signalFile,
                    'Pour éviter les problèmes de mémoire, il est recommandé de redémarrer le serveur web ou PHP-FPM.'
                ]);
                
                $io->note([
                    'Pour configurer le redémarrage automatique, ajoutez un cron qui:',
                    '1. Détecte la présence du fichier ' . $signalFile,
                    '2. Redémarre le service PHP-FPM ou le serveur web',
                    '3. Supprime le fichier signal',
                    '',
                    'Exemple de cron (Linux):',
                    '*/5 * * * * if [ -f ' . $signalFile . ' ]; then systemctl restart php-fpm && rm ' . $signalFile . '; fi'
                ]);
            } else {
                $io->warning('Impossible de créer le fichier signal de redémarrage: ' . $signalFile);
            }
        } catch (\Throwable $e) {
            // Ne pas bloquer l'exécution si la création du signal échoue
            $io->warning('Erreur lors de la création du signal de redémarrage: ' . $e->getMessage());
        }
    }
    
    /**
     * Vérifie les limites de mémoire et prend des actions appropriées
     * 
     * @param SymfonyStyle $io Interface de sortie
     * @param int $warningLimit Limite d'avertissement en MB
     * @param int $criticalLimit Limite critique en MB
     * @return void
     * @throws \RuntimeException Si la limite critique est dépassée
     */
    private function checkMemoryLimits(SymfonyStyle $io, int $warningLimit, int $criticalLimit): void
    {
        $currentMemory = memory_get_usage(true) / 1024 / 1024;
        $peakMemory = memory_get_peak_usage(true) / 1024 / 1024;
        
        $io->text([
            sprintf('Mémoire actuelle: %.2f MB', $currentMemory),
            sprintf('Pic de mémoire: %.2f MB', $peakMemory)
        ]);
        
        if ($currentMemory >= $criticalLimit) {
            throw new \RuntimeException(sprintf(
                'Limite de mémoire critique atteinte: %.1f MB (limite: %d MB). Arrêt de l\'importation pour éviter un crash.',
                $currentMemory,
                $criticalLimit
            ));
        } elseif ($currentMemory >= $warningLimit) {
            $io->warning(sprintf(
                'Utilisation élevée de la mémoire: %.1f MB (limite d\'avertissement: %d MB)',
                $currentMemory,
                $warningLimit
            ));
        }
    }
    
    /**
     * Formate un temps en secondes en une chaîne lisible
     * 
     * @param float $seconds Temps en secondes
     * @return string Temps formaté
     */
    private function formatTime(float $seconds): string
    {
        if ($seconds < 60) {
            return sprintf('%.1f sec', $seconds);
        } elseif ($seconds < 3600) {
            $minutes = floor($seconds / 60);
            $secs = $seconds % 60;
            return sprintf('%d min %d sec', $minutes, $secs);
        } else {
            $hours = floor($seconds / 3600);
            $minutes = floor(($seconds % 3600) / 60);
            $secs = $seconds % 60;
            return sprintf('%d h %d min %d sec', $hours, $minutes, $secs);
        }
    }
    
    /**
     * Affiche des recommandations pour optimiser les performances et la consommation mémoire
     * 
     * @param SymfonyStyle $io Interface de sortie
     */
    private function showPerformanceRecommendations(SymfonyStyle $io): void
    {
        $io->section('Recommandations pour optimiser les performances et la consommation mémoire');
        
        $memoryRecommendations = [
            'Pour réduire l\'utilisation de la mémoire, utilisez --page-size=20 --batch-size=20',
            'Pour les environnements très contraints en mémoire, utilisez --page-size=10 --batch-size=10',
            'Importez les données jour par jour avec l\'option --date pour limiter le volume de données',
            'Assurez-vous que le garbage collector PHP est activé (gc_enable=1 dans php.ini)',
            'Augmentez la mémoire PHP si possible (memory_limit dans php.ini)'
        ];
        
        $performanceRecommendations = [
            'Pour accélérer l\'importation (si mémoire suffisante), utilisez --page-size=100 --batch-size=50',
            'Exécutez l\'importation pendant les heures creuses pour minimiser l\'impact sur les utilisateurs',
            'Utilisez l\'option --date pour importer les données par jour et éviter les timeouts',
            'Assurez-vous que les index de base de données sont optimisés pour les requêtes fréquentes'
        ];
        
        $io->text('<info>Recommandations pour économiser la mémoire :</info>');
        $io->listing($memoryRecommendations);
        
        $io->text('<info>Recommandations pour améliorer les performances :</info>');
        $io->listing($performanceRecommendations);
        
        $io->note([
            'Exemple pour environnement contraint en mémoire :',
            'php bin/console app:import:actes --date=2024-01-15 --page-size=20 --batch-size=20 --memory-warning=1000 --memory-critical=1500'
        ]);
    }

    /**
     * Notifie tous les admins des entités juridiques ayant activé les notifications d'erreur
     */
    private function notifyAdminsOfError(\Throwable $exception, ?string $date): void
    {
        try {
            // Récupérer toutes les entités juridiques avec notifications d'erreur activées
//            $entitesJuridiques = $this->em->getRepository(EntiteJuridique::class)
//                ->createQueryBuilder('ej')
//                ->where("JSON_EXTRACT(ej.parametre, '$.notifications.NotifierAdminDesErreurProduite') = true")
//                ->getQuery()
//                ->getResult();

            // Récupérer toutes les entités juridiques avec notifications d'erreur activées
            $entitesJuridiques = $this->em->getRepository(EntiteJuridique::class)
                ->findAll();

            // Filtrer les entités qui ont les notifications d'erreur activées
            $entitesJuridiques = array_filter($entitesJuridiques, function($ej) {
                $parametre = $ej->getParametre();
                return isset($parametre['notifications']['NotifierAdminDesErreurProduite']) &&
                    $parametre['notifications']['NotifierAdminDesErreurProduite'] === true;
            });

            foreach ($entitesJuridiques as $entiteJuridique) {
                $this->errorNotificationService->sendErrorNotificationToAdmin(
                    $entiteJuridique,
                    'Import des actes',
                    $exception->getMessage(),
                    [
                        'type' => 'Commande console',
                        'command' => 'app:import:actes',
                        'date' => $date ?: 'J-7 (par défaut)',
                        'stackTrace' => $exception->getTraceAsString(),
                        'file' => $exception->getFile(),
                        'line' => $exception->getLine()
                    ]
                );
            }

        } catch (\Exception $notificationError) {
            // Si l'envoi de notification échoue, on ne fait que logger pour ne pas masquer l'erreur principale
            error_log('Erreur lors de l\'envoi de notifications d\'erreur : ' . $notificationError->getMessage());
        }
    }
}