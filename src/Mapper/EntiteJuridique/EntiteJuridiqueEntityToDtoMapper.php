<?php

namespace App\Mapper\EntiteJuridique;

use App\ApiResource\Structure\EntiteJuridiqueDto;
use App\ApiResource\Structure\TenantDto;
use App\Entity\Structure\EntiteJuridique;
use Symfonycasts\MicroMapper\AsMapper;
use Symfonycasts\MicroMapper\MapperInterface;
use Symfonycasts\MicroMapper\MicroMapperInterface;

#[AsMapper(from: EntiteJuridique::class, to: EntiteJuridiqueDto::class)]
class EntiteJuridiqueEntityToDtoMapper implements MapperInterface
{
    public function __construct(private MicroMapperInterface $microMapper) {}
    public function load(object $from, string $toClass, array $context): object
    {
        $hopital = $from;
        assert($hopital instanceof EntiteJuridique);

        $dto = new EntiteJuridiqueDto();
        $dto->id = $hopital->getId(); // On charge uniquement l'ID ici

        return $dto;
    }

    public function populate(object $from, object $to, array $context): object
    {
        $hopital = $from;
        $dto = $to;
        assert($hopital instanceof EntiteJuridique);
        assert($dto instanceof EntiteJuridiqueDto);

        $dto->code = $hopital->getCode();
        $dto->nom = $hopital->getNom();
        $dto->adresse = $hopital->getAdresse();
        $dto->telephone = $hopital->getTelephone();
        $dto->email = $hopital->getEmail();
        $dto->dateCreation = $hopital->getDateCreation();
        $dto->parametre = $hopital->getParametre();

        // Récupération de l'ID du Tenant
        //$dto->tenantId = $hopital->getTenant()?->getId();
        // Convertir Tenant en URI API Platform
        if ($hopital->getTenant()) {
            $dto->tenant = $this->microMapper->map($hopital->getTenant(), TenantDto::class, [
                MicroMapperInterface::MAX_DEPTH => 0, // On ne charge que l'ID
            ]);
        }

        return $dto;
    }
}
