# Docker Environment

Configuration Docker pour reproduire l'environnement de production avec FrankenPHP v1.7.0 + PHP 8.4.8 + PostgreSQL + Redis.

## Utilisation

### Demarrage
```bash
docker compose -f docker-compose.frankenphp.yml up -d
```

### Acces
- Application: http://localhost:8000
- Admin: http://localhost:8000/manager/dtnib/admin/login
- API: http://localhost:8000/api
- PostgreSQL: localhost:5433
- Redis: localhost:6380

### Commandes utiles
```bash
# Logs
docker compose -f docker-compose.frankenphp.yml logs -f

# Shell dans le conteneur
docker compose -f docker-compose.frankenphp.yml exec frankenphp bash

# Cache Symfony
docker compose -f docker-compose.frankenphp.yml exec frankenphp php bin/console cache:clear

# Migrations
docker compose -f docker-compose.frankenphp.yml exec frankenphp php bin/console doctrine:migrations:migrate

# Fixtures
docker compose -f docker-compose.frankenphp.yml exec frankenphp php bin/console doctrine:fixtures:load
```

### Arret
```bash
docker compose -f docker-compose.frankenphp.yml down
```

## Configuration

### Services
- **frankenphp**: Application PHP avec FrankenPHP v1.7.0 + PHP 8.4.8
- **postgres**: Base de donnees PostgreSQL 16
- **redis**: Cache Redis 7

### Variables d'environnement
Configuration dans le fichier docker-compose.frankenphp.yml:
- DATABASE_URL: ********************************************/supra_dev
- CACHE_DSN: redis://redis:6379

### PHP
Configuration dans docker/frankenphp/php.ini:
- OPcache active
- Extensions requises installees
- Logs dans var/log/
