<?php

namespace App\State\Batch\Base;

use ApiPlatform\State\ProcessorInterface;
use App\Domain\Service\Batch\BatchOptimizationConfigService;
use App\Domain\Service\Notification\BatchNotificationService;
use App\Entity\Importation\Importation;
use App\Entity\Structure\EntiteJuridique;
use Doctrine\DBAL\Exception\DeadlockException;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Exception\BadRequestException;

/**
 * Classe de base optimisée pour les BatchProcessor
 * 
 * Fonctionnalités :
 * - Transactions par micro-batch pour éviter les timeouts
 * - Gestion mémoire avec clear() automatique
 * - BULK operations quand possible
 * - Notifications optimisées
 * - Gestion des erreurs avec retry automatique
 */
abstract class BatchProcessorOptimized implements ProcessorInterface
{
    protected BatchOptimizationConfigService $configService;
    protected BatchNotificationService $notificationService;
    protected LoggerInterface $logger;
    protected EntityManagerInterface $entityManager;
    
    // Statistiques du batch en cours
    protected array $batchStats = [];
    protected string $currentBatchId;
    protected array $currentConfig = [];

    public function __construct(
        BatchOptimizationConfigService $configService,
        BatchNotificationService $notificationService,
        LoggerInterface $logger,
        EntityManagerInterface $entityManager
    ) {
        $this->configService = $configService;
        $this->notificationService = $notificationService;
        $this->logger = $logger;
        $this->entityManager = $entityManager;
    }

    /**
     * Traite un batch de données avec optimisations
     */
    protected function processOptimizedBatch(
        array $arrayData,
        string $resourceType,
        ?EntiteJuridique $entiteJuridique = null
    ): array {
        $totalRecords = count($arrayData);
        
        // Configuration optimisée selon le volume
        $this->currentConfig = $this->configService->getResourceConfig($resourceType, $totalRecords);
        
        // Démarrer le batch avec notifications optimisées
        $this->currentBatchId = $this->notificationService->startBatch(
            $resourceType,
            $totalRecords,
            $this->currentConfig['disable_emails']
        );

        $this->logger->info('Démarrage batch optimisé', [
            'batch_id' => $this->currentBatchId,
            'resource_type' => $resourceType,
            'total_records' => $totalRecords,
            'config' => $this->currentConfig,
        ]);

        $results = [];
        $errors = [];
        $processedCount = 0;
        $createdCount = 0;
        $updatedCount = 0;

        try {
            // Traitement par micro-batch
            $microBatches = array_chunk($arrayData, $this->currentConfig['micro_batch_size']);
            
            foreach ($microBatches as $batchIndex => $microBatch) {
                $microBatchResult = $this->processMicroBatch(
                    $microBatch,
                    $batchIndex,
                    $resourceType,
                    $entiteJuridique
                );

                $results = array_merge($results, $microBatchResult['results']);
                $errors = array_merge($errors, $microBatchResult['errors']);
                $processedCount += $microBatchResult['processed'];
                $createdCount += $microBatchResult['created'];
                $updatedCount += $microBatchResult['updated'];

                // Gestion mémoire
                if (($batchIndex + 1) % $this->getMemoryClearFrequency() === 0) {
                    $this->clearEntityManagerMemory();
                }

                // Mise à jour des stats
                $this->notificationService->updateBatchStats(
                    $microBatchResult['processed'],
                    $microBatchResult['created'],
                    $microBatchResult['updated']
                );
            }

            // Créer l'enregistrement d'importation
            $importation = $this->createImportationRecord(
                $this->entityManager,
                $resourceType,
                empty($errors),
                $entiteJuridique?->getId()
            );

            // Finaliser le batch
            $finalStats = $this->notificationService->finalizeBatch($entiteJuridique ?? new EntiteJuridique());

            return [
                'success' => true,
                'processed' => $processedCount,
                'created' => $createdCount,
                'updated' => $updatedCount,
                'errors' => $errors,
                'results' => $results,
                'importation_created' => $importation !== null && $importation->getId() !== null,
                'batch_stats' => $finalStats,
            ];

        } catch (\Exception $e) {
            $this->handleBatchError($e, $entiteJuridique);
            throw $e;
        }
    }

    /**
     * Traite un micro-batch avec transaction isolée
     */
    protected function processMicroBatch(
        array $microBatch,
        int $batchIndex,
        string $resourceType,
        ?EntiteJuridique $entiteJuridique = null
    ): array {
        $results = [];
        $errors = [];
        $processed = 0;
        $created = 0;
        $updated = 0;

        $retryCount = 0;
        $maxRetries = 3;

        while ($retryCount <= $maxRetries) {
            try {
                $this->entityManager->beginTransaction();

                foreach ($microBatch as $index => $item) {
                    try {
                        $globalIndex = ($batchIndex * $this->currentConfig['micro_batch_size']) + $index;
                        
                        $result = $this->processItem($item, $globalIndex, $resourceType);
                        
                        if ($result) {
                            $results[] = $result['dto'];
                            $processed++;
                            
                            if ($result['is_new']) {
                                $created++;
                            } else {
                                $updated++;
                            }
                        }

                    } catch (\Exception $e) {
                        $errors[] = [
                            'index' => $globalIndex,
                            'data' => $item,
                            'error' => $e->getMessage(),
                        ];

                        // Ajouter l'erreur au service de notification
                        if ($entiteJuridique) {
                            $this->notificationService->addBatchError($entiteJuridique, $e, [
                                'item_index' => $globalIndex,
                                'micro_batch' => $batchIndex,
                            ]);
                        }
                    }
                }

                $this->entityManager->flush();
                $this->entityManager->commit();
                
                break; // Succès, sortir de la boucle de retry

            } catch (DeadlockException $e) {
                $this->entityManager->rollback();
                $retryCount++;
                
                if ($retryCount > $maxRetries) {
                    throw $e;
                }
                
                // Attendre avant de retry (backoff exponentiel)
                usleep(100000 * pow(2, $retryCount)); // 100ms, 200ms, 400ms
                
                $this->logger->warning('Deadlock détecté, retry', [
                    'batch_id' => $this->currentBatchId,
                    'micro_batch' => $batchIndex,
                    'retry' => $retryCount,
                ]);

            } catch (\Exception $e) {
                $this->entityManager->rollback();
                throw $e;
            }
        }

        return [
            'results' => $results,
            'errors' => $errors,
            'processed' => $processed,
            'created' => $created,
            'updated' => $updated,
        ];
    }

    /**
     * Traite un élément individuel - à implémenter dans les classes filles
     */
    abstract protected function processItem(array $item, int $index, string $resourceType): ?array;

    /**
     * Gère les erreurs de batch
     */
    protected function handleBatchError(\Exception $e, ?EntiteJuridique $entiteJuridique = null): void
    {
        $this->logger->error('Erreur critique dans le batch', [
            'batch_id' => $this->currentBatchId,
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString(),
        ]);

        if ($entiteJuridique) {
            $this->notificationService->addBatchError($entiteJuridique, $e, [
                'is_critical' => true,
                'batch_terminated' => true,
            ]);
        }
    }

    /**
     * Nettoie la mémoire de l'EntityManager
     */
    protected function clearEntityManagerMemory(): void
    {
        $this->entityManager->clear();
        
        if (function_exists('gc_collect_cycles')) {
            gc_collect_cycles();
        }
        
        $this->logger->debug('Mémoire EntityManager nettoyée', [
            'batch_id' => $this->currentBatchId,
            'memory_usage' => memory_get_usage(true),
        ]);
    }

    /**
     * Retourne la fréquence de nettoyage mémoire
     */
    protected function getMemoryClearFrequency(): int
    {
        return max(1, intval($this->currentConfig['memory_clear_interval'] / $this->currentConfig['micro_batch_size']));
    }

    /**
     * Crée un enregistrement d'importation (hérité de BatchProcessorBase)
     */
    protected function createImportationRecord(
        EntityManagerInterface $entityManager,
        string $resourceName,
        bool $isSuccessful = true,
        ?string $entiteJuridiqueId = null
    ): Importation {
        try {
            $importation = new Importation();
            $importation->setNomRessource($resourceName);
            $importation->setEstReussie($isSuccessful);
            $importation->setDateImportation(new \DateTime());

            if ($entiteJuridiqueId) {
                $entiteJuridique = $entityManager->getRepository(EntiteJuridique::class)->find($entiteJuridiqueId);
                if ($entiteJuridique) {
                    $importation->setEntiteJuridique($entiteJuridique);
                }
            }

            $entityManager->persist($importation);
            
            if (!$entityManager->getConnection()->isTransactionActive()) {
                $entityManager->flush($importation);
            }

            return $importation;

        } catch (\Exception $e) {
            $this->logger->error('Erreur création Importation', [
                'error' => $e->getMessage(),
                'resource' => $resourceName,
            ]);

            $fallbackImportation = new Importation();
            $fallbackImportation->setNomRessource($resourceName);
            $fallbackImportation->setEstReussie($isSuccessful);
            
            return $fallbackImportation;
        }
    }

    /**
     * Valide les champs requis d'un élément
     */
    protected function validateRequiredFields(array $item, array $requiredFields, int $index): void
    {
        foreach ($requiredFields as $field) {
            if (!isset($item[$field]) || empty($item[$field])) {
                throw new BadRequestException("Missing required field '$field' at index $index");
            }
        }
    }
}
