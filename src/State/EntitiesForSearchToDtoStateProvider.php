<?php

namespace App\State;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use App\ApiResource\SearchDto;
use App\Domain\Extension\CoreSupraFilter\Agent\AgentCategoryExcludeFilter;
use App\Domain\Extension\CoreSupraFilter\Cr\CrCategoryExcludeFilter;
use App\Domain\Extension\CoreSupraFilter\Pole\PoleCategoryExcludeFilter;
use App\Domain\Extension\CoreSupraFilter\Service\ServiceCategoryExcludeFilter;
use App\Domain\Extension\CoreSupraFilter\Uf\UfsCategoryExcludeFilter;
use App\Repository\AgentRepository;
use App\Repository\PoleRepository;
use App\Repository\ServiceRepository;
use App\Repository\UfsRepository;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Contracts\Cache\CacheInterface;
use Symfony\Contracts\Cache\ItemInterface;

class EntitiesForSearchToDtoStateProvider implements ProviderInterface
{
    public function __construct(
        private AgentRepository $agentRepository,
        private PoleRepository $poleRepository,
        private ServiceRepository $serviceRepository,
        private UfsRepository $ufsRepository,
        private CacheInterface $cache,
        #[Autowire('%env(int:API_CACHE_TTL)%')] private int $apiCacheTtl = 3600,
    ) {
    }

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): object|array|null
    {
        // Clé de cache pour les entités de recherche
        $cacheKey = 'supra_search_entities_all_for_fuse_js';

        return $this->cache->get($cacheKey, function (ItemInterface $item) {
            $item->expiresAfter($this->apiCacheTtl); // TTL depuis la variable d'environnement
            
            try {
                $searchDtos = [];
                
                // Récupérer les agents via repository
                $searchDtos = array_merge($searchDtos, $this->getPraticiens());
                
                // Récupérer les pôles via repository
                $searchDtos = array_merge($searchDtos, $this->getPoles());
                
                // Récupérer les services via repository
                $searchDtos = array_merge($searchDtos, $this->getServices());
                
                // Récupérer les UFs via repository
                $searchDtos = array_merge($searchDtos, $this->getUfs());
                
                if (empty($searchDtos)) {
                    $dto = new SearchDto();
                    $dto->id = "no_data";
                    $dto->type = "praticien";
                    $dto->nom = "Aucune donnée";
                    $dto->prenom = "Base vide";
                    $dto->fullName = "Aucune donnée";
                    $dto->keywords = ["vide"];
                    
                    return [$dto];
                }
                
                return $searchDtos;
                
            } catch (\Exception $e) {
                $dto = new SearchDto();
                $dto->id = "error";
                $dto->type = "praticien";
                $dto->nom = "Erreur";
                $dto->prenom = substr($e->getMessage(), 0, 50);
                $dto->fullName = "Erreur SQL";
                $dto->keywords = ["erreur"];
                
                return [$dto];
            }
        });
    }

    private function getPraticiens(): array
    {
        $today = new \DateTime();
        $excludedCategories = AgentCategoryExcludeFilter::getExcludedCategories();

        $queryBuilder = $this->agentRepository->createQueryBuilder('a')
            ->where('a.isActif = :isActif')
            ->andWhere('a.dateDepart IS NULL OR a.dateDepart > :today')
            ->setParameter('isActif', true)
            ->setParameter('today', $today);

        // Ajouter le filtre des catégories
        if (!empty($excludedCategories)) {
            $queryBuilder
                ->andWhere('(UPPER(a.categorie) NOT IN (:excludedCategories) OR a.categorie IS NULL)')
                ->setParameter('excludedCategories', array_map('strtoupper', $excludedCategories));
        }


        $agents = $queryBuilder
            ->orderBy('a.nom', 'ASC')
            ->addOrderBy('a.prenom', 'ASC')
            ->getQuery()
            ->getResult();



        $dtos = [];
        foreach ($agents as $agent) {
            $dto = new SearchDto();
            $dto->id = (string)$agent->getId();
            $dto->type = 'praticien';
            
            // Utiliser les getters de l'entité
            $dto->nom = $agent->getNom();
            $dto->prenom = $agent->getPrenom();
            $dto->matricule = $agent->getHrUser(); // hrUser comme matricule
            
            // Construire le nom complet avec titre si disponible
            $fullNameParts = array_filter([
                $agent->getTitre(),
                $agent->getNom(),
                $agent->getPrenom()
            ]);
            $dto->fullName = implode(' ', $fullNameParts);
            
            // Créer des mots-clés pour la recherche
            $keywords = array_filter([
                $agent->getNom(),
                $agent->getPrenom(),
                $agent->getTitre(),
                $agent->getHrUser(),
                $agent->getEmail(),
                $dto->fullName
            ]);
            $dto->keywords = array_unique($keywords);
            
            $dtos[] = $dto;
        }

        return $dtos;
    }

    private function getPoles(): array
    {
        $today = new \DateTime();
        $excludedPolecodes =  PoleCategoryExcludeFilter::getExcludedPolecodes();

        $queryBuilder = $this->poleRepository->createQueryBuilder('p')
            ->where('p.isActif = :isActif')
            ->andWhere('p.datfin IS NULL OR p.datfin > :today')
            ->setParameter('isActif', true)
            ->setParameter('today', $today);

        // Ajouter le filtre des codes de pôles
        if (!empty($excludedPolecodes)) {
            $queryBuilder
                ->andWhere('(UPPER(p.polecode) NOT IN (:excludedPolecodes) OR p.polecode IS NULL)')
                ->setParameter('excludedPolecodes', array_map('strtoupper', $excludedPolecodes));
        }

        $poles = $queryBuilder
            ->orderBy('p.libelle', 'ASC')
            ->getQuery()
            ->getResult();

        $dtos = [];
        foreach ($poles as $pole) {
            $dto = new SearchDto();
            $dto->id = (string)$pole->getId();
            $dto->type = 'pole';
            $dto->nom = $pole->getLibelle();
            $dto->keywords = [
                $pole->getLibelle(),
                $pole->getPolecode(),
                $dto->fullName
            ];
            
            $dtos[] = $dto;
        }

        return $dtos;
    }

    private function getServices(): array
    {
        $today = new \DateTime();
        $excludedSecodes =  ServiceCategoryExcludeFilter::getExcludedSecodes();

        $queryBuilder = $this->serviceRepository->createQueryBuilder('s')
            ->where('s.isActif = :isActif')
            ->andWhere('s.datfin IS NULL OR s.datfin > :today')
            ->setParameter('isActif', true)
            ->setParameter('today', $today);

        // Ajouter le filtre des codes de services
        if (!empty($excludedSecodes)) {
            $queryBuilder
                ->andWhere('(UPPER(s.secode) NOT IN (:excludedSecodes) OR s.secode IS NULL)')
                ->setParameter('excludedSecodes', array_map('strtoupper', $excludedSecodes));
        }

        $services = $queryBuilder
            ->orderBy('s.libelle', 'ASC')
            ->getQuery()
            ->getResult();

        $dtos = [];
        foreach ($services as $service) {
            $dto = new SearchDto();
            $dto->id = (string)$service->getId();
            $dto->type = 'service';
            $dto->nom = $service->getLibelle();
            $dto->fullName = sprintf('%s (%s)', $service->getLibelle(), $service->getSecode());
            $dto->keywords = [
                $service->getLibelle(),
                $service->getSecode(),
                $dto->fullName
            ];
            
            $dtos[] = $dto;
        }

        return $dtos;
    }

    private function getUfs(): array
    {
        $today = new \DateTime();
        $excludedUfcodes =  UfsCategoryExcludeFilter::getExcludedUfcodes();
        $excludedCrcodes =  CrCategoryExcludeFilter::getExcludedCrcodes();
        $excludedPolecodes = PoleCategoryExcludeFilter::getExcludedPolecodes();

        $queryBuilder = $this->ufsRepository->createQueryBuilder('u')
            ->leftJoin('u.cr', 'c')
            ->leftJoin('c.pole', 'p')
            ->where('u.isActif = :isActif')
            ->andWhere('u.datfin IS NULL OR u.datfin > :today')
            ->setParameter('isActif', true)
            ->setParameter('today', $today);

        // Filtrage direct des UFs
        if (!empty($excludedUfcodes)) {
            $queryBuilder
                ->andWhere('(UPPER(u.ufcode) NOT IN (:excludedUfcodes) OR u.ufcode IS NULL)')
                ->setParameter('excludedUfcodes', array_map('strtoupper', $excludedUfcodes));
        }

        // Filtrage cascade des CRs
        if (!empty($excludedCrcodes)) {
            $queryBuilder
                ->andWhere('(UPPER(c.crcode) NOT IN (:excludedCrcodes) OR c.crcode IS NULL)')
                ->setParameter('excludedCrcodes', array_map('strtoupper', $excludedCrcodes));
        }

        // Filtrage cascade des pôles
        if (!empty($excludedPolecodes)) {
            $queryBuilder
                ->andWhere('(UPPER(p.polecode) NOT IN (:excludedPolecodes) OR p.polecode IS NULL)')
                ->setParameter('excludedPolecodes', array_map('strtoupper', $excludedPolecodes));
        }

        $ufs = $queryBuilder
            ->orderBy('u.libelle', 'ASC')
            ->getQuery()
            ->getResult();

        $dtos = [];
        foreach ($ufs as $uf) {
            $dto = new SearchDto();
            $dto->id = (string)$uf->getId();
            $dto->type = 'ufs';
            $dto->nom = $uf->getLibelle();
            $dto->fullName = sprintf('%s (%s)', $uf->getLibelle(), $uf->getUfcode());
            $dto->keywords = [
                $uf->getLibelle(),
                $uf->getUfcode(),
                $dto->fullName
            ];
            
            $dtos[] = $dto;
        }

        return $dtos;
    }
}
