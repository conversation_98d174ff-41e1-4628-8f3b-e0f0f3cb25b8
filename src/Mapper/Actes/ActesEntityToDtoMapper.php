<?php

namespace App\Mapper\Actes;

use ApiPlatform\Metadata\IriConverterInterface;
use App\ApiResource\Activite\ActesDto;
use App\ApiResource\Praticien\AgentDto;
use App\ApiResource\Structure\CrDto;
use App\ApiResource\Structure\EntiteJuridiqueDto;
use App\ApiResource\Structure\UfsDto;
use App\Entity\Activite\Actes;
use Symfonycasts\MicroMapper\AsMapper;
use Symfonycasts\MicroMapper\MapperInterface;
use Symfonycasts\MicroMapper\MicroMapperInterface;

#[AsMapper(from: Actes::class, to: ActesDto::class)]
class ActesEntityToDtoMapper implements MapperInterface
{
    public function __construct(
        private MicroMapperInterface $microMapper,
        private IriConverterInterface $iriConverter,
    ) {}

    public function load(object $from, string $toClass, array $context): object
    {
        $acte = $from;
        assert($acte instanceof Actes);

        $dto = new ActesDto();
        $dto->id = $acte->getId(); // On charge uniquement l'ID ici

        return $dto;
    }

    public function populate(object $from, object $to, array $context): object
    {
        $acte = $from;
        $dto = $to;
        assert($acte instanceof Actes);
        assert($dto instanceof ActesDto);

        $dto->dateCreation = $acte->getDateCreation();

        $dto->code = $acte->getCode();
        $dto->description = $acte->getDescription();
//        $dto->category = $acte->getCategory();
//        $dto->categoriePrincipaleCode = $acte->getCategoriePrincipaleCode();
        $dto->dateRealisation = $acte->getDateRealisation();
        $dto->typeActe = $acte->getTypeActe();
        $dto->nombreDeRealisation = $acte->getNombreDeRealisation();
        $dto->annee = $acte->getAnnee();
        $dto->mois = $acte->getMois();

        // Champs du trait HorodatageTrait
        $dto->validFrom = $acte->getValidFrom();
        $dto->validTo = $acte->getValidTo();
        $dto->periodeType = $acte->getPeriodeType();
        $dto->source = $acte->getSource();

        // Convertir Agent en IRI API Platform
        if ($acte->getAgent()) {
            $dto->agent = $this->microMapper->map($acte->getAgent(), AgentDto::class, [
                MicroMapperInterface::MAX_DEPTH => 2,
            ]);
//            if ($acte->getAgent() && $acte->getAgent()->getHopital()) {
//                $dto->agent->hopital =  $this->microMapper->map($acte->getAgent()->getHopital(),EntiteJuridiqueDto::class);
//            }
            unset($dto->agent->hopital);
        }

        // Mapper les nouveaux champs
        $dto->internum = $acte->getInternum();
        $dto->semaineIso = $acte->getSemaineIso();
        $dto->typeVenue = $acte->getTypeVenue();
        $dto->libTypeVenue = $acte->getLibTypeVenue();
        $dto->activite = $acte->getActivite();
        $dto->activiteLib = $acte->getActiviteLib();
        $dto->icrA = $acte->getIcrA();
        $dto->coefficient = $acte->getCoefficient();
        $dto->lettreCoef = $acte->getLettreCoef();

        // Convertir UF Principal en IRI API Platform
        if ($acte->getUfPrincipal()) {
            $dto->ufPrincipal = $this->microMapper->map($acte->getUfPrincipal(), UfsDto::class, [
                MicroMapperInterface::MAX_DEPTH => 2,
            ]);
        }

         if ($acte->getUfDemande()) {
            $dto->ufDemande = $this->microMapper->map($acte->getUfDemande(), UfsDto::class, [
                MicroMapperInterface::MAX_DEPTH => 2,
            ]);
        }

         if ($acte->getUfIntervention()) {
            $dto->ufIntervention = $this->microMapper->map($acte->getUfIntervention(), UfsDto::class, [
                MicroMapperInterface::MAX_DEPTH => 2,
            ]);
        }


        return $dto;
    }
}
