-- Active pg_trgm et tsvector (si ce n’est pas déjà fait)
CREATE EXTENSION IF NOT EXISTS pg_trgm; -- pour la recherche floue
CREATE EXTENSION IF NOT EXISTS unaccent; -- pour ignorer les accents

-- Ajoute des indexes pour accélérer la recherche
-- Full-Text Search pour Praticiens
ALTER TABLE praticien ADD COLUMN search_vector tsvector
    GENERATED ALWAYS AS (
        setweight(to_tsvector('simple', coalesce(nom, '')), 'A') ||
        setweight(to_tsvector('simple', coalesce(prenom, '')), 'B') ||
        setweight(to_tsvector('simple', coalesce(specialite, '')), 'C') ||
        setweight(to_tsvector('simple', coalesce(matricule, '')), 'D')
        ) STORED;

CREATE INDEX praticien_search_idx ON praticien USING GIN(search_vector);

-- Recherche floue sur les noms
CREATE INDEX praticien_name_trgm ON praticien USING GIN(nom gin_trgm_ops);
CREATE INDEX pole_name_trgm ON pole USING GIN(nom gin_trgm_ops);
CREATE INDEX service_name_trgm ON service USING GIN(nom gin_trgm_ops);
CREATE INDEX ufs_name_trgm ON ufs USING GIN(nom gin_trgm_ops);


----------------------------------------------------
--  Ajouter tsvector aux autres entités (Pôle, Service, UFS)

-- Full-Text Search pour Pôles
ALTER TABLE pole ADD COLUMN search_vector tsvector
    GENERATED ALWAYS AS (
        setweight(to_tsvector('simple', coalesce(nom, '')), 'A')
        ) STORED;
CREATE INDEX pole_search_idx ON pole USING GIN(search_vector);

-- Full-Text Search pour Services
ALTER TABLE service ADD COLUMN search_vector tsvector
    GENERATED ALWAYS AS (
        setweight(to_tsvector('simple', coalesce(nom, '')), 'A')
        ) STORED;
CREATE INDEX service_search_idx ON service USING GIN(search_vector);

-- Full-Text Search pour UFS
ALTER TABLE ufs ADD COLUMN search_vector tsvector
    GENERATED ALWAYS AS (
        setweight(to_tsvector('simple', coalesce(nom, '')), 'A')
        ) STORED;
CREATE INDEX ufs_search_idx ON ufs USING GIN(search_vector);
