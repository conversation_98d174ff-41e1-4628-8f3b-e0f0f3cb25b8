<?php

namespace App\Domain\Service\Activite;

use App\Entity\Importation\Importation;
use App\Repository\ImportationRepository;

/**
 * Service pour récupérer et afficher les informations de détection de problème pour les actes.
 */
class ActesDetectionProblemService
{
    public function __construct(
        private readonly ImportationRepository $importationRepository
    ) {}

    /**
     * Récupère la dernière importation réussie pour les actes
     * 
     * @return Importation|null L'importation la plus récente ou null si aucune n'est trouvée
     */
    public function getLatestActesImportation(): ?Importation
    {
        return $this->importationRepository->findLatestSuccessfulByRessource('Actes');
    }

    /**
     * Récupère les informations de détection de problème depuis la dernière importation réussie
     * 
     * @return array|null Les informations de détection de problème ou null si aucune n'est trouvée
     */
    public function getDetectProbleme(): ?array
    {
        $importation = $this->getLatestActesImportation();
        if (!$importation) {
            return null;
        }
        
        return $importation->getDetectProbleme();
    }

    /**
     * Vérifie si un internum fait partie des interventions non enrichies
     * 
     * @param string $internum Le numéro d'intervention à vérifier
     * @return bool True si l'internum fait partie des interventions non enrichies, false sinon
     */
    public function isInterventionNonEnrichie(string $internum): bool
    {
        $importation = $this->getLatestActesImportation();
        if (!$importation) {
            return false;
        }
        
        return $importation->isInterventionNonEnrichie($internum);
    }

    /**
     * Formate les informations de détection de problème pour l'affichage
     * 
     * @return string Les informations formatées en HTML
     */
    public function getDetectProblemeDisplay(): string
    {
        $importation = $this->getLatestActesImportation();
        if (!$importation) {
            return 'Aucune information de détection de problème disponible.';
        }
        
        return $importation->getDetectProblemeDisplay();
    }
}