<?php

namespace App\Controller\Admin;

use App\Entity\Importation\Importation;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\BooleanField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateTimeField;
use EasyCorp\Bundle\EasyAdminBundle\Field\IdField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;

/**
 * Contrôleur CRUD pour l'entité Importation.
 * 
 * Permet de gérer les enregistrements d'importation (Agent, Structure, Actes)
 * dans l'interface d'administration.
 */
class ImportationCrudController extends AbstractCrudController
{
    public static function getEntityFqcn(): string
    {
        return Importation::class;
    }

    public function configureActions(Actions $actions): Actions
    {
        return $actions
            ->add(Crud::PAGE_INDEX, Action::DETAIL)
            ->disable(Action::NEW, Action::EDIT); // Désactiver la création et l'édition car les importations sont créées par les commandes
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setEntityLabelInSingular('Importation')
            ->setEntityLabelInPlural('Importations')
            ->setPageTitle('index', 'Historique des importations automatiques ')
            ->setPageTitle('detail', 'Détail de l\'importation')
            ->setDefaultSort(['dateImportation' => 'DESC']) // Trier par date d'importation décroissante
            ->showEntityActionsInlined();
    }

    public function configureFields(string $pageName): iterable
    {
        if ($pageName === Crud::PAGE_INDEX) {
            return [
                IdField::new('id')->hideOnForm(),
                TextField::new('nomRessource')
                    ->setLabel('Type de ressource')
                    ->setHelp('Type de ressource importée (Agent, Structure, Actes)'),
                DateTimeField::new('dateImportation')
                    ->setLabel('Date d\'importation')
                    ->setFormat('dd/MM/yyyy HH:mm:ss')
                    ->setTimezone('Europe/Paris'),
                BooleanField::new('estReussie')
                    ->setLabel('Statut')
                    ->renderAsSwitch(false) // Afficher comme badge au lieu d'un switch
                    ->setHelp('Indique si l\'importation s\'est terminée avec succès'),
                AssociationField::new('entiteJuridique')
                    ->setLabel('Entité juridique')
                    ->setHelp('Entité juridique concernée par l\'importation'),
                BooleanField::new('isActif')
                    ->setLabel('Actif')
                    ->renderAsSwitch(false),
            ];
        }
        
        // Vue détaillée
        return [
            IdField::new('id')->hideOnForm(),
            TextField::new('nomRessource')
                ->setLabel('Type de ressource')
                ->setHelp('Type de ressource importée (Agent, Structure, Actes)'),
            DateTimeField::new('dateImportation')
                ->setLabel('Date d\'importation')
                ->setFormat('dd/MM/yyyy HH:mm:ss')
                ->setTimezone('Europe/Paris'),
            BooleanField::new('estReussie')
                ->setLabel('Statut de l\'importation')
                ->renderAsSwitch(false)
                ->setHelp('Indique si l\'importation s\'est terminée avec succès'),
            AssociationField::new('entiteJuridique')
                ->setLabel('Entité juridique')
                ->setHelp('Entité juridique concernée par l\'importation'),
            DateTimeField::new('dateCreation')
                ->setLabel('Date de création')
                ->setFormat('dd/MM/yyyy HH:mm:ss')
                ->setTimezone('Europe/Paris')
                ->setHelp('Date de création de l\'enregistrement'),
            BooleanField::new('isActif')
                ->setLabel('Actif pour le système')
                ->renderAsSwitch(false)
                ->setHelp('Indique si l\'enregistrement est actif'),
            TextField::new('detectProblemeDisplay')
                ->setLabel('Détection de problèmes')
                ->setHelp('Informations sur les problèmes détectés lors de l\'importation')
                ->renderAsHtml()
                ->hideOnIndex(),
        ];
    }
}