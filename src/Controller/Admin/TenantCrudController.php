<?php

namespace App\Controller\Admin;

use App\Entity\Structure\Tenant;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\BooleanField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateField;
use EasyCorp\Bundle\EasyAdminBundle\Field\IdField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextareaField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextEditorField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;

class TenantCrudController extends AbstractCrudController
{
    public static function getEntityFqcn(): string
    {
        return Tenant::class;
    }

    public function configureActions(Actions $actions): Actions
    {
        return $actions
            ->add(Crud::PAGE_INDEX, Action::DETAIL);
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setEntityLabelInSingular('Tenant')
            ->setEntityLabelInPlural('Tenants')
            ->setPageTitle('index', 'Gestion des Tenants')
            ->setPageTitle('detail', 'Détails du Tenant')
            ->setPageTitle('new', 'Créer un nouveau Tenant')
            ->setPageTitle('edit', 'Modifier le Tenant')
            ->setHelp('index', '<strong>Astuce :</strong> Pour gagner de la place, double-cliquez sur la barre de séparation pour masquer ou afficher le menu latéral.')
            ->showEntityActionsInlined();
    }

    public function configureFields(string $pageName): iterable
    {
//        return [
//            IdField::new('id')->hideOnForm(), // Cache l'ID dans le formulaire
//            TextField::new('nom', 'Nom du tenant'),
//            TextareaField::new('description', 'Description'),
//        ];

        return [
            TextField::new('id')->hideOnForm(),

            TextField::new('code')
                ->setLabel('Code')
                ->setHelp('Code unique du tenant'),

            TextField::new('nom')
                ->setLabel('Nom')
                ->setHelp('Nom du tenant (CHU, établissement,Sirh-rpa-dev...)'),
            BooleanField::new('is_actif')->setLabel('Est actif'),

            TextField::new('description')
                ->setLabel('Description')
                ->setHelp('Brève description du tenant'),

            DateField::new('date_creation')
                ->setLabel('Date de Création')
                ->setHelp('Date à laquelle ce tenant a été créé')
                ->setFormTypeOption('mapped', false)
                ->hideOnForm(),

            AssociationField::new('hopitals')
                ->setLabel('Hôpitaux associés')
                ->setHelp('Liste des hôpitaux liés à ce tenant')
                ->setFormTypeOption('by_reference', false)
                ->hideOnIndex(), // On ne l'affiche pas dans la liste pour éviter d'encombrer
        ];
    }

}
