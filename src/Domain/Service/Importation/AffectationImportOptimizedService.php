<?php

namespace App\Domain\Service\Importation;

use App\Controller\Importation\Agent\Dto\AffectationAgentUfDto;
use App\Domain\Converter\DataConverter;
use App\Domain\Service\Batch\BatchOptimizationConfigService;
use App\Domain\Service\Core\AgentFinderService;
use App\Domain\Service\Core\UfsFinderService;
use App\Domain\Service\Notification\BatchNotificationService;
use App\Entity\Praticien\Agent;
use App\Entity\Praticien\AgentUfs;
use App\Entity\Structure\EntiteJuridique;
use App\Entity\Structure\Ufs;
use Doctrine\DBAL\Exception\DeadlockException;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;

/**
 * Service optimisé pour l'importation d'affectations avec gestion des gros volumes
 * 
 * Optimisations :
 * - Transactions par micro-batch
 * - Gestion mémoire avec clear() automatique
 * - Notifications optimisées
 * - Retry automatique sur deadlock
 */
class AffectationImportOptimizedService
{
    public function __construct(
        private readonly EntityManagerInterface $em,
        private readonly UfsFinderService $ufsFinderService,
        private readonly AgentFinderService $agentFinderService,
        private readonly BatchOptimizationConfigService $configService,
        private readonly BatchNotificationService $notificationService,
        private readonly LoggerInterface $logger
    ) {}

    /**
     * @param AffectationAgentUfDto[] $dtos
     */
    public function importAffectationsOptimized(array $dtos, string $ejCode): array
    {
        // Validation de l'entité juridique en début de traitement
        $entiteJuridique = $this->em->getRepository(EntiteJuridique::class)
            ->findOneBy(['code' => $ejCode]);

        if (!$entiteJuridique) {
            throw new \Exception("Entité juridique non trouvée : {$ejCode}");
        }

        $totalRecords = count($dtos);
        
        // Configuration optimisée selon le volume
        $config = $this->configService->getResourceConfig('Affectations', $totalRecords);
        
        // Démarrer le batch avec notifications optimisées
        $batchId = $this->notificationService->startBatch(
            'Affectations',
            $totalRecords,
            $config['disable_emails']
        );

        $this->logger->info('Démarrage import affectations optimisé', [
            'batch_id' => $batchId,
            'ej_code' => $ejCode,
            'total_records' => $totalRecords,
            'config' => $config,
        ]);

        $result = [
            'processed' => 0,
            'created' => 0,
            'updated' => 0,
            'errors' => []
        ];

        try {
            // Traitement par micro-batch
            $microBatches = array_chunk($dtos, $config['micro_batch_size']);
            
            foreach ($microBatches as $batchIndex => $microBatch) {
                $microBatchResult = $this->processMicroBatchAffectations(
                    $microBatch,
                    $batchIndex,
                    $entiteJuridique,
                    $config
                );

                $result['processed'] += $microBatchResult['processed'];
                $result['created'] += $microBatchResult['created'];
                $result['updated'] += $microBatchResult['updated'];
                $result['errors'] = array_merge($result['errors'], $microBatchResult['errors']);

                // Gestion mémoire
                if (($batchIndex + 1) % $this->getMemoryClearFrequency($config) === 0) {
                    $this->clearEntityManagerMemory();
                }

                // Mise à jour des stats
                $this->notificationService->updateBatchStats(
                    $microBatchResult['processed'],
                    $microBatchResult['created'],
                    $microBatchResult['updated']
                );
            }

            // Finaliser le batch
            $finalStats = $this->notificationService->finalizeBatch($entiteJuridique);
            $result['batch_stats'] = $finalStats;

            $this->logger->info('Import affectations terminé', [
                'batch_id' => $batchId,
                'result' => $result,
            ]);

            return $result;

        } catch (\Exception $e) {
            $this->handleBatchError($e, $entiteJuridique);
            throw $e;
        }
    }

    /**
     * Traite un micro-batch d'affectations avec transaction isolée
     */
    private function processMicroBatchAffectations(
        array $microBatch,
        int $batchIndex,
        EntiteJuridique $entiteJuridique,
        array $config
    ): array {
        $processed = 0;
        $created = 0;
        $updated = 0;
        $errors = [];

        $retryCount = 0;
        $maxRetries = 3;

        while ($retryCount <= $maxRetries) {
            try {
                $this->em->beginTransaction();

                foreach ($microBatch as $index => $dto) {
                    try {
                        $globalIndex = ($batchIndex * $config['micro_batch_size']) + $index;
                        
                        $result = $this->processAffectationItem($dto, $entiteJuridique);
                        
                        if ($result) {
                            $processed++;
                            if ($result['is_new']) {
                                $created++;
                            } else {
                                $updated++;
                            }
                        }

                    } catch (\Exception $e) {
                        $errors[] = [
                            'index' => $globalIndex,
                            'dto' => $dto,
                            'error' => $e->getMessage(),
                        ];

                        // Ajouter l'erreur au service de notification
                        $this->notificationService->addBatchError($entiteJuridique, $e, [
                            'item_index' => $globalIndex,
                            'micro_batch' => $batchIndex,
                            'agent_connexion' => $dto->uConnexion ?? 'unknown',
                            'code_uf' => $dto->codeUf ?? 'unknown',
                        ]);
                    }
                }

                $this->em->flush();
                $this->em->commit();
                
                break; // Succès, sortir de la boucle de retry

            } catch (DeadlockException $e) {
                $this->em->rollback();
                $retryCount++;
                
                if ($retryCount > $maxRetries) {
                    throw $e;
                }
                
                // Attendre avant de retry (backoff exponentiel)
                usleep(100000 * pow(2, $retryCount)); // 100ms, 200ms, 400ms
                
                $this->logger->warning('Deadlock détecté, retry', [
                    'micro_batch' => $batchIndex,
                    'retry' => $retryCount,
                ]);

            } catch (\Exception $e) {
                $this->em->rollback();
                throw $e;
            }
        }

        return [
            'processed' => $processed,
            'created' => $created,
            'updated' => $updated,
            'errors' => $errors,
        ];
    }

    /**
     * Traite un élément d'affectation individuel
     */
    private function processAffectationItem(AffectationAgentUfDto $dto, EntiteJuridique $entiteJuridique): ?array
    {
        // Validation préalable du DTO
        $this->validateAffectationDto($dto);
        // 1. Recherche de l'agent
        $agent = $this->agentFinderService->findAgentByConnexionAndEj(
            $dto->uConnexion, // agentHrU = uConnexion
            $entiteJuridique,
            null // pas de dateReference pour l'instant
        );

        if (!$agent) {
            throw new \Exception("Agent non trouvé : {$dto->uConnexion}");
        }

        // 2. Validation des dates
        $dateDebut = DataConverter::parseDateFromFormat($dto->dateDebut, 'd/m/Y');
        $dateFin = DataConverter::parseDateFromFormat($dto->dateFin, 'd/m/Y');

        if (!$dateDebut || !$dateFin) {
            throw new \Exception("Dates invalides : {$dto->dateDebut} - {$dto->dateFin}");
        }

        // 3. Recherche UFS avec validation SCD Type 2
        $ufs = $this->ufsFinderService->findUfByCodeAndEj(
            $dto->codeUf,
            $entiteJuridique,
            null, // pas de dateReference
            $dateDebut, // période complète
            $dateFin
        );

        if (!$ufs) {
            throw new \Exception("UFS non trouvée ou invalide pour la période : {$dto->codeUf}");
        }

        // 4. Recherche ou création de l'affectation
        $agentUfs = $this->findOrCreateAgentUfs($agent, $ufs, $dateDebut, $dateFin);
        $isNew = $agentUfs->getId() === null;

        // 5. Mise à jour des données d'affectation
        $this->updateAffectationData($agentUfs, $dto, $dateDebut, $dateFin);

        if ($isNew) {
            $this->em->persist($agentUfs);
        }

        return [
            'is_new' => $isNew,
            'agent_ufs' => $agentUfs,
        ];
    }

    /**
     * Recherche ou crée une affectation AgentUfs (copié du service original)
     */
    private function findOrCreateAgentUfs(Agent $agent, Ufs $ufs, \DateTime $dateDebut, \DateTime $dateFin): AgentUfs
    {
        // Recherche d'une affectation existante qui se chevauche (utilise les vrais noms de champs)
        $existing = $this->em->getRepository(AgentUfs::class)
            ->createQueryBuilder('au')
            ->where('au.agent = :agent')
            ->andWhere('au.ufs = :ufs')
            ->andWhere('au.date_debut <= :dateFin')
            ->andWhere('au.date_fin >= :dateDebut')
            ->setParameter('agent', $agent)
            ->setParameter('ufs', $ufs)
            ->setParameter('dateDebut', $dateDebut)
            ->setParameter('dateFin', $dateFin)
            ->getQuery()
            ->getOneOrNullResult();

        if ($existing) {
            return $existing;
        }

        // Création d'une nouvelle affectation (comme dans le service original)
        $agentUfs = new AgentUfs();
        $agentUfs->setAgent($agent);
        $agentUfs->setUfs($ufs);

        return $agentUfs;
    }

    /**
     * Met à jour les données d'une affectation (copié du service original)
     */
    private function updateAffectationData(AgentUfs $agentUfs, AffectationAgentUfDto $dto, \DateTime $dateDebut, \DateTime $dateFin): void
    {
        $this->populateAffectation($agentUfs, $dto, $dateDebut, $dateFin);
        // Cette logique assume qu'il ne peut y avoir qu'une seule affectation par agent/UFS pour une période donnée, ce qui est cohérent avec la gestion des affectations RH.
    }

    /**
     * Remplit les données d'affectation (copié du service original)
     */
    private function populateAffectation(AgentUfs $affectation, AffectationAgentUfDto $dto, \DateTime $dateDebut, \DateTime $dateFin): void
    {
        $affectation->setDateDebut($dateDebut);
        $affectation->setDateFin($dateFin);

        // Affectation de validFrom à dateDebut et validTo à null
        $affectation->setValidFrom($dateDebut);
        $affectation->setValidTo(null);

        // Calcul du type de période basé sur les dates converties (pas les DTOs)
        $interval = $dateDebut->diff($dateFin);
        $days = (int)$interval->format('%a');

        if ($days < 32) {
            $periodeType = \App\Domain\Enum\PeriodeType::HEBDOMADAIRE;
        } elseif ($days < 366) {
            $periodeType = \App\Domain\Enum\PeriodeType::MENSUEL;
        } else {
            $periodeType = \App\Domain\Enum\PeriodeType::ANNUEL;
        }

        $affectation->setPeriodeType($periodeType);
        $affectation->setSource($dto->source ?? 'IMPORTATION Manuelle');

        // Utilisation des méthodes statiques de DataConverter
        $affectation->setRgt(DataConverter::cleanString($dto->rgt));
        $affectation->setEtpStatutaire(DataConverter::toFloat($dto->etpStatutaire));
        $affectation->setTauxAffectation(DataConverter::toInt($dto->tauxAffectation));
        $affectation->setSommeEtp(DataConverter::toFloat($dto->sommeEtp));
        $affectation->setAffectationPrincipale(DataConverter::cleanString($dto->affectationPrincipale));
        $affectation->setTypeGrade(DataConverter::cleanString($dto->typeGrade));
        $affectation->setLibelleGrade(DataConverter::cleanString($dto->libelleGrade));
        $affectation->setAbsences(DataConverter::toInt($dto->absences));

        // Calcul automatique de l'ETP basé sur l'ETP statutaire et le taux d'affectation
        $etp = DataConverter::calculateEtp(
            DataConverter::toFloat($dto->etpStatutaire),
            DataConverter::toInt($dto->tauxAffectation)
        );
        $affectation->setEtp($etp);
    }

    /**
     * Gère les erreurs de batch
     */
    private function handleBatchError(\Exception $e, EntiteJuridique $entiteJuridique): void
    {
        $this->logger->error('Erreur critique dans l\'import affectations', [
            'ej_code' => $entiteJuridique->getCode(),
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString(),
        ]);

        $this->notificationService->addBatchError($entiteJuridique, $e, [
            'is_critical' => true,
            'batch_terminated' => true,
        ]);
    }

    /**
     * Nettoie la mémoire de l'EntityManager
     */
    private function clearEntityManagerMemory(): void
    {
        $this->em->clear();
        
        if (function_exists('gc_collect_cycles')) {
            gc_collect_cycles();
        }
        
        $this->logger->debug('Mémoire EntityManager nettoyée', [
            'memory_usage' => memory_get_usage(true),
        ]);
    }

    /**
     * Retourne la fréquence de nettoyage mémoire
     */
    private function getMemoryClearFrequency(array $config): int
    {
        return max(1, intval($config['memory_clear_interval'] / $config['micro_batch_size']));
    }

    /**
     * Valide un DTO d'affectation
     */
    private function validateAffectationDto(AffectationAgentUfDto $dto): void
    {
        if (empty($dto->uConnexion)) {
            throw new \InvalidArgumentException("Le champ uConnexion est obligatoire");
        }

        if (empty($dto->codeUf)) {
            throw new \InvalidArgumentException("Le champ codeUf est obligatoire");
        }

        if (empty($dto->dateDebut)) {
            throw new \InvalidArgumentException("Le champ dateDebut est obligatoire");
        }

        if (empty($dto->dateFin)) {
            throw new \InvalidArgumentException("Le champ dateFin est obligatoire");
        }

        // Validation du format des dates
        if (!preg_match('/^\d{2}\/\d{2}\/\d{4}$/', $dto->dateDebut)) {
            throw new \InvalidArgumentException("Format de dateDebut invalide. Attendu: dd/mm/yyyy");
        }

        if (!preg_match('/^\d{2}\/\d{2}\/\d{4}$/', $dto->dateFin)) {
            throw new \InvalidArgumentException("Format de dateFin invalide. Attendu: dd/mm/yyyy");
        }
    }

    /**
     * Retourne les statistiques détaillées du traitement
     */
    public function getDetailedStats(array $result): array
    {
        $totalProcessed = $result['processed'];
        $totalErrors = count($result['errors']);
        $totalRecords = $totalProcessed + $totalErrors;

        return [
            'total_records' => $totalRecords,
            'processed' => $totalProcessed,
            'created' => $result['created'],
            'updated' => $result['updated'],
            'errors' => $totalErrors,
            'success_rate' => $totalRecords > 0 ? round(($totalProcessed / $totalRecords) * 100, 2) : 0,
            'error_rate' => $totalRecords > 0 ? round(($totalErrors / $totalRecords) * 100, 2) : 0,
            'batch_stats' => $result['batch_stats'] ?? null,
        ];
    }
}
