###> Environment: Production ###
APP_ENV=prod
APP_DEBUG=false
APP_SECRET=production_ultra_secure_secret_key_minimum_256_bits_cryptographically_strong

## commande pour generer la clef de l'application
## php bin/console secrets:generate


###> Database Production ###
DATABASE_USER=supra_prod
DATABASE_PASSWORD=production_ultra_secure_database_password_with_special_chars
DATABASE_HOST=db-prod.chu-nancy.fr
DATABASE_PORT=5432
DATABASE_NAME=supra_prod

DATABASE_URL="postgresql://${DATABASE_USER}:${DATABASE_PASSWORD}@${DATABASE_HOST}:${DATABASE_PORT}/${DATABASE_NAME}?serverVersion=15&charset=utf8"

###> JWT Production ###
JWT_PASSPHRASE=production_jwt_ultra_secure_passphrase_minimum_256_bits
# JWT_SECRET should be defined in a local environment file and not committed to version control
JWT_SECRET_KEY=%kernel.project_dir%/config/jwt/private.pem
JWT_PUBLIC_KEY=%kernel.project_dir%/config/jwt/public.pem

###> Cache Production ###
CACHE_DSN=redis://redis-prod.chu-nancy.fr:6379?auth=production_redis_ultra_secure_password
API_CACHE_TTL=3600

###> Mail Production ###
MAILER_DSN=smtp://mail.chu-nancy.fr:587?encryption=tls&auth_mode=login&username=<EMAIL>&password=production_mail_ultra_secure_password
MAILER_FROM_EMAIL=<EMAIL>

###> LDAP Production ###
LDAP_HOST=ldap://k12adc01.chu-nancy.fr:389
LDAP_DOMAIN=U001PRD
LDAP_BASE_DN=DC=u001prd,DC=local

###> Services Production ###
DATA_INTEGRATOR_BASE_URI=http://data-integrator.chu-nancy.fr:3000/api

###> CORS Production ###
CORS_ALLOW_ORIGIN='^https://supra\.chu-nancy\.fr$'

###> Security Production ###
HTTPS_ONLY=true
SECURE_COOKIES=true