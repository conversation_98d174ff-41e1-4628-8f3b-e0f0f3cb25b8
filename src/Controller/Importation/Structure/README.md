# Importation de la structure

L'import de la structure (Pôles, Services, CRs, UFs) se fait uniquement via la commande Symfony:

```bash
php bin/console app:import:structure
```
Voir la classe `Command/ImportStructureCommand` pour le fonctionnement et le service `Domain/Service/Importation/StructureImportService` appelé.

# Les Dtos
Les Dtos utilisés pour l'import de la structure sont les suivants:
- `CrImportDto`: Les champs du collecteur d'acte (CA) pour les CRs.
- `PoleImportDto`: Les champs du CA   pour les Pôles.
- `ServiceImportDto`: Les champs du CA pour les Services.
- `UfImportDto`: Les champs du CA pour les UFs.