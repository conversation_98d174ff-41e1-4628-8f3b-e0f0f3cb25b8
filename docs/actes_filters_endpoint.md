# Actes Filters Endpoint Documentation

## Overview

The Actes Filters endpoint provides a way to retrieve filters for actes (medical acts) based on applied filters and periods. It returns lists of practitioners, poles, and CRs (Clinical Responsibility Centers) that have actes matching the specified filters and periods.

## Endpoint URL

```
GET /api/actes/filters-simple
```

## Authentication

This endpoint requires authentication with a valid JWT token. The token should be provided in the `Authorization` header with the `Bearer` scheme.

```
Authorization: Bearer <token>
```

## Query Parameters

### Period Parameters

You can specify up to three periods to filter actes by date:

- `p1Start`: Start date of period 1 (format: YYYY-MM-DD)
- `p1End`: End date of period 1 (format: YYYY-MM-DD)
- `p2Start`: Start date of period 2 (format: YYYY-MM-DD)
- `p2End`: End date of period 2 (format: YYYY-MM-DD)
- `p3Start`: Start date of period 3 (format: YYYY-MM-DD)
- `p3End`: End date of period 3 (format: YYYY-MM-DD)

### Filter Parameters

You can also apply additional filters:

- `ejcode`: Code of the entité juridique (hospital)
- `practitioner`: ID of the practitioner
- `pole`: ID of the pole
- `cr`: ID of the CR
- `typeVenue`: Type of venue (integer)

## Response Structure

The response is a JSON object with the following structure:

```json
{
  "practitioners": [
    {
      "@id": "/api/agents/123",
      "nom": "MARTIN",
      "prenom": "Jean",
      "titre": "Dr",
      "displayName": "Dr MARTIN Jean"
    }
  ],
  "poles": [
    {
      "@id": "/api/poles/456",
      "poleCode": "CARD",
      "libelle": "Cardiologie"
    }
  ],
  "crs": [
    {
      "@id": "/api/crs/789",
      "crcode": "CARD01",
      "libelle": "Cardiologie interventionnelle"
    }
  ],
  "totalActes": 1250,
  "appliedFilters": {
    "ejcode": "ej0001"
  },
  "appliedPeriods": {
    "p1": "2025-01-01 to 2025-06-30",
    "p2": "2024-01-01 to 2024-06-30",
    "p3": "2023-01-01 to 2023-06-30"
  },
  "generationTimeMs": 25
}
```

### Response Fields

- `practitioners`: Array of practitioners who have actes matching the filters
- `poles`: Array of poles that have actes matching the filters
- `crs`: Array of CRs that have actes matching the filters
- `totalActes`: Total number of actes matching the filters
- `appliedFilters`: Object containing the filters that were applied
- `appliedPeriods`: Object containing the periods that were applied
- `generationTimeMs`: Time taken to generate the response in milliseconds

## Example Requests

### Basic Request (No Parameters)

```
GET /api/actes/filters-simple
```

### With Period Parameters

```
GET /api/actes/filters-simple?p1Start=2025-01-01&p1End=2025-06-30&p2Start=2024-01-01&p2End=2024-06-30&p3Start=2023-01-01&p3End=2023-06-30
```

### With Ejcode Parameter

```
GET /api/actes/filters-simple?ejcode=ej0001
```

### With Both Period and Ejcode Parameters

```
GET /api/actes/filters-simple?p1Start=2025-01-01&p1End=2025-06-30&p2Start=2024-01-01&p2End=2024-06-30&p3Start=2023-01-01&p3End=2023-06-30&ejcode=ej0001
```

## Updating Client Code

If you were previously using the `/api/actes/filters` endpoint, you'll need to update your client code to use the new `/api/actes/filters-simple` endpoint. Here's how:

1. Update the URL in your API calls from `/api/actes/filters` to `/api/actes/filters-simple`
2. The query parameters and response structure remain the same, so no other changes should be needed

### Example (JavaScript)

```javascript
// Old code
fetch('/api/actes/filters?ejcode=ej0001', {
  headers: {
    'Authorization': 'Bearer ' + token
  }
})
.then(response => response.json())
.then(data => {
  // Process data
});

// New code
fetch('/api/actes/filters-simple?ejcode=ej0001', {
  headers: {
    'Authorization': 'Bearer ' + token
  }
})
.then(response => response.json())
.then(data => {
  // Process data
});
```

## Troubleshooting

If you encounter any issues with the endpoint, please check the following:

1. Make sure you're using a valid JWT token
2. Check that the token has not expired
3. Verify that the query parameters are correctly formatted
4. If you're getting a 404 error, make sure you're using the correct URL (`/api/actes/filters-simple`)

## Notes

- The endpoint uses caching to improve performance. The cache TTL is 3600 seconds (1 hour).
- The endpoint is stateless and requires authentication for each request.
- The endpoint is designed to be used with the cascade filtering pattern, where selecting a filter (e.g., a practitioner) will update the available options for other filters.