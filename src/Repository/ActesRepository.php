<?php

namespace App\Repository;

use App\Entity\Activite\Actes;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

class ActesRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Actes::class);
    }


    /**
     * Somme les nombre_de_realisation pour un acte et une UF sur une période
     *
     * @param string $acteCode Code de l'acte (ex: "46C2DXI8VA")
     * @param string $ufId ID de l'UF
     * @param \DateTime $startDate Date de début de période
     * @param \DateTime $endDate Date de fin de période
     * @return int Somme des réalisations
     */
    public function sumRealisationsByActeAndUfForPeriod(
        string $acteCode,
        string $ufId,
        \DateTime $startDate,
        \DateTime $endDate
    ): int {
        $result = $this->createQueryBuilder('a')
            ->select('SUM(a.nombre_de_realisation)')
            ->where('a.code = :acteCode')
            ->andWhere('a.ufIntervention = :ufId')
            ->andWhere('a.date_realisation BETWEEN :startDate AND :endDate')
            ->andWhere('a.isActif = true')
            ->setParameter('acteCode', $acteCode)
            ->setParameter('ufId', $ufId)
            ->setParameter('startDate', $startDate)
            ->setParameter('endDate', $endDate)
            ->getQuery()
            ->getSingleScalarResult();

        return (int) ($result ?? 0);
    }

    /**
     * Somme les nombre_de_realisation pour un acte et un Praticien sur une période
     *
     * @param string $acteCode Code de l'acte (ex: "46C2DXI8VA")
     * @param string $praticienId ID du praticien
     * @param \DateTime $startDate Date de début de période
     * @param \DateTime $endDate Date de fin de période
     * @return int Somme des réalisations
     */
    public function sumRealisationsByActeAndPraticienForPeriod(
        string $acteCode,
        string $praticienId,
        \DateTime $startDate,
        \DateTime $endDate
    ): int {
        $result = $this->createQueryBuilder('a')
            ->select('SUM(a.nombre_de_realisation)')
            ->where('a.code = :acteCode')
            ->andWhere('a.agent = :praticienId')
            ->andWhere('a.date_realisation BETWEEN :startDate AND :endDate')
            ->andWhere('a.isActif = true')
            ->setParameter('acteCode', $acteCode)
            ->setParameter('praticienId', $praticienId)
            ->setParameter('startDate', $startDate)
            ->setParameter('endDate', $endDate)
            ->getQuery()
            ->getSingleScalarResult();

        return (int) ($result ?? 0);
    }

    /**
     * Récupère toutes les UF qui ont réalisé un acte spécifique
     *
     * @param string $acteCode Code de l'acte
     * @return array Liste des UF avec id, ufcode, libelle
     */
    public function getUfsForActe(string $acteCode): array
    {
        return $this->createQueryBuilder('a')
            ->select('DISTINCT uf.id, uf.ufcode, uf.libelle')
            ->join('a.ufIntervention', 'uf')
            ->where('a.code = :acteCode')
            ->andWhere('a.isActif = true')
            ->andWhere('uf.isActif = true')
            ->setParameter('acteCode', $acteCode)
            ->getQuery()
            ->getResult();
    }

    /**
     * Récupère tous les Praticiens qui ont réalisé un acte spécifique
     *
     * @param string $acteCode Code de l'acte
     * @return array Liste des praticiens avec id, nom, prenom, titre
     */
    public function getPraticiensForActe(string $acteCode): array
    {
        return $this->createQueryBuilder('a')
            ->select('DISTINCT ag.id, ag.nom, ag.prenom, ag.titre')
            ->join('a.agent', 'ag')
            ->where('a.code = :acteCode')
            ->andWhere('a.isActif = true')
            ->andWhere('ag.isActif = true')
            ->setParameter('acteCode', $acteCode)
            ->getQuery()
            ->getResult();
    }

    /**
     * Calcule le total des réalisations pour un acte sur une période
     *
     * Utilisé pour calculer les fréquences (pourcentages)
     *
     * @param string $acteCode Code de l'acte
     * @param \DateTime $startDate Date de début de période
     * @param \DateTime $endDate Date de fin de période
     * @return int Total des réalisations
     */
    public function getTotalRealisationsForActeInPeriod(
        string $acteCode,
        \DateTime $startDate,
        \DateTime $endDate
    ): int {
        $result = $this->createQueryBuilder('a')
            ->select('SUM(a.nombre_de_realisation)')
            ->where('a.code = :acteCode')
            ->andWhere('a.date_realisation BETWEEN :startDate AND :endDate')
            ->andWhere('a.isActif = true')
            ->setParameter('acteCode', $acteCode)
            ->setParameter('startDate', $startDate)
            ->setParameter('endDate', $endDate)
            ->getQuery()
            ->getSingleScalarResult();

        return (int) ($result ?? 0);
    }

    /**
     * Recupere les statistiques mensuelles pour un acte sur une periode donnee
     */
    public function findMonthlyStatsByPeriod(string $acteCode, string $start, string $end): array
    {
        $sql = "
            SELECT 
                CONCAT(a.annee::text, '-', LPAD(a.mois::text, 2, '0')) AS year_month,
                SUM(a.nombre_de_realisation) AS count
            FROM actes a
            WHERE a.code = :code
                AND a.is_actif = true
                AND a.date_realisation BETWEEN :start AND :end
            GROUP BY year_month
            ORDER BY year_month ASC
        ";

        $stmt = $this->getEntityManager()->getConnection()->prepare($sql);
        $result = $stmt->executeQuery([
            'code' => $acteCode,
            'start' => $start,
            'end' => $end
        ]);

        $stats = [];
        while ($row = $result->fetchAssociative()) {
            $stats[$row['year_month']] = (int) $row['count'];
        }

        return $stats;
    }

    /**
     * Recupere les statistiques agregees par acte pour plusieurs codes d'actes sur 3 periodes
     */
    public function getMultiActeStatsByActeCodes(
        array $acteCodes,
        ?\DateTime $p1Start,
        ?\DateTime $p1End,
        ?\DateTime $p2Start,
        ?\DateTime $p2End,
        ?\DateTime $p3Start,
        ?\DateTime $p3End
    ): array {
        if (empty($acteCodes)) {
            return [];
        }

        // Convertir les codes en chaines quotees pour PostgreSQL
        $cleanCodes = array_map(function($code) {
            return "'" . addslashes(trim($code)) . "'";
        }, $acteCodes);
        $placeholders = implode(',', $cleanCodes);
        
        // Construire les conditions de periode dynamiquement
        $p1Condition = ($p1Start && $p1End) ? 
            "a.date_realisation BETWEEN '{$p1Start->format('Y-m-d')}' AND '{$p1End->format('Y-m-d')}'" : 
            "1=0";
            
        $p2Condition = ($p2Start && $p2End) ? 
            "a.date_realisation BETWEEN '{$p2Start->format('Y-m-d')}' AND '{$p2End->format('Y-m-d')}'" : 
            "1=0";
            
        $p3Condition = ($p3Start && $p3End) ? 
            "a.date_realisation BETWEEN '{$p3Start->format('Y-m-d')}' AND '{$p3End->format('Y-m-d')}'" : 
            "1=0";
        
        $sql = "
            SELECT 
                a.code,
                a.description,
                COUNT(DISTINCT a.id) as total_activites,
                SUM(CASE WHEN $p1Condition THEN a.nombre_de_realisation ELSE 0 END) as p1_count,
                SUM(CASE WHEN $p2Condition THEN a.nombre_de_realisation ELSE 0 END) as p2_count,
                SUM(CASE WHEN $p3Condition THEN a.nombre_de_realisation ELSE 0 END) as p3_count
            FROM actes a
            WHERE a.code IN ($placeholders)
                AND a.is_actif = true
            GROUP BY a.code, a.description
            ORDER BY a.code ASC
        ";

        $stmt = $this->getEntityManager()->getConnection()->prepare($sql);
        $result = $stmt->executeQuery();

        $stats = [];
        while ($row = $result->fetchAssociative()) {
            $stats[] = [
                'code' => $row['code'],
                'description' => $row['description'],
                'total_activites' => (int)$row['total_activites'],
                'p1_count' => (int)$row['p1_count'],
                'p2_count' => (int)$row['p2_count'],
                'p3_count' => (int)$row['p3_count'],
            ];
        }

        return $stats;
    }

    /**
     * Recupere les praticiens disponibles selon les filtres et periodes
     * Pour les filtres en cascade intelligents
     */
    public function findAvailablePractitioners(array $filters, array $periods): array
    {
        $qb = $this->createQueryBuilder('a')
            ->select('DISTINCT ag.id, ag.nom, ag.prenom, ag.titre')
            ->join('a.agent', 'ag')
            ->where('ag.isActif = true')
            ->andWhere('ag.nom IS NOT NULL')
            ->andWhere('ag.prenom IS NOT NULL');

        // Appliquer les filtres de periodes
        $this->applyPeriodFilters($qb, $periods);
        
        // Appliquer les autres filtres (sauf practitioner)
        $this->applyFiltersExcept($qb, $filters, ['practitioner']);

        $results = $qb->getQuery()->getResult();
        
        return array_map(function($row) {
            return [
                '@id' => '/api/agents/' . $row['id'],
                'nom' => $row['nom'],
                'prenom' => $row['prenom'],
                'titre' => $row['titre'],
                'displayName' => trim(($row['titre'] ? $row['titre'] . ' ' : '') . 
                                    $row['nom'] . ' ' . $row['prenom'])
            ];
        }, $results);
    }

    /**
     * Recupere les poles disponibles selon les filtres et periodes
     */
    public function findAvailablePoles(array $filters, array $periods): array
    {
        $qb = $this->createQueryBuilder('a')
            ->select('DISTINCT p.id, p.poleCode, p.libelle')
            ->join('a.ufIntervention', 'uf')
            ->join('uf.pole', 'p')
            ->where('p.isActif = true');

        // Appliquer les filtres de periodes
        $this->applyPeriodFilters($qb, $periods);
        
        // Appliquer les autres filtres (sauf pole)
        $this->applyFiltersExcept($qb, $filters, ['pole']);

        $results = $qb->getQuery()->getResult();
        
        return array_map(function($row) {
            return [
                '@id' => '/api/poles/' . $row['id'],
                'poleCode' => $row['poleCode'],
                'libelle' => $row['libelle']
            ];
        }, $results);
    }

    /**
     * Recupere les CRs disponibles selon les filtres et periodes
     */
    public function findAvailableCrs(array $filters, array $periods): array
    {
        $qb = $this->createQueryBuilder('a')
            ->select('DISTINCT cr.id, cr.crcode, cr.libelle')
            ->join('a.ufIntervention', 'uf')
            ->join('uf.cr', 'cr')
            ->where('cr.isActif = true');

        // Appliquer les filtres de periodes
        $this->applyPeriodFilters($qb, $periods);
        
        // Appliquer les autres filtres (sauf cr)
        $this->applyFiltersExcept($qb, $filters, ['cr']);

        $results = $qb->getQuery()->getResult();
        
        return array_map(function($row) {
            return [
                '@id' => '/api/crs/' . $row['id'],
                'crcode' => $row['crcode'],
                'libelle' => $row['libelle']
            ];
        }, $results);
    }

    /**
     * Compte le nombre total d'actes selon les filtres et periodes
     */
    public function countActesByFilters(array $filters, array $periods): int
    {
        $qb = $this->createQueryBuilder('a')
            ->select('COUNT(a.id)');

        // Appliquer les filtres de periodes
        $this->applyPeriodFilters($qb, $periods);
        
        // Appliquer tous les filtres
        $this->applyFiltersExcept($qb, $filters, []);

        return (int) $qb->getQuery()->getSingleScalarResult();
    }

    /**
     * Applique les filtres de periodes a la requete
     */
    private function applyPeriodFilters($qb, array $periods): void
    {
        if (empty($periods)) {
            return;
        }

        $periodConditions = [];
        $paramIndex = 0;

        foreach ($periods as $periodKey => $period) {
            $startParam = 'periodStart' . $paramIndex;
            $endParam = 'periodEnd' . $paramIndex;
            
            $periodConditions[] = "(a.dateRealisation >= :$startParam AND a.dateRealisation <= :$endParam)";
            $qb->setParameter($startParam, $period['start']);
            $qb->setParameter($endParam, $period['end']);
            
            $paramIndex++;
        }

        if (!empty($periodConditions)) {
            $qb->andWhere('(' . implode(' OR ', $periodConditions) . ')');
        }
    }

    /**
     * Applique les filtres sauf ceux specifies dans $except
     */
    private function applyFiltersExcept($qb, array $filters, array $except): void
    {
        foreach ($filters as $filterKey => $filterValue) {
            if (in_array($filterKey, $except) || empty($filterValue)) {
                continue;
            }

            switch ($filterKey) {
                case 'ejcode':
                    // Filtre par etablissement si necessaire
                    // A implementer selon votre logique metier
                    break;
                    
                case 'practitioner':
                    // Extraire l'ID du praticien depuis l'URI API Platform
                    if (preg_match('/\/api\/agents\/(\d+)/', $filterValue, $matches)) {
                        $qb->join('a.agent', 'ag_filter')
                           ->andWhere('ag_filter.id = :practitionerId')
                           ->setParameter('practitionerId', $matches[1]);
                    }
                    break;
                    
                case 'pole':
                    // Extraire l'ID du pole depuis l'URI API Platform
                    if (preg_match('/\/api\/poles\/(\d+)/', $filterValue, $matches)) {
                        $qb->join('a.ufIntervention', 'uf_pole_filter')
                           ->join('uf_pole_filter.pole', 'p_filter')
                           ->andWhere('p_filter.id = :poleId')
                           ->setParameter('poleId', $matches[1]);
                    }
                    break;
                    
                case 'cr':
                    // Extraire l'ID du CR depuis l'URI API Platform
                    if (preg_match('/\/api\/crs\/(\d+)/', $filterValue, $matches)) {
                        $qb->join('a.ufIntervention', 'uf_cr_filter')
                           ->join('uf_cr_filter.cr', 'cr_filter')
                           ->andWhere('cr_filter.id = :crId')
                           ->setParameter('crId', $matches[1]);
                    }
                    break;
                    
                case 'typeVenue':
                    $qb->andWhere('a.typeVenue = :typeVenue')
                       ->setParameter('typeVenue', $filterValue);
                    break;
            }
        }
    }
}
