<?php

namespace App\Factory\Activite;

use App\Domain\Enum\PeriodeType;
use App\Entity\Activite\Liberal;
use App\Factory\Praticien\AgentFactory;
use Zenstruck\Foundry\Persistence\PersistentProxyObjectFactory;

/**
 * @extends PersistentProxyObjectFactory<Liberal>
 */
final class LiberalFactory extends PersistentProxyObjectFactory
{
    public function __construct()
    {
    }

    public static function class(): string
    {
        return Liberal::class;
    }

    protected function defaults(): array|callable
    {
        $validFrom = self::faker()->dateTimeBetween('-3 years', '-6 months');
        $validTo = (self::faker()->boolean(70)) ? self::faker()->dateTimeBetween($validFrom, 'now') : null;
        
        $dateDebut = self::faker()->dateTimeBetween('-1 year', 'now');
        $dateFin = self::faker()->dateTimeBetween($dateDebut, '+3 hours');

        return [
            'agent' => AgentFactory::random(),
            'nom_acte' => self::faker()->word(),
            'date_realisation' => self::faker()->dateTimeBetween($dateDebut, $dateFin),
            'dateDebut' => $dateDebut,
            'dateFin' => $dateFin,
            'tarif' => self::faker()->randomFloat(2, 50, 2000),
            'code_acte' => self::faker()->optional()->regexify('[A-Z]{3}[0-9]{3}'),
            'type_acte' => self::faker()->randomElement(['CCAM', 'NGAP', 'NABM']),
            'validFrom' => $validFrom,
            'validTo' => $validTo,
            'periodeType' => self::faker()->randomElement(PeriodeType::VALUES),
            'source' => self::faker()->optional()->randomElement(['CNAM', 'RPPS', 'MANUAL']),
        ];
    }

    protected function initialize(): static
    {
        return $this;
    }
}
