<?php

namespace App\Controller\Importation\Actes\Dto;

use App\Domain\Service\Importation\ActeImportService;

/**
 * Data Transfer Object pour l'importation des actes.
 *
 * @see ActeImportService::importActes()
 */
class ActeImportDto
{
    public ?string $internum = null;
    public ?string $codeActe = null;
    public ?string $descriptionActe = null;
    public ?string $typeActe = null;
    public ?int $anneeActe = null;
    public ?int $moisActe = null;
    public ?string $dateActe = null;
    public ?int $semaineIso = null;

    public ?string $periodeType = null;
    public ?int $nombreActes = null;
    public ?string $praticienMatricule = null; // le hrUser de l'agent i.e le U
    public ?string $ufPrincipalCode = null; // le code de l'UF principale
    public ?string $source = null;
    public ?int $typeVenue = null;
    public ?string $libTypeVenue = null;
    public ?string $ufDemande = null; // le code de l'UF de demande
    public ?string $libUfDemande = null;
    public ?string $ufIntervention = null; // le code de l'UF d'intervention
    public ?string $libUfIntervention = null;
    public ?int $icrA = null;
    public ?float $coefficient = null;
    public ?string $lettreCoef = null;
    public ?string $regroupement = null;
    public ?string $activite = null;
    public ?string $activiteLib = null;

    public function validate(): void
    {
        if (empty($this->praticienMatricule)) {
            throw new \InvalidArgumentException('Le matricule [hrUser] du praticien est obligatoire');
        }

        if (empty($this->codeActe)) {
            throw new \InvalidArgumentException('Le code acte est obligatoire');
        }

        if (empty($this->dateActe)) {
            throw new \InvalidArgumentException('La date de l\'acte est obligatoire');
        }

        if (empty($this->ufIntervention)) {
            throw new \InvalidArgumentException('Le code UF Intervention est obligatoire');
        }
    }
}