<?php

namespace App\ApiResource\Praticien;

use ApiPlatform\Doctrine\Orm\Filter\OrderFilter;
use ApiPlatform\Doctrine\Orm\Filter\SearchFilter;
use ApiPlatform\Doctrine\Orm\State\Options;
use ApiPlatform\Metadata\ApiFilter;
use ApiPlatform\Metadata\ApiProperty;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\GetCollection;
use ApiPlatform\Metadata\Link;
use ApiPlatform\Metadata\Post;
use App\ApiResource\Base\BaseDto;
use App\ApiResource\Structure\EntiteJuridiqueDto;
use App\Entity\Praticien\Agent;
use App\State\EntityClassDtoStateProcessor;
use App\State\EntityToDtoStateProvider;

#[ApiResource(
    shortName: 'agent',
    operations: [
        new Get(
            uriTemplate: '/agents/{id}'
        ),
        new GetCollection(
            uriTemplate: '/agents',
        ),
        new GetCollection(
            uriTemplate: '/{ejcode}/agents',
            uriVariables: [
                'ejcode' => new Link(
                    schema: ['type' => 'string'],
                    description: 'Code de l\'entité juridique',
                    required: true
                )
            ]
        ),
        new Post()
    ],
    paginationItemsPerPage: 10,
    provider: EntityToDtoStateProvider::class,
    processor: EntityClassDtoStateProcessor::class,
    stateOptions: new Options(entityClass: Agent::class),
)]
#[ApiFilter(OrderFilter::class, properties: [
    'nom' => 'ASC',
    'prenom' => 'ASC',
    'email' => 'ASC',
    'dateVenue' => 'DESC',
    'dateMaj' => 'DESC',
    'matricule' => 'ASC',
    'categorie' => 'ASC',
])]
class AgentDto extends BaseDto
{
    // CollecteurTrait fields
    #[ApiProperty(description: "Email de l'agent", example: "<EMAIL>")]
    #[ApiFilter(SearchFilter::class, strategy: 'partial')]
    public ?string $email = null;

    #[ApiProperty(description: "Nom de l'agent", example: "Dupont")]
    #[ApiFilter(SearchFilter::class, strategy: 'partial')]
    public ?string $nom = null;

    #[ApiProperty(description: "Prénom de l'agent", example: "Jean")]
    #[ApiFilter(SearchFilter::class, strategy: 'partial')]
    public ?string $prenom = null;

    #[ApiProperty(description: "Matricule de l'agent", example: "AG123456")]
    #[ApiFilter(SearchFilter::class, strategy: 'exact')]
    public ?string $matricule = null;

    #[ApiProperty(description: "Identifiant RH", example: "HR123456")]
    #[ApiFilter(SearchFilter::class, strategy: 'partial')]
    public ?string $hrUser = null;

    #[ApiProperty(description: "Titre de l'agent", example: "Dr")]
    #[ApiFilter(SearchFilter::class, strategy: 'partial')]
    public ?string $titre = null;

    #[ApiProperty(description: "Catégorie de l'agent", example: "PH")]
    #[ApiFilter(SearchFilter::class, strategy: 'partial')]
    public ?string $categorie = null;

    #[ApiProperty(description: "Code établissement", example: "001")]
    public ?string $etablissement = null;

    #[ApiProperty(description: "Date de départ prévue")]
    public ?\DateTimeInterface $dateDepart = null;

    #[ApiProperty(description: "Date d'arrivée")]
    public ?\DateTimeInterface $dateVenue = null;

    #[ApiProperty(description: "Date de dernière mise à jour")]
    public ?\DateTimeInterface $dateMaj = null;

    #[ApiProperty(description: "Créateur de la fiche")]
    public ?string $createurFiche = null;

    #[ApiProperty(description: "Est administrateur")]
    public bool $isAdmin = false;

    #[ApiProperty(description: "Est anesthésiste")]
    public bool $isAnesthesiste = false;

    // NativeFieldTrait fields
    #[ApiProperty(description: "Nom complet", example: "Dr Jean Dupont")]
    #[ApiFilter(SearchFilter::class, strategy: 'partial')]
    public ?string $fullName = null;

    #[ApiProperty(description: "URL de la photo de profil")]
    public ?string $imgUrl = null;

    #[ApiProperty(description: "Numéro de badge", example: "BADGE1234")]
    public ?string $badge = null;

    #[ApiProperty(description: "Rôles de l'utilisateur", example: ["ROLE_USER"])]
    public array $roles = [];

    // Relations
    #[ApiProperty(description: "Hôpital de rattachement")]
    #[ApiFilter(SearchFilter::class,properties: ['hopital.code'=>'exact'])]
    public ?EntiteJuridiqueDto $hopital = null;

    #[ApiProperty(description: "Affectations aux UFs", example: ["/api/agent_ufs/1"])]
    #[ApiFilter(SearchFilter::class, properties: [
        'agentUfs.rgt' => 'partial',
        'agentUfs.typeGrade' => 'exact',
        'agentUfs.libelleGrade' => 'partial',
        'agentUfs.etp' => 'exact',
        'agentUfs.ufs.ufcode' => 'exact',
        'agentUfs.ufs.cr.crcode' => 'partial'
    ])]
    public $affectations = null;
}
