<?php

namespace App\ApiResource\Activite;

use ApiPlatform\Doctrine\Orm\Filter\SearchFilter;
use ApiPlatform\Doctrine\Orm\State\Options;
use ApiPlatform\Metadata\ApiFilter;
use ApiPlatform\Metadata\ApiProperty;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\GetCollection;
use ApiPlatform\Metadata\Link;
use ApiPlatform\Metadata\Post;
use App\ApiResource\Base\BaseDto;
use App\Domain\Enum\PeriodeType;
use App\Entity\Activite\Sigaps;
use App\State\Batch\SigapsBatchProcessor;
use App\State\EntityClassDtoStateProcessor;
use App\State\EntityToDtoStateProvider;

#[ApiResource(
    shortName: 'sigaps',
    operations: [
        new Get(),
        new GetCollection(
            uriTemplate: '/sigaps'
        ),
        new GetCollection(
            uriTemplate: '/{ejcode}/sigaps',
            uriVariables: [
                'ejcode' => new Link(
                    schema: ['type' => 'string'],
                    description: 'Code de l\'entité juridique',
                    required: true
                )
            ]
        ),
        new Post(
            formats: ['json' => ['application/json']]
        ),
        new Post(
            uriTemplate: '/sigaps/batch',
            formats: ['json' => ['application/json']],
            processor: SigapsBatchProcessor::class // pour le traitement en batch CSV (juste pour accepter un tableau d'objets
        )
    ],
    paginationItemsPerPage: 10,
    provider: EntityToDtoStateProvider::class,
    processor: EntityClassDtoStateProcessor::class,
    stateOptions: new Options(entityClass: Sigaps::class),
)]
#[ApiFilter(SearchFilter::class, properties: [
    'categorie' => 'partial',
    'agent.hrUser' => 'exact'  // Filtre sur le hrUser de l'agent lié
])]
class SigapsDto extends BaseDto
{
    #[ApiProperty(description: "Date de début de la période SIGAPS", example: "2024-01-01T00:00:00")]
    public ?\DateTimeInterface $dateDebut = null;

    #[ApiProperty(description: "Date de fin de la période SIGAPS", example: "2024-12-31T23:59:59")]
    public ?\DateTimeInterface $dateFin = null;

    #[ApiProperty(description: "Score SIGAPS", example: "125.50")]
    public ?string $score = null;

    #[ApiProperty(
        description: "Répartition par catégorie",
        writable: false,
        example: '{"A+": 0, "A": 12, "B": 18, "C": 7, "D": 3}',
        openapiContext: [
            'type' => 'object',
            'additionalProperties' => ['type' => 'integer'],
            'example' => ['A+' => 0, 'A' => 12, 'B' => 18, 'C' => 7, 'D' => 3]
        ]
    )]
    public ?array $repartitionParCategorie = null;
    
    #[ApiProperty(description: "Nombre de publications de catégorie A+", example: 2, readable: false, writable: true)]
    public ?int $repartition_A_plus = null;
    
    #[ApiProperty(description: "Nombre de publications de catégorie A", example: 5, readable: false, writable: true)]
    public ?int $repartition_A = null;
    
    #[ApiProperty(description: "Nombre de publications de catégorie B", example: 8, readable: false, writable: true)]
    public ?int $repartition_B = null;
    
    #[ApiProperty(description: "Nombre de publications de catégorie C", example: 3, readable: false, writable: true)]
    public ?int $repartition_C = null;
    
    #[ApiProperty(description: "Nombre de publications de catégorie D", example: 1, readable: false, writable: true)]
    public ?int $repartition_D = null;

    #[ApiProperty(description: "Catégorie principale", example: "A")]
    public ?string $categorie = null;

    #[ApiProperty(description: "Nombre de publications", example: 15)]
    public ?int $nombrePublication = null;

    #[ApiProperty(description: "Agent lié au SIGAPS, transmis par hrUser", example: "u12345")]
    public ?string $agentHrU = null;

    #[ApiProperty(description: "Dernière affectation connue de l'agent", readable: true, writable: false, example: "Service de cardiologie")]
    public ?string $derniereAffectionDeAgent = null;

    #[ApiProperty(description: "Nom, prénom et titre de l'agent", readable: true, writable: false, example: "Dr. John Doe")]
    public ?string $agentNomPrenomTitre = null;


    // Champs du trait HorodatageTrait (générés automatiquement)
    #[ApiProperty(description: "Date de début de validité", readable: true, writable: false, example: "2024-01-01T00:00:00")]
    public ?\DateTimeInterface $validFrom = null;

    #[ApiProperty(description: "Date de fin de validité", readable: true, writable: false, example: "2024-12-31T23:59:59")]
    public ?\DateTimeInterface $validTo = null;

    #[ApiProperty(description: "Type de période", readable: true, writable: false, example: "ANNUEL")]
    public string $periodeType = PeriodeType::ANNUEL;

    #[ApiProperty(description: "Source des données", example: "CNAM", readable: true, writable: false)]
    public ?string $source = null;
}
