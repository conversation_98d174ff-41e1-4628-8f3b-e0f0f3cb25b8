<?php

namespace App\Command;

use App\Domain\Service\Importation\AffectationAutoImportService;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

/**
 * Commande pour le chargement automatique des affectations agents/UF depuis des fichiers CSV.
 *
 * Cette commande vérifie toutes les entités juridiques ayant le chargement automatique activé
 * et traite les fichiers CSV trouvés dans leurs dossiers configurés.
 *
 * Structure CSV attendue (séparateur point-virgule) :
 *
 * uConnexion;matricule;date_debut;date_fin;code_uf;type_grade
 * jdupont;12345;2024-01-01;2024-12-31;UF001;Médecin
 * mmartin;67890;2024-02-01;;UF002;Infirmier
 *
 * Exemple de configuration cron Linux (tous les 5 du mois à 6h00) :
 * 0 6 5 * * /usr/bin/php /path/to/your/app/bin/console app:affectation:auto-import >> /var/log/affectation-auto-import.log 2>&1
 *
 * Pour tester manuellement :
 * php bin/console app:affectation:auto-import
 */
#[AsCommand(
    name: 'app:affectation:auto-import',
    description: 'Vérifie et traite automatiquement les fichiers d\'affectation dans les dossiers configurés'
)]
class AffectationAutoImportCommand extends Command
{
    public function __construct(
        private readonly AffectationAutoImportService $autoImportService
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $io->title('Démarrage du chargement automatique des affectations');

        try {
            $results = $this->autoImportService->processAllEntitesJuridiques();

            if (empty($results)) {
                $io->info('Aucune entité juridique n\'a le chargement automatique activé.');
                return Command::SUCCESS;
            }

            $io->section('Résultats du traitement');

            foreach ($results as $ejCode => $result) {
                $io->text("Entité Juridique: <info>{$ejCode}</info>");

                if (isset($result['error'])) {
                    $io->error("Erreur: {$result['error']}");
                    continue;
                }

                if ($result['filesProcessed'] === 0) {
                    $io->text("  Aucun fichier trouvé à traiter");
                } else {
                    $io->text("  Fichiers traités: {$result['filesProcessed']}");
                    $io->text("  Affectations créées: {$result['totalCreated']}");
                    $io->text("  Affectations mises à jour: {$result['totalUpdated']}");

                    if (!empty($result['errors'])) {
                        $io->warning("  Erreurs rencontrées: " . count($result['errors']));
                        foreach ($result['errors'] as $error) {
                            $io->text("    - {$error}");
                        }
                    }
                }
                $io->newLine();
            }

            $io->success('Traitement terminé avec succès');
            return Command::SUCCESS;

        } catch (\Exception $e) {
            $io->error("Erreur lors du traitement: {$e->getMessage()}");
            return Command::FAILURE;
        }
    }
}
