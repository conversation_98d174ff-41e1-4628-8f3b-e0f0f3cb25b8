<?php

namespace App\Tests\Command;

use App\Command\AffectationAutoImportCommand;
use App\Domain\Service\Importation\AffectationAutoImportService;
use Symfony\Component\Console\Tester\CommandTester;

it('can be instantiated', function () {
    $autoImportService = $this->createMock(AffectationAutoImportService::class);
    
    $command = new AffectationAutoImportCommand($autoImportService);
    
    expect($command)->toBeInstanceOf(AffectationAutoImportCommand::class);
});

it('configures the command correctly', function () {
    $autoImportService = $this->createMock(AffectationAutoImportService::class);
    
    $command = new AffectationAutoImportCommand($autoImportService);
    
    // Use getName() method to get the command name
    expect($command->getName())->toBe('app:affectation:auto-import');
});

it('executes successfully with entities having auto-import enabled', function () {
    $autoImportService = $this->createMock(AffectationAutoImportService::class);
    
    // Mock processAllEntitesJuridiques to return sample results
    $autoImportService->method('processAllEntitesJuridiques')
        ->willReturn([
            'EJ001' => [
                'filesProcessed' => 2,
                'totalCreated' => 10,
                'totalUpdated' => 5,
                'errors' => []
            ],
            'EJ002' => [
                'filesProcessed' => 1,
                'totalCreated' => 3,
                'totalUpdated' => 2,
                'errors' => ['Error in line 5: Invalid format']
            ]
        ]);
    
    $command = new AffectationAutoImportCommand($autoImportService);
    $commandTester = new CommandTester($command);
    
    $exitCode = $commandTester->execute([]);
    
    expect($exitCode)->toBe(0);
    expect($commandTester->getDisplay())->toContain('Démarrage du chargement automatique des affectations');
    expect($commandTester->getDisplay())->toContain('Entité Juridique: EJ001');
    expect($commandTester->getDisplay())->toContain('Fichiers traités: 2');
    expect($commandTester->getDisplay())->toContain('Affectations créées: 10');
    expect($commandTester->getDisplay())->toContain('Affectations mises à jour: 5');
    expect($commandTester->getDisplay())->toContain('Entité Juridique: EJ002');
    expect($commandTester->getDisplay())->toContain('Erreurs rencontrées: 1');
    expect($commandTester->getDisplay())->toContain('Error in line 5: Invalid format');
    expect($commandTester->getDisplay())->toContain('Traitement terminé avec succès');
});

it('executes successfully with no entities having auto-import enabled', function () {
    $autoImportService = $this->createMock(AffectationAutoImportService::class);
    
    // Mock processAllEntitesJuridiques to return empty results
    $autoImportService->method('processAllEntitesJuridiques')
        ->willReturn([]);
    
    $command = new AffectationAutoImportCommand($autoImportService);
    $commandTester = new CommandTester($command);
    
    $exitCode = $commandTester->execute([]);
    
    expect($exitCode)->toBe(0);
    expect($commandTester->getDisplay())->toContain('Démarrage du chargement automatique des affectations');
    expect($commandTester->getDisplay())->toContain('Aucune entité juridique n\'a le chargement automatique activé');
});

it('handles entity with error', function () {
    $autoImportService = $this->createMock(AffectationAutoImportService::class);
    
    // Mock processAllEntitesJuridiques to return results with an entity error
    $autoImportService->method('processAllEntitesJuridiques')
        ->willReturn([
            'EJ001' => [
                'error' => 'Configuration directory not found'
            ]
        ]);
    
    $command = new AffectationAutoImportCommand($autoImportService);
    $commandTester = new CommandTester($command);
    
    $exitCode = $commandTester->execute([]);
    
    expect($exitCode)->toBe(0);
    expect($commandTester->getDisplay())->toContain('Entité Juridique: EJ001');
    expect($commandTester->getDisplay())->toContain('Erreur: Configuration directory not found');
});

it('handles entity with no files to process', function () {
    $autoImportService = $this->createMock(AffectationAutoImportService::class);
    
    // Mock processAllEntitesJuridiques to return results with an entity with no files
    $autoImportService->method('processAllEntitesJuridiques')
        ->willReturn([
            'EJ001' => [
                'filesProcessed' => 0,
                'totalCreated' => 0,
                'totalUpdated' => 0,
                'errors' => []
            ]
        ]);
    
    $command = new AffectationAutoImportCommand($autoImportService);
    $commandTester = new CommandTester($command);
    
    $exitCode = $commandTester->execute([]);
    
    expect($exitCode)->toBe(0);
    expect($commandTester->getDisplay())->toContain('Entité Juridique: EJ001');
    expect($commandTester->getDisplay())->toContain('Aucun fichier trouvé à traiter');
});

it('handles service exception', function () {
    $autoImportService = $this->createMock(AffectationAutoImportService::class);
    
    // Mock processAllEntitesJuridiques to throw an exception
    $autoImportService->method('processAllEntitesJuridiques')
        ->willThrowException(new \Exception('Service error'));
    
    $command = new AffectationAutoImportCommand($autoImportService);
    $commandTester = new CommandTester($command);
    
    $exitCode = $commandTester->execute([]);
    
    expect($exitCode)->toBe(1);
    expect($commandTester->getDisplay())->toContain('Erreur lors du traitement: Service error');
});