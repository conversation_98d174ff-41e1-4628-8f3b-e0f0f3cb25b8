<?php

namespace App\ApiResource\Structure;

use ApiPlatform\Doctrine\Orm\Filter\SearchFilter;
use ApiPlatform\Doctrine\Orm\State\Options;
use ApiPlatform\Metadata\ApiFilter;
use ApiPlatform\Metadata\ApiProperty;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\GetCollection;
use ApiPlatform\Metadata\Link;
use App\ApiResource\Base\BaseDto;
use App\ApiResource\Trait\HorodatageDtoTrait;
use App\Entity\Structure\Ufs;
use App\State\EntityClassDtoStateProcessor;
use App\State\EntityToDtoStateProvider;

#[ApiResource(
    shortName: 'ufs',
    operations: [
        new Get(),
        new GetCollection(
            uriTemplate: '/ufs',
        ),
        new GetCollection(
            uriTemplate: '/{ejcode}/ufs',
            uriVariables: [
                'ejcode' => new Link(
                    description: 'Code de l\'entité juridique',
                    schema: ['type' => 'string'],
                    required: false
                )
            ]
        )
    ],
    paginationItemsPerPage: 10,
    provider: EntityToDtoStateProvider::class,
    processor: EntityClassDtoStateProcessor::class,
    stateOptions: new Options(entityClass: Ufs::class),
)]
class UfsDto extends BaseDto
{
    #[ApiProperty(description: "Établissement", example: "CHU")]
    #[ApiFilter(SearchFilter::class, strategy: 'partial')]
    public ?string $etab = null;

    #[ApiProperty(description: "Code UF", example: "1234")]
    #[ApiFilter(SearchFilter::class, strategy: 'partial')]
    public ?string $ufcode = null;

    #[ApiProperty(description: "Date de début", example: "2023-01-01")]
    public ?\DateTimeInterface $datdeb = null;

    #[ApiProperty(description: "Date de fin", example: "2033-01-01")]
    public ?\DateTimeInterface $datfin = null;

    #[ApiProperty(description: "Date de clôture", example: "2025-01-01")]
    public ?\DateTimeInterface $datclos = null;

    #[ApiProperty(description: "Libellé de l'UF", example: "Unité de Soins Intensifs")]
    #[ApiFilter(SearchFilter::class, strategy: 'partial')]
    public ?string $libelle = null;

    #[ApiProperty(description: "Code TA", example: "01")]
    public ?string $tacode = null;

    #[ApiProperty(description: "Code CD", example: "123")]
    public ?string $cdcode = null;

    #[ApiProperty(description: "Lettre budgétaire", example: "A")]
    public ?string $lettrebudg = null;

    #[ApiProperty(description: "Code SE", example: "SE01")]
    public ?string $secode = null;

    #[ApiProperty(description: "Code CG", example: "CG01")]
    public ?string $cgcode = null;

    #[ApiProperty(description: "Code UM", example: "UM01")]
    public ?string $umcode = null;

    #[ApiProperty(description: "Utilisateur PF", example: "USER123")]
    public ?string $pfuser = null;

    #[ApiProperty(description: "Date de création", example: "2023-01-01")]
    public ?\DateTimeInterface $dmdacre = null;

    #[ApiProperty(description: "Date de mise à jour", example: "2023-01-01")]
    public ?\DateTimeInterface $dmdamaj = null;

    #[ApiProperty(description: "Top médical", example: "1")]
    public ?string $topmedical = null;

    #[ApiProperty(description: "Code CR", example: "CR01")]
    public ?string $crcode = null;

    #[ApiProperty(description: "Code SA", example: "SA01")]
    public ?string $sacode = null;

    #[ApiProperty(description: "Lien vers le CR associé", example: "/api/crs/1")]
    public ?CrDto $cr = null;

    #[ApiProperty(description: "Lien vers le Service associé", example: "/api/services/1")]
    public ?ServiceDto $service = null;

    #[ApiProperty(description: "Lien vers le Pôle associé", example: "/api/poles/1")]
    public ?PoleDto $pole = null;

    use HorodatageDtoTrait;
}
