<?php

namespace App\Factory\Structure;

use App\Entity\Structure\EntiteJuridique;
use Zenstruck\Foundry\Persistence\PersistentProxyObjectFactory;

/**
 * @extends PersistentProxyObjectFactory<EntiteJuridique>
 */
final class EntiteJuridiqueFactory extends PersistentProxyObjectFactory
{
    /**
     * @see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#factories-as-services
     *
     * @todo inject services if required
     */
    public function __construct()
    {
    }

    public static function class(): string
    {
        return EntiteJuridique::class;
    }

    /**
     * @see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#model-factories
     *
     * @todo add your default values here
     */
    protected function defaults(): array|callable
    {
        return [
            'code' => 'EJ-' . self::faker()->unique()->numerify('#####'),
            'nom' => strtoupper(self::faker()->company()),
            'adresse' => strtoupper(self::faker()->address()),
            'telephone' => strtoupper(self::faker()->phoneNumber()),
            'email' => strtoupper(self::faker()->companyEmail()),
            'is_actif' => true,
            'parametre' => [
                "addresses" => [
                    "affectationAgentFilesPaths" => "/mnt/bo/files/agents",
                    "chargementAutomatiqueDesAffectationEstActif" => self::faker()->boolean(30)
                ],
                "notifications" => [
                    "adminEmail" => "<EMAIL>",
                    "damResponssableDataAffectionEmail" => "<EMAIL>",
                    "notifierErreurAuResponssableDamAffectation" => self::faker()->boolean(20),
                    "notifierErreurAuAdminPourAffectation" => self::faker()->boolean(20),
                    "NotifierAdminDesErreurProduite" => self::faker()->boolean(10)
                ],
                "apiEndpoints" => [
                    "dataIntegratorApiInfo" => "HTTPS://DATA-INTEGRATOR.CHRU-NANCY.FR/API/NANCY/COLLECTEUR",
                    "backendUrlInfo" => "HTTPS://SUPRA-BACKEND.CHRU-NANCY.FR/API/NANCY"
                ]
            ],
            'ldapConfig' => [
                'host' => 'ldap://selve.chu-nancy.fr:389', // IMPORTANT: Garder en minuscules pour la correspondance
                'domain' => 'U001PRD',
                'base_dn' => 'DC=chu-nancy,DC=fr', // IMPORTANT: Garder en minuscules pour la correspondance
                'last_check' => null,
                'is_active' => true,
                'service_account_email' => '<EMAIL>', // IMPORTANT: Garder en minuscules pour la correspondance
                'service_account_password' => 'wos54chunancy'
            ],
        ];
    }

    /**
     * @see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#initialization
     */
    protected function initialize(): static
    {
        return $this
            // ->afterInstantiate(function(EntiteJuridique $hopital): void {})
        ;
    }
}
