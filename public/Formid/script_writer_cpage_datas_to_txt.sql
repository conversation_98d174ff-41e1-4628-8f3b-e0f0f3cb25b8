-- Script de transformation de données de la base de données de cpage en fichiers .txt.
-- cpage to txt.



-- Configuration.
SET ECHO      OFF
SET TERM      ON
SET HEAD      OFF
SET VERI      OFF
SET FEED      OFF
SET PAUSE     OFF
SET PAGES     0
SET RECSEP    OFF
SET TRIMSPOOL ON
SET SPACE     0

BTITLE        OFF
TTITLE        OFF

CLEAR BREAKS
CLEAR COMPUTE
CLEAR COLUMNS
CLEAR SCREEN

SET LINES 1000

set linesize 1000
set feedback off
set serveroutput on size 1000000



-- Début de l'écriture.



-- UFs actives.
SPOOL UF.txt
SELECT
    DISTINCT(('001UF' || TRIM(T1.CODEUF))) || ';' || TRIM(T2.LIBEUF) || ';' ||
            TRIM(T2.SER_COSESE) || ';' || TRIM(T4.LIBESE) || ';' ||
            ('001PO0' || TRIM(T2.POA_NUPAPA)) || ';' || TRIM(T6.LIBEPA) || ';' ||
            ('001' || TRIM(T2.ETA_NUETET)) || ';' || TRIM(T8.LILOET) || ';'
FROM PNANCY_HRZE T1 -- Identifiants des UFs, services, pôles, établissements, et libellés longs des UFs.
         INNER JOIN UFO T2 -- Quel services liés aux UFs actives ? Et libellés longs des UFs.
                    ON ('0' || TRIM(T2.NUUFUF)) = TRIM(T1.CODEUF)
                        AND T2.DATFUF = (SELECT MAX(T3.DATFUF)
                                         FROM UFO T3
                                         WHERE TRIM(T3.NUUFUF) = TRIM(T2.NUUFUF)
                                         GROUP BY TRIM(T3.NUUFUF))
         INNER JOIN SER T4 -- Libellés longs des services.
                    ON TRIM(T4.COSESE) = TRIM(T2.SER_COSESE)
                        AND T4.DATFSE = (SELECT MAX(T5.DATFSE)
                                         FROM SER T5
                                         WHERE TRIM(T5.COSESE) = TRIM(T4.COSESE)
                                         GROUP BY TRIM(T5.COSESE))
         INNER JOIN POA T6 -- Libellés longs des pôles.
                    ON TRIM(T6.NUPAPA) = TRIM(T2.POA_NUPAPA)
                        AND T6.DATFPA = (SELECT MAX(T7.DATFPA)
                                         FROM POA T7
                                         WHERE TRIM(T7.NUPAPA) = TRIM(T6.NUPAPA)
                                         GROUP BY TRIM(T7.NUPAPA))
         INNER JOIN ETA T8 -- Libellés longs des établissements.
                    ON TRIM(T8.NUETET) = TRIM(T2.ETA_NUETET)
                        AND T8.DATFET = (SELECT MAX(T9.DATFET)
                                         FROM ETA T9
                                         WHERE TRIM(T9.NUETET) = TRIM(T8.NUETET)
                                         GROUP BY TRIM(T9.NUETET));



-- Grades actifs.
SPOOL GRADE.txt
SELECT
    DISTINCT(TRIM(T1.CDCODE)) || ';' ||
            UPPER(TRIM(T1.LIBLON)) || ';'
FROM PNANCY_HRZDDRU T1 /* Grades actifs. */;



-- Agents actifs.
SPOOL AGENT.txt
SELECT
    DISTINCT(TRIM(T1.MGRAPH)) || ';' ||
            TRIM(T1.MATRIC) || ';' ||
            TRIM(T1.NURPPS) || ';' ||
            TRIM(T1.NOMUSE) || ';' || TRIM(T1.PRENOM) || ';' ||
            TRIM(T1.EMAILC) || ';' ||
            (CASE
                 WHEN TRIM(T2.TELE1ICAGE)IS NOT NULL
                     AND TRIM(T2.TYPE1ICAGE) = 'S'
                     AND TRIM(T2.TLRO1ICAGE) = 'N'
                     THEN TRIM(T2.TELE1ICAGE)
                 WHEN TRIM(T2.TELE2ICAGE)IS NOT NULL
                     AND TRIM(T2.TYPE2ICAGE) = 'S'
                     AND TRIM(T2.TLRO2ICAGE) = 'N'
                     THEN TRIM(T2.TELE2ICAGE)
                 WHEN TRIM(T2.TELE3ICAGE)IS NOT NULL
                     AND TRIM(T2.TYPE3ICAGE) = 'S'
                     AND TRIM(T2.TLRO3ICAGE) = 'N'
                     THEN TRIM(T2.TELE3ICAGE)
                END) || ';' ||
            (CASE
                 WHEN T1.TYPPER = 'M'
                     THEN 1
                 ELSE 0
                END) || ';' ||
            T1.CODGRA || ';'
FROM PNANCY_HRZY T1
         LEFT OUTER JOIN PICAGE T2
                         ON TRIM(T2.AGENTICAGE) = TRIM(T1.NUAGAG);



-- Affectations agents - UFs actives.
SPOOL AFFECTATION_AGENT_UF.txt
SELECT
    T2.MATRICUCLE_PAIE || ';' ||
    T2.CODE_UF || ';' ||
    MAX(T2.PRINCIPALE) || ';'
FROM (SELECT
          TRIM(T1.MATRIC) AS MATRICUCLE_PAIE,
          '001UF' || TRIM(T1.CODE_UF) AS CODE_UF,
          CASE
              WHEN T1.TYPE_AFFECTATION_UF = 'UFPRIN'
                  THEN 1
              ELSE 0
              END AS PRINCIPALE
      FROM PNANCY_HRZY -- Affectations agents - UFs actives.
               UNPIVOT (CODE_UF FOR TYPE_AFFECTATION_UF IN  -- n colonnes vers n lignes.
                   (UFPRIN, UF01SE, UF02SE, UF03SE, UF04SE, UF05SE, UF06SE, UF07SE, UF08SE, UF09SE)) T1) T2
GROUP BY T2.MATRICUCLE_PAIE, T2.CODE_UF;



-- Fin de l'écriture.
SPOOL OFF;

QUIT;
