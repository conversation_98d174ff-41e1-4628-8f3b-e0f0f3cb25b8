<?php

namespace App\Domain\Service\Security;

use App\Entity\Praticien\Agent;
use Psr\Cache\CacheItemPoolInterface;
use Symfony\Contracts\Cache\CacheInterface;

/**
 * Service de cache ultra-rapide pour l'authentification admin
 */
class AuthCacheService
{
    private const CACHE_TTL = 300; // 5 minutes
    private const RATE_LIMIT_TTL = 900; // 15 minutes
    
    public function __construct(
        private CacheInterface $cache
    ) {}

    /**
     * Cache un agent admin pour éviter les requêtes DB répétées
     */
    public function cacheAdminUser(string $email, Agent $agent): void
    {
        $cacheKey = $this->getAdminUserCacheKey($email);
        
        $userData = [
            'id' => $agent->getId(),
            'email' => $agent->getEmail(),
            'roles' => $agent->getRoles(),
            'nom' => $agent->getNom(),
            'prenom' => $agent->getPrenom(),
            'cached_at' => time()
        ];
        
        $this->cache->delete($cacheKey); // Force refresh
        $item = $this->cache->getItem($cacheKey);
        $item->set($userData);
        $item->expiresAfter(self::CACHE_TTL);
        $this->cache->save($item);
    }

    /**
     * Récupère un agent admin depuis le cache
     */
    public function getCachedAdminUser(string $email): ?array
    {
        $cacheKey = $this->getAdminUserCacheKey($email);
        $item = $this->cache->getItem($cacheKey);
        
        if ($item->isHit()) {
            $userData = $item->get();
            // Vérifier que l'utilisateur a toujours ROLE_ADMIN
            if (in_array('ROLE_ADMIN', $userData['roles'] ?? [])) {
                return $userData;
            }
        }
        
        return null;
    }

    /**
     * Cache les informations de rate limiting
     */
    public function cacheRateLimitInfo(string $email, string $ip, array $rateLimitInfo): void
    {
        $cacheKey = $this->getRateLimitCacheKey($email, $ip);
        
        $item = $this->cache->getItem($cacheKey);
        $item->set($rateLimitInfo);
        $item->expiresAfter(self::RATE_LIMIT_TTL);
        $this->cache->save($item);
    }

    /**
     * Récupère les infos de rate limiting depuis le cache
     */
    public function getCachedRateLimitInfo(string $email, string $ip): ?array
    {
        $cacheKey = $this->getRateLimitCacheKey($email, $ip);
        $item = $this->cache->getItem($cacheKey);
        
        if ($item->isHit()) {
            $rateLimitInfo = $item->get();
            
            // Vérifier si le cache n'est pas expiré côté métier
            if (isset($rateLimitInfo['cached_until']) && time() < $rateLimitInfo['cached_until']) {
                return $rateLimitInfo;
            }
        }
        
        return null;
    }

    /**
     * Invalide le cache d'un utilisateur (en cas de changement de rôles)
     */
    public function invalidateUserCache(string $email): void
    {
        $cacheKey = $this->getAdminUserCacheKey($email);
        $this->cache->delete($cacheKey);
    }

    /**
     * Invalide le cache de rate limiting (après connexion réussie)
     */
    public function invalidateRateLimitCache(string $email, string $ip): void
    {
        $cacheKey = $this->getRateLimitCacheKey($email, $ip);
        $this->cache->delete($cacheKey);
    }

    /**
     * Cache une tentative de connexion échouée
     */
    public function cacheFailedAttempt(string $email, string $ip): void
    {
        $cacheKey = $this->getFailedAttemptsCacheKey($email, $ip);
        $item = $this->cache->getItem($cacheKey);
        
        $attempts = $item->isHit() ? $item->get() : [];
        $attempts[] = time();
        
        // Garder seulement les tentatives des 15 dernières minutes
        $cutoff = time() - self::RATE_LIMIT_TTL;
        $attempts = array_filter($attempts, fn($timestamp) => $timestamp > $cutoff);
        
        $item->set($attempts);
        $item->expiresAfter(self::RATE_LIMIT_TTL);
        $this->cache->save($item);
    }

    /**
     * Vérifie rapidement le rate limiting depuis le cache
     */
    public function isRateLimited(string $email, string $ip, int $maxAttempts = 5): array
    {
        $cacheKey = $this->getFailedAttemptsCacheKey($email, $ip);
        $item = $this->cache->getItem($cacheKey);
        
        if (!$item->isHit()) {
            return ['allowed' => true, 'timeUntilAllowed' => 0];
        }
        
        $attempts = $item->get();
        $cutoff = time() - self::RATE_LIMIT_TTL;
        $recentAttempts = array_filter($attempts, fn($timestamp) => $timestamp > $cutoff);
        
        if (count($recentAttempts) < $maxAttempts) {
            return ['allowed' => true, 'timeUntilAllowed' => 0];
        }
        
        // Calculer le temps restant
        $oldestAttempt = min($recentAttempts);
        $timeUntilAllowed = ($oldestAttempt + self::RATE_LIMIT_TTL) - time();
        
        return [
            'allowed' => false, 
            'timeUntilAllowed' => max(0, $timeUntilAllowed)
        ];
    }

    private function getAdminUserCacheKey(string $email): string
    {
        return 'auth.admin_user.' . md5(strtolower($email));
    }

    private function getRateLimitCacheKey(string $email, string $ip): string
    {
        return 'auth.rate_limit.' . md5($email . '|' . $ip);
    }

    private function getFailedAttemptsCacheKey(string $email, string $ip): string
    {
        return 'auth.failed_attempts.' . md5($email . '|' . $ip);
    }
}
