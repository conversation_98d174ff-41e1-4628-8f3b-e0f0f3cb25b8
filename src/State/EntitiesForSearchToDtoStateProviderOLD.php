<?php

namespace App\State;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use App\ApiResource\SearchDto;
use App\Repository\AgentRepository;
use App\Repository\PoleRepository;
use App\Repository\ServiceRepository;
use App\Repository\UfsRepository;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Contracts\Cache\CacheInterface;
use Symfony\Contracts\Cache\ItemInterface;

class EntitiesForSearchToDtoStateProviderOLD implements ProviderInterface
{
    public function __construct(
        private AgentRepository $agentRepository,
        private PoleRepository $poleRepository,
        private ServiceRepository $serviceRepository,
        private UfsRepository $ufsRepository,
        private CacheInterface $cache,
        #[Autowire('%env(int:API_CACHE_TTL)%')] private int $apiCacheTtl = 3600,
    ) {
    }

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): object|array|null
    {
        // Clé de cache pour les entités de recherche
        $cacheKey = 'supra_search_entities_all_for_fuse_js';

        return $this->cache->get($cacheKey, function (ItemInterface $item) {
            $item->expiresAfter($this->apiCacheTtl); // TTL depuis la variable d'environnement
            
            try {
                $searchDtos = [];
                
                // Récupérer les agents via repository
                $searchDtos = array_merge($searchDtos, $this->getPraticiens());
                
                // Récupérer les pôles via repository
                $searchDtos = array_merge($searchDtos, $this->getPoles());
                
                // Récupérer les services via repository
                $searchDtos = array_merge($searchDtos, $this->getServices());
                
                // Récupérer les UFs via repository
                $searchDtos = array_merge($searchDtos, $this->getUfs());
                
                if (empty($searchDtos)) {
                    $dto = new SearchDto();
                    $dto->id = "no_data";
                    $dto->type = "praticien";
                    $dto->nom = "Aucune donnée";
                    $dto->prenom = "Base vide";
                    $dto->fullName = "Aucune donnée";
                    $dto->keywords = ["vide"];
                    
                    return [$dto];
                }
                
                return $searchDtos;
                
            } catch (\Exception $e) {
                $dto = new SearchDto();
                $dto->id = "error";
                $dto->type = "praticien";
                $dto->nom = "Erreur";
                $dto->prenom = substr($e->getMessage(), 0, 50);
                $dto->fullName = "Erreur SQL";
                $dto->keywords = ["erreur"];
                
                return [$dto];
            }
        });
    }

    private function getPraticiens(): array
    {
        // Utiliser le repository pour récupérer les agents actifs
        $agents = $this->agentRepository->findBy(
            ['isActif' => true], 
            ['nom' => 'ASC', 'prenom' => 'ASC'], 
            50
        );

        $dtos = [];
        foreach ($agents as $agent) {
            $dto = new SearchDto();
            $dto->id = (string)$agent->getId();
            $dto->type = 'praticien';
            
            // Utiliser les getters de l'entité
            $dto->nom = $agent->getNom();
            $dto->prenom = $agent->getPrenom();
            $dto->matricule = $agent->getHrUser(); // hrUser comme matricule
            
            // Construire le nom complet avec titre si disponible
            $fullNameParts = array_filter([
                $agent->getTitre(),
                $agent->getNom(),
                $agent->getPrenom()
            ]);
            $dto->fullName = implode(' ', $fullNameParts);
            
            // Créer des mots-clés pour la recherche
            $keywords = array_filter([
                $agent->getNom(),
                $agent->getPrenom(),
                $agent->getTitre(),
                $agent->getHrUser(),
                $agent->getEmail(),
                $dto->fullName
            ]);
            $dto->keywords = array_unique($keywords);
            
            $dtos[] = $dto;
        }

        return $dtos;
    }

    private function getPoles(): array
    {
        // Utiliser le repository pour récupérer les pôles actifs
        $poles = $this->poleRepository->findBy(
            ['isActif' => true], 
            ['libelle' => 'ASC']
        );

        $dtos = [];
        foreach ($poles as $pole) {
            $dto = new SearchDto();
            $dto->id = (string)$pole->getId();
            $dto->type = 'pole';
            $dto->nom = $pole->getLibelle();
            $dto->keywords = [
                $pole->getLibelle(),
                $pole->getPolecode(),
                $dto->fullName
            ];
            
            $dtos[] = $dto;
        }

        return $dtos;
    }

    private function getServices(): array
    {
        // Utiliser le repository pour récupérer les services actifs
        $services = $this->serviceRepository->findBy(
            ['isActif' => true], 
            ['libelle' => 'ASC']
        );

        $dtos = [];
        foreach ($services as $service) {
            $dto = new SearchDto();
            $dto->id = (string)$service->getId();
            $dto->type = 'service';
            $dto->nom = $service->getLibelle();
            $dto->fullName = sprintf('%s (%s)', $service->getLibelle(), $service->getSecode());
            $dto->keywords = [
                $service->getLibelle(),
                $service->getSecode(),
                $dto->fullName
            ];
            
            $dtos[] = $dto;
        }

        return $dtos;
    }

    private function getUfs(): array
    {
        // Utiliser le repository pour récupérer les UFs actives
        $ufs = $this->ufsRepository->findBy(
            ['isActif' => true], 
            ['libelle' => 'ASC']
        );

        $dtos = [];
        foreach ($ufs as $uf) {
            $dto = new SearchDto();
            $dto->id = (string)$uf->getId();
            $dto->type = 'ufs';
            $dto->nom = $uf->getLibelle();
            $dto->fullName = sprintf('%s (%s)', $uf->getLibelle(), $uf->getUfcode());
            $dto->keywords = [
                $uf->getLibelle(),
                $uf->getUfcode(),
                $dto->fullName
            ];
            
            $dtos[] = $dto;
        }

        return $dtos;
    }
}
