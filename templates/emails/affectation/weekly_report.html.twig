<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rapport hebdomadaire - Importations d'affectations</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #007bff; color: white; padding: 20px; border-radius: 5px; text-align: center; }
        .content { background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-top: 20px; }
        .stats-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 20px 0; }
        .stat-card { background-color: white; padding: 15px; border-radius: 5px; text-align: center; border-left: 4px solid #007bff; }
        .stat-number { font-size: 24px; font-weight: bold; color: #007bff; }
        .stat-label { font-size: 14px; color: #666; }
        .period { background-color: #e7f3ff; padding: 10px; border-radius: 5px; margin: 15px 0; text-align: center; }
        .footer { text-align: center; margin-top: 20px; font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 Rapport hebdomadaire</h1>
        <p>Importations automatiques d'affectations</p>
        <p>Entité Juridique: {{ entiteJuridique.code }}</p>
    </div>

    <div class="content">
        <div class="period">
            <strong>Période :</strong> {{ weekStart|date('d/m/Y') }} au {{ weekEnd|date('d/m/Y') }}
        </div>

        <h2>Résumé de la semaine</h2>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">{{ stats.totalFiles ?? 0 }}</div>
                <div class="stat-label">Fichiers traités</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ stats.totalAffectationsCreated ?? 0 }}</div>
                <div class="stat-label">Affectations créées</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ stats.totalAffectationsUpdated ?? 0 }}</div>
                <div class="stat-label">Affectations mises à jour</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" style="color: {{ stats.totalErrors > 0 ? '#dc3545' : '#28a745' }};">{{ stats.totalErrors ?? 0 }}</div>
                <div class="stat-label">Erreurs</div>
            </div>
        </div>

        {% if stats.dailyBreakdown is defined and stats.dailyBreakdown is not empty %}
        <div style="background-color: white; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h3>Détail par jour</h3>
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="background-color: #f8f9fa;">
                        <th style="padding: 8px; text-align: left; border-bottom: 1px solid #dee2e6;">Date</th>
                        <th style="padding: 8px; text-align: center; border-bottom: 1px solid #dee2e6;">Fichiers</th>
                        <th style="padding: 8px; text-align: center; border-bottom: 1px solid #dee2e6;">Créées</th>
                        <th style="padding: 8px; text-align: center; border-bottom: 1px solid #dee2e6;">Mises à jour</th>
                        <th style="padding: 8px; text-align: center; border-bottom: 1px solid #dee2e6;">Erreurs</th>
                    </tr>
                </thead>
                <tbody>
                    {% for day, data in stats.dailyBreakdown %}
                    <tr>
                        <td style="padding: 8px; border-bottom: 1px solid #dee2e6;">{{ day|date('d/m/Y') }}</td>
                        <td style="padding: 8px; text-align: center; border-bottom: 1px solid #dee2e6;">{{ data.files ?? 0 }}</td>
                        <td style="padding: 8px; text-align: center; border-bottom: 1px solid #dee2e6;">{{ data.created ?? 0 }}</td>
                        <td style="padding: 8px; text-align: center; border-bottom: 1px solid #dee2e6;">{{ data.updated ?? 0 }}</td>
                        <td style="padding: 8px; text-align: center; border-bottom: 1px solid #dee2e6; color: {{ data.errors > 0 ? '#dc3545' : 'inherit' }};">{{ data.errors ?? 0 }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% endif %}

        {% if stats.recentErrors is defined and stats.recentErrors is not empty %}
        <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h4 style="color: #856404; margin-top: 0;">⚠️ Erreurs récentes</h4>
            <ul style="color: #856404;">
                {% for error in stats.recentErrors %}
                <li>{{ error.date|date('d/m/Y H:i') }} - {{ error.message }}</li>
                {% endfor %}
            </ul>
        </div>
        {% endif %}

        {% if stats.recommendations is defined and stats.recommendations is not empty %}
        <div style="background-color: #d1ecf1; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h4 style="color: #0c5460; margin-top: 0;">💡 Recommandations</h4>
            <ul style="color: #0c5460;">
                {% for recommendation in stats.recommendations %}
                <li>{{ recommendation }}</li>
                {% endfor %}
            </ul>
        </div>
        {% endif %}

        <div style="margin-top: 20px;">
            <p><strong>Dossier surveillé :</strong> {{ entiteJuridique.affectationAgentFilesPaths }}</p>
            <p><strong>Chargement automatique :</strong>
                {% if entiteJuridique.chargementAutomatiqueDesAffectationEstActif %}
                    <span style="color: #28a745;">✅ Activé</span>
                {% else %}
                    <span style="color: #dc3545;">❌ Désactivé</span>
                {% endif %}
            </p>
        </div>
    </div>

    <div class="footer">
        <p>Rapport généré automatiquement le {{ timestamp|date('d/m/Y à H:i:s') }}<br>
        Système SUPRA - Entité Juridique: {{ entiteJuridique.code }}</p>
    </div>
</body>
</html>
