<?php

namespace App\Mapper\AgentHopitalRole;

use App\ApiResource\Praticien\AgentDto;
use App\ApiResource\Praticien\AgentHopitalRoleDto;
use App\ApiResource\Structure\EntiteJuridiqueDto;
use App\ApiResource\Structure\TenantDto;
use App\Entity\Praticien\AgentHopitalRole;
use Symfonycasts\MicroMapper\AsMapper;
use Symfonycasts\MicroMapper\MapperInterface;
use Symfonycasts\MicroMapper\MicroMapperInterface;

#[AsMapper(from: AgentHopitalRole::class, to: AgentHopitalRoleDto::class)]
class AgentHopitalRoleEntityToDtoMapper implements MapperInterface
{
    public function __construct(private MicroMapperInterface $microMapper) {}

    public function load(object $from, string $toClass, array $context): object
    {
        $role = $from;
        assert($role instanceof AgentHopitalRole);

        $dto = new AgentHopitalRoleDto();
        $dto->id = $role->getId(); // On charge uniquement l'ID ici

        return $dto;
    }

    public function populate(object $from, object $to, array $context): object
    {
        $role = $from;
        $dto = $to;
        assert($role instanceof AgentHopitalRole);
        assert($dto instanceof AgentHopitalRoleDto);

        $dto->dateCreation = $role->getDateCreation();

        $dto->role = $role->getRole();
        $dto->dateAffectation = $role->getDateAffectation();

        // Convertir Agent en IRI API Platform
        if ($role->getAgent()) {
            $dto->agent = $this->microMapper->map($role->getAgent(), AgentDto::class, [
                MicroMapperInterface::MAX_DEPTH => 0,
            ]);
        }

        // Convertir EntiteJuridique en IRI API Platform
        if ($role->getHopital()) {
            $dto->hopital = $this->microMapper->map($role->getHopital(), EntiteJuridiqueDto::class, [
                MicroMapperInterface::MAX_DEPTH => 0,
            ]);
        }

        return $dto;
    }
}
