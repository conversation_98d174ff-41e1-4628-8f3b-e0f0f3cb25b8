<?php

namespace App\Controller\Admin\Domain;

use App\Controller\Importation\Actes\Dto\ActeImportDto;
use App\Entity\Activite\Actes;
use App\Entity\Praticien\Agent;
use App\Entity\Structure\EntiteJuridique;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class ActesCsvImportController extends AbstractController
{
    #[Route('/admin/import/actes', name: 'admin_import_actes_csv')]
    public function importActesCsv(
        Request $request,
        EntityManagerInterface $entityManager
    ): Response {
        $flashBag = $request->getSession()->getFlashBag();

        // Récupérer toutes les entités juridiques disponibles
        $entitesJuridiques = $entityManager->getRepository(EntiteJuridique::class)->findAll();

        if ($request->isMethod('POST')) {
            /** @var UploadedFile $file */
            $file = $request->files->get('csv_file');
            $entiteJuridiqueId = $request->request->get('entite_juridique_id');

            // Validation de l'entité juridique
            if (empty($entiteJuridiqueId)) {
                $flashBag->add('danger', '❌ Veuillez sélectionner une entité juridique.');
                return $this->render('admin/import_csv.html.twig', [
                    'entity' => 'Actes',
                    'entitesJuridiques' => $entitesJuridiques
                ]);
            }

            $entiteJuridique = $entityManager->getRepository(EntiteJuridique::class)->find($entiteJuridiqueId);
            if (!$entiteJuridique) {
                $flashBag->add('danger', '❌ Entité juridique introuvable.');
                return $this->render('admin/import_csv.html.twig', [
                    'entity' => 'Actes',
                    'entitesJuridiques' => $entitesJuridiques
                ]);
            }

            if (!$file || $file->getClientOriginalExtension() !== 'csv') {
                $flashBag->add('danger', '❌ Veuillez fournir un fichier CSV valide.');
                return $this->render('admin/import_csv.html.twig', [
                    'entity' => 'Actes',
                    'entitesJuridiques' => $entitesJuridiques
                ]);
            }

            try {
                $result = $this->processImport($file, $entiteJuridique, $entityManager);

                if ($result['success']) {
                    $flashBag->add('success', sprintf('✅ Importation réussie des actes pour "%s" avec %d lignes !',
                        $entiteJuridique->getNom(), $result['count']));
                } else {
                    $flashBag->add('danger', '⚠️ Des erreurs sont survenues lors de l\'import.');
                    foreach (array_slice($result['errors'], 0, 10) as $error) {
                        $flashBag->add('danger', $error);
                    }
                    if (count($result['errors']) > 10) {
                        $flashBag->add('danger', sprintf('⚠️ %d autres erreurs non affichées.', count($result['errors']) - 10));
                    }
                }
            } catch (\Exception $e) {
                $flashBag->add('danger', '❌ Erreur lors de l\'import : ' . $e->getMessage());
            }

            return $this->redirectToRoute('admin');
        }

        return $this->render('admin/import_csv.html.twig', [
            'entity' => 'Actes',
            'entitesJuridiques' => $entitesJuridiques
        ]);
    }

    private function processImport(UploadedFile $file, EntiteJuridique $entiteJuridique, EntityManagerInterface $entityManager): array
    {
        $csv = new \SplFileObject($file->getPathname(), 'r');
        $csv->setFlags(\SplFileObject::READ_CSV);
        $csv->setCsvControl(';'); // Utiliser le point-virgule comme délimiteur

        $batchSize = 1000;
        $errors = [];
        $count = 0;

        // Désactivation temporaire des logs Doctrine
        $entityManager->getConnection()->getConfiguration()->setSQLLogger(null);

        // Lire l'entête
        $header = $csv->fgetcsv();
        if (!$header || count($header) === 1) {
            throw new \InvalidArgumentException('Le fichier CSV est vide ou mal formé.');
        }

        // Nettoyer l'entête
        $header = array_map(fn($col) => strtoupper(trim($col)), $header);

        while (!$csv->eof()) {
            $row = $csv->fgetcsv();

            if (!is_array($row) || count($row) !== count($header)) {
                continue;
            }

            $row = array_map(fn($value) => trim($value), $row);
            $record = array_combine($header, $row);

            try {
                $dto = $this->createDtoFromCsvRecord($record);
                $acte = $this->createActeFromDto($dto, $entiteJuridique, $entityManager);

                if ($acte) {
                    $entityManager->persist($acte);
                    $count++;

                    if ($count % $batchSize === 0) {
                        $entityManager->flush();
                        $entityManager->clear();
                        // Recharger l'entité après clear
                        $entiteJuridique = $entityManager->getRepository(EntiteJuridique::class)->find($entiteJuridique->getId());
                    }
                }
            } catch (\Exception $e) {
                $errors[] = "⚠️ Erreur ligne " . ($count + 1) . " : " . $e->getMessage();
            }
        }

        $entityManager->flush();
        $entityManager->clear();

        return [
            'success' => empty($errors),
            'count' => $count,
            'errors' => $errors
        ];
    }

    private function createDtoFromCsvRecord(array $record): ActeImportDto
    {
        $dto = new ActeImportDto();
        $reflection = new \ReflectionClass(ActeImportDto::class);

        foreach ($reflection->getProperties(\ReflectionProperty::IS_PUBLIC) as $property) {
            $propertyName = $property->getName();
            $csvColumnName = $this->convertToSnakeCase($propertyName);

            if (isset($record[$csvColumnName])) {
                $value = $record[$csvColumnName];

                if (!empty($value)) {
                    $value = $this->convertValueToPropertyType($value, $property);
                }

                $property->setValue($dto, $value);
            }
        }

        return $dto;
    }

    private function convertToSnakeCase(string $input): string
    {
        $result = preg_replace('/([a-z])([A-Z])/', '$1_$2', $input);
        return strtoupper($result);
    }

    private function convertValueToPropertyType($value, \ReflectionProperty $property)
    {
        $type = $property->getType();

        if (!$type) {
            return $value;
        }

        $typeName = $type->getName();

        switch ($typeName) {
            case 'int':
                return (int) $value;
            case 'float':
                return (float) $value;
            case 'bool':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case 'string':
            default:
                return $value;
        }
    }

    private function createActeFromDto(ActeImportDto $dto, EntiteJuridique $entiteJuridique, EntityManagerInterface $entityManager): Actes
    {
        // Validation des champs obligatoires
        if (empty($dto->codeActe) || empty($dto->descriptionActe)) {
            throw new \InvalidArgumentException('⚠️ Code et description sont obligatoires pour les actes');
        }

        // Validation du DTO
        try {
            $dto->validate();
        } catch (\InvalidArgumentException $e) {
            throw new \InvalidArgumentException('⚠️ ' . $e->getMessage());
        }

        $acte = new Actes();
        
        // Champs de base
        $acte->setInternum($dto->internum);
        $acte->setCode($dto->codeActe);
        $acte->setDescription($dto->descriptionActe);
        $acte->setTypeActe($dto->typeActe ?? 'CCAM');

        // Champs temporels
        if ($dto->anneeActe) {
            $acte->setAnnee($dto->anneeActe);
        }
        if ($dto->moisActe) {
            $acte->setMois($dto->moisActe);
        }
        
        // Date de réalisation et génération de validFrom et validTo
        $dateActe = null;
        if ($dto->dateActe) {
            try {
                $dateActe = \DateTime::createFromFormat('d/m/Y', $dto->dateActe);
                if ($dateActe === false) {
                    throw new \InvalidArgumentException('Failed to parse time string: ' . $dto->dateActe);
                }
                $acte->setDateRealisation($dateActe);
                
                // Définir validFrom et validTo à la date de l'acte
                $acte->setValidFrom($dateActe);
                $acte->setValidTo($dateActe);
            } catch (\Exception $e) {
                throw new \InvalidArgumentException('Failed to parse time string: ' . $dto->dateActe);
            }
        } else {
            // Si pas de date d'acte, utiliser la date actuelle
            $now = new \DateTime();
            $acte->setValidFrom($now);
            $acte->setValidTo($now);
        }
        
        // Autres champs temporels
        if ($dto->semaineIso) {
            $acte->setSemaineIso($dto->semaineIso);
        }
        if ($dto->periodeType) {
            $acte->setPeriodeType($dto->periodeType);
        }
        
        // Champs quantitatifs
        if ($dto->nombreActes) {
            $acte->setNombreDeRealisation($dto->nombreActes);
        }
        
        // Champs de métadonnées
        $acte->setSource($dto->source ?? 'import');
        
        // Champs spécifiques au type de venue
        if ($dto->typeVenue) {
            $acte->setTypeVenue($dto->typeVenue);
        }
        if ($dto->libTypeVenue) {
            $acte->setLibTypeVenue($dto->libTypeVenue);
        }
        
        // Champs spécifiques aux coefficients et regroupements
        if ($dto->icrA) {
            $acte->setIcrA($dto->icrA);
        }
        if ($dto->coefficient) {
            $acte->setCoefficient($dto->coefficient);
        }
        if ($dto->lettreCoef) {
            $acte->setLettreCoef($dto->lettreCoef);
        }
        if ($dto->regroupement) {
            $acte->setRegroupement($dto->regroupement);
        }
        if ($dto->activite) {
            $acte->setActivite($dto->activite);
        }
        if ($dto->activiteLib) {
            $acte->setActiviteLib($dto->activiteLib);
        }

        // Gérer les relations avec agent de la même entité juridique
        if ($dto->praticienMatricule) {
            $agent = $entityManager->getRepository(Agent::class)->findOneBy([
                'hrUser' => $dto->praticienMatricule,
                'hopital' => $entiteJuridique
            ]);
            if ($agent) {
                $acte->setAgent($agent);
            } else {
                throw new \InvalidArgumentException('⚠️ Agent non trouvé : ' . $dto->praticienMatricule . ' pour l\'entité juridique : ' . $entiteJuridique->getNom());
            }
        }

        // Gérer les relations avec les UFs
        if ($dto->ufPrincipalCode) {
            $ufPrincipal = $this->findUfByCodeAndEj($dto->ufPrincipalCode, $entiteJuridique, $dateActe, $entityManager);
            if ($ufPrincipal) {
                $acte->setUfPrincipal($ufPrincipal);
            } else {
                throw new \InvalidArgumentException('⚠️ UF principale non trouvée : ' . $dto->ufPrincipalCode . ' pour l\'entité juridique : ' . $entiteJuridique->getNom());
            }
        }
        
        if ($dto->ufDemande) {
            $ufDemande = $this->findUfByCodeAndEj($dto->ufDemande, $entiteJuridique, $dateActe, $entityManager);
            if ($ufDemande) {
                $acte->setUfDemande($ufDemande);
            }
        }
        
        if ($dto->ufIntervention) {
            $ufIntervention = $this->findUfByCodeAndEj($dto->ufIntervention, $entiteJuridique, $dateActe, $entityManager);
            if ($ufIntervention) {
                $acte->setUfIntervention($ufIntervention);
            }
        }

        return $acte;
    }
    
    /**
     * Recherche une UF par son code dans une entité juridique donnée avec gestion de la dimension temporelle.
     * 
     * @param string|null $ufCode Code de l'UF à rechercher
     * @param EntiteJuridique $entiteJuridique Entité juridique dans laquelle rechercher l'UF
     * @param \DateTime|null $dateReference Date de référence pour la recherche (dimension temporelle)
     * @param EntityManagerInterface $entityManager Entity manager pour effectuer la requête
     * @return \App\Entity\Structure\Ufs|null L'UF trouvée ou null si aucune UF ne correspond
     */
    private function findUfByCodeAndEj(?string $ufCode, EntiteJuridique $entiteJuridique, ?\DateTime $dateReference, EntityManagerInterface $entityManager): ?object
    {
        if (empty($ufCode)) {
            return null;
        }

        // Première tentative: recherche par le chemin UF -> CR -> Pole -> EntiteJuridique
        $qb = $entityManager->createQueryBuilder()
            ->select('u')
            ->from('App\Entity\Structure\Ufs', 'u')
            ->join('u.cr', 'cr')
            ->join('cr.pole', 'p')
            ->where('u.ufcode = :ufCode')
            ->andWhere('p.hopital = :entiteJuridique')
            ->setParameter('ufCode', $ufCode)
            ->setParameter('entiteJuridique', $entiteJuridique);

        // Filtre temporel pour SCD type 2
        if ($dateReference) {
            $qb->andWhere('u.datdeb <= :dateRef')
                ->andWhere('(u.datfin IS NULL OR u.datfin >= :dateRef)')
                ->setParameter('dateRef', $dateReference);
        }

        // Ordre par date de fin décroissante pour prendre la version la plus récente
        $qb->orderBy('u.datfin', 'DESC');

        $result = $qb->getQuery()->getOneOrNullResult();
        
        // Si aucun résultat, recherche simplifiée par code UF uniquement
        if (!$result) {
            $qb = $entityManager->createQueryBuilder()
                ->select('u')
                ->from('App\Entity\Structure\Ufs', 'u')
                ->where('u.ufcode = :ufCode')
                ->setParameter('ufCode', $ufCode);
                
            // Filtre temporel pour SCD type 2
            if ($dateReference) {
                $qb->andWhere('u.datdeb <= :dateRef')
                    ->andWhere('(u.datfin IS NULL OR u.datfin >= :dateRef)')
                    ->setParameter('dateRef', $dateReference);
            }
            
            // Ordre par date de fin décroissante pour prendre la version la plus récente
            $qb->orderBy('u.datfin', 'DESC');
            
            $result = $qb->getQuery()->getOneOrNullResult();
        }

        return $result;
    }
}
