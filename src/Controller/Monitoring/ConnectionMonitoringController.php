<?php
namespace App\Controller\Monitoring;

use App\Entity\Praticien\Agent;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/api/monitoring')]
class ConnectionMonitoringController extends AbstractController
{
    public function __construct(private EntityManagerInterface $em)
    {}

    #[Route('/connections', name: 'monitor_connections', methods: ['GET'])]
    public function getConnectionStats(): JsonResponse
    {
        $agents = $this->em->getRepository(Agent::class)->findAll();
        $stats = [];

        foreach ($agents as $agent) {
            $connectionStats = $agent->getConnectionStats();
            if ($connectionStats['total_connections'] > 0) {
                $stats[] = [
                    'agent_id' => $agent->getId(),
                    'name' => $agent->getNom() . ' ' . $agent->getPrenom(),
                    'email' => $agent->getEmail(),
                    'total_connections' => $connectionStats['total_connections'],
                    'first_connection' => $connectionStats['first_connection'],
                    'last_connection' => $connectionStats['last_connection'],
                    'monthly_stats' => $connectionStats['monthly_stats'],
                    'daily_stats' => $connectionStats['daily_stats']
                ];
            }
        }

        return new JsonResponse([
            'success' => true,
            'total_agents' => count($stats),
            'stats' => $stats
        ]);
    }
}