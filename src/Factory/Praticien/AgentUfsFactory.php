<?php

namespace App\Factory\Praticien;

use App\Domain\Enum\PeriodeType;
use App\Entity\Praticien\AgentUfs;
use App\Factory\Structure\UfsFactory;
use Zenstruck\Foundry\Persistence\PersistentProxyObjectFactory;

/**
 * @extends PersistentProxyObjectFactory<AgentUfs>
 */
final class AgentUfsFactory extends PersistentProxyObjectFactory
{
    public function __construct()
    {
    }

    public static function class(): string
    {
        return AgentUfs::class;
    }

    protected function defaults(): array|callable
    {
        return [
            // Relations
            'agent' => AgentFactory::random(),
            'ufs' => UfsFactory::random(),

            // Champs de base
            'date_debut' => self::faker()->dateTimeBetween('-5 years', 'now'),
            'date_fin' => self::faker()->optional(0.7)->dateTimeBetween('now', '+5 years'),
            'isActif' => true,

            // DamAffectationTrait fields
            'rgt' => self::faker()->randomElement(['CDI', 'CDD', 'VAC']),
            'etpStatutaire' => self::faker()->randomFloat(2, 0.5, 1),
            'tauxAffectation' => self::faker()->numberBetween(50, 100),
            'etp' => self::faker()->randomFloat(2, 0.5, 1),
            'affectationPrincipale' => self::faker()->randomElement(['O', 'N']),
            'typeGrade' => self::faker()->randomElement(['A', 'B', 'C']),
            'libelleGrade' => self::faker()->word(),
            'absences' => self::faker()->numberBetween(0, 30),

            // HorodatageTrait fields
            'validFrom' => self::faker()->dateTimeBetween('-1 year', 'now'),
            'validTo' => self::faker()->optional(0.3)->dateTimeBetween('now', '+1 year'),
            'periodeType' => self::faker()->randomElement(PeriodeType::VALUES),
            'source' => self::faker()->word(),
        ];
    }

    protected function initialize(): static
    {
        return $this;
    }
}