<?php

namespace App\ApiResource\Activite\Partials;

/**
 * DTO pour les statistiques multi-actes sur 3 périodes
 *
 * Cette classe représente les données agrégées de réalisations
 * par acte médical sur 3 périodes temporelles (P1, P2, P3).
 *
 * Route API: POST /api/actes/multi-stats
 *
 * Exemple d'utilisation Postman:
 * POST http://localhost:8000/api/actes/multi-stats?p1Start=2024-01-01&p1End=2024-12-31&p2Start=2023-01-01&p2End=2023-12-31&p3Start=2022-01-01&p3End=2022-12-31
 * Body: {"activiteIds": ["1", "2", "3", "4", "5"]}
 */
class ActeMultiStatsDto
{
    public function __construct(
        /** @var string Code de l'acte médical */
        public readonly string $acteCode,

        /** @var string Description de l'acte médical */
        public readonly string $acteDescription,

        /** @var int Nombre total de réalisations en période P1 (actuelle) */
        public readonly int $p1Count,

        /** @var int Nombre total de réalisations en période P2 (année précédente) */
        public readonly int $p2Count,

        /** @var int Nombre total de réalisations en période P3 (il y a 2 ans) */
        public readonly int $p3Count,

        /** @var int Nombre total d'activités sélectionnées pour cet acte */
        public readonly int $totalActivites,

        /** @var int Total des réalisations sur les 3 périodes */
        public readonly int $totalRealisations
    ) {}
}