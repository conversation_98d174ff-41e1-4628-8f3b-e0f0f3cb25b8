<?php

namespace App\Entity\Activite\Trait;

use Doctrine\DBAL\Types\Types;

use Doctrine\ORM\Mapping as ORM;

/**
 *  L'une des mission initiales de SUPRA était de détecter les problèmes liés à la saisie des actes.
 *  Ce trait permet de stocker les informations de debug liées à la détection des problèmes
 *  Une fonctionalité a exploitée dans le futur (Voir Monsieur PETITCOLAS Didier pour en savoir plus)
 *  Pour l'instant je le garde pour debuguage rapide et pour pouvoir repondre à des questions du type :
 * *  - "Pourquoi cet acte n'a pas été enrichi ?"
 * *  - "Combien d'actes ont été enrichis ?"
 * *  - "Mais j'ai posé un acte, pourquoi il n'est pas dans la liste ?"
 */
trait DetectImportProblemTrait
{
    #[ORM\Column(type: Types::JSON, nullable: true)]
    private ?array $detectProbleme = null;

    public function getDetectProbleme(): ?array
    {
        return $this->detectProbleme;
    }

    public function setDetectProbleme(?array $detectProbleme): static
    {
        $this->detectProbleme = $detectProbleme;
        return $this;
    }

    /**
     * Stocke les informations de debug du refresh des actes
     */
    public function setRefreshDebugInfo(array $refreshResult): static
    {
        $this->detectProbleme = [
            'lastRefresh' => $refreshResult['lastRefreshedAt'] ?? null,
            'total' => $refreshResult['total'] ?? 0,
            'enrichies' => $refreshResult['debug']['enrichies'] ?? 0,
            'nonEnrichies' => $refreshResult['debug']['nonEnrichies'] ?? 0,
            'interventionsNonEnrichies' => $refreshResult['interventionsNonEnrichies'] ?? [],
            'logs' => $refreshResult['logs'] ?? []
        ];
        return $this;
    }
}