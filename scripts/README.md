# Generateur de Donnees de Test CSV

Scripts PHP pour generer des fichiers CSV de test pour valider les imports batch.

## Fichiers Disponibles

- **generate_mock_data.php** : Generateur principal PHP
- **interactive_generator.php** : Interface interactive
- **populate_agents.php** : Generation d'agents fictifs
- **README.md** : Cette documentation

## Utilisation

### Interface Interactive
```bash
php interactive_generator.php
```

### Generation Directe
```bash
# Generation simple
php generate_mock_data.php affectations 1000

# Tous les types
php generate_mock_data.php all 500

# Types specifiques
php generate_mock_data.php sigaps 2000
php generate_mock_data.php actes 1500
```

## Types de Donnees Generees

### 1. Affectations
```csv
uConnexion,dateDebut,dateFin,codeUf,matricule,typeGrade,libelleGrade,rgt,etpStatutaire,tauxAffectation,sommeEtp,affectationPrincipale,absences,source
HR650575,01/12/2024,31/12/2024,7AJH,458565,<PERSON>,<PERSON><PERSON><PERSON>,RGT001,1.0,100,1.0,O,5,I<PERSON>ORTA<PERSON>ON Manuelle
```

### 2. SIGAPS
```csv
agentHrU,dateDebut,dateFin,score,categorie,nombrePublication,repartitionParCategorie,source
HR650575,2024-01-01,2024-12-31,85.5,A+,15,CatA+:8,IMPORTATION Manuelle
```

### 3. Liberal
```csv
agentHrU,dateDebut,dateFin,typeActe,nomActe,tarif,dateRealisation,source
HR650575,2024-03-01,2024-03-31,CCAM,Acte Liberal 42,125.50,2024-03-15,IMPORTATION Manuelle
```

### 4. Actes
```csv
ufPrincipalCode,ufDemandeCode,ufInterventionCode,code,description,agentHrUser,dateRealisation,typeActe,nombreDeRealisation,annee,mois,internum,typeVenue,libTypeVenue,activite,activiteLib,icrA,coefficient,lettreCoef
1MY5,RGTI,V3R5,ACT0042,Description acte 42,HR650575,2022-08-29,CCAM,2,2022,8,45904301,1,Consultation,5,Consultation specialisee,51,1,50,A
```

### 5. Gardes/Astreintes
```csv
agentHrU,dateDebut,dateFin,typeGarde,totalGarde,totalAstreinte,dateGarde,source
HR650575,2024-06-01,2024-06-02,GARDE,12,0,2024-06-01,IMPORTATION Manuelle
```

## Options de Volume

| Volume | Lignes | Usage | Temps |
|--------|--------|-------|-------|
| Petit | 10-100 | Tests fonctionnels | < 1s |
| Moyen | 1000 | Tests de performance | 1-3s |
| Gros | 10000+ | Tests d'optimisation | 5-15s |

## Scenarios de Test

### Tests Fonctionnels
```bash
php generate_mock_data.php affectations 50
php generate_mock_data.php sigaps 25
```

### Tests de Performance
```bash
php generate_mock_data.php affectations 1000
php generate_mock_data.php actes 1500
```

### Tests de Charge
```bash
php generate_mock_data.php affectations 10000
php generate_mock_data.php all 5000
```

## Generation d'agents fictifs

Pour peupler la base de donnees avec 100 agents fictifs :

```bash
php bin/console app:populate-agents
```

### Fonctionnalites

Cette commande :
- Verifie l'existence d'une entite juridique et en cree une si necessaire
- Genere 100 agents fictifs avec des donnees aleatoires
- Affiche une barre de progression pendant la generation
- Confirme la creation des agents a la fin

### Utilisation

Cette commande est utile pour :
- Tester les fonctionnalites qui necessitent des agents
- Preparer un environnement de developpement avec des donnees
- Demonstrer les fonctionnalites de l'application

### Remarques

Les agents generes sont fictifs et ne doivent pas etre utilises en production.