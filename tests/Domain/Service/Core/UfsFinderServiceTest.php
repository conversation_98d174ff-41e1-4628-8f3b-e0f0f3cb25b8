<?php

namespace App\Tests\Domain\Service\Core;

use App\Domain\Service\Core\UfsFinderService;
use App\Entity\Structure\EntiteJuridique;
use App\Entity\Structure\Ufs;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\QueryBuilder;
use Doctrine\ORM\Query;
use PHPUnit\Framework\MockObject\MockObject;

it('can be instantiated', function () {
    $entityManager = $this->createMock(EntityManagerInterface::class);
    
    $service = new UfsFinderService($entityManager);
    
    expect($service)->toBeInstanceOf(UfsFinderService::class);
});

it('returns null when ufCode is empty', function () {
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $entiteJuridique = $this->createMock(EntiteJuridique::class);
    
    $service = new UfsFinderService($entityManager);
    $result = $service->findUfByCodeAndEj(null, $entiteJuridique);
    
    expect($result)->toBeNull();
    
    $result = $service->findUfByCodeAndEj('', $entiteJuridique);
    
    expect($result)->toBeNull();
});

it('finds UF by code and entity juridique without date parameters', function () {
    // Create mocks
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $queryBuilder = $this->createMock(QueryBuilder::class);
    $query = $this->createMock(Query::class);
    $ufs = $this->createMock(Ufs::class);
    $entiteJuridique = $this->createMock(EntiteJuridique::class);
    
    // Configure mocks with specific expectations
    $entityManager->expects($this->once())
        ->method('createQueryBuilder')
        ->willReturn($queryBuilder);
    
    $queryBuilder->expects($this->once())
        ->method('select')
        ->with('u')
        ->willReturnSelf();
        
    $queryBuilder->expects($this->once())
        ->method('from')
        ->with(Ufs::class, 'u')
        ->willReturnSelf();
    
    // Expect join calls
    $queryBuilder->method('join')
        ->willReturnSelf();
    
    $queryBuilder->expects($this->once())
        ->method('where')
        ->with('u.ufcode = :ufCode')
        ->willReturnSelf();
        
    $queryBuilder->method('andWhere')
        ->willReturnSelf();
    
    // Expect parameter settings
    $queryBuilder->method('setParameter')
        ->willReturnSelf();
    
    $queryBuilder->expects($this->once())
        ->method('orderBy')
        ->with('u.datfin', 'DESC')
        ->willReturnSelf();
        
    $queryBuilder->expects($this->once())
        ->method('getQuery')
        ->willReturn($query);
    
    $query->expects($this->once())
        ->method('getOneOrNullResult')
        ->willReturn($ufs);
    
    // Create service and call method
    $service = new UfsFinderService($entityManager);
    $result = $service->findUfByCodeAndEj('UF001', $entiteJuridique);
    
    // Assertions
    expect($result)->toBe($ufs);
});

it('finds UF by code and entity juridique with reference date', function () {
    // Create mocks
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $queryBuilder = $this->createMock(QueryBuilder::class);
    $query = $this->createMock(Query::class);
    $ufs = $this->createMock(Ufs::class);
    $entiteJuridique = $this->createMock(EntiteJuridique::class);
    $dateReference = new \DateTime('2025-08-09');
    
    // Configure mocks with specific expectations
    $entityManager->expects($this->once())
        ->method('createQueryBuilder')
        ->willReturn($queryBuilder);
    
    $queryBuilder->expects($this->once())
        ->method('select')
        ->with('u')
        ->willReturnSelf();
        
    $queryBuilder->expects($this->once())
        ->method('from')
        ->with(Ufs::class, 'u')
        ->willReturnSelf();
    
    // Expect join calls
    $queryBuilder->method('join')
        ->willReturnSelf();
    
    $queryBuilder->expects($this->once())
        ->method('where')
        ->with('u.ufcode = :ufCode')
        ->willReturnSelf();
        
    // Expect andWhere calls
    $queryBuilder->method('andWhere')
        ->willReturnSelf();
    
    // Expect parameter settings
    $queryBuilder->method('setParameter')
        ->willReturnSelf();
    
    $queryBuilder->expects($this->once())
        ->method('orderBy')
        ->with('u.datfin', 'DESC')
        ->willReturnSelf();
        
    $queryBuilder->expects($this->once())
        ->method('getQuery')
        ->willReturn($query);
    
    $query->expects($this->once())
        ->method('getOneOrNullResult')
        ->willReturn($ufs);
    
    // Create service and call method
    $service = new UfsFinderService($entityManager);
    $result = $service->findUfByCodeAndEj('UF001', $entiteJuridique, $dateReference);
    
    // Assertions
    expect($result)->toBe($ufs);
});

it('finds UF by code and entity juridique with date range', function () {
    // Create mocks
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $queryBuilder = $this->createMock(QueryBuilder::class);
    $query = $this->createMock(Query::class);
    $ufs = $this->createMock(Ufs::class);
    $entiteJuridique = $this->createMock(EntiteJuridique::class);
    $dateDebut = new \DateTime('2025-01-01');
    $dateFin = new \DateTime('2025-12-31');
    
    // Configure mocks with specific expectations
    $entityManager->expects($this->once())
        ->method('createQueryBuilder')
        ->willReturn($queryBuilder);
    
    $queryBuilder->expects($this->once())
        ->method('select')
        ->with('u')
        ->willReturnSelf();
        
    $queryBuilder->expects($this->once())
        ->method('from')
        ->with(Ufs::class, 'u')
        ->willReturnSelf();
    
    // Expect join calls
    $queryBuilder->method('join')
        ->willReturnSelf();
    
    $queryBuilder->expects($this->once())
        ->method('where')
        ->with('u.ufcode = :ufCode')
        ->willReturnSelf();
        
    // Expect andWhere calls
    $queryBuilder->method('andWhere')
        ->willReturnSelf();
    
    // Expect parameter settings
    $queryBuilder->method('setParameter')
        ->willReturnSelf();
    
    $queryBuilder->expects($this->once())
        ->method('orderBy')
        ->with('u.datfin', 'DESC')
        ->willReturnSelf();
        
    $queryBuilder->expects($this->once())
        ->method('getQuery')
        ->willReturn($query);
    
    $query->expects($this->once())
        ->method('getOneOrNullResult')
        ->willReturn($ufs);
    
    // Create service and call method
    $service = new UfsFinderService($entityManager);
    $result = $service->findUfByCodeAndEj('UF001', $entiteJuridique, null, $dateDebut, $dateFin);
    
    // Assertions
    expect($result)->toBe($ufs);
});

it('returns null when UF not found', function () {
    // Create mocks
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $queryBuilder = $this->createMock(QueryBuilder::class);
    $query = $this->createMock(Query::class);
    $entiteJuridique = $this->createMock(EntiteJuridique::class);
    
    // Configure mocks with specific expectations
    $entityManager->expects($this->once())
        ->method('createQueryBuilder')
        ->willReturn($queryBuilder);
    
    $queryBuilder->expects($this->once())
        ->method('select')
        ->with('u')
        ->willReturnSelf();
        
    $queryBuilder->expects($this->once())
        ->method('from')
        ->with(Ufs::class, 'u')
        ->willReturnSelf();
    
    // Expect join calls
    $queryBuilder->method('join')
        ->willReturnSelf();
    
    $queryBuilder->expects($this->once())
        ->method('where')
        ->with('u.ufcode = :ufCode')
        ->willReturnSelf();
        
    $queryBuilder->method('andWhere')
        ->willReturnSelf();
    
    // Expect parameter settings
    $queryBuilder->method('setParameter')
        ->willReturnSelf();
    
    $queryBuilder->expects($this->once())
        ->method('orderBy')
        ->with('u.datfin', 'DESC')
        ->willReturnSelf();
        
    $queryBuilder->expects($this->once())
        ->method('getQuery')
        ->willReturn($query);
    
    $query->expects($this->once())
        ->method('getOneOrNullResult')
        ->willReturn(null);
    
    // Create service and call method
    $service = new UfsFinderService($entityManager);
    $result = $service->findUfByCodeAndEj('UF001', $entiteJuridique);
    
    // Assertions
    expect($result)->toBeNull();
});

it('validates if UF is valid for period', function () {
    // Create mocks
    $ufs = $this->createMock(Ufs::class);
    $dateDebut = new \DateTime('2025-01-01');
    $dateFin = new \DateTime('2025-12-31');
    
    // Configure mocks
    $ufs->method('getDatdeb')
        ->willReturn(new \DateTime('2024-01-01')); // UF starts before period
    
    $ufs->method('getDatfin')
        ->willReturn(new \DateTime('2026-12-31')); // UF ends after period
    
    // Create service and call method
    $service = new UfsFinderService($this->createMock(EntityManagerInterface::class));
    $result = $service->isUfValidForPeriod($ufs, $dateDebut, $dateFin);
    
    // Assertions
    expect($result)->toBeTrue();
});

it('validates if UF is invalid for period due to start date', function () {
    // Create mocks
    $ufs = $this->createMock(Ufs::class);
    $dateDebut = new \DateTime('2025-01-01');
    $dateFin = new \DateTime('2025-12-31');
    
    // Configure mocks
    $ufs->method('getDatdeb')
        ->willReturn(new \DateTime('2025-06-01')); // UF starts after period start
    
    $ufs->method('getDatfin')
        ->willReturn(new \DateTime('2026-12-31')); // UF ends after period
    
    // Create service and call method
    $service = new UfsFinderService($this->createMock(EntityManagerInterface::class));
    $result = $service->isUfValidForPeriod($ufs, $dateDebut, $dateFin);
    
    // Assertions
    expect($result)->toBeFalse();
});

it('validates if UF is invalid for period due to end date', function () {
    // Create mocks
    $ufs = $this->createMock(Ufs::class);
    $dateDebut = new \DateTime('2025-01-01');
    $dateFin = new \DateTime('2025-12-31');
    
    // Configure mocks
    $ufs->method('getDatdeb')
        ->willReturn(new \DateTime('2024-01-01')); // UF starts before period
    
    $ufs->method('getDatfin')
        ->willReturn(new \DateTime('2025-06-30')); // UF ends before period end
    
    // Create service and call method
    $service = new UfsFinderService($this->createMock(EntityManagerInterface::class));
    $result = $service->isUfValidForPeriod($ufs, $dateDebut, $dateFin);
    
    // Assertions
    expect($result)->toBeFalse();
});

it('validates if UF with null end date is valid for period', function () {
    // Create mocks
    $ufs = $this->createMock(Ufs::class);
    $dateDebut = new \DateTime('2025-01-01');
    $dateFin = new \DateTime('2025-12-31');
    
    // Configure mocks
    $ufs->method('getDatdeb')
        ->willReturn(new \DateTime('2024-01-01')); // UF starts before period
    
    $ufs->method('getDatfin')
        ->willReturn(null); // UF has no end date
    
    // Create service and call method
    $service = new UfsFinderService($this->createMock(EntityManagerInterface::class));
    $result = $service->isUfValidForPeriod($ufs, $dateDebut, $dateFin);
    
    // Assertions
    expect($result)->toBeTrue();
});