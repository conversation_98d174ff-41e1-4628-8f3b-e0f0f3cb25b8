<?php

namespace App\Domain\Extension\CoreSupraFilter\Agent;

use ApiPlatform\Doctrine\Orm\Extension\QueryCollectionExtensionInterface;
use ApiPlatform\Doctrine\Orm\Util\QueryNameGeneratorInterface;
use ApiPlatform\Metadata\Operation;
use App\Domain\Extension\CoreSupraFilter\BaseCustomFilterExtension;
use App\Entity\Praticien\Agent;
use Doctrine\ORM\QueryBuilder;

class AgentCategoryFilterExtension extends BaseCustomFilterExtension implements QueryCollectionExtensionInterface
{
    public function applyToCollection(
        QueryBuilder $queryBuilder,
        QueryNameGeneratorInterface $queryNameGenerator,
        string $resourceClass,
        ?Operation $operation = null,
        array $context = []
    ): void {
        if ($resourceClass !== Agent::class) {
            return;
        }

        // Utiliser la méthode héritée
        if (!$this->isApiPlatformContext($context)) {
            return;
        }

        $this->addCategoryFilter($queryBuilder, $queryNameGenerator);
    }

    private function addCategoryFilter(QueryBuilder $queryBuilder, QueryNameGeneratorInterface $queryNameGenerator): void
    {
        $rootAlias = $queryBuilder->getRootAliases()[0];
        $excludedCategories = AgentCategoryExcludeFilter::getExcludedCategories();

        if (!empty($excludedCategories)) {
            $parameterName = $queryNameGenerator->generateParameterName('excludedCategories');

            $queryBuilder
                ->andWhere(sprintf(
                    '(UPPER(%s.categorie) NOT IN (:%s) OR %s.categorie IS NULL)',
                    $rootAlias,
                    $parameterName,
                    $rootAlias
                ))
                ->setParameter($parameterName, array_map('strtoupper', $excludedCategories));
        }
    }
    /**
     * Fonction alternative si vous voulez aussi IGNORER les agents avec catégorie NULL
     *
     * Pour utiliser cette fonction :
     * 1. Remplacez l'appel à addCategoryFilter() par addCategoryFilter_SI_TU_VEUX_IGNORER_LES_AGENT_AVEC_CATEGORIE_NULL()
     * 2. Cette version exclura AUSSI les agents avec categorie = NULL
     *
     * Comportement :
     * -  Agents avec catégorie dans EXCLUDED_CATEGORIES
     * -  Agents avec catégorie = NULL
     * -  Uniquement les agents du domaine médical seront retournés ( SSi le champs categorie du collecteur permet de decider cela )
     */

    private function addCategoryFilter_SI_TU_VEUX_IGNORER_LES_AGENT_AVEC_CATEGORIE_NULL(
        QueryBuilder $queryBuilder,
        QueryNameGeneratorInterface $queryNameGenerator
    ): void {
        $rootAlias = $queryBuilder->getRootAliases()[0];
        $excludedCategories = AgentCategoryExcludeFilter::getExcludedCategories();

        if (!empty($excludedCategories)) {
            $parameterName = $queryNameGenerator->generateParameterName('excludedCategories');

            $queryBuilder
                ->andWhere(sprintf(
                    '%s.categorie IS NOT NULL AND UPPER(%s.categorie) NOT IN (:%s)',
                    $rootAlias,
                    $rootAlias,
                    $parameterName
                ))
                ->setParameter($parameterName, array_map('strtoupper', $excludedCategories));
        } else {
            // Si pas de catégories à exclure, on ignore quand même les NULL
            $queryBuilder->andWhere(sprintf('%s.categorie IS NOT NULL', $rootAlias));
        }
    }

}