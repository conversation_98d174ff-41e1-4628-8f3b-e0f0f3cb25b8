# 🏗️ Structure des Entités

Ce document décrit l'organisation des entités dans le projet afin d'assurer une structure claire et maintenable.

## 📂 Organisation des Dossiers

Les entités sont organisées en sous-dossiers selon leur rôle dans l'application :

### **1️⃣ Activité (`Entity/Activite/`)
Contient les entités liées aux enregistrements d'activité médicale.

- `Actes.php` : Gestion des actes médicaux.
- `Enseignements.php` : Suivi des heures d'enseignement.
- `GardesAstreintes.php` : Gestion des gardes et astreintes.
- `Liberal.php` : Gestion des praticiens en mode libéral.
- `Sigaps.php` : Suivi des publications scientifiques.

### **2️⃣ Base (`Entity/Base/`)
Stocke les entités de base utilisées par toutes les entités du projet.

- `BaseEntity.php` : Contient l'ID unique et les champs communs (`id`, `isActif`).

### **3️⃣ Praticien (`Entity/Praticien/`)
Regroupe les entités liées aux praticiens et leur affectation.

- `Agent.php` : Modélise un agent hospitalier.
- `Praticien.php` : Modélise un praticien hospitalier.
- `PraticiensUfs.php` : Relie les praticiens aux unités fonctionnelles (UF).

### **4️⃣ Structure (`Entity/Structure/`)
Gère la hiérarchie hospitalière.

- `Hopital.php` : Modélise un hôpital.
- `Pole.php` : Modélise un pôle hospitalier.
- `Service.php` : Modélise un service médical.
- `Tenant.php` : Gestion des tenants multi-hôpitaux.
- `Ufs.php` : Modélise une unité fonctionnelle (UF).

### **5️⃣ Trait (`Entity/Trait/`)
Regroupe les traits réutilisables dans plusieurs entités.

- `HorodatageTrait.php` : Ajoute les champs `validFrom` et `validTo` pour la gestion de l'historisation.

## ✅ Bonnes Pratiques
- **Chaque entité est placée dans le bon dossier** pour garder une architecture propre.
- **Les entités réutilisables héritent de `BaseEntity`**.
- **Les champs communs comme `validFrom` et `validTo` sont extraits dans `HorodatageTrait`**.

📌 Cette structure assure une **organisation claire, modulaire et maintenable** pour le projet !

