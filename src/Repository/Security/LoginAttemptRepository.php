<?php

namespace App\Repository\Security;

use App\Entity\Security\LoginAttempt;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * Repository for LoginAttempt entity
 * 
 * Provides methods to query login attempts for rate limiting and security monitoring
 */
class LoginAttemptRepository extends ServiceEntityRepository
{
    /**
     * Maximum number of failed attempts allowed within the time window
     */
    private const MAX_ATTEMPTS = 5;
    
    /**
     * Time window for rate limiting in seconds (15 minutes)
     */
    private const TIME_WINDOW = 900;
    
    /**
     * Retention period for login attempts in days (30 days)
     */
    private const RETENTION_PERIOD = 30;

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, LoginAttempt::class);
    }

    /**
     * Count recent failed login attempts by IP address
     */
    public function countRecentFailedAttemptsByIp(string $ipAddress): int
    {
        $timeWindow = new \DateTimeImmutable(sprintf('-%d seconds', self::TIME_WINDOW));
        
        return $this->createQueryBuilder('l')
            ->select('COUNT(l.id)')
            ->where('l.ipAddress = :ip')
            ->andWhere('l.timestamp >= :time')
            ->andWhere('l.success = :success')
            ->setParameter('ip', $ipAddress)
            ->setParameter('time', $timeWindow)
            ->setParameter('success', false)
            ->getQuery()
            ->getSingleScalarResult();
    }

    /**
     * Count recent failed login attempts by email
     */
    public function countRecentFailedAttemptsByEmail(string $email): int
    {
        $timeWindow = new \DateTimeImmutable(sprintf('-%d seconds', self::TIME_WINDOW));
        
        return $this->createQueryBuilder('l')
            ->select('COUNT(l.id)')
            ->where('l.email = :email')
            ->andWhere('l.timestamp >= :time')
            ->andWhere('l.success = :success')
            ->setParameter('email', $email)
            ->setParameter('time', $timeWindow)
            ->setParameter('success', false)
            ->getQuery()
            ->getSingleScalarResult();
    }

    /**
     * Check if login is allowed based on IP address and email
     * 
     * @return bool True if login is allowed, false if rate limited
     */
    public function isLoginAllowed(string $ipAddress, string $email): bool
    {
        $ipAttempts = $this->countRecentFailedAttemptsByIp($ipAddress);
        $emailAttempts = $this->countRecentFailedAttemptsByEmail($email);
        
        return $ipAttempts < self::MAX_ATTEMPTS && $emailAttempts < self::MAX_ATTEMPTS;
    }

    /**
     * Get the remaining time in seconds until login is allowed again
     * 
     * @return int Seconds until login is allowed again, or 0 if login is allowed
     */
    public function getTimeUntilLoginAllowed(string $ipAddress, string $email): int
    {
        if ($this->isLoginAllowed($ipAddress, $email)) {
            return 0;
        }
        
        // Find the most recent failed attempt
        $timeWindow = new \DateTimeImmutable(sprintf('-%d seconds', self::TIME_WINDOW));
        
        $mostRecentAttempt = $this->createQueryBuilder('l')
            ->where('(l.ipAddress = :ip OR l.email = :email)')
            ->andWhere('l.timestamp >= :time')
            ->andWhere('l.success = :success')
            ->setParameter('ip', $ipAddress)
            ->setParameter('email', $email)
            ->setParameter('time', $timeWindow)
            ->setParameter('success', false)
            ->orderBy('l.timestamp', 'DESC')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
        
        if (!$mostRecentAttempt) {
            return 0;
        }
        
        $unlockTime = $mostRecentAttempt->getTimestamp()->modify(sprintf('+%d seconds', self::TIME_WINDOW));
        $now = new \DateTimeImmutable();
        
        if ($unlockTime <= $now) {
            return 0;
        }
        
        return $unlockTime->getTimestamp() - $now->getTimestamp();
    }

    /**
     * OPTIMISATION: Récupère les infos de rate limiting en une seule requête
     *
     * @return array{allowed: bool, timeUntilAllowed: int}
     */
    public function getRateLimitInfo(string $ipAddress, string $email): array
    {
        $timeWindow = new \DateTimeImmutable(sprintf('-%d seconds', self::TIME_WINDOW));

        // Une seule requête pour récupérer toutes les infos nécessaires
        $result = $this->createQueryBuilder('l')
            ->select('COUNT(l.id) as failedCount, MAX(l.timestamp) as lastAttempt')
            ->where('(l.ipAddress = :ip OR l.email = :email)')
            ->andWhere('l.timestamp >= :time')
            ->andWhere('l.success = :success')
            ->setParameter('ip', $ipAddress)
            ->setParameter('email', $email)
            ->setParameter('time', $timeWindow)
            ->setParameter('success', false)
            ->getQuery()
            ->getSingleResult();

        $failedCount = (int) $result['failedCount'];
        $allowed = $failedCount < self::MAX_ATTEMPTS;

        if ($allowed) {
            return ['allowed' => true, 'timeUntilAllowed' => 0];
        }

        // Calculer le temps restant
        $lastAttempt = $result['lastAttempt'];
        if ($lastAttempt) {
            $unlockTime = $lastAttempt->modify(sprintf('+%d seconds', self::TIME_WINDOW));
            $now = new \DateTimeImmutable();
            $timeUntilAllowed = max(0, $unlockTime->getTimestamp() - $now->getTimestamp());
        } else {
            $timeUntilAllowed = 0;
        }

        return ['allowed' => false, 'timeUntilAllowed' => $timeUntilAllowed];
    }

    /**
     * Clean up old login attempts
     * 
     * @return int Number of deleted records
     */
    public function cleanupOldAttempts(): int
    {
        $cutoffDate = new \DateTimeImmutable(sprintf('-%d days', self::RETENTION_PERIOD));
        
        return $this->createQueryBuilder('l')
            ->delete()
            ->where('l.timestamp < :cutoff')
            ->setParameter('cutoff', $cutoffDate)
            ->getQuery()
            ->execute();
    }
}