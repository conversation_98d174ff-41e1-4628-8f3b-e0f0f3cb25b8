{# 
   Template pour afficher les informations de détection de problème pour les actes
   Ce template utilise le service ActesDetectionProblemService pour récupérer et afficher les informations
#}

{% set service = field.customOptions.get('detectionProblemService') %}
{% set acte = entity.instance %}

<div class="detection-probleme">
    {% if acte.internum %}
        {% set isNonEnrichie = service.isInterventionNonEnrichie(acte.internum) %}
        {% if isNonEnrichie %}
            <div class="alert alert-warning">
                <i class="fa fa-exclamation-triangle"></i>
                Cet acte fait partie des interventions non enrichies.
            </div>
        {% endif %}
    {% endif %}
    
    <div class="card">
        <div class="card-header">
            <h5>Informations globales de détection de problème</h5>
        </div>
        <div class="card-body">
            {{ service.getDetectProblemeDisplay|raw }}
        </div>
    </div>
</div>