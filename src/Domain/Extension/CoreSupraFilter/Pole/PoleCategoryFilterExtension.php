<?php

namespace App\Domain\Extension\CoreSupraFilter\Pole;

use ApiPlatform\Doctrine\Orm\Extension\QueryCollectionExtensionInterface;
use ApiPlatform\Doctrine\Orm\Util\QueryNameGeneratorInterface;
use ApiPlatform\Metadata\Operation;
use App\Domain\Extension\CoreSupraFilter\BaseCustomFilterExtension;
use App\Entity\Structure\Pole;
use Doctrine\ORM\QueryBuilder;

class PoleCategoryFilterExtension extends BaseCustomFilterExtension implements QueryCollectionExtensionInterface
{
    public function applyToCollection(
        QueryBuilder $queryBuilder,
        QueryNameGeneratorInterface $queryNameGenerator,
        string $resourceClass,
        ?Operation $operation = null,
        array $context = []
    ): void {
        if ($resourceClass !== Pole::class) {
            return;
        }

        if (!$this->isApiPlatformContext($context)) {
            return;
        }

        $this->addPolecodeFilter($queryBuilder, $queryNameGenerator);
    }

    private function addPolecodeFilter(QueryBuilder $queryBuilder, QueryNameGeneratorInterface $queryNameGenerator): void
    {
        $rootAlias = $queryBuilder->getRootAliases()[0];
        $excludedPolecodes = PoleCategoryExcludeFilter::getExcludedPolecodes();

        if (!empty($excludedPolecodes)) {
            $parameterName = $queryNameGenerator->generateParameterName('excludedPolecodes');

            $queryBuilder
                ->andWhere(sprintf(
                    '(UPPER(%s.polecode) NOT IN (:%s) OR %s.polecode IS NULL)',
                    $rootAlias,
                    $parameterName,
                    $rootAlias
                ))
                ->setParameter($parameterName, array_map('strtoupper', $excludedPolecodes));
        }
    }

    /**
     * Fonction alternative si vous voulez IGNORER les pôles avec polecode NULL
     *
     * Pour utiliser cette fonction :
     * 1. Remplacez l'appel à addPolecodeFilter() par addPolecodeFilter_SI_TU_VEUX_IGNORER_LES_POLES_AVEC_POLECODE_NULL()
     * 2. Cette version exclura AUSSI les pôles avec polecode = NULL
     *
     * Comportement :
     * -  Pôles avec polecode dans EXCLUDED_POLECODES
     * -  Pôles avec polecode = NULL
     * -  Uniquement pôles avec polecode valide et non exclu
     */
    /*
    private function addPolecodeFilter_SI_TU_VEUX_IGNORER_LES_POLES_AVEC_POLECODE_NULL(
        QueryBuilder $queryBuilder,
        QueryNameGeneratorInterface $queryNameGenerator
    ): void {
        $rootAlias = $queryBuilder->getRootAliases()[0];
        $excludedPolecodes = PoleCategoryExcludeFilter::getExcludedPolecodes();

        if (!empty($excludedPolecodes)) {
            $parameterName = $queryNameGenerator->generateParameterName('excludedPolecodes');

            $queryBuilder
                ->andWhere(sprintf(
                    '%s.polecode IS NOT NULL AND UPPER(%s.polecode) NOT IN (:%s)',
                    $rootAlias,
                    $rootAlias,
                    $parameterName
                ))
                ->setParameter($parameterName, array_map('strtoupper', $excludedPolecodes));
        } else {
            // Si pas de codes à exclure, on ignore quand même les NULL
            $queryBuilder->andWhere(sprintf('%s.polecode IS NOT NULL', $rootAlias));
        }
    }
    */
}