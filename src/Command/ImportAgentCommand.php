<?php

namespace App\Command;

use App\Command\DataIntegratorGlobalCacheClearCommand;
use App\Domain\Service\Importation\AgentImportService;
use App\Domain\Service\Notification\ErrorNotificationEmailService;
use App\Entity\Importation\Importation;
use App\Entity\Structure\EntiteJuridique;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\ArrayInput;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\Console\Application;

/**
 * Commande pour l'importation complète des agents depuis le data_integrator.
 * 
 * Cette commande synchronise tous les agents (praticiens, personnel médical et non-médical)
 * depuis le service data_integrator avec leurs informations personnelles et professionnelles :
 * - Données d'identité (nom, prénom, matricule)
 * - Informations de contact (email, téléphone)
 * - Données professionnelles (grade, statut, spécialité)
 * - Informations d'authentification (identifiants LDAP)
 * 
 * Cette importation est généralement effectuée périodiquement pour maintenir
 * la cohérence des données d'agents dans le système.
 * 
 * En cas d'erreur critique, notifie automatiquement tous les admins des entités
 * juridiques ayant activé les notifications d'erreur (NotifierAdminDesErreurProduite = true).
 * 
 * Usage :
 * php bin/console app:import:agent
 *
 * Ou en version optimisée pour les performances :
 *
 * php bin/console app:import:agent -p 1000 -b 200 --memory-warning=1024 --memory-critical=1536
 *
 * Exemple de cron Linux (tous les 5 du mois à 4h00) :
 * 0 4 5 * * /usr/bin/php /path/to/supra/bin/console app:import:agent >> /var/log/supra-agent-import.log 2>&1
 */
#[AsCommand(
    name: 'app:import:agent',
    description: 'Importe tous les agents depuis le data_integrator',
)]
class ImportAgentCommand extends Command
{
    /**
     * Limite de mémoire par défaut en MB avant d'émettre un avertissement
     */
    private const MEMORY_WARNING_LIMIT = 1024; // 1 GB

    /**
     * Limite de mémoire par défaut en MB avant d'arrêter l'importation
     */
    private const MEMORY_CRITICAL_LIMIT = 1536; // 1.5 GB
    
    /**
     * Instance de l'application Symfony
     */
    private ?Application $application = null;

    public function __construct(
        private readonly AgentImportService $importService,
        private readonly ErrorNotificationEmailService $errorNotificationService,
        private readonly EntityManagerInterface $em
    ) {
        parent::__construct();
    }
    
    protected function initialize(InputInterface $input, OutputInterface $output): void
    {
        $this->application = $this->getApplication();
    }
    
    /**
     * Execute la commande de nettoyage du cache global du data integrator
     * 
     * @return array|null La réponse de l'API contenant success et ejcode, ou null en cas d'erreur
     */
    private function executeDataIntegratorCacheClearCommand(OutputInterface $output): ?array
    {
        if (!$this->application) {
            return null;
        }
        
        try {
            $command = $this->application->find('app:data-integrator:global-cache-clear');
            
            // Exécuter la commande pour l'affichage dans la console
            $arguments = new ArrayInput([]);
            $command->run($arguments, $output);
            
            // Appeler directement la méthode pour récupérer la réponse de l'API
            if ($command instanceof DataIntegratorGlobalCacheClearCommand) {
                return $command->clearGlobalCacheAndGetResponse();
            }
        } catch (\Exception $e) {
            // Log l'erreur mais ne pas interrompre le processus
            $output->writeln('<error>Erreur lors de la récupération de la réponse API: ' . $e->getMessage() . '</error>');
        }
        
        return null;
    }
    
    protected function configure(): void
    {
        $this
            ->addOption(
                'page-size',
                'p',
                InputOption::VALUE_OPTIONAL,
                'Nombre d\'agents à récupérer par page API',
                500
            )
            ->addOption(
                'batch-size',
                'b',
                InputOption::VALUE_OPTIONAL,
                'Nombre d\'agents à traiter avant de flush/clear',
                50
            )
            ->addOption(
                'memory-warning',
                null,
                InputOption::VALUE_OPTIONAL,
                'Limite de mémoire en MB avant d\'émettre un avertissement',
                self::MEMORY_WARNING_LIMIT
            )
            ->addOption(
                'memory-critical',
                null,
                InputOption::VALUE_OPTIONAL,
                'Limite de mémoire en MB avant d\'arrêter l\'importation',
                self::MEMORY_CRITICAL_LIMIT
            )
            ->addOption(
                'skip-cache-clear',
                null,
                InputOption::VALUE_NONE,
                'Ne pas vider le cache avant l\'importation'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $result = Command::FAILURE;
        
        try {
            // Récupérer les options de la ligne de commande
            $pageSize = (int) $input->getOption('page-size');
            $batchSize = (int) $input->getOption('batch-size');
            $memoryWarningLimit = (int) $input->getOption('memory-warning');
            $memoryCriticalLimit = (int) $input->getOption('memory-critical');
            $skipCacheClear = (bool) $input->getOption('skip-cache-clear');
            
            // Afficher les paramètres d'importation
            $io->section('Début de l\'importation des agents');
            $io->table(
                ['Paramètre', 'Valeur'],
                [
                    ['Taille de page API', $pageSize],
                    ['Taille de batch', $batchSize],
                    ['Limite mémoire avertissement', $memoryWarningLimit . ' MB'],
                    ['Limite mémoire critique', $memoryCriticalLimit . ' MB'],
                    ['Skip cache clear', $skipCacheClear ? 'Oui' : 'Non'],
                ]
            );
            
            // Nettoyer le cache du data integrator avant l'importation
            if (!$skipCacheClear) {
                $io->section('Nettoyage du cache du data integrator avant importation');
                $this->executeDataIntegratorCacheClearCommand($output);
                
                $io->text('Nettoyage du cache Doctrine avant importation...');
                $this->clearCache($io);
            }
            
            // Vérifier la mémoire disponible avant de commencer
            $this->checkMemoryLimits($io, $memoryWarningLimit, $memoryCriticalLimit);
            
            // Start transaction
            $this->em->beginTransaction();
            
            try {
                $progressBar = null;
                $startTime = microtime(true);
                $memoryWarningIssued = false;
                $memoryReadings = [];
                
                // Fonction de callback pour mettre à jour la barre de progression
                $progressCallback = function (
                    int $processedAgents, 
                    int $totalAgents, 
                    float $eta, 
                    float $currentMemory = 0, 
                    float $peakMemory = 0
                ) use (
                    $io, 
                    &$progressBar, 
                    $startTime, 
                    $memoryWarningLimit, 
                    $memoryCriticalLimit, 
                    &$memoryWarningIssued,
                    &$memoryReadings
                ) {
                    // Initialiser la barre de progression si nécessaire
                    if ($progressBar === null) {
                        $io->text(sprintf('Importation de %d agents...', $totalAgents));
                        $progressBar = $io->createProgressBar($totalAgents);
                        $progressBar->setFormat(
                            ' %current%/%max% [%bar%] %percent:3s%% | %elapsed:6s%/%estimated:-6s% | %memory:6s% | Peak: %peak:6s% | ETA: %remaining:-6s%'
                        );
                        $progressBar->start();
                    } else {
                        $progressBar->setProgress($processedAgents);
                    }
                    
                    // Afficher l'ETA (temps restant estimé)
                    $etaFormatted = $this->formatTime($eta);
                    $progressBar->setMessage($etaFormatted, 'remaining');
                    
                    // Afficher la mémoire utilisée
                    $memoryFormatted = number_format($currentMemory, 1) . ' MB';
                    $progressBar->setMessage($memoryFormatted, 'memory');
                    
                    // Afficher la mémoire maximale
                    $peakFormatted = number_format($peakMemory, 1) . ' MB';
                    $progressBar->setMessage($peakFormatted, 'peak');
                    
                    // Enregistrer la mesure de mémoire pour l'analyse de tendance
                    $memoryReadings[] = $currentMemory;
                    
                    // Vérifier les limites de mémoire
                    if ($currentMemory >= $memoryCriticalLimit) {
                        throw new \RuntimeException(sprintf(
                            'Limite de mémoire critique atteinte: %.1f MB (limite: %d MB). Arrêt de l\'importation pour éviter un crash.',
                            $currentMemory,
                            $memoryCriticalLimit
                        ));
                    } elseif ($currentMemory >= $memoryWarningLimit && !$memoryWarningIssued) {
                        $memoryWarningIssued = true;
                        $progressBar->clear();
                        $io->warning(sprintf(
                            'Utilisation élevée de la mémoire: %.1f MB (limite d\'avertissement: %d MB)',
                            $currentMemory,
                            $memoryWarningLimit
                        ));
                        $progressBar->display();
                    }
                };
                
                // Lancer l'importation avec les paramètres configurés
                $this->importService->importAgents($pageSize, $batchSize, $progressCallback);
                
                // Finaliser la barre de progression
                if ($progressBar !== null) {
                    $progressBar->finish();
                    $io->newLine(2);
                }
                
                // Calculer et afficher le temps total d'exécution
                $totalTime = microtime(true) - $startTime;
                $io->text(sprintf('Temps total d\'exécution: %s', $this->formatTime($totalTime)));
                
                // Afficher les statistiques de mémoire
                if (!empty($memoryReadings)) {
                    $avgMemory = array_sum($memoryReadings) / count($memoryReadings);
                    $maxMemory = max($memoryReadings);
                    $io->text([
                        sprintf('Mémoire moyenne utilisée: %.1f MB', $avgMemory),
                        sprintf('Mémoire maximale utilisée: %.1f MB', $maxMemory),
                        sprintf('Tendance mémoire: %s', $this->analyzeMemoryTrend($memoryReadings))
                    ]);
                }
                
                // Commit transaction if everything is successful
                $this->em->commit();
                
                // Recommandations pour optimiser les performances
                $this->showPerformanceRecommendations($io);
    
                $io->success('✅ Importation des agents terminée avec succès');
                $result = Command::SUCCESS;
    
            } catch (\Throwable $e) {
                // Rollback transaction in case of error
                if ($this->em->getConnection()->isTransactionActive()) {
                    $this->em->rollback();
                }
                
                $io->error('❌ Erreur lors de l\'importation : ' . $e->getMessage());
    
                // Notifier toutes les entités juridiques qui ont activé les notifications d'erreur
                $this->notifyAdminsOfError($e);
                
                $result = Command::FAILURE;
            }
            
            // Nettoyer le cache du data integrator après l'importation (même en cas d'erreur)
            if (!$skipCacheClear) {
                $io->section('Nettoyage du cache du data integrator après importation');
                $this->executeDataIntegratorCacheClearCommand($output);
            }
            
            // Enregistrer le résultat de l'importation
            $io->section('Enregistrement du résultat de l\'importation');
            try {
                // Récupérer la réponse de l'API pour obtenir l'ejcode
                $apiResponse = $this->executeDataIntegratorCacheClearCommand($output);
                $ejcode = $apiResponse['ejcode'] ?? null;
                
                $importation = new Importation();
                $importation->setNomRessource('Agent');
                $importation->setEstReussie($result === Command::SUCCESS);
                
                // Associer l'EntiteJuridique si l'ejcode est disponible
                if ($ejcode) {
                    $entiteJuridique = $this->em->getRepository(EntiteJuridique::class)->findOneBy(['code' => $ejcode]);
                    if ($entiteJuridique) {
                        $importation->setEntiteJuridique($entiteJuridique);
                        $io->info(sprintf('EntiteJuridique associée: %s (code: %s)', $entiteJuridique->getNom(), $ejcode));
                    } else {
                        $io->warning(sprintf('Aucune EntiteJuridique trouvée avec le code: %s', $ejcode));
                    }
                } else {
                    $io->warning('Aucun code d\'EntiteJuridique trouvé dans la réponse API');
                }
                
                // Persister l'entité
                $this->em->persist($importation);
                $this->em->flush();
                
                $io->success('Résultat de l\'importation enregistré avec succès');
            } catch (\Throwable $e) {
                $io->warning('Impossible d\'enregistrer le résultat de l\'importation : ' . $e->getMessage());
                // Ne pas modifier le résultat de la commande si l'enregistrement échoue
            }
            
            return $result;
            
        } catch (\Throwable $e) {
            $io->error('❌ Erreur lors du nettoyage du cache : ' . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * Notifie tous les admins des entités juridiques ayant activé les notifications d'erreur
     */
    private function notifyAdminsOfError(\Throwable $exception): void
    {
        try {
            // Récupérer toutes les entités juridiques avec notifications d'erreur activées
            $entitesJuridiques = $this->em->getRepository(EntiteJuridique::class)
                ->findAll();
                
            // Filtrer les entités qui ont les notifications d'erreur activées
            $entitesJuridiques = array_filter($entitesJuridiques, function($ej) {
                $parametre = $ej->getParametre();
                return isset($parametre['notifications']['NotifierAdminDesErreurProduite']) && 
                       $parametre['notifications']['NotifierAdminDesErreurProduite'] === true;
            });

            foreach ($entitesJuridiques as $entiteJuridique) {
                $this->errorNotificationService->sendErrorNotificationToAdmin(
                    $entiteJuridique,
                    'Import des agents',
                    $exception->getMessage(),
                    [
                        'type' => 'Commande console',
                        'command' => 'app:import:agent',
                        'frequence' => 'Tous les 5 du mois',
                        'donnees' => ['Identité', 'Contact', 'Periodicité', 'CA'],
                        'stackTrace' => $exception->getTraceAsString(),
                        'file' => $exception->getFile(),
                        'line' => $exception->getLine()
                    ]
                );
            }

        } catch (\Exception $notificationError) {
            // Si l'envoi de notification échoue, on ne fait que logger pour ne pas masquer l'erreur principale
            error_log('Erreur lors de l\'envoi de notifications d\'erreur : ' . $notificationError->getMessage());
        }
    }
    
    /**
     * Nettoie le cache avant l'importation pour libérer de la mémoire
     * 
     * @param SymfonyStyle $io Interface de sortie
     * @return void
     */
    private function clearCache(SymfonyStyle $io): void
    {
        try {
            // Vider le cache Doctrine pour libérer de la mémoire
            $this->em->clear();
            
            // Forcer le garbage collector
            if (gc_enabled()) {
                $collectedCycles = gc_collect_cycles();
                $io->text(sprintf('Garbage collector: %d cycles collectés', $collectedCycles));
            }
            
            // Afficher la mémoire disponible après nettoyage
            $memoryUsage = round(memory_get_usage(true) / 1024 / 1024, 2);
            $io->text(sprintf('Mémoire utilisée après nettoyage: %.2f MB', $memoryUsage));
            
            $io->success('Cache nettoyé avec succès');
        } catch (\Exception $e) {
            $io->warning('Erreur lors du nettoyage du cache: ' . $e->getMessage());
        }
    }
    
    /**
     * Vérifie les limites de mémoire avant de commencer l'importation
     * 
     * @param SymfonyStyle $io Interface de sortie
     * @param int $warningLimit Limite d'avertissement en MB
     * @param int $criticalLimit Limite critique en MB
     * @return void
     * @throws \RuntimeException Si la mémoire disponible est insuffisante
     */
    private function checkMemoryLimits(SymfonyStyle $io, int $warningLimit, int $criticalLimit): void
    {
        // Récupérer la limite de mémoire PHP
        $memoryLimit = $this->getPhpMemoryLimitInMB();
        
        // Récupérer l'utilisation actuelle de la mémoire
        $currentMemory = round(memory_get_usage(true) / 1024 / 1024, 2);
        
        $io->text([
            sprintf('Limite de mémoire PHP: %s MB', $memoryLimit === -1 ? 'Illimitée' : $memoryLimit),
            sprintf('Utilisation actuelle: %.2f MB', $currentMemory),
            sprintf('Limite d\'avertissement: %d MB', $warningLimit),
            sprintf('Limite critique: %d MB', $criticalLimit),
        ]);
        
        // Vérifier si la limite critique est supérieure à la limite PHP
        if ($memoryLimit !== -1 && $criticalLimit >= $memoryLimit) {
            $io->warning(sprintf(
                'La limite critique (%d MB) est supérieure ou égale à la limite PHP (%d MB). ' .
                'Considérez augmenter la limite PHP ou réduire la limite critique.',
                $criticalLimit,
                $memoryLimit
            ));
        }
        
        // Vérifier si l'utilisation actuelle est déjà élevée
        if ($currentMemory >= $warningLimit) {
            $io->warning(sprintf(
                'L\'utilisation actuelle de la mémoire (%.2f MB) est déjà supérieure à la limite d\'avertissement (%d MB). ' .
                'Considérez redémarrer le serveur PHP avant l\'importation.',
                $currentMemory,
                $warningLimit
            ));
        }
        
        // Vérifier si l'utilisation actuelle est critique
        if ($currentMemory >= $criticalLimit) {
            throw new \RuntimeException(sprintf(
                'L\'utilisation actuelle de la mémoire (%.2f MB) est déjà supérieure à la limite critique (%d MB). ' .
                'Importation annulée pour éviter un crash. Redémarrez le serveur PHP avant de réessayer.',
                $currentMemory,
                $criticalLimit
            ));
        }
    }
    
    /**
     * Récupère la limite de mémoire PHP en MB
     * 
     * @return int Limite en MB, -1 si illimitée
     */
    private function getPhpMemoryLimitInMB(): int
    {
        $memoryLimit = ini_get('memory_limit');
        
        // Si la limite est désactivée
        if ($memoryLimit === '-1') {
            return -1;
        }
        
        // Convertir en MB
        if (preg_match('/^(\d+)(.)$/', $memoryLimit, $matches)) {
            $value = (int) $matches[1];
            $unit = strtolower($matches[2]);
            
            switch ($unit) {
                case 'g':
                    $value *= 1024;
                    break;
                case 'm':
                    // Déjà en MB
                    break;
                case 'k':
                    $value /= 1024;
                    break;
                default:
                    $value /= 1024 * 1024; // Convertir des octets en MB
            }
            
            return (int) $value;
        }
        
        // Si le format n'est pas reconnu, retourner la valeur en MB
        return (int) ($memoryLimit / 1024 / 1024);
    }
    
    /**
     * Analyse la tendance d'utilisation de la mémoire
     * 
     * @param array $memoryReadings Tableau des mesures de mémoire
     * @return string Description de la tendance
     */
    private function analyzeMemoryTrend(array $memoryReadings): string
    {
        if (count($memoryReadings) < 3) {
            return 'Pas assez de données pour analyser la tendance';
        }
        
        // Calculer la tendance en comparant le premier et le dernier tiers des mesures
        $count = count($memoryReadings);
        $firstThird = array_slice($memoryReadings, 0, (int) ($count / 3));
        $lastThird = array_slice($memoryReadings, (int) (2 * $count / 3));
        
        $firstAvg = array_sum($firstThird) / count($firstThird);
        $lastAvg = array_sum($lastThird) / count($lastThird);
        
        $difference = $lastAvg - $firstAvg;
        $percentChange = ($firstAvg > 0) ? ($difference / $firstAvg) * 100 : 0;
        
        // Analyser la tendance
        if (abs($percentChange) < 5) {
            return 'Stable (variation < 5%)';
        } elseif ($percentChange > 0) {
            if ($percentChange > 50) {
                return sprintf('Forte augmentation (+%.1f%%)', $percentChange);
            } else {
                return sprintf('Augmentation progressive (+%.1f%%)', $percentChange);
            }
        } else {
            if (abs($percentChange) > 50) {
                return sprintf('Forte diminution (%.1f%%)', $percentChange);
            } else {
                return sprintf('Diminution progressive (%.1f%%)', $percentChange);
            }
        }
    }
    
    /**
     * Affiche des recommandations pour optimiser les performances
     * 
     * @param SymfonyStyle $io Interface de sortie
     * @return void
     */
    private function showPerformanceRecommendations(SymfonyStyle $io): void
    {
        $io->section('Recommandations pour optimiser les performances');
        
        $recommendations = [
            'Configuration PHP' => [
                'Augmentez memory_limit dans php.ini (recommandé: 2G ou plus)',
                'Activez opcache pour améliorer les performances PHP',
                'Augmentez opcache.memory_consumption à 256M ou plus',
                'Définissez opcache.revalidate_freq=0 en production',
            ],
            'Configuration Symfony' => [
                'Utilisez APP_ENV=prod pour les imports en production',
                'Exécutez bin/console cache:clear --env=prod avant les imports importants',
                'Exécutez bin/console cache:warmup --env=prod après cache:clear',
                'Désactivez le profiler et le debug en production',
            ],
            'Configuration Doctrine' => [
                'Optimisez les requêtes avec des index appropriés',
                'Utilisez des requêtes DQL optimisées plutôt que des findBy pour les grandes quantités de données',
                'Considérez l\'utilisation de requêtes natives SQL pour les opérations critiques en performance',
            ],
            'Planification' => [
                'Exécutez les imports pendant les heures creuses',
                'Divisez les grands imports en plusieurs petits lots si possible',
                'Redémarrez le serveur PHP avant les imports importants pour libérer la mémoire',
            ],
        ];
        
        foreach ($recommendations as $category => $items) {
            $io->text('<info>' . $category . '</info>');
            foreach ($items as $item) {
                $io->text('  • ' . $item);
            }
            $io->newLine();
        }
    }
    
    /**
     * Formate un temps en secondes en une chaîne lisible (heures, minutes, secondes)
     * 
     * @param float $seconds Temps en secondes
     * @return string Temps formaté
     */
    private function formatTime(float $seconds): string
    {
        $seconds = max(0, round($seconds));
        
        if ($seconds < 60) {
            return $seconds . 's';
        }
        
        $minutes = floor($seconds / 60);
        $secondsRemainder = $seconds % 60;
        
        if ($minutes < 60) {
            return sprintf('%dm %02ds', $minutes, $secondsRemainder);
        }
        
        $hours = floor($minutes / 60);
        $minutesRemainder = $minutes % 60;
        
        return sprintf('%dh %02dm %02ds', $hours, $minutesRemainder, $secondsRemainder);
    }
}