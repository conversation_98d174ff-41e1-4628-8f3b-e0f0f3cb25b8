<?php

namespace App\Mapper\AgentUf;

use App\ApiResource\Praticien\AgentDto;
use App\ApiResource\Praticien\AgentUfsDto;
use App\ApiResource\Structure\UfsDto;
use App\Entity\Praticien\AgentUfs;
use App\Repository\AgentUfsRepository;
use Symfonycasts\MicroMapper\AsMapper;
use Symfonycasts\MicroMapper\MapperInterface;
use Symfonycasts\MicroMapper\MicroMapperInterface;

#[AsMapper(from: AgentUfs::class, to: AgentUfsDto::class)]
class AgentUfsEntityToDtoMapper implements MapperInterface
{
    public function __construct(
        private MicroMapperInterface $microMapper,
        private AgentUfsRepository $agentUfsRepository
    ) {}

    public function load(object $from, string $toClass, array $context): object
    {
        $praticiensUfs = $from;
        assert($praticiensUfs instanceof AgentUfs);

        $dto = new AgentUfsDto();
        $dto->id = $praticiensUfs->getId();

        return $dto;
    }

    public function populate(object $from, object $to, array $context): object
    {
        $agentsUfs = $from;
        $dto = $to;
        assert($agentsUfs instanceof AgentUfs);
        assert($dto instanceof AgentUfsDto);

        // Champs de base
        $dto->dateCreation = $agentsUfs->getDateCreation();
        $dto->dateDebut = $agentsUfs->getDateDebut();
        $dto->dateFin = $agentsUfs->getDateFin();

        // Champs du DamAffectationTrait
        $dto->rgt = $agentsUfs->getRgt();
        $dto->etpStatutaire = $agentsUfs->getEtpStatutaire();
        $dto->tauxAffectation = $agentsUfs->getTauxAffectation();
        $dto->etp = $agentsUfs->getEtp();
        $dto->sommeEtp = $agentsUfs->getSommeEtp();
        $dto->affectationPrincipale = $agentsUfs->getAffectationPrincipale();
        $dto->typeGrade = $agentsUfs->getTypeGrade();
        $dto->libelleGrade = $agentsUfs->getLibelleGrade();
        $dto->absences = $agentsUfs->getAbsences();

        // Champs du trait HorodatageTrait
        $dto->validFrom = $agentsUfs->getValidFrom();
        $dto->validTo = $agentsUfs->getValidTo();
        $dto->periodeType = $agentsUfs->getPeriodeType();
        $dto->source = $agentsUfs->getSource();

        // Relations
        if ($agentsUfs->getAgent()) {
            $dto->agent = $this->microMapper->map($agentsUfs->getAgent(), AgentDto::class, [
                MicroMapperInterface::MAX_DEPTH => 0,
            ]);
        }

        if ($agentsUfs->getUfs()) {
            $dto->ufs = $this->microMapper->map($agentsUfs->getUfs(), UfsDto::class, [
                MicroMapperInterface::MAX_DEPTH => 1,
            ]);
            
            // Calcul de l'effectif de l'UFS pour la période de l'affectation
            // On s'assure que l'effectif commence à 1 au minimum (jamais 0)
            $effectif = $this->agentUfsRepository->countEffectifForUfs(
                $agentsUfs->getUfs(),
                $agentsUfs->getDateDebut(),
                $agentsUfs->getDateFin()
            );
            $dto->effectifDeUf = max(1, $effectif);
        }

        return $dto;
    }
}