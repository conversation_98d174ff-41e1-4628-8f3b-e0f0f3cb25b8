<?php

namespace App\Domain\Service\Core;


use App\Entity\Structure\EntiteJuridique;
use App\Entity\Structure\Ufs;
use Doctrine\ORM\EntityManagerInterface;

/**
 * Service spécialisé pour la recherche des Unités Fonctionnelles
 * avec gestion multi-tenant et dimension temporelle (SCD Type 2).
 */
class UfsFinderService
{
    public function __construct(
        private readonly EntityManagerInterface $em
    )
    {
    }

    /**
     * Recherche une UF par son code dans une entité juridique donnée avec gestion de la dimension temporelle.
     *
     * @param string|null $ufCode Code de l'UF à rechercher
     * @param EntiteJuridique $entiteJuridique Entité juridique de rattachement
     * @param \DateTime|null $dateReference Date de référence unique (pour vérification ponctuelle)
     * @param \DateTime|null $dateDebut Date de début de période (pour validation de période complète)
     * @param \DateTime|null $dateFin Date de fin de période (pour validation de période complète)
     * @return Ufs|null
     */
    public function findUfByCodeAndEj(
        ?string $ufCode,
        EntiteJuridique $entiteJuridique,
        ?\DateTime $dateReference = null,
        ?\DateTime $dateDebut = null,
        ?\DateTime $dateFin = null
    ): ?Ufs {
        if (empty($ufCode)) {
            return null;
        }

        $qb = $this->em->createQueryBuilder()
            ->select('u')
            ->from(Ufs::class, 'u')
            ->join('u.cr', 'cr')
            ->join('cr.pole', 'p')
            ->where('u.ufcode = :ufCode')
            ->andWhere('p.hopital = :entiteJuridique')
            ->setParameter('ufCode', $ufCode)
            ->setParameter('entiteJuridique', $entiteJuridique);

        // Cas 1: Validation d'une période complète (dateDebut + dateFin)
        if ($dateDebut && $dateFin) {
            $qb->andWhere('u.datdeb <= :dateDebut')
                ->andWhere('(u.datfin IS NULL OR u.datfin >= :dateFin)')
                ->setParameter('dateDebut', $dateDebut)
                ->setParameter('dateFin', $dateFin);
        }
        // Cas 2: Validation ponctuelle avec une date de référence
        elseif ($dateReference) {
            $qb->andWhere('u.datdeb <= :dateRef')
                ->andWhere('(u.datfin IS NULL OR u.datfin >= :dateRef)')
                ->setParameter('dateRef', $dateReference);
        }

        // Ordre par date de fin décroissante pour prendre la version la plus récente
        $qb->orderBy('u.datfin', 'DESC');

        return $qb->getQuery()->getOneOrNullResult();
    }

    /**
     * Vérifie qu'une UF est valide pour une période donnée.
     */
    public function isUfValidForPeriod(Ufs $ufs, \DateTimeInterface $dateDebut, \DateTimeInterface $dateFin): bool
    {
        return $ufs->getDatdeb() <= $dateDebut &&
            ($ufs->getDatfin() === null || $ufs->getDatfin() >= $dateFin);
    }
}