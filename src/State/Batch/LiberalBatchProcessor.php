<?php

namespace App\State\Batch;

use ApiPlatform\Metadata\Operation;
use App\ApiResource\Activite\LiberalDto;
use App\Domain\Service\Batch\BatchOptimizationConfigService;
use App\Domain\Service\Notification\BatchNotificationService;
use App\Entity\Activite\Liberal;
use App\Entity\Structure\EntiteJuridique;
use App\Mapper\Liberal\LiberalDtoToEntityMapper;
use App\State\Batch\Base\BatchProcessorOptimized;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Exception\BadRequestException;
use Symfony\Component\HttpFoundation\RequestStack;

class LiberalBatchProcessor extends BatchProcessorOptimized
{
    public function __construct(
        private LiberalDtoToEntityMapper $mapper,
        private RequestStack $requestStack,
        BatchOptimizationConfigService $configService,
        BatchNotificationService $notificationService,
        LoggerInterface $logger,
        EntityManagerInterface $entityManager
    ) {
        parent::__construct($configService, $notificationService, $logger, $entityManager);
    }

    public function process(mixed $data, Operation $operation, array $uriVariables = [], array $context = [])
    {
        // Récupérer le contenu JSON brut de la requête
        $request = $this->requestStack->getCurrentRequest();
        $content = $request->getContent();

        // Décoder le JSON manuellement pour garder la structure de tableau
        $arrayData = json_decode($content, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new BadRequestException('Invalid JSON: ' . json_last_error_msg());
        }

        if (!is_array($arrayData)) {
            throw new BadRequestException('Expected JSON array, got: ' . gettype($arrayData));
        }

        if (empty($arrayData)) {
            throw new BadRequestException('Array cannot be empty');
        }

        // Add ejcode to context if available
        if (isset($uriVariables['ejcode'])) {
            $context['ejcode'] = $uriVariables['ejcode'];
        }

        // Récupérer l'entité juridique si disponible
        $entiteJuridique = null;
        if (isset($uriVariables['ejcode'])) {
            $entiteJuridique = $this->entityManager->getRepository(EntiteJuridique::class)
                ->findOneBy(['code' => $uriVariables['ejcode']]);
        }

        // Utiliser le traitement optimisé
        return $this->processOptimizedBatch($arrayData, 'Liberal', $entiteJuridique);
    }

    /**
     * Traite un élément individuel du batch
     */
    protected function processItem(array $item, int $index, string $resourceType): ?array
    {
        // Validation des champs requis
        $this->validateRequiredFields($item, ['dateDebut', 'agentHrU'], $index);

        // Créer le DTO à partir des données JSON
        $dto = $this->createDtoFromArray($item);

        // Utiliser le mapper existant
        $entity = $this->mapper->load($dto, Liberal::class, []);
        $isNew = $entity->getId() === null;
        $entity = $this->mapper->populate($dto, $entity, []);

        $this->entityManager->persist($entity);

        // Créer une réponse simple sans objets DateTime
        $responseData = [
            'id' => $entity->getId(),
            'typeActe' => $entity->getTypeActe(),
            'nomActe' => $entity->getNomActe(),
            'codeActe' => $entity->getCodeActe(),
            'tarif' => $entity->getTarif(),
            'dateRealisation' => $entity->getDateRealisation()?->format('Y-m-d H:i:s'),
            'dateDebut' => $entity->getDateDebut()?->format('Y-m-d H:i:s'),
            'dateFin' => $entity->getDateFin()?->format('Y-m-d H:i:s'),
            'validFrom' => $entity->getValidFrom()?->format('Y-m-d H:i:s'),
            'validTo' => $entity->getValidTo()?->format('Y-m-d H:i:s'),
            'periodeType' => $entity->getPeriodeType(),
            'source' => $entity->getSource(),
        ];

        return [
            'dto' => $responseData,
            'is_new' => $isNew,
        ];
    }


    private function createDtoFromArray(array $item): LiberalDto
    {
        $dto = new LiberalDto();

        // Champs de base
        $dto->typeActe = $item['typeActe'];
        $dto->nomActe = $item['nomActe'] ?? null;
        $dto->codeActe = $item['codeActe'] ?? null;
        $dto->dateRealisation = isset($item['dateRealisation']) ? new \DateTime($item['dateRealisation']) : null;
        $dto->dateDebut = isset($item['dateDebut']) ? new \DateTime($item['dateDebut']) : null;
        $dto->dateFin = isset($item['dateFin']) ? new \DateTime($item['dateFin']) : null;
        $dto->tarif = $item['tarif'] ?? null;
        $dto->agentHrU = $item['agentHrU'] ?? null;
        $dto->isActif = $item['isActif'] ?? true;

        return $dto;
    }

    private function createResponseDto(Liberal $entity): LiberalDto
    {
        $dto = new LiberalDto();
        $dto->id = $entity->getId();
        $dto->typeActe = $entity->getTypeActe();
        $dto->nomActe = $entity->getNomActe();
        $dto->codeActe = $entity->getCodeActe();
        $dto->dateRealisation = $entity->getDateRealisation();
        $dto->dateDebut = $entity->getDateDebut();
        $dto->dateFin = $entity->getDateFin();
        $dto->tarif = $entity->getTarif();
        $dto->validFrom = $entity->getValidFrom();
        $dto->validTo = $entity->getValidTo();
        $dto->periodeType = $entity->getPeriodeType();
        $dto->source = $entity->getSource();

        return $dto;
    }
}