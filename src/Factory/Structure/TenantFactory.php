<?php

namespace App\Factory\Structure;

use App\Entity\Structure\Tenant;
use Zenstruck\Foundry\Persistence\PersistentProxyObjectFactory;

/**
 * @extends PersistentProxyObjectFactory<Tenant>
 */
final class TenantFactory extends PersistentProxyObjectFactory
{
    /**
     * @see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#factories-as-services
     *
     * @todo inject services if required
     */
    public function __construct()
    {
    }

    public static function class(): string
    {
        return Tenant::class;
    }

    /**
     * @see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#model-factories
     *
     * @todo add your default values here
     */
    protected function defaults(): array|callable
    {
        return [
            'nom' => strtoupper(self::faker()->company()),  // Nom réaliste
            'description' => strtoupper(self::faker()->sentence(10)), // Texte court
            'is_actif' => self::faker()->boolean(90),  // 90% des tenants actifs
            'code' => 'TEN-' . self::faker()->unique()->numerify('#####'),
        ];
    }

    /**
     * @see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#initialization
     */
    protected function initialize(): static
    {
        return $this
            // ->afterInstantiate(function(Tenant $tenant): void {})
        ;
    }
}
