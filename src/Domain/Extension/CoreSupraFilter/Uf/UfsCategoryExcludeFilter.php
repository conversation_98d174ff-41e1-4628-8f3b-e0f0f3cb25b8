<?php

namespace App\Domain\Extension\CoreSupraFilter\Uf;

/**
 * Filtre de codes d'unités fonctionnelles (UF) à exclure du retour de l'API.
 *
 * OBJECTIF : Restreindre l'accès aux listes d'UFs en appliquant trois niveaux de filtrage :
 * 1. Exclusion directe par ufcode
 * 2. Exclusion en cascade si le CR parent est exclu
 * 3. Exclusion en cascade si le pôle grand-parent est exclu
 *
 * FONCTIONNEMENT :
 * - Les codes listés dans EXCLUDED_UFCODES sont filtrés des collections
 * - Les UFs appartenant à des CRs exclus sont également filtrées (cascade)
 * - Les UFs appartenant à des pôles exclus sont également filtrées (cascade)
 * - La comparaison est insensible à la casse (UPPER() en SQL)
 * - Les UFs avec ufcode = NULL sont INCLUS par défaut
 * - Le filtrage ne s'applique PAS aux accès individuels (GET /api/ufs/{id})
 *
 * CONFIGURATION ACTUELLE :
 * - Excluss : UFs identifiées comme non pertinentes + UFs de CRs/pôles exclus
 * - Inclus : UFs opérationnelles + UFs sans code défini
 *
 * @todo Affiner la liste des codes avec l'équipe métier
 * @todo Vérifier la cohérence avec les exclusions de CRs et pôles parents
 *
 * @see UfsCategoryFilterExtension Pour l'implémentation Doctrine
 * @see CrCategoryExcludeFilter Pour les exclusions de CRs parents
 * @see PoleCategoryExcludeFilter Pour les exclusions de pôles grands-parents
 */
class UfsCategoryExcludeFilter
{
    /**
     * Codes d'UFs à exclure directement du retour API
     *
     * UTILISATION :
     * - Si vous avez exclu tous les pôles nécessaires dans PoleCategoryExcludeFilter,
     *   laissez ce tableau VIDE []
     * - Si une UF non-médicale persiste malgré le filtrage des pôles, c'est probablement
     *   qu'elle n'est pas rattachée au bon pôle dans le système
     * - Dans ce cas, ajoutez son ufcode ici en attendant la correction des données
     *
     * Exemple : ['UF_ADMIN', 'UF_GHOST', 'UF_ERREUR']
     */
    private const EXCLUDED_UFCODES = [
        // Tableau vide par défaut - le filtrage se fait via les pôles
        // Ajoutez ici uniquement les codes d'UFs "orphelines" mal rattachées
    ];

    public static function isExcluded(?string $ufcode): bool
    {
        if ($ufcode === null) {
            return false;
        }

        return in_array(strtoupper($ufcode), array_map('strtoupper', self::EXCLUDED_UFCODES), true);
    }

    public static function getExcludedUfcodes(): array
    {
        return self::EXCLUDED_UFCODES;
    }
}