<?php

namespace App\Controller\Importation\Structure\Dto;

/*
 * Data Transfer Object pour l'importation des Crs.
 *
 * @see \App\Domain\Service\Importation\StructureImportService::importCrs()
 */
class CrImportDto
{
    public ?string $etab = null;
    public ?string $crCode = null;
    public ?string $datDeb = null;
    public ?string $datFin = null;
    public ?string $libelle = null;
    public ?string $nomResp = null;
    public ?string $poleCode = null;
    public ?string $pfUser = null;
    public ?string $dmdaCre = null;
    public ?string $dmdAcre = null;
    public ?string $dmdaMaj = null;
    public ?string $dmdAmaj = null;
}