<?php

namespace App\Entity\Structure;

use ApiPlatform\Doctrine\Orm\Filter\SearchFilter;
use ApiPlatform\Metadata\ApiFilter;
use App\Entity\Base\BaseEntity;
use App\Entity\Trait\HorodatageTrait;
use App\Repository\CrRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: CrRepository::class)]
#[ApiFilter(SearchFilter::class, properties: [
    'crcode' => 'partial',
    'libelle' => 'partial'
])]
class Cr extends BaseEntity
{
    use HorodatageTrait;

    #[ORM\Column(length: 5, nullable: true)]
    private ?string $etab = null;

    #[ORM\Column(length: 4, nullable: true)]
    private ?string $crcode = null;

    #[ORM\Column(type: 'date', nullable: true)]
    private ?\DateTimeInterface $datdeb = null;

    #[ORM\Column(type: 'date', nullable: true)]
    private ?\DateTimeInterface $datfin = null;

    #[ORM\Column(length: 100, nullable: true)]
    private ?string $libelle = null;

    #[ORM\Column(length: 25, nullable: true)]
    private ?string $nomresp = null;

    #[ORM\Column(length: 5, nullable: true)]
    private ?string $polecode = null;

    #[ORM\Column(length: 20, nullable: true)]
    private ?string $pfuser = null;

    #[ORM\Column(type: 'date', nullable: true)]
    private ?\DateTimeInterface $dmdacre = null;

    #[ORM\Column(type: 'date', nullable: true)]
    private ?\DateTimeInterface $dmdamaj = null;

    #[ORM\ManyToOne(inversedBy: 'crs')]
    #[ORM\JoinColumn(nullable: true)]
    private ?Pole $pole = null;

    /**
     * @var Collection<int, Ufs>
     */
    #[ORM\OneToMany(targetEntity: Ufs::class, mappedBy: 'cr')]
    private Collection $ufs;

    public function __construct()
    {
        parent::__construct();
        $this->ufs = new ArrayCollection();
    }

    public function getEtab(): ?string
    {
        return $this->etab;
    }

    public function setEtab(?string $etab): static
    {
        $this->etab = $etab;
        return $this;
    }

    public function getCrcode(): ?string
    {
        return $this->crcode;
    }

    public function setCrcode(?string $crcode): static
    {
        $this->crcode = $crcode;
        return $this;
    }

    public function getDatdeb(): ?\DateTimeInterface
    {
        return $this->datdeb;
    }

    public function setDatdeb(?\DateTimeInterface $datdeb): static
    {
        $this->datdeb = $datdeb;
        return $this;
    }

    public function getDatfin(): ?\DateTimeInterface
    {
        return $this->datfin;
    }

    public function setDatfin(?\DateTimeInterface $datfin): static
    {
        $this->datfin = $datfin;
        return $this;
    }

    public function getLibelle(): ?string
    {
        return $this->libelle;
    }

    public function setLibelle(?string $libelle): static
    {
        $this->libelle = $libelle;
        return $this;
    }

    public function getNomresp(): ?string
    {
        return $this->nomresp;
    }

    public function setNomresp(?string $nomresp): static
    {
        $this->nomresp = $nomresp;
        return $this;
    }

    public function getPolecode(): ?string
    {
        return $this->polecode;
    }

    public function setPolecode(?string $polecode): static
    {
        $this->polecode = $polecode;
        return $this;
    }

    public function getPfuser(): ?string
    {
        return $this->pfuser;
    }

    public function setPfuser(?string $pfuser): static
    {
        $this->pfuser = $pfuser;
        return $this;
    }

    public function getDmdacre(): ?\DateTimeInterface
    {
        return $this->dmdacre;
    }

    public function setDmdacre(?\DateTimeInterface $dmdacre): static
    {
        $this->dmdacre = $dmdacre;
        return $this;
    }

    public function getDmdamaj(): ?\DateTimeInterface
    {
        return $this->dmdamaj;
    }

    public function setDmdamaj(?\DateTimeInterface $dmdamaj): static
    {
        $this->dmdamaj = $dmdamaj;
        return $this;
    }

    public function getPole(): ?Pole
    {
        return $this->pole;
    }

    public function setPole(?Pole $pole): static
    {
        $this->pole = $pole;
        return $this;
    }

    /**
     * @return Collection<int, Ufs>
     */
    public function getUfs(): Collection
    {
        return $this->ufs;
    }

    public function addUf(Ufs $uf): static
    {
        if (!$this->ufs->contains($uf)) {
            $this->ufs->add($uf);
            $uf->setCr($this);
        }

        return $this;
    }

    public function removeUf(Ufs $uf): static
    {
        if ($this->ufs->removeElement($uf)) {
            // set the owning side to null (unless already changed)
            if ($uf->getCr() === $this) {
                $uf->setCr(null);
            }
        }

        return $this;
    }

    public function __toString(): string
    {
        return sprintf(
            '%s (%s)',
            $this->libelle,
            $this->crcode
        );
    }
}
