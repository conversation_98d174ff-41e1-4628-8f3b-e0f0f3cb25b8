<?php

namespace App\Mapper\AgentUf;

use App\ApiResource\Praticien\AgentUfsDto;
use App\Entity\Praticien\AgentUfs;
use App\Repository\AgentRepository;
use App\Repository\AgentUfsRepository;
use App\Repository\UfsRepository;
use Doctrine\DBAL\Driver\PDO\Exception;
use Symfonycasts\MicroMapper\AsMapper;
use Symfonycasts\MicroMapper\MapperInterface;

#[AsMapper(from: AgentUfsDto::class, to: AgentUfs::class)]
class AgentUfsDtoToEntityMapper implements MapperInterface
{
    public function __construct(
        private AgentUfsRepository $praticiensUfsRepository,
        private AgentRepository    $agentRepository,
        private UfsRepository      $ufsRepository
    ) {}

    public function load(object $from, string $toClass, array $context): object
    {
        $dto = $from;
        assert($dto instanceof AgentUfsDto);

        $praticiensUfs = $dto->id ? $this->praticiensUfsRepository->find($dto->id) : new AgentUfs();
        if ($dto->id && !$praticiensUfs) {
            throw new Exception('Association Praticien-UFS non trouvée');
        }

        return $praticiensUfs;
    }

    public function populate(object $from, object $to, array $context): object
    {
        $dto = $from;
        $praticiensUfs = $to;
        assert($dto instanceof AgentUfsDto);
        assert($praticiensUfs instanceof AgentUfs);

        // Champs de base et traits
        $this->mapBasicFields($dto, $praticiensUfs);

        // Relations
        $this->mapAgentRelation($dto, $praticiensUfs);
        $this->mapUfsRelation($dto, $praticiensUfs);

        return $praticiensUfs;
    }

    private function mapBasicFields(AgentUfsDto $dto, AgentUfs $praticiensUfs): void
    {
        $praticiensUfs->setDateDebut($dto->dateDebut);
        $praticiensUfs->setDateFin($dto->dateFin);

        // DamAffectationTrait
        $praticiensUfs->setRgt($dto->rgt);
        $praticiensUfs->setEtpStatutaire($dto->etpStatutaire);
        $praticiensUfs->setTauxAffectation($dto->tauxAffectation);
        $praticiensUfs->setEtp($dto->etp);
        $praticiensUfs->setAffectationPrincipale($dto->affectationPrincipale);
        $praticiensUfs->setTypeGrade($dto->typeGrade);
        $praticiensUfs->setLibelleGrade($dto->libelleGrade);
        $praticiensUfs->setAbsences($dto->absences);

        // HorodatageTrait
        $praticiensUfs->setValidFrom($dto->dateDebut);
        $praticiensUfs->setValidTo(null);
        $praticiensUfs->setPeriodeType($dto->periodeType);
        $praticiensUfs->setSource($dto->source);
    }

    private function mapAgentRelation(AgentUfsDto $dto, AgentUfs $praticiensUfs): void
    {
        $agent = null;

        if ($dto->agent) {
            $agentId = basename($dto->agent->id);
            $agent = $this->agentRepository->find($agentId);
        } elseif ($dto->hrUser) {
            $agent = $this->agentRepository->findOneBy(['hrUser' => $dto->hrUser]);
        } elseif ($dto->matricule) {
            $agent = $this->agentRepository->findOneBy(['matricule' => $dto->matricule]);
        } elseif ($dto->email) {
            $agent = $this->agentRepository->findOneBy(['email' => $dto->email]);
        }

        if ($agent) {
            $praticiensUfs->setAgent($agent);
            // Mise à jour du matricule si nécessaire
            $this->updateAgentMatricule($agent, $dto);
        } elseif ($dto->agent || $dto->hrUser || $dto->matricule || $dto->email) {
            throw new Exception('Agent non trouvé');
        }
    }

    private function updateAgentMatricule(Agent $agent, AgentUfsDto $dto): void
    {
        if ($dto->matricule && ($agent->getMatricule() === null || $agent->getMatricule() !== $dto->matricule)) {
            $agent->setMatricule($dto->matricule);
            $this->agentRepository->save($agent, true);
        }
    }

    private function mapUfsRelation(AgentUfsDto $dto, AgentUfs $praticiensUfs): void
    {
        $ufs = null;

        if ($dto->ufs) {
            $ufsId = basename($dto->ufs->id);
            $ufs = $this->ufsRepository->find($ufsId);
        } elseif ($dto->codeUf) {
            $ufs = $this->ufsRepository->findOneBy(['code' => $dto->codeUf]);
        }

        if ($ufs) {
            $praticiensUfs->setUfs($ufs);
        } elseif ($dto->ufs || $dto->codeUf) {
            throw new Exception('UFS non trouvé');
        }
    }


}