<?php

namespace App\Domain\Filter;

use ApiPlatform\Doctrine\Orm\Filter\AbstractFilter;
use ApiPlatform\Doctrine\Orm\Util\QueryNameGeneratorInterface;
use ApiPlatform\Metadata\Operation;
use Doctrine\ORM\QueryBuilder;
use Symfony\Component\PropertyInfo\Type;

/**
 * Filtre de recherche multi-champs optimise pour les actes medicaux.
 * 
 * Recherche dans :
 * - Code et description de l'acte
 * - Nom, prenom, titre de l'agent
 * - Libelle et code UF (principal, demande, intervention)
 * - Type d'acte et libelle type de venue
 * 
 * Optimisations :
 * - Utilise LOWER pour PostgreSQL (insensible a la casse)
 * - Evite les jointures multiples sur la meme table
 * - Support de la recherche par mots multiples
 */
class MultiFieldSearchFilter extends AbstractFilter
{
    protected function filterProperty(string $property, $value, QueryBuilder $queryBuilder, QueryNameGeneratorInterface $queryNameGenerator, string $resourceClass, ?Operation $operation = null, array $context = []): void
    {
        if ($property !== 'search' || empty($value)) {
            return;
        }

        $alias = $queryBuilder->getRootAliases()[0];
        $searchTerms = $this->parseSearchTerms($value);
        
        // Si plusieurs mots, on cherche chaque mot individuellement (ET logique)
        foreach ($searchTerms as $index => $term) {
            $parameterName = $queryNameGenerator->generateParameterName('search_' . $index);
            $this->addSearchCondition($queryBuilder, $alias, $parameterName, $term);
        }
    }

    /**
     * Parse les termes de recherche (support des guillemets pour phrases exactes)
     */
    private function parseSearchTerms(string $value): array
    {
        $value = trim($value);
        
        // Support des phrases entre guillemets
        if (preg_match_all('/"([^"]+)"|(\S+)/', $value, $matches)) {
            $terms = [];
            foreach ($matches[0] as $match) {
                $term = trim($match, '"');
                if (!empty($term)) {
                    $terms[] = $term;
                }
            }
            return $terms;
        }
        
        // Fallback : split par espaces
        return array_filter(explode(' ', $value), fn($term) => !empty(trim($term)));
    }

    /**
     * Ajoute les conditions de recherche pour un terme donne
     */
    private function addSearchCondition(QueryBuilder $queryBuilder, string $alias, string $parameterName, string $term): void
    {
        // Jointures optimisees (une seule fois par table)
        $this->addJoinsIfNeeded($queryBuilder, $alias);
        
        $searchPattern = '%' . strtolower($term) . '%';
        
        // Conditions de recherche etendues
        $orConditions = $queryBuilder->expr()->orX(
            // Acte : code et description
            $queryBuilder->expr()->like('LOWER(' . $alias . '.code)', ':' . $parameterName),
            $queryBuilder->expr()->like('LOWER(' . $alias . '.description)', ':' . $parameterName),
            $queryBuilder->expr()->like('LOWER(' . $alias . '.typeActe)', ':' . $parameterName),
            $queryBuilder->expr()->like('LOWER(' . $alias . '.internum)', ':' . $parameterName),
            
            // Agent : nom, prenom, titre, email
            $queryBuilder->expr()->like('LOWER(search_agent.nom)', ':' . $parameterName),
            $queryBuilder->expr()->like('LOWER(search_agent.prenom)', ':' . $parameterName),
            $queryBuilder->expr()->like('LOWER(search_agent.titre)', ':' . $parameterName),
            $queryBuilder->expr()->like('LOWER(search_agent.email)', ':' . $parameterName),
            
            // UF Principal
            $queryBuilder->expr()->like('LOWER(search_uf_principal.libelle)', ':' . $parameterName),
            $queryBuilder->expr()->like('LOWER(search_uf_principal.ufcode)', ':' . $parameterName),
            
            // UF Demande
            $queryBuilder->expr()->like('LOWER(search_uf_demande.libelle)', ':' . $parameterName),
            $queryBuilder->expr()->like('LOWER(search_uf_demande.ufcode)', ':' . $parameterName),
            
            // UF Intervention
            $queryBuilder->expr()->like('LOWER(search_uf_intervention.libelle)', ':' . $parameterName),
            $queryBuilder->expr()->like('LOWER(search_uf_intervention.ufcode)', ':' . $parameterName)
        );
        
        $queryBuilder
            ->andWhere($orConditions)
            ->setParameter($parameterName, $searchPattern);
    }

    /**
     * Ajoute les jointures necessaires (une seule fois)
     */
    private function addJoinsIfNeeded(QueryBuilder $queryBuilder, string $alias): void
    {
        $joins = [];
        foreach ($queryBuilder->getDQLPart('join') as $rootAlias => $joinArray) {
            foreach ($joinArray as $join) {
                $joins[] = $join->getAlias();
            }
        }
        
        // Jointure Agent
        if (!in_array('search_agent', $joins)) {
            $queryBuilder->leftJoin($alias . '.agent', 'search_agent');
        }
        
        // Jointures UF
        if (!in_array('search_uf_principal', $joins)) {
            $queryBuilder->leftJoin($alias . '.ufPrincipal', 'search_uf_principal');
        }
        
        if (!in_array('search_uf_demande', $joins)) {
            $queryBuilder->leftJoin($alias . '.ufDemande', 'search_uf_demande');
        }
        
        if (!in_array('search_uf_intervention', $joins)) {
            $queryBuilder->leftJoin($alias . '.ufIntervention', 'search_uf_intervention');
        }
    }

    public function getDescription(string $resourceClass): array
    {
        return [
            'search' => [
                'property' => 'search',
                'type' => Type::BUILTIN_TYPE_STRING,
                'required' => false,
                'description' => 'Recherche multi-champs : code, description, agent (nom/prenom/titre/email), UF (libelle/code), type acte, numero intervention. Support des phrases entre guillemets.',
                'openapi' => [
                    'example' => 'consultation cardiologie',
                    'examples' => [
                        'simple' => [
                            'summary' => 'Recherche simple',
                            'value' => 'CCAM123'
                        ],
                        'multiple' => [
                            'summary' => 'Mots multiples',
                            'value' => 'consultation martin'
                        ],
                        'phrase' => [
                            'summary' => 'Phrase exacte',
                            'value' => '"Dr Martin" cardiologie'
                        ]
                    ]
                ]
            ],
        ];
    }
}
