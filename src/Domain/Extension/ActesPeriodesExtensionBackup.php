<?php

namespace App\Domain\Extension;

use ApiPlatform\Doctrine\Orm\Extension\QueryCollectionExtensionInterface;
use ApiPlatform\Doctrine\Orm\Util\QueryNameGeneratorInterface;
use Doctrine\ORM\QueryBuilder;
use Symfony\Component\HttpFoundation\RequestStack;
use App\Entity\Activite\Actes;

/**
 * Extension Doctrine pour filtrer la ressource /api/actes sur plusieurs périodes dynamiques (SCD Type 2).
 *
 * Cette extension permet de retourner tous les actes :
 *  - dont la période de validité [validFrom, validTo] recouvre au moins l'une des périodes demandées (p1, p2, p3, ...)
 *  - ET dont la date de réalisation (date_realisation) est comprise dans cette même période.
 *  - ET optionnellement, appartenant à une entité juridique spécifique (via le paramètre ejcode).
 *
 * Exemple d'appel frontend :
 *   [GET] /api/actes?p1Start=2024-10-01&p1End=2025-03-31&p2Start=2023-10-01&p2End=2024-03-31&p3Start=2022-10-01&p3End=2023-03-31&ejcode=ej0001
 *
 * Logique :
 *   Pour chaque période, l'acte est retenu si :
 *     validFrom <= periodeEnd
 *     AND (validTo IS NULL OR validTo >= periodeStart)
 *     AND date_realisation >= periodeStart
 *     AND date_realisation <= periodeEnd
 *   (on combine toutes les périodes avec OR)
 *
 * Si le paramètre ejcode est présent, l'acte doit également appartenir à l'entité juridique correspondante.
 * Pour le filtrage par ejcode, l'extension utilise six chemins de jointure possibles :
 *   1. Actes -> UFs (ufPrincipal) -> CR -> Pole -> EntiteJuridique (hopital)
 *   2. Actes -> UFs (ufIntervention) -> CR -> Pole -> EntiteJuridique (hopital)
 *   3. Actes -> UFs (ufDemande) -> CR -> Pole -> EntiteJuridique (hopital)
 *   4. Actes -> UFs (ufPrincipal) -> Service -> EntiteJuridique (hopital)
 *   5. Actes -> UFs (ufIntervention) -> Service -> EntiteJuridique (hopital)
 *   6. Actes -> UFs (ufDemande) -> Service -> EntiteJuridique (hopital)
 * 
 * Cette approche robuste permet de capturer tous les actes liés à l'entité juridique, même si
 * certains champs UF sont null (ce qui est souvent le cas pour ufPrincipal et parfois pour ufIntervention).
 * Elle prend également en compte les deux chemins possibles d'une UF vers une EntiteJuridique :
 * soit via un CR et un Pole, soit directement via un Service.
 *
 * Cette extension ne fait rien si aucun paramètre pXStart/pXEnd ou ejcode n'est passé dans la requête.
 */
class ActesPeriodesExtensionBackup implements QueryCollectionExtensionInterface
{
    private RequestStack $requestStack;

    /**
     * @param RequestStack $requestStack Pour accéder à la requête courante (query params)
     */
    public function __construct(RequestStack $requestStack)
    {
        $this->requestStack = $requestStack;
    }

    /**
     * Ajoute dynamiquement des conditions WHERE sur le QueryBuilder pour filtrer les actes sur plusieurs périodes.
     *
     * @param QueryBuilder $qb
     * @param QueryNameGeneratorInterface $queryNameGenerator
     * @param string $resourceClass
     * @param \ApiPlatform\Metadata\Operation|null $operation
     * @param array $context
     */
    public function applyToCollection(
        QueryBuilder $qb,
        QueryNameGeneratorInterface $queryNameGenerator,
        string $resourceClass,
        ?\ApiPlatform\Metadata\Operation $operation = null,
        array $context = []
    ): void {
        // Ne s'applique que sur la ressource Actes (évite de filtrer sur d'autres entités)
        if ($resourceClass !== Actes::class) {
            return;
        }

        $request = $this->requestStack->getCurrentRequest();
        if (!$request) {
            return;
        }

        $periods = [];
        $ejcode = $request->query->get('ejcode');

        // Récupère dynamiquement tous les couples pXStart/pXEnd présents dans la requête
        // Exemple : p1Start/p1End, p2Start/p2End, ...
        foreach ($request->query->all() as $key => $value) {
            if (preg_match('/^p\d+Start$/', $key)) {
                $index = substr($key, 1, -5); // extrait le numéro (ex: '1' dans 'p1Start')
                $start = $value;
                $endKey = "p{$index}End";
                $end = $request->query->get($endKey);
                if ($end) {
                    $periods[] = [$start, $end];
                }
            }
        }

        // S'il n'y a pas de période passée et pas d'ejcode, on ne filtre rien
        if (count($periods) === 0 && !$ejcode) {
            return;
        }

        // Si un ejcode est spécifié, on ajoute les joins nécessaires pour filtrer par entité juridique
        if ($ejcode) {
            // La pluspart du temps ufPrincipal et ufIntervention peuvent être null
            // On utilise donc plusieurs chemins de jointure pour être sûr de capturer tous les actes
            
            // Générer des alias uniques pour chaque chemin de jointure via Service
            $ufPrincipalAlias = $queryNameGenerator->generateJoinAlias('ufPrincipal');
            $servicePrincipalAlias = $queryNameGenerator->generateJoinAlias('servicePrincipal');
            $hopitalPrincipalAlias = $queryNameGenerator->generateJoinAlias('hopitalPrincipal');
            
            $ufInterventionAlias = $queryNameGenerator->generateJoinAlias('ufIntervention');
            $serviceInterventionAlias = $queryNameGenerator->generateJoinAlias('serviceIntervention');
            $hopitalInterventionAlias = $queryNameGenerator->generateJoinAlias('hopitalIntervention');
            
            $ufDemandeAlias = $queryNameGenerator->generateJoinAlias('ufDemande');
            $serviceDemandeAlias = $queryNameGenerator->generateJoinAlias('serviceDemande');
            $hopitalDemandeAlias = $queryNameGenerator->generateJoinAlias('hopitalDemande');
            
            // Générer des alias uniques pour chaque chemin de jointure via CR -> Pole
            $crPrincipalAlias = $queryNameGenerator->generateJoinAlias('crPrincipal');
            $polePrincipalAlias = $queryNameGenerator->generateJoinAlias('polePrincipal');
            $hopitalCrPrincipalAlias = $queryNameGenerator->generateJoinAlias('hopitalCrPrincipal');
            
            $crInterventionAlias = $queryNameGenerator->generateJoinAlias('crIntervention');
            $poleInterventionAlias = $queryNameGenerator->generateJoinAlias('poleIntervention');
            $hopitalCrInterventionAlias = $queryNameGenerator->generateJoinAlias('hopitalCrIntervention');
            
            $crDemandeAlias = $queryNameGenerator->generateJoinAlias('crDemande');
            $poleDemandeAlias = $queryNameGenerator->generateJoinAlias('poleDemande');
            $hopitalCrDemandeAlias = $queryNameGenerator->generateJoinAlias('hopitalCrDemande');
            
            // Ajouter toutes les jointures nécessaires via Service
            $qb->leftJoin('o.ufPrincipal', $ufPrincipalAlias)
               ->leftJoin("$ufPrincipalAlias.service", $servicePrincipalAlias)
               ->leftJoin("$servicePrincipalAlias.hopital", $hopitalPrincipalAlias)
               
               ->leftJoin('o.ufIntervention', $ufInterventionAlias)
               ->leftJoin("$ufInterventionAlias.service", $serviceInterventionAlias)
               ->leftJoin("$serviceInterventionAlias.hopital", $hopitalInterventionAlias)
               
               ->leftJoin('o.ufDemande', $ufDemandeAlias)
               ->leftJoin("$ufDemandeAlias.service", $serviceDemandeAlias)
               ->leftJoin("$serviceDemandeAlias.hopital", $hopitalDemandeAlias);
               
            // Ajouter toutes les jointures nécessaires via CR -> Pole
            $qb->leftJoin("$ufPrincipalAlias.cr", $crPrincipalAlias)
               ->leftJoin("$crPrincipalAlias.pole", $polePrincipalAlias)
               ->leftJoin("$polePrincipalAlias.hopital", $hopitalCrPrincipalAlias)
               
               ->leftJoin("$ufInterventionAlias.cr", $crInterventionAlias)
               ->leftJoin("$crInterventionAlias.pole", $poleInterventionAlias)
               ->leftJoin("$poleInterventionAlias.hopital", $hopitalCrInterventionAlias)
               
               ->leftJoin("$ufDemandeAlias.cr", $crDemandeAlias)
               ->leftJoin("$crDemandeAlias.pole", $poleDemandeAlias)
               ->leftJoin("$poleDemandeAlias.hopital", $hopitalCrDemandeAlias);
            
            // Construire une condition OR pour capturer les actes qui correspondent à l'ejcode
            // via n'importe quel chemin de jointure
            $orCondition = $qb->expr()->orX(
                // Via Service
                $qb->expr()->eq("$hopitalPrincipalAlias.code", ':ejcode'),
                $qb->expr()->eq("$hopitalInterventionAlias.code", ':ejcode'),
                $qb->expr()->eq("$hopitalDemandeAlias.code", ':ejcode'),
                // Via CR -> Pole
                $qb->expr()->eq("$hopitalCrPrincipalAlias.code", ':ejcode'),
                $qb->expr()->eq("$hopitalCrInterventionAlias.code", ':ejcode'),
                $qb->expr()->eq("$hopitalCrDemandeAlias.code", ':ejcode')
            );
            
            $qb->andWhere($orCondition)
               ->setParameter('ejcode', $ejcode);
        }

        // S'il n'y a pas de période passée, on ne continue pas avec le filtrage par période
        if (count($periods) === 0) {
            // Si on a un ejcode, on a déjà appliqué le filtre, donc on peut retourner
            // Sans cette condition, on essaierait de construire un WHERE avec un $orX vide
            return;
        }

        // On va construire un WHERE (... OR ... OR ...) pour toutes les périodes
        $orX = $qb->expr()->orX();
        $i = 0;
        foreach ($periods as [$start, $end]) {
            $paramStart = "period_start_$i";
            $paramEnd   = "period_end_$i";

            // Pour chaque période, on ajoute la condition SCD2 + date_realisation dans la période
            // validFrom <= periodeEnd
            // AND (validTo IS NULL OR validTo >= periodeStart)
            // AND date_realisation >= periodeStart
            // AND date_realisation <= periodeEnd
            $orX->add(
                $qb->expr()->andX(
                    $qb->expr()->lte('o.validFrom', ":$paramEnd"),
                    $qb->expr()->orX(
                        $qb->expr()->isNull('o.validTo'),
                        $qb->expr()->gte('o.validTo', ":$paramStart")
                    ),
                    $qb->expr()->gte('o.date_realisation', ":$paramStart"),
                    $qb->expr()->lte('o.date_realisation', ":$paramEnd")
                )
            );
            $qb->setParameter($paramStart, $start);
            $qb->setParameter($paramEnd, $end);
            $i++;
        }

        // Ajoute la condition globale au QueryBuilder (WHERE (... OR ... OR ...))
        $qb->andWhere($orX);
    }
}
