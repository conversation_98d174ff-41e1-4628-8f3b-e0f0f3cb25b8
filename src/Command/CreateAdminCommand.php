<?php

namespace App\Command;

use App\Domain\Enum\AgentRoleType;
use App\Entity\Praticien\Agent;
use App\Entity\Praticien\AgentHopitalRole;
use App\Entity\Structure\EntiteJuridique;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;

/**
 * Commande pour créer un utilisateur administrateur pour EasyAdmin.
 * Cette commande permet de créer un administrateur avec des options pour spécifier
 * le nom, le prénom, le code de l'entité juridique et le code HR User.
 * Elle inclut également une option pour lister les entités juridiques disponibles.
 * Utilisation :
 * php bin/console app:create-admin email password [CODE_ENTITE_JURIDIQUE] [options]
 * Options :
 * --nom=Nom de l'administrateur (par défaut: "Admin")
 * --prenom=Prénom de l'administrateur (par défaut: "Super")
 * --hr-user=Code HR User (par défaut: "ADMIN001")
 * --list-ej Lister les entités juridiques disponibles
 * Exemple :
 * php bin/console app:create-admin <EMAIL> pass123 CHU001 \
 * --nom="Dupont" \
 * --prenom="Jean" \
 * --hr-user="ADMIN_CHU"
 * ***
 * php bin/console app:create-admin <EMAIL> <EMAIL> EJ-001 --nom="Administrateur" --prenom="iSMA-SUPRA" --hr-user="ISMA_ADMIN_CHU"
 */
#[AsCommand(
    name: 'app:create-admin',
    description: 'Créer un utilisateur administrateur pour EasyAdmin',
)]
class CreateAdminCommand extends Command
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private UserPasswordHasherInterface $passwordHasher
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addArgument('email', InputArgument::OPTIONAL, 'Email de l\'administrateur')
            ->addArgument('password', InputArgument::OPTIONAL, 'Mot de passe de l\'administrateur')
            ->addArgument('entite-juridique-code', InputArgument::OPTIONAL, 'Code de l\'entité juridique')
            ->addOption('nom', null, InputOption::VALUE_OPTIONAL, 'Nom de l\'administrateur', 'Admin')
            ->addOption('prenom', null, InputOption::VALUE_OPTIONAL, 'Prénom de l\'administrateur', 'Super')
            ->addOption('hr-user', null, InputOption::VALUE_OPTIONAL, 'Code HR User', 'ADMIN001')
            ->addOption('list-ej', null, InputOption::VALUE_NONE, 'Lister les entités juridiques disponibles')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        // Option pour lister les entités juridiques
        if ($input->getOption('list-ej')) {
            return $this->listEntitesJuridiques($io);
        }

        $email = $input->getArgument('email');
        $password = $input->getArgument('password');
        $entiteJuridiqueCode = $input->getArgument('entite-juridique-code');
        $nom = $input->getOption('nom');
        $prenom = $input->getOption('prenom');
        $hrUser = $input->getOption('hr-user');

        // Vérifier que les arguments obligatoires sont fournis quand on ne liste pas les EJ
        if (!$email || !$password) {
            $io->error('Les arguments email et password sont obligatoires pour créer un administrateur.');
            $io->note('Usage: php bin/console app:create-admin <email> <password> [entite-juridique-code]');
            $io->note('Pour lister les entités juridiques: php bin/console app:create-admin --list-ej');
            return Command::FAILURE;
        }

        // Vérifier si l'utilisateur existe déjà
        $existingUser = $this->entityManager->getRepository(Agent::class)->findOneBy(['email' => $email]);
        if ($existingUser) {
            $io->error(sprintf('Un utilisateur avec l\'email "%s" existe déjà !', $email));
            return Command::FAILURE;
        }

        // Gestion de l'entité juridique
        if (!$entiteJuridiqueCode) {
            $io->warning('Aucun code d\'entité juridique spécifié.');
            $this->listEntitesJuridiques($io);
            $io->note('Utilisez : php bin/console app:create-admin email password CODE_ENTITE_JURIDIQUE');
            return Command::FAILURE;
        }

        // Récupérer l'entité juridique par son code
        $entiteJuridique = $this->entityManager->getRepository(EntiteJuridique::class)->findOneBy(['code' => $entiteJuridiqueCode]);
        if (!$entiteJuridique) {
            $io->error(sprintf('Aucune entité juridique trouvée avec le code "%s" !', $entiteJuridiqueCode));
            $this->listEntitesJuridiques($io);
            return Command::FAILURE;
        }

        // Créer l'utilisateur administrateur
        $admin = new Agent();
        $admin->setEmail($email);
        $admin->setNom($nom);
        $admin->setPrenom($prenom);
        $admin->setHrUser($hrUser);
        $admin->setHopital($entiteJuridique);

        // Hasher le mot de passe
        $hashedPassword = $this->passwordHasher->hashPassword($admin, $password);
        $admin->setPassword($hashedPassword);

        // Assigner le rôle CME (Chef de Médecine PLUS HAUT NIVEAU) par défaut
        // Vous pouvez ajuster le rôle par défaut selon vos besoins
        $admin->setRoles([AgentRoleType::CME,AgentRoleType::ADMIN]);

        // Activer le compte
        $admin->setIsActif(true);

        // Sauvegarder l'agent en base
        $this->entityManager->persist($admin);
        $this->entityManager->flush();

        // Créer la relation AgentHopitalRole
        $agentHopitalRole = new AgentHopitalRole($admin, $entiteJuridique, AgentRoleType::ADMIN);
        $this->entityManager->persist($agentHopitalRole);
        $this->entityManager->flush();

        $io->success([
            'Utilisateur administrateur créé avec succès !',
            sprintf('Email: %s', $email),
            sprintf('Nom: %s %s', $prenom, $nom),
            sprintf('HR User: %s', $hrUser),
            sprintf('Entité juridique: %s (%s)', $entiteJuridique->getNom(), $entiteJuridique->getCode()),
            sprintf('Rôle assigné: %s', AgentRoleType::ADMIN),
            '',
            '✅ Agent créé dans la table Agent',
            '✅ Relation AgentHopitalRole créée',
            '',
            '🔗 Vous pouvez maintenant vous connecter à l\'administration :',
            '   URL: /admin/login'
        ]);

        return Command::SUCCESS;
    }

    private function listEntitesJuridiques(SymfonyStyle $io): int
    {
        $io->title('Liste des entités juridiques disponibles');

        $entites = $this->entityManager->getRepository(EntiteJuridique::class)->findAll();
        if (empty($entites)) {
            $io->warning('Aucune entité juridique trouvée.');
            return Command::SUCCESS;
        }

        foreach ($entites as $entite) {
            $io->success(sprintf('Code: %s - Nom: %s', $entite->getCode(), $entite->getNom()));
        }

        return Command::SUCCESS;
    }
}
