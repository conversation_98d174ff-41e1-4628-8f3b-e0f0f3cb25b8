<?php

namespace App\Controller\Api;

use Doctrine\DBAL\Connection;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Contracts\Cache\CacheInterface;
use Symfony\Contracts\Cache\ItemInterface;

/**
 * RAPID Contrôleur pour les filtres en cascade des actes médicaux.
 *
 * Fournit les listes de praticiens, pôles et CRs disponibles selon les périodes
 * et filtres appliqués pour alimenter les autocompletes intelligents côté frontend.
 *
 * Logique de cascade intelligente :
 * - Praticien sélectionné → Propose seulement les pôles/CRs où ce praticien a posé des actes
 * - Pôle sélectionné → Propose seulement les praticiens qui ont posé des actes dans ce pôle
 * - CR sélectionné → Propose seulement les praticiens qui ont posé des actes dans ce service
 * - Type de venue → Filtre tous les actes selon le type de venue
 *
 * @deprecated ce controller est deprecier il doit etre refactoriser ( separer la logique metier dans le dossier Domaine/service )
 */
class ActesFiltersSimpleController extends AbstractController
{
    public function __construct(
        private Connection     $connection,
        private CacheInterface $cache,
        private int            $apiCacheTtl = 3600
    )
    {
    }

    /**
     * Endpoint principal pour récupérer les filtres en cascade
     *
     * GET /api/actes/filters
     * GET /api/actes/filters?p1Start=2024-01-01&p1End=2024-12-31&ejcode=ej001
     * GET /api/actes/filters?practitioner=/api/agent/123&p1Start=2024-01-01&p1End=2024-12-31
     */
    #[Route("/api/cascade/actes/filters", name: "api_actes_filters", methods: ["GET"])]
    public function getFilters(Request $request): JsonResponse
    {
        $startTime = microtime(true);

        try {
            // Extraire les filtres et périodes de la requête
            $filters = $this->extractFilters($request);
            $periods = $this->extractPeriods($request);

            // Générer une clé de cache unique
            $cacheKey = $this->generateCacheKey($filters, $periods);

            // Récupérer les données depuis le cache ou les calculer
            $data = $this->cache->get($cacheKey, function (ItemInterface $item) use ($filters, $periods, $startTime) {
                $item->expiresAfter($this->apiCacheTtl);

                // Construire les conditions SQL de base
                $whereConditions = [];
                $params = [];

                // Appliquer les filtres de périodes
                $this->applyPeriodFilters($whereConditions, $params, $periods);

                // Appliquer le filtre ejcode si présent
                if (isset($filters['ejcode'])) {
                    $whereConditions[] = "EXISTS (
                        SELECT 1 FROM ufs uf 
                        INNER JOIN cr c ON uf.cr_id = c.id
                        INNER JOIN pole p ON c.pole_id = p.id
                        INNER JOIN entite_juridique ej ON p.hopital_id = ej.id
                        WHERE a.uf_intervention_id = uf.id AND ej.code = :ejcode
                    )";
                    $params['ejcode'] = $filters['ejcode'];
                }

                // Appliquer le filtre typeVenue si présent
                if (isset($filters['typeVenue'])) {
                    $whereConditions[] = "a.type_venue = :typeVenue";
                    $params['typeVenue'] = $filters['typeVenue'];
                }

                // Récupérer les données selon la logique de cascade
                // Note: practitioners are now only included within poles and crs, not at the top level
                $poles = $this->getAvailablePoles($filters, $whereConditions, $params);
                $crs = $this->getAvailableCrs($filters, $whereConditions, $params);
                $totalActes = $this->getTotalActes($whereConditions, $params);
                
                /**
                 * Structure de la réponse JSON attendue:
                 * {
                 *   "poles": [                                  // Liste des pôles disponibles
                 *     {
                 *       "@id": "/api/poles/456",                // URI de référence du pôle
                 *       "poleCode": "8IM",                      // Code du pôle
                 *       "libelle": "Cardiologie Pôle",          // Libellé du pôle
                 *       "crs": [                                // Liste des CRs appartenant à ce pôle
                 *         {
                 *           "@id": "/api/crs/789",              // URI de référence du CR
                 *           "crcode": "CARD01",                 // Code du CR
                 *           "libelle": "Cardiologie Interventionnelle", // Libellé du CR
                 *           "practitioners": [                  // Liste des praticiens ayant posé des actes dans ce CR
                 *             {
                 *               "@id": "/api/agent/123",        // URI de référence du praticien
                 *               "nom": "BALDE",                 // Nom du praticien
                 *               "prenom": "ismaila",            // Prénom du praticien
                 *               "titre": "Pr",                  // Titre du praticien
                 *               "displayName": "Pr BALDE ismaila"  // Nom complet formaté
                 *             }
                 *           ]
                 *         }
                 *       ]
                 *     }
                 *   ],
                 *   "totalActes": 1412,                         // Nombre total d'actes correspondant aux filtres
                 *   "appliedFilters": {                         // Filtres appliqués à la requête
                 *     "ejcode": "ej0001"                        // Code de l'entité juridique
                 *   },
                 *   "appliedPeriods": {                         // Périodes appliquées à la requête
                 *     "p1": "2025-01-01 to 2025-06-30"          // Période 1
                 *   },
                 *   "generationTimeMs": 197                     // Temps de génération en millisecondes
                 * }
                 */
                
                return [
                    'poles' => $poles,
                    'totalActes' => $totalActes,
                    'appliedFilters' => $filters,
                    'appliedPeriods' => $this->formatAppliedPeriods($periods),
                    'generationTimeMs' => (int)((microtime(true) - $startTime) * 1000)
                ];
            });

            return $this->json($data);

        } catch (\Exception $e) {
            return $this->json([
                'error' => 'Erreur lors de la récupération des filtres',
                'message' => $e->getMessage(),
                'generationTimeMs' => (int)((microtime(true) - $startTime) * 1000)
            ], 500);
        }
    }

    /**
     * Extraire les filtres de la requête
     */
    private function extractFilters(Request $request): array
    {
        $filters = [];

        if ($request->query->get('ejcode')) {
            $filters['ejcode'] = $request->query->get('ejcode');
        }

        if ($request->query->get('practitioner')) {
            $filters['practitioner'] = $request->query->get('practitioner');
        }

        if ($request->query->get('pole')) {
            $filters['pole'] = $request->query->get('pole');
        }

        if ($request->query->get('cr')) {
            $filters['cr'] = $request->query->get('cr');
        }

        if ($request->query->get('typeVenue')) {
            $filters['typeVenue'] = (int)$request->query->get('typeVenue');
        }

        return $filters;
    }

    /**
     * Extraire les périodes de la requête
     */
    private function extractPeriods(Request $request): array
    {
        $periods = [];

        if ($request->query->get('p1Start') && $request->query->get('p1End')) {
            $periods['p1'] = [
                'start' => $request->query->get('p1Start'),
                'end' => $request->query->get('p1End')
            ];
        }

        if ($request->query->get('p2Start') && $request->query->get('p2End')) {
            $periods['p2'] = [
                'start' => $request->query->get('p2Start'),
                'end' => $request->query->get('p2End')
            ];
        }

        if ($request->query->get('p3Start') && $request->query->get('p3End')) {
            $periods['p3'] = [
                'start' => $request->query->get('p3Start'),
                'end' => $request->query->get('p3End')
            ];
        }

        return $periods;
    }

    /**
     * Appliquer les filtres de périodes aux conditions SQL
     */
    private function applyPeriodFilters(array &$whereConditions, array &$params, array $periods): void
    {
        if (empty($periods)) {
            return;
        }

        $periodConditions = [];
        $paramIndex = 0;

        foreach ($periods as $periodKey => $period) {
            $startParam = 'periodStart' . $paramIndex;
            $endParam = 'periodEnd' . $paramIndex;

            $periodConditions[] = "(a.date_realisation >= :$startParam AND a.date_realisation <= :$endParam)";
            $params[$startParam] = $period['start'];
            $params[$endParam] = $period['end'];

            $paramIndex++;
        }

        if (!empty($periodConditions)) {
            $whereConditions[] = '(' . implode(' OR ', $periodConditions) . ')';
        }
    }

    /**
     * Récupérer les praticiens disponibles selon les filtres
     * Logique de cascade : si un pôle/CR est sélectionné, ne montrer que les praticiens de ce pôle/CR
     */
    private function getAvailablePractitioners(array $filters, array $baseConditions, array $baseParams): array
    {
        // Si un praticien est déjà sélectionné, le retourner directement
        if (isset($filters['practitioner'])) {
            $practitionerId = $this->extractIdFromApiUri($filters['practitioner']);
            if ($practitionerId) {
                $sql = "SELECT DISTINCT ag.id, ag.nom, ag.prenom, ag.titre 
                        FROM agent ag 
                        WHERE ag.id = :practitionerId AND ag.is_actif = true";

                $result = $this->connection->executeQuery($sql, ['practitionerId' => $practitionerId])->fetchAllAssociative();
                return array_map([$this, 'formatPractitioner'], $result);
            }
            return [];
        }

        // Construire la requête avec les filtres de cascade
        $sql = "SELECT DISTINCT ag.id, ag.nom, ag.prenom, ag.titre 
                FROM actes a
                INNER JOIN agent ag ON a.agent_id = ag.id
                INNER JOIN ufs uf_int ON a.uf_intervention_id = uf_int.id";

        $conditions = array_merge($baseConditions, ['ag.is_actif = true', 'ag.nom IS NOT NULL', 'ag.prenom IS NOT NULL']);
        $params = $baseParams;

        // Filtre cascade : si un pôle est sélectionné
        if (isset($filters['pole'])) {
            $poleId = $this->extractIdFromApiUri($filters['pole']);
            if ($poleId) {
                $sql .= " INNER JOIN cr c ON uf_int.cr_id = c.id";
                $sql .= " INNER JOIN pole p ON c.pole_id = p.id";
                $conditions[] = "p.id = :poleId";
                $params['poleId'] = $poleId;
            }
        }

        // Filtre cascade : si un CR est sélectionné
        if (isset($filters['cr'])) {
            $crId = $this->extractIdFromApiUri($filters['cr']);
            if ($crId) {
                $sql .= " INNER JOIN cr cr ON uf_int.cr_id = cr.id";
                $conditions[] = "cr.id = :crId";
                $params['crId'] = $crId;
            }
        }

        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(' AND ', $conditions);
        }

        $sql .= " ORDER BY ag.nom, ag.prenom LIMIT 100"; // Limiter pour les performances

        $results = $this->connection->executeQuery($sql, $params)->fetchAllAssociative();
        return array_map([$this, 'formatPractitioner'], $results);
    }

    /**
     * Récupérer les pôles disponibles selon les filtres
     */
    private function getAvailablePoles(array $filters, array $baseConditions, array $baseParams): array
    {
        // Si un pôle est déjà sélectionné, le retourner directement
        if (isset($filters['pole'])) {
            $poleId = $this->extractIdFromApiUri($filters['pole']);
            if ($poleId) {
                $sql = "SELECT DISTINCT p.id, p.polecode, p.libelle 
                        FROM pole p 
                        WHERE p.id = :poleId AND p.is_actif = true";

                $result = $this->connection->executeQuery($sql, ['poleId' => $poleId])->fetchAllAssociative();
                return array_map([$this, 'formatPole'], $result);
            }
            return [];
        }

        // Construire la requête avec les filtres de cascade
        $sql = "SELECT DISTINCT p.id, p.polecode, p.libelle 
        FROM actes a
        INNER JOIN ufs uf_int ON a.uf_intervention_id = uf_int.id
        INNER JOIN cr c ON uf_int.cr_id = c.id
        INNER JOIN pole p ON c.pole_id = p.id";

        $conditions = $baseConditions;
        $params = $baseParams;

        // Filtre cascade : si un praticien est sélectionné
        if (isset($filters['practitioner'])) {
            $practitionerId = $this->extractIdFromApiUri($filters['practitioner']);
            if ($practitionerId) {
                $sql .= " INNER JOIN agent ag ON a.agent_id = ag.id";
                $conditions[] = "ag.id = :practitionerId";
                $params['practitionerId'] = $practitionerId;
            }
        }

        // Filtre cascade : si un CR est sélectionné
        if (isset($filters['cr'])) {
            $crId = $this->extractIdFromApiUri($filters['cr']);
            if ($crId) {
                $conditions[] = "c.id = :crId";
                $params['crId'] = $crId;
            }
        }

        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(' AND ', $conditions);
        }

        $sql .= " ORDER BY p.libelle LIMIT 100";

        $results = $this->connection->executeQuery($sql, $params)->fetchAllAssociative();
        return array_map([$this, 'formatPole'], $results);
    }

    /**
     * Récupérer les CRs disponibles selon les filtres
     */
    private function getAvailableCrs(array $filters, array $baseConditions, array $baseParams): array
    {
        // Si un CR est déjà sélectionné, le retourner directement
        if (isset($filters['cr'])) {
            $crId = $this->extractIdFromApiUri($filters['cr']);
            if ($crId) {
                $sql = "SELECT DISTINCT cr.id, cr.crcode, cr.libelle 
                        FROM cr cr 
                        WHERE cr.id = :crId AND cr.is_actif = true";

                $result = $this->connection->executeQuery($sql, ['crId' => $crId])->fetchAllAssociative();
                return array_map([$this, 'formatCr'], $result);
            }
            return [];
        }

        // Construire la requête avec les filtres de cascade
        $sql = "SELECT DISTINCT cr.id, cr.crcode, cr.libelle 
                FROM actes a
                INNER JOIN ufs uf_int ON a.uf_intervention_id = uf_int.id
                INNER JOIN cr cr ON uf_int.cr_id = cr.id";

        $conditions = array_merge($baseConditions, ['cr.is_actif = true']);
        $params = $baseParams;

        // Filtre cascade : si un praticien est sélectionné
        if (isset($filters['practitioner'])) {
            $practitionerId = $this->extractIdFromApiUri($filters['practitioner']);
            if ($practitionerId) {
                $sql .= " INNER JOIN agent ag ON a.agent_id = ag.id";
                $conditions[] = "ag.id = :practitionerId";
                $params['practitionerId'] = $practitionerId;
            }
        }

        // Filtre cascade : si un pôle est sélectionné
        if (isset($filters['pole'])) {
            $poleId = $this->extractIdFromApiUri($filters['pole']);
            if ($poleId) {
                $sql .= " INNER JOIN pole p ON cr.pole_id = p.id";
                $conditions[] = "p.id = :poleId";
                $params['poleId'] = $poleId;
            }
        }

        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(' AND ', $conditions);
        }

        $sql .= " ORDER BY cr.libelle LIMIT 100";

        $results = $this->connection->executeQuery($sql, $params)->fetchAllAssociative();
        return array_map([$this, 'formatCr'], $results);
    }

    /**
     * Compter le nombre total d'actes selon les filtres
     */
    private function getTotalActes(array $conditions, array $params): int
    {
        $sql = "SELECT COUNT(DISTINCT a.id) as total FROM actes a";

        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(' AND ', $conditions);
        }

        $result = $this->connection->executeQuery($sql, $params)->fetchAssociative();
        return (int)($result['total'] ?? 0);
    }

    /**
     * Formater un praticien pour l'API
     */
    private function formatPractitioner(array $row): array
    {
        $practitionerId = $row['id'];
        
        // Récupérer les pôles où le praticien a posé des actes
        $poles = $this->getPolesForPractitioner($practitionerId);
        
        return [
            '@id' => '/api/agent/' . $practitionerId,
            'nom' => $row['nom'],
            'prenom' => $row['prenom'],
            'titre' => $row['titre'],
            'displayName' => trim(($row['titre'] ? $row['titre'] . ' ' : '') .
                $row['nom'] . ' ' . $row['prenom']),
            'poles' => $poles
        ];
    }
    
    /**
     * Récupérer les pôles où un praticien a posé des actes
     */
    private function getPolesForPractitioner(string $practitionerId): array
    {
        $sql = "SELECT DISTINCT p.id, p.polecode, p.libelle 
                FROM actes a
                INNER JOIN agent ag ON a.agent_id = ag.id
                INNER JOIN ufs uf_int ON a.uf_intervention_id = uf_int.id
                INNER JOIN cr c ON uf_int.cr_id = c.id
                INNER JOIN pole p ON c.pole_id = p.id
                WHERE ag.id = :practitionerId";
                
        $results = $this->connection->executeQuery($sql, ['practitionerId' => $practitionerId])->fetchAllAssociative();
        
        return array_map(function($pole) use ($practitionerId) {
            $poleId = $pole['id'];
            
            // Créer une version simplifiée du pôle pour éviter les références circulaires
            $formattedPole = [
                '@id' => '/api/poles/' . $poleId,
                'poleCode' => $pole['polecode'],
                'libelle' => $pole['libelle']
            ];
            
            // Ajouter les CRs pour ce pôle où le praticien a posé des actes
            $formattedPole['crs'] = $this->getCrsForPractitionerAndPole($practitionerId, $poleId);
            
            return $formattedPole;
        }, $results);
    }
    
    /**
     * Récupérer les CRs d'un pôle où un praticien a posé des actes
     */
    private function getCrsForPractitionerAndPole(string $practitionerId, string $poleId): array
    {
        $sql = "SELECT DISTINCT cr.id, cr.crcode, cr.libelle 
                FROM actes a
                INNER JOIN agent ag ON a.agent_id = ag.id
                INNER JOIN ufs uf_int ON a.uf_intervention_id = uf_int.id
                INNER JOIN cr cr ON uf_int.cr_id = cr.id
                WHERE ag.id = :practitionerId AND cr.pole_id = :poleId";
                
        $results = $this->connection->executeQuery($sql, [
            'practitionerId' => $practitionerId,
            'poleId' => $poleId
        ])->fetchAllAssociative();
        
        return array_map(function($cr) {
            return [
                '@id' => '/api/crs/' . $cr['id'],
                'crcode' => $cr['crcode'],
                'libelle' => $cr['libelle']
            ];
        }, $results);
    }

    /**
     * Formater un pôle pour l'API
     */
    private function formatPole(array $row): array
    {
        $poleId = $row['id'];
        
        // Récupérer les CRs de ce pôle
        $crs = $this->getCrsForPole($poleId);
        
        return [
            '@id' => '/api/poles/' . $poleId,
            'poleCode' => $row['polecode'],
            'libelle' => $row['libelle'],
            'crs' => $crs
        ];
    }
    
    /**
     * Récupérer les praticiens qui ont posé des actes dans un pôle
     */
    private function getPractitionersForPole(string $poleId): array
    {
        $sql = "SELECT DISTINCT ag.id, ag.nom, ag.prenom, ag.titre 
                FROM actes a
                INNER JOIN agent ag ON a.agent_id = ag.id
                INNER JOIN ufs uf_int ON a.uf_intervention_id = uf_int.id
                INNER JOIN cr c ON uf_int.cr_id = c.id
                INNER JOIN pole p ON c.pole_id = p.id
                WHERE p.id = :poleId AND ag.is_actif = true";
                
        $results = $this->connection->executeQuery($sql, ['poleId' => $poleId])->fetchAllAssociative();
        
        return array_map(function($practitioner) {
            return [
                '@id' => '/api/agent/' . $practitioner['id'],
                'nom' => $practitioner['nom'],
                'prenom' => $practitioner['prenom'],
                'titre' => $practitioner['titre'],
                'displayName' => trim(($practitioner['titre'] ? $practitioner['titre'] . ' ' : '') .
                    $practitioner['nom'] . ' ' . $practitioner['prenom'])
            ];
        }, $results);
    }
    
    /**
     * Récupérer les CRs d'un pôle
     */
    private function getCrsForPole(string $poleId): array
    {
        $sql = "SELECT DISTINCT cr.id, cr.crcode, cr.libelle 
                FROM cr cr
                WHERE cr.pole_id = :poleId AND cr.is_actif = true";
                
        $results = $this->connection->executeQuery($sql, ['poleId' => $poleId])->fetchAllAssociative();
        
        return array_map(function($cr) use ($poleId) {
            $crId = $cr['id'];
            
            // Créer une version simplifiée du CR pour éviter les références circulaires
            $formattedCr = [
                '@id' => '/api/crs/' . $crId,
                'crcode' => $cr['crcode'],
                'libelle' => $cr['libelle']
            ];
            
            // Ajouter les praticiens qui ont posé des actes dans ce CR
            $formattedCr['practitioners'] = $this->getPractitionersForCrSimplified($crId);
            
            return $formattedCr;
        }, $results);
    }
    
    /**
     * Version simplifiée de getPractitionersForCr pour éviter les références circulaires
     */
    private function getPractitionersForCrSimplified(string $crId): array
    {
        $sql = "SELECT DISTINCT ag.id, ag.nom, ag.prenom, ag.titre 
                FROM actes a
                INNER JOIN agent ag ON a.agent_id = ag.id
                INNER JOIN ufs uf_int ON a.uf_intervention_id = uf_int.id
                INNER JOIN cr cr ON uf_int.cr_id = cr.id
                WHERE cr.id = :crId AND ag.is_actif = true";
                
        $results = $this->connection->executeQuery($sql, ['crId' => $crId])->fetchAllAssociative();
        
        return array_map(function($practitioner) use ($crId) {
            $practitionerId = $practitioner['id'];
            
            return [
                '@id' => '/api/agent/' . $practitionerId,
                'nom' => $practitioner['nom'],
                'prenom' => $practitioner['prenom'],
                'titre' => $practitioner['titre'],
                'displayName' => trim(($practitioner['titre'] ? $practitioner['titre'] . ' ' : '') .
                    $practitioner['nom'] . ' ' . $practitioner['prenom'])
            ];
        }, $results);
    }
    
    /**
     * Récupérer les actes posés par un praticien dans un CR spécifique
     */
    private function getActesForPractitionerInCr(string $practitionerId, string $crId): array
    {
        $sql = "SELECT a.id, a.code, a.description, a.date_realisation, a.type_venue 
                FROM actes a
                INNER JOIN agent ag ON a.agent_id = ag.id
                INNER JOIN ufs uf_int ON a.uf_intervention_id = uf_int.id
                INNER JOIN cr cr ON uf_int.cr_id = cr.id
                WHERE ag.id = :practitionerId AND cr.id = :crId
                ORDER BY a.date_realisation DESC";
                
        $results = $this->connection->executeQuery($sql, [
            'practitionerId' => $practitionerId,
            'crId' => $crId
        ])->fetchAllAssociative();
        
        return array_map(function($acte) {
            return [
                '@id' => '/api/actes/' . $acte['id'],
                'code' => $acte['code'],
                'dateRealisation' => $acte['date_realisation'],
                'typeVenue' => $acte['type_venue']
            ];
        }, $results);
    }


    /**
     * Formater un CR pour l'API
     */
    private function formatCr(array $row): array
    {
        $crId = $row['id'];
        
        // Récupérer le pôle auquel appartient ce CR (version simplifiée)
        $pole = $this->getPoleForCrSimplified($crId);
        
        // Récupérer les praticiens qui ont posé des actes dans ce CR (version simplifiée)
        $practitioners = $this->getPractitionersForCrSimplified($crId);
        
        return [
            '@id' => '/api/crs/' . $crId,
            'crcode' => $row['crcode'],
            'libelle' => $row['libelle'],
            'pole' => $pole,
            'practitioners' => $practitioners
        ];
    }
    
    /**
     * Version simplifiée de getPoleForCr pour éviter les références circulaires
     */
    private function getPoleForCrSimplified(string $crId): ?array
    {
        $sql = "SELECT DISTINCT p.id, p.polecode, p.libelle 
                FROM cr cr
                INNER JOIN pole p ON cr.pole_id = p.id
                WHERE cr.id = :crId";
                
        $result = $this->connection->executeQuery($sql, ['crId' => $crId])->fetchAssociative();
        
        if ($result) {
            return [
                '@id' => '/api/poles/' . $result['id'],
                'poleCode' => $result['polecode'],
                'libelle' => $result['libelle']
            ];
        }
        
        return null;
    }

    /**
     * Formater les périodes appliquées
     */
    private function formatAppliedPeriods(array $periods): array
    {
        $formatted = [];
        foreach ($periods as $key => $period) {
            $formatted[$key] = $period['start'] . ' to ' . $period['end'];
        }
        return $formatted;
    }

    /**
     * Générer une clé de cache unique
     */
    private function generateCacheKey(array $filters, array $periods): string
    {
        $key = 'actes_filters_' . md5(json_encode(['filters' => $filters, 'periods' => $periods]));
        return preg_replace('/[^A-Za-z0-9_]/', '_', $key);
    }

    /**
     * Extract ID from API Platform URI (e.g., "/api/agent/123" -> 123)
     */
    private function extractIdFromApiUri(string $uri): ?string
    {
        if (preg_match('/\/api\/[^\/]+\/([^\/]+)/', $uri, $matches)) {
            return $matches[1];
        }
        return null;
    }
}



