<?php

namespace App\Factory\Activite;

use App\Domain\Enum\PeriodeType;
use App\Entity\Activite\GardesAstreintes;
use App\Factory\Praticien\AgentFactory;
use Zenstruck\Foundry\Persistence\PersistentProxyObjectFactory;

/**
 * @extends PersistentProxyObjectFactory<GardesAstreintes>
 */
final class GardesAstreintesFactory extends PersistentProxyObjectFactory
{
    public function __construct()
    {
    }

    public static function class(): string
    {
        return GardesAstreintes::class;
    }

    protected function defaults(): array|callable
    {
        $validFrom = self::faker()->dateTimeBetween('-3 years', '-6 months');
        $validTo = (self::faker()->boolean(70)) ? self::faker()->dateTimeBetween($validFrom, 'now') : null;
        
        $dateDebut = self::faker()->dateTimeBetween('-1 year', 'now');
        $dateFin = self::faker()->dateTimeBetween($dateDebut, '+1 month');

        return [
            'agent' => AgentFactory::random(),
            'dateDebut' => $dateDebut,
            'dateFin' => $dateFin,
            'totalGarde' => self::faker()->numberBetween(0, 20),
            'totalAstreinte' => self::faker()->numberBetween(0, 15),
            'date_garde' => self::faker()->dateTimeBetween($dateDebut, $dateFin),
            'type_garde' => self::faker()->randomElement(['NUIT', 'WEEK-END', 'JOUR FÉRIÉ', 'ASTREINTE']),
            'validFrom' => $validFrom,
            'validTo' => $validTo,
            'periodeType' => self::faker()->randomElement(PeriodeType::VALUES),
            'source' => self::faker()->optional()->randomElement(['CNAM', 'RPPS', 'MANUAL']),
        ];
    }

    protected function initialize(): static
    {
        return $this;
    }
}
