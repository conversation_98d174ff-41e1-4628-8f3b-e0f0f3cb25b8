<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class LoginApiController extends AbstractController
{
    #[Route('/api/login', name: 'login_api', methods: ['POST'])]
    public function index(): Response
    {
        return new Response(null, 204); // Symfony gère l'authentification automatiquement
    }

    #[Route('/api/login-ad', name: 'login_api_via_ad', methods: ['POST'])]
    public function ad_ldap(): Response
    {
        return new Response(null, 204); // Symfony gère l'authentification automatiquement
    }

}