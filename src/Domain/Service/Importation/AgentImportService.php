<?php

namespace App\Domain\Service\Importation;

use App\Command\ImportAgentCommand;
use App\Controller\Importation\Agent\AgentImportController;
use App\Controller\Importation\Agent\Dto\AgentImportDto;
use App\Entity\Praticien\Agent;
use App\Entity\Structure\EntiteJuridique;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Doctrine\ORM\EntityManagerInterface;

/**
 * Service d'importation des agents depuis le data_integrator.
 *
 * Comportement UPSERT :
 * - Création automatique : Si un agent n'a pas de hrUser, un hrUser unique est généré
 * - Mise à jour complète : Tous les champs sont mis à jour avec les nouvelles valeurs
 * - Synchronisation : L'agent local est synchronisé avec les données du data_integrator
 * - Pas de doublon : Le hrUser étant unique, impossible d'avoir des doublons
 *
 * @see AgentImportService::upsertAgent()
 * @see ImportAgentCommand
 * @see AgentImportController
 */
class AgentImportService
{
    public function __construct(
        private readonly HttpClientInterface $client,
        private readonly EntityManagerInterface $em,
        #[Autowire('%data_integrator_base_uri%')] private readonly string $baseUri,
    ) {}

    /**
     * Importe tous les agents depuis le data_integrator
     * 
     * @param int $pageSize Nombre d'agents à récupérer par page (défaut: 500)
     * @param int $batchSize Nombre d'agents à traiter avant de flush/clear (défaut: 50)
     * @param callable|null $progressCallback Fonction de callback pour suivre la progression
     * @return void
     */
    public function importAgents(int $pageSize = 500, int $batchSize = 50, ?callable $progressCallback = null): void
    {
        // Cache pour les entités juridiques
        $ejCache = [];
        
        // Clear global cache before refresh
        $clearCache = $this->client->request('GET', $this->baseUri . '/global-cache/clear')->toArray();
        if (($clearCache['success'] ?? null) !== true) {
            throw new \Exception('Erreur lors du clear du cache global [data-integrator] : ' . ($clearCache['message'] ?? ''));
        }
        
        $refresh = $this->client->request('GET', $this->baseUri . '/agents/refresh')->toArray();
        if (($refresh['success'] ?? null) !== true) {
            throw new \Exception('Erreur lors du refresh des agents : ' . ($refresh['message'] ?? ''));
        }

        $page = 1;
        $totalPages = 1;
        $totalAgents = 0;
        $processedAgents = 0;
        $batchCount = 0;
        $startTime = microtime(true);
        $peakMemory = 0;

        // Premier appel pour récupérer le nombre total d'agents
        $firstPageRes = $this->client->request('GET', $this->baseUri . '/agents', [
            'query' => ['page' => 1, 'pageSize' => $pageSize]
        ])->toArray();
        
        $totalAgents = $firstPageRes['total'] ?? 0;
        $totalPages = $firstPageRes['paginationTotal'] ?? 1;
        
        // Traitement de toutes les pages
        while ($page <= $totalPages) {
            // Ne pas refaire la requête pour la première page
            $res = ($page === 1) ? $firstPageRes : $this->client->request('GET', $this->baseUri . '/agents', [
                'query' => ['page' => $page, 'pageSize' => $pageSize]
            ])->toArray();

            $ejCode = $res['ejCode'] ?? null;
            $currentPageAgents = count($res['data']);
            
            // Précharger l'entité juridique pour cette page et la mettre en cache
            if (!empty($ejCode) && !isset($ejCache[$ejCode])) {
                $ejCache[$ejCode] = $this->em->getRepository(EntiteJuridique::class)->findOneBy(['code' => $ejCode]);
                if (!$ejCache[$ejCode]) {
                    throw new \Exception('EntiteJuridique non trouvée pour le code ' . $ejCode);
                }
            }

            foreach ($res['data'] as $agentData) {
                // Persistance avec l'entité juridique mise en cache
                $this->upsertAgent($agentData, $ejCode, $ejCache);
                
                $batchCount++;
                
                // Flush et clear périodiquement pour libérer la mémoire
                if ($batchCount >= $batchSize) {
                    $this->em->flush();
                    $this->em->clear(Agent::class); // Ne clear que les entités Agent
                    
                    // Forcer le garbage collector
                    if (gc_enabled()) {
                        gc_collect_cycles();
                    }
                    
                    $batchCount = 0;
                    
                    // Restaurer les entités en cache après le clear
                    foreach ($ejCache as $code => $entity) {
                        if (!$this->em->contains($entity)) {
                            $ejCache[$code] = $this->em->getRepository(EntiteJuridique::class)->findOneBy(['code' => $code]);
                        }
                    }
                }
            }

            $processedAgents += $currentPageAgents;
            
            // Mesurer l'utilisation de la mémoire
            $currentMemory = memory_get_usage(true) / 1024 / 1024;
            $peakMemory = max($peakMemory, $currentMemory);
            
            // Notifier la progression
            if ($progressCallback !== null) {
                $elapsedTime = microtime(true) - $startTime;
                $estimatedTotalTime = ($totalAgents > 0) ? ($elapsedTime * $totalAgents / $processedAgents) : 0;
                $eta = $estimatedTotalTime - $elapsedTime;
                
                $progressCallback($processedAgents, $totalAgents, $eta, $currentMemory, $peakMemory);
            }

            $page++;

            // Vérification de sécurité pour éviter les boucles infinies
            if ($page > 10000) {
                throw new \Exception('Trop de pages à traiter, vérifiez la pagination de l\'API [data-integrator]');
            }
        }

        // Flush final pour les entités restantes
        $this->em->flush();
        
        // Clear global cache after all agents have been processed
        $clearCache = $this->client->request('GET', $this->baseUri . '/global-cache/clear')->toArray();
        if (($clearCache['success'] ?? null) !== true) {
            throw new \Exception('Erreur lors du clear du cache global data-integrator: ' . ($clearCache['message'] ?? ''));
        }
    }

    /**
     * Met à jour ou crée un agent à partir des données fournies
     * 
     * Si l'agent n'a pas de hrUser, un hrUser unique est généré automatiquement
     * en utilisant les informations disponibles (nom, prénom, email, matricule).
     * 
     * @param array $data Données de l'agent
     * @param string|null $ejCode Code de l'entité juridique
     * @param array $ejCache Cache des entités juridiques
     * @return void
     */
    private function upsertAgent(array $data, ?string $ejCode, array &$ejCache = []): void
    {
        // Utiliser l'entité juridique du cache si disponible
        $ej = null;
        if (!empty($ejCode)) {
            $ej = $ejCache[$ejCode] ?? null;
            
            // Si pas dans le cache, charger et mettre en cache
            if (!$ej) {
                $ej = $this->em->getRepository(EntiteJuridique::class)->findOneBy(['code' => $ejCode]);
                if ($ej) {
                    $ejCache[$ejCode] = $ej;
                }
            }
        }
        
        if (!$ej) {
            throw new \Exception('EntiteJuridique non trouvée pour l\'Agent ' . ($data['hrUser'] ?? ''));
        }

        // Optimisation: utiliser le hrUser comme identifiant unique
        $hrUser = $data['hrUser'] ?? null;
        
        // Si l'agent n'a pas de hrUser, on en génère un à partir des informations disponibles
        if (empty($hrUser)) {
            // Récupérer les informations disponibles
            $nom = $data['nom'] ?? '';
            $prenom = $data['prenom'] ?? '';
            $email = $data['email'] ?? '';
            
            // Générer un hrUser unique basé sur les informations disponibles
            if (!empty($nom) && !empty($prenom)) {
                // Format: NOM_PRENOM_TIMESTAMP
                $hrUser = strtoupper(trim($nom)) . '_' . strtoupper(trim($prenom)) . '_' . time();
            } elseif (!empty($email)) {
                // Format: EMAIL_TIMESTAMP
                $hrUser = str_replace('@', '_at_', $email) . '_' . time();
            } else {
                // Aucune information disponible, générer un hrUser complètement aléatoire
                $hrUser = 'AGENT_' . uniqid() . '_' . time();
            }
            
            // Tronquer le hrUser s'il est trop long (pour éviter les problèmes de base de données)
            $hrUser = substr($hrUser, 0, 50);
            
            // Log la création d'un hrUser fictif
            error_log(sprintf(
                "Agent sans hrUser détecté. Création d'un hrUser fictif: %s. Données: %s",
                $hrUser,
                json_encode(array_filter($data))
            ));
        }

        // Recherche par hrUser unique
        $agent = $this->em->getRepository(Agent::class)->findOneBy(['hrUser' => $hrUser]);
        if (!$agent) {
            $agent = new Agent();
            $agent->setHrUser($hrUser);
            $this->em->persist($agent);
        }

        // Mise à jour des champs via les traits (optimisé pour éviter les setters inutiles)
        $this->updateAgentField($agent, 'setNom', $data['nom'] ?? '');
        $this->updateAgentField($agent, 'setPrenom', $data['prenom'] ?? '');
        $this->updateAgentField($agent, 'setTitre', $data['titre'] ?? null);
        $this->updateAgentField($agent, 'setCategorie', $data['categorie'] ?? null);
        $this->updateAgentField($agent, 'setEtablissement', $data['etablissement'] ?? null);
        $this->updateAgentField($agent, 'setEmail', $data['email'] ?? null);
        $this->updateAgentField($agent, 'setCreateurFiche', $data['createurFiche'] ?? null);
        $this->updateAgentField($agent, 'setIsAdmin', $data['isAdmin'] ?? false);
        $this->updateAgentField($agent, 'setIsAnesthesiste', $data['isAnesthesiste'] ?? false);
        
        // Construction du fullName à partir du titre, nom et prénom
        // On utilise les valeurs qui ne sont pas null
        $fullNameParts = [];
        
        // Ajouter le titre s'il existe
        if (!empty($data['titre'])) {
            $fullNameParts[] = $data['titre'];
        }
        
        // Ajouter le nom s'il existe
        if (!empty($data['nom'])) {
            $fullNameParts[] = $data['nom'];
        }
        
        // Ajouter le prénom s'il existe
        if (!empty($data['prenom'])) {
            $fullNameParts[] = $data['prenom'];
        }
        
        // Construire le fullName en joignant les parties avec un espace
        $fullName = implode(' ', $fullNameParts);
        
        // Mettre à jour le fullName uniquement s'il y a au moins une partie non vide
        if (!empty($fullName)) {
            $this->updateAgentField($agent, 'setFullName', $fullName);
        }

        // Gestion des dates (optimisée pour éviter les créations de DateTime inutiles)
        if (!empty($data['dateDepart'])) {
            $this->updateAgentDateTime($agent, 'setDateDepart', $data['dateDepart']);
        }
        if (!empty($data['dateVenue'])) {
            $this->updateAgentDateTime($agent, 'setDateVenue', $data['dateVenue']);
        }
        if (!empty($data['dateMaj'])) {
            $this->updateAgentDateTime($agent, 'setDateMaj', $data['dateMaj']);
        }

        // Assignation de l'EntiteJuridique
        $agent->setHopital($ej);
        
        // Ensure all agents have at least ROLE_USER role
        // Get current roles without the dynamically added ROLE_USER
        $currentRoles = $agent->getRoles();
        // Remove ROLE_USER if it was added dynamically by getRoles()
        $currentRoles = array_filter($currentRoles, function($role) {
            return $role !== 'ROLE_USER';
        });
        
        // If no roles or only had the dynamically added ROLE_USER
        if (empty($currentRoles)) {
            $agent->setRoles(['ROLE_USER']);
        }
    }
    
    /**
     * Met à jour un champ d'agent uniquement si la valeur a changé
     * 
     * @param Agent $agent L'agent à mettre à jour
     * @param string $setter Le nom de la méthode setter
     * @param mixed $value La nouvelle valeur
     * @return void
     */
    private function updateAgentField(Agent $agent, string $setter, $value): void
    {
        $getter = 'g' . substr($setter, 1); // Convertir setXxx en getXxx
        if (method_exists($agent, $getter) && $agent->$getter() !== $value) {
            $agent->$setter($value);
        }
    }
    
    /**
     * Met à jour un champ DateTime d'agent uniquement si la valeur a changé
     * 
     * @param Agent $agent L'agent à mettre à jour
     * @param string $setter Le nom de la méthode setter
     * @param string $dateString La chaîne de date
     * @return void
     */
    private function updateAgentDateTime(Agent $agent, string $setter, string $dateString): void
    {
        $getter = 'g' . substr($setter, 1); // Convertir setXxx en getXxx
        if (method_exists($agent, $getter)) {
            $currentDate = $agent->$getter();
            $newDate = new \DateTime($dateString);
            
            // Comparer les dates uniquement si la date actuelle existe
            if (!$currentDate || $currentDate->format('Y-m-d H:i:s') !== $newDate->format('Y-m-d H:i:s')) {
                $agent->$setter($newDate);
            }
        }
    }
}