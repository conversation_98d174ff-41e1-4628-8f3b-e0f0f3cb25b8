<?php

namespace App\Mapper\Service;

use App\ApiResource\Structure\PoleDto;
use App\ApiResource\Structure\ServiceDto;
use App\Entity\Structure\Service;
use Symfonycasts\MicroMapper\AsMapper;
use Symfonycasts\MicroMapper\MapperInterface;
use Symfonycasts\MicroMapper\MicroMapperInterface;

#[AsMapper(from: Service::class, to: ServiceDto::class)]
class ServiceEntityToDtoMapper implements MapperInterface
{
    public function __construct(private MicroMapperInterface $microMapper) {}

    public function load(object $from, string $toClass, array $context): object
    {
        $service = $from;
        assert($service instanceof Service);

        $dto = new ServiceDto();
        $dto->id = $service->getId(); // On charge uniquement l'ID ici

        return $dto;
    }

    public function populate(object $from, object $to, array $context): object
    {
        $service = $from;
        $dto = $to;
        assert($service instanceof Service);
        assert($dto instanceof ServiceDto);

        $dto->etab = $service->getEtab();
        $dto->secode = $service->getSecode();
        $dto->datdeb = $service->getDatdeb();
        $dto->datfin = $service->getDatfin();
        $dto->libelle = $service->getLibelle();
        $dto->cdcode = $service->getCdcode();
        $dto->tacode = $service->getTacode();
        $dto->pfuser = $service->getPfuser();
        $dto->dmdacre = $service->getDmdacre();
        $dto->dmdamaj = $service->getDmdamaj();

        // Champs d'horodatage
        $dto->isActif = $service->isActive();
        $dto->validFrom = $service->getValidFrom();
        $dto->validTo = $service->getValidTo();
        $dto->periodeType = $service->getPeriodeType();
        $dto->source = $service->getSource();

        // Convertir Pole en IRI API Platform
//        if ($service->getPole()) {
//            $dto->pole = $this->microMapper->map($service->getPole(), PoleDto::class, [
//                MicroMapperInterface::MAX_DEPTH => 0, // On ne charge que l'ID
//            ]);
//        }

        return $dto;
    }
}
