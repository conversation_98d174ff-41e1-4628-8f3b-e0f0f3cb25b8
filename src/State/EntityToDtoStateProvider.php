<?php
// bin/console make:state-provider
namespace App\State;

use ApiPlatform\Doctrine\Orm\Paginator;
use ApiPlatform\Doctrine\Orm\State\CollectionProvider;
use ApiPlatform\Doctrine\Orm\State\ItemProvider;
use ApiPlatform\Metadata\CollectionOperationInterface;
use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\Pagination\TraversablePaginator;
use ApiPlatform\State\ProviderInterface;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Contracts\Cache\CacheInterface;
use Symfony\Contracts\Cache\ItemInterface;
use Symfonycasts\MicroMapper\MicroMapperInterface;

class EntityToDtoStateProvider implements ProviderInterface
{
    public function __construct(
        #[Autowire(service: CollectionProvider::class)] private ProviderInterface $collectionProvider,
        #[Autowire(service: ItemProvider::class)] private ProviderInterface $itemProvider,
        private MicroMapperInterface $microMapper,
        private CacheInterface $cache,
        private EntityManagerInterface $em,
        private RequestStack $requestStack,
        #[Autowire('%env(int:API_CACHE_TTL)%')] private int $apiCacheTtl = 3600
    )
    {
    }

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): object|array|null
    {
        $resourceClass = $operation->getClass();

        // Gestion du filtre par ejcode
        // i.e : on reçoit une requête avec un paramètre ejcode api/{ejcode}/actes.
        if (isset($uriVariables['ejcode'])) {
            // Dans provide()
            if (isset($uriVariables['ejcode'])) {
                $ej = $this->em->getRepository(\App\Entity\Structure\EntiteJuridique::class)->findOneBy(['code' => $uriVariables['ejcode']]);

                if (!$ej) {
                    throw new \RuntimeException('Entité juridique non trouvée: ' . $uriVariables['ejcode']);
                }
                $context = $this->applyEjcodeFilter($resourceClass, $ej, $context);
            }
        }

        if ($operation instanceof CollectionOperationInterface) {
            $page = $context['pagination']["page"] ?? 1;
            $perPage = $context['pagination']["items_per_page"] ?? 10;

            $resourceKey = preg_replace('/[{}()\/\\\\@:]/', '_', $resourceClass);

            $filters = $context['filters'] ?? [];
            ksort($filters);
            $filtersHash = $filters ? md5(json_encode($filters)) : 'no_filters';
            
            // Include ejcode in cache key if present to avoid cache conflicts
            $ejcodeKey = isset($uriVariables['ejcode']) ? '_ej_' . $uriVariables['ejcode'] : '';
            
            // Include period parameters in cache key if present
            $periodParams = '';
            $request = $this->requestStack->getCurrentRequest();
            if ($request) {
                $periodKeys = [];
                foreach ($request->query->all() as $key => $value) {
                    if (preg_match('/^p\d+(Start|End)$/', $key)) {
                        $periodKeys[$key] = $value;
                    }
                }
                if (!empty($periodKeys)) {
                    ksort($periodKeys);
                    $periodParams = '_periods_' . md5(json_encode($periodKeys));
                }
            }

            $cacheKey = sprintf(
                'supra_api_cache_collection_%s_p%d_n%d_%s%s%s',
                $resourceKey,
                $page,
                $perPage,
                $filtersHash,
                $ejcodeKey,
                $periodParams
            );
            $cacheKey = preg_replace('/[^A-Za-z0-9_]/', '_', $cacheKey);

            return $this->cache->get($cacheKey, function (ItemInterface $item) use ($operation, $uriVariables, $context, $resourceClass) {
                $item->expiresAfter($this->apiCacheTtl);

                $entities = $this->collectionProvider->provide($operation, $uriVariables, $context);

                // Vérification du type de $entities
                if ($entities instanceof Paginator) {
                    $dtos = [];
                    foreach ($entities as $entity) {
                        $dtos[] = $this->mapEntityToDto($entity, $resourceClass);
                    }
                    return new TraversablePaginator(
                        new \ArrayIterator($dtos),
                        $entities->getCurrentPage(),
                        $entities->getItemsPerPage(),
                        $entities->getTotalItems()
                    );
                }

                // Si la pagination est désactivée, gérer comme une collection simple
                if (is_iterable($entities)) {
                    $dtos = [];
                    foreach ($entities as $entity) {
                        $dtos[] = $this->mapEntityToDto($entity, $resourceClass);
                    }
                    return $dtos;
                }

                throw new \RuntimeException('Expected Paginator or iterable, got ' . gettype($entities));
            });
        }

        $entity = $this->itemProvider->provide($operation, $uriVariables, $context);
        if (!$entity) {
            return null;
        }
        return $this->mapEntityToDto($entity, $resourceClass);
    }

    private function mapEntityToDto(object $entity, string $resourceClass): object
    {
        return $this->microMapper->map($entity, $resourceClass);
    }
    private function applyEjcodeFilter(string $resourceClass, object $ej, array $context): array
    {
        switch ($resourceClass) {
            case \App\Entity\Activite\Actes::class:
                $context['filters']['or'] = [
                    ['agent.hopital' => $ej->getId()],
                    ['ufPrincipal.cr.pole.hopital' => $ej->getId()]
                ];
                break;
            case \App\Entity\Structure\Pole::class:
            case \App\Entity\Structure\Service::class:
                $context['filters']['hopital'] = $ej->getId();
                break;
            case \App\Entity\Structure\Ufs::class:
                $context['filters']['cr.pole.hopital'] = $ej->getId();
                break;
            case \App\Entity\Structure\Cr::class:
                $context['filters']['pole.hopital'] = $ej->getId();
                break;
            case \App\Entity\Praticien\Agent::class:
                $context['filters']['hopital'] = $ej->getId();
                break;
            case \App\Entity\Activite\GardesAstreintes::class:
                $context['filters']['agent.hopital'] = $ej->getId();
                break;
            case \App\Entity\Activite\Liberal::class:
                $context['filters']['agent.hopital'] = $ej->getId();
                break;
            case \App\Entity\Activite\Sigaps::class:
                $context['filters']['agent.hopital'] = $ej->getId();
                break;
            default:
                break;
        }
        return $context;
    }}
