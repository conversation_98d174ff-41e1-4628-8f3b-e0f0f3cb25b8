<?php
namespace App\Domain\Service;

use App\Entity\Praticien\Agent;
use App\Entity\Structure\EntiteJuridique;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\{JsonResponse, Request, Response};
use Psr\Log\LoggerInterface;
use Symfony\Component\Security\Core\Exception\AuthenticationException;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Http\Authenticator\AbstractAuthenticator;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\UserBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Credentials\CustomCredentials;
use Symfony\Component\Security\Http\Authenticator\Passport\Passport;
use Symfony\Component\DependencyInjection\Attribute\Autowire;

/**
 * LDAP Authentication service
 * 
 * Handles authentication against LDAP directory and user management
 */
// @todo a confirmer avec le metier s'il faut laisser un utilisateur d'un hopital x
//  a pouvoir se connecter à un hopital y, car j'ai ca actuellement
class AuthLdap extends AbstractAuthenticator
{
    private array $ldapData = []; // Stockage temporaire des données LDAP

    public function __construct(
        private EntityManagerInterface $em,
        private JwtTokenService $jwtTokenService,
        private LdapService $ldapService,
        private UserManager $userManager,
        private RequestValidator $requestValidator,
        #[Autowire('%kernel.environment%')] private string $environment // Injection automatique de l'environnement
    ) {}

    public function supports(Request $request): bool
    {
        return $request->getPathInfo() === '/api/login-ad' && $request->isMethod('POST');
    }

    public function authenticate(Request $request): Passport
    {
        try {
            $credentials = $this->requestValidator->extractAuthCredentials($request);
            $username = $credentials['username'];
            $password = $credentials['password'];
            $hopitalCode = $credentials['hopitalCode'];

            return new Passport(
                new UserBadge($username, function($username) use ($hopitalCode) {
                    $hopital = $this->findAndValidateHospital($hopitalCode);

                    //  Utilisateur virtuel minimal
                    $virtualAgent = new Agent();
                    $virtualAgent->setEmail($username); // Utiliser comme identifiant temporaire
                    // Ne pas persister, juste pour l'authentification

                    return $virtualAgent;
                }),
                new CustomCredentials(
                    function($password, Agent $virtualAgent) use ($username, $hopitalCode) {
                        return $this->verifyLdapAndStoreData($password, $username, $hopitalCode);
                    },
                    $password
                )
            );
        } catch (\Exception $e) {
            throw new AuthenticationException('Erreur lors de l\'authentification: ' . $e->getMessage());
        }
    }

    /**
     *  Vérifier LDAP et stocker les données temporairement
     */
    private function verifyLdapAndStoreData(string $password, string $username, string $hopitalCode): bool
    {
        $hopital = $this->findAndValidateHospital($hopitalCode);
        $ldapConn = null;

        try {
            // Connexion et vérification LDAP
            $ldapConn = $this->ldapService->connect($hopital->getLdapHost());
            $this->ldapService->bindWithServiceAccount($ldapConn, null, null, $hopital);

            $entries = $this->ldapService->searchUser($ldapConn, $username, $hopital->getLdapBaseDn());

            if (empty($entries) || !isset($entries[0]['dn'])) {
                throw new AuthenticationException('Utilisateur non trouvé dans LDAP');
            }

            $userDn = $entries[0]['dn'];
            $this->ldapService->bindWithUserCredentials($ldapConn, $userDn, $password);

            // Récupérer les attributs utilisateur
            $this->ldapService->bindWithServiceAccount($ldapConn, null, null, $hopital);
            $entries = $this->ldapService->searchUser($ldapConn, $username, $hopital->getLdapBaseDn());
            $attributes = $this->ldapService->extractUserAttributes($entries);

            //  Stocker temporairement les données LDAP
            $this->ldapData = [
                'username' => $username,
                'attributes' => $attributes,
                'password' => $password,
                'hopital' => $hopital
            ];

            return true;

        } finally {
            if ($ldapConn) {
                $this->ldapService->closeConnection($ldapConn);
            }
        }
    }

    /**
     *  MAINTENANT on crée/met à jour l'utilisateur réel
     */
    /**
     *  OPTIMISÉ : MAINTENANT on crée/met à jour l'utilisateur réel
     */
    public function onAuthenticationSuccess(Request $request, TokenInterface $token, string $firewallName): ?Response
    {
        try {
            // Utiliser les données LDAP stockées
            $ldapData = $this->ldapData;

            if (empty($ldapData)) {
                throw new \RuntimeException('Données LDAP manquantes');
            }

            if (!isset($ldapData['username'], $ldapData['attributes'], $ldapData['hopital'])) {
                throw new \RuntimeException('Données LDAP corrompues');
            }

            //  OPTIMISÉ : Récupérer les infos de création/initialisation
            $result = $this->userManager->findOrCreateUser($ldapData['username'], $ldapData['hopital']);
            $realUser = $result['agent'];
            $isNew = $result['isNew'];
            $needsLdapInit = $result['needsLdapInit'];

            //  LOGIQUE OPTIMISÉE AVEC isLdapInitialized
            if ($needsLdapInit) {
                // Première connexion OU utilisateur existant non initialisé LDAP
                $this->userManager->updateUserFromLdapAttributes($realUser, $ldapData['attributes']);
                $this->userManager->updateUserPassword($realUser, $ldapData['password']);

                //  Marquer comme initialisé LDAP
                $this->userManager->markAsLdapInitialized($realUser);

                $this->userManager->saveUser($realUser);

                // User created or reactivated and initialized

            } else {
                //  OPTIMISATION : Utilisateur déjà initialisé - mise à jour minimale
                // Juste le mot de passe pour la sécurité
                $this->userManager->updateUserPassword($realUser, $ldapData['password']);
                $this->userManager->saveUser($realUser);

                // Initialized user - minimal update (password only)
            }

            // Stats de connexion (toujours mis à jour)
            $this->userManager->updateConnectionStats($realUser);

            $code_ej = $realUser->getHopital()?->getCode();

            if (!$code_ej) {
                throw new \RuntimeException('Code hôpital manquant pour l\'utilisateur');
            }

            $jwtToken = $this->jwtTokenService->generate($realUser, $code_ej);

            //  Nettoyer les données temporaires
            $this->ldapData = [];

            // Préparer la réponse de base
            $response = [
                'success' => true,
                'message' => 'Authentification LDAP réussie',
                'token' => $jwtToken
            ];

            // En développement seulement : inclure les données utilisateur pour faciliter le debug
            if ($this->environment === 'dev') {
                $response['user'] = [
                    'matricule' => $realUser->getMatricule(),
                    'hrUser' => $realUser->getHrUser(),
                    'email' => $realUser->getEmail(),
                    'nom' => $realUser->getNom(),
                    'prenom' => $realUser->getPrenom(),
                    'id' => $realUser->getId(),
                    'hopital_code' => $code_ej
                ];
            }

            return new JsonResponse($response);

        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Erreur lors de la génération du token',
                'error' => $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
    public function onAuthenticationFailure(Request $request, AuthenticationException $exception): ?Response
    {
        //  Nettoyer les données en cas d'échec
        $this->ldapData = [];

        return new JsonResponse([
            'success' => false,
            'message' => 'Échec de l\'authentification LDAP',
            'error' => $exception->getMessage()
        ], Response::HTTP_UNAUTHORIZED);
    }

    private function findAndValidateHospital(string $hopitalCode): EntiteJuridique
    {
        $hopital = $this->em->getRepository(EntiteJuridique::class)
            ->findOneBy(['code' => $hopitalCode]);

        if (!$hopital) {
            throw new AuthenticationException('Hôpital (EJ) non trouvé');
        }

        if (!$hopital->isLdapActive()) {
            throw new AuthenticationException('LDAP désactivé pour cet hôpital');
        }

        return $hopital;
    }
}
