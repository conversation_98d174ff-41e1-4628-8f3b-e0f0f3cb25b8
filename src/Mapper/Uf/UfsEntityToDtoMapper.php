<?php

namespace App\Mapper\Uf;

use App\ApiResource\Structure\CrDto;
use App\ApiResource\Structure\PoleDto;
use App\ApiResource\Structure\ServiceDto;
use App\ApiResource\Structure\UfsDto;
use App\Entity\Structure\Ufs;
use App\Repository\UfsRepository;
use Symfonycasts\MicroMapper\AsMapper;
use Symfonycasts\MicroMapper\MapperInterface;
use Symfonycasts\MicroMapper\MicroMapperInterface;

#[AsMapper(from: Ufs::class, to: UfsDto::class)]
class UfsEntityToDtoMapper implements MapperInterface
{
    public function __construct(
        private MicroMapperInterface $microMapper,
        private UfsRepository $ufsRepository
    ) {}

    public function load(object $from, string $toClass, array $context): object
    {
        $ufs = $from;
        assert($ufs instanceof Ufs);

        $dto = new UfsDto();
        $dto->id = $ufs->getId(); // On charge uniquement l'ID ici

        return $dto;
    }

    public function populate(object $from, object $to, array $context): object
    {
        $ufs = $from;
        $dto = $to;
        assert($ufs instanceof Ufs);
        assert($dto instanceof UfsDto);

        $dto->etab = $ufs->getEtab();
        $dto->ufcode = $ufs->getUfcode();
        $dto->datdeb = $ufs->getDatdeb();
        $dto->datfin = $ufs->getDatfin();
        $dto->datclos = $ufs->getDatclos();
        $dto->libelle = $ufs->getLibelle();
        $dto->tacode = $ufs->getTacode();
        $dto->cdcode = $ufs->getCdcode();
        $dto->lettrebudg = $ufs->getLettrebudg();
        $dto->secode = $ufs->getSecode();
        $dto->cgcode = $ufs->getCgcode();
        $dto->umcode = $ufs->getUmcode();
        $dto->pfuser = $ufs->getPfuser();
        $dto->dmdacre = $ufs->getDmdacre();
        $dto->dmdamaj = $ufs->getDmdamaj();
        $dto->topmedical = $ufs->getTopmedical();
        $dto->crcode = $ufs->getCrcode();
        $dto->sacode = $ufs->getSacode();

        // Champs d'horodatage
        $dto->isActif = $ufs->isActive();
        $dto->validFrom = $ufs->getValidFrom();
        $dto->validTo = $ufs->getValidTo();
        $dto->periodeType = $ufs->getPeriodeType();
        $dto->source = $ufs->getSource();

        // Convertir Cr en IRI API Platform
        if ($ufs->getCr()) {
            $dto->cr = $this->microMapper->map($ufs->getCr(), CrDto::class, [
                MicroMapperInterface::MAX_DEPTH => 0, // On ne charge que l'ID
            ]);
        }

        // Convertir Service en IRI API Platform
        if ($ufs->getService()) {
            $dto->service = $this->microMapper->map($ufs->getService(), ServiceDto::class, [
                MicroMapperInterface::MAX_DEPTH => 0, // On ne charge que l'ID
            ]);
        }

        if ($pole = $this->ufsRepository->findPoleByUf($ufs)) {
            $dto->pole = $this->microMapper->map($pole, PoleDto::class, [
                MicroMapperInterface::MAX_DEPTH => 0,
            ]);
        }

        return $dto;
    }
}
