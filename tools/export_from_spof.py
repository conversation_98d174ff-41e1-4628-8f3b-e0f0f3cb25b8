import cx_Oracle
import csv

# Informations de connexion
host = "odyhis.world"
port = "1521"  # Port par défaut d'Oracle
service_name = "odyhis.world"  # Service Oracle
username = "CLICKVIEW"
password = "QLICK768"

# Construction du DSN Oracle
dsn_tns = cx_Oracle.makedsn(host, port, service_name=service_name)

# Connexion à la base Oracle
try:
    conn = cx_Oracle.connect(user=username, password=password, dsn=dsn_tns)
    print("✅ Connexion réussie à Oracle")
except cx_Oracle.DatabaseError as e:
    print("❌ Erreur de connexion :", e)
    exit()

# Définition de la requête SQL
sql = """
SELECT
    CODE_ACTE,
    COALESCE(LIBELLEC_A, LIBELLEL_A) AS description_acte,
    TYPEACTE,
    ANNEE_ACTE,
    MOIS_ACTE,
    TO_DATE(ANNEE_ACTE || '-' || MOIS_ACTE || '-01', 'YYYY-MM-DD') AS date_realisation_estimee,
    NB,
    MATPAIEPRINC,
    UFPRINCIPAL
FROM homere.EX_MOIS_ACTES2
WHERE TYPEACTE IN ('CCAM', 'NGAP', 'LABO')
  AND ANNEE_ACTE = 2024
ORDER BY ANNEE_ACTE DESC, MOIS_ACTE DESC;
"""

# Exécution de la requête
cursor = conn.cursor()
cursor.execute(sql)

# Export des résultats en CSV
output_file = "export_actes_2024.csv"
with open(output_file, "w", newline="", encoding="utf-8") as file:
    writer = csv.writer(file)

    # Écriture des en-têtes
    writer.writerow([i[0] for i in cursor.description])

    # Écriture des lignes de données
    writer.writerows(cursor)

# Fermeture des connexions
cursor.close()
conn.close()

print(f"✅ Export terminé : {output_file}")
