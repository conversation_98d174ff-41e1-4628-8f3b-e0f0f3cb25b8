<?php

// This is a simple test script to verify the calculation of semaineIso from dateRealisation

function testSemaineIsoCalculation() {
    echo "Testing semaineIso calculation from dateRealisation\n";
    echo "==================================================\n\n";

    // Test cases with different dates
    $testCases = [
        [
            'description' => 'End of August 2025',
            'dateRealisation' => '2025-08-27T14:30:00+02:00',
            'expectedWeek' => 35
        ],
        [
            'description' => 'Beginning of September 2025',
            'dateRealisation' => '2025-09-01T10:00:00+02:00',
            'expectedWeek' => 36
        ],
        [
            'description' => 'End of December 2025 (week spanning two years)',
            'dateRealisation' => '2025-12-30T08:00:00+02:00',
            'expectedWeek' => 1
        ],
        [
            'description' => 'Beginning of January 2025 (week spanning two years)',
            'dateRealisation' => '2025-01-01T12:00:00+02:00',
            'expectedWeek' => 1
        ]
    ];
    
    foreach ($testCases as $index => $case) {
        echo "Test Case " . ($index + 1) . ": " . $case['description'] . "\n";
        echo "Input dateRealisation: " . $case['dateRealisation'] . "\n";
        
        $date = new \DateTime($case['dateRealisation']);
        $calculatedWeek = (int)$date->format('W');
        
        echo "Calculated semaineIso: " . $calculatedWeek . "\n";
        echo "Expected semaineIso: " . $case['expectedWeek'] . "\n";
        
        if ($calculatedWeek === $case['expectedWeek']) {
            echo "✓ Test passed\n";
        } else {
            echo "✗ Test failed\n";
        }
        
        echo "\n";
    }
    
    echo "Note: The ISO week number is calculated according to ISO 8601:\n";
    echo "- Weeks start on Monday\n";
    echo "- The first week of the year contains the first Thursday of the year\n";
    echo "- This means that some days from the end of December might belong to week 1 of the next year\n";
    echo "- And some days from the beginning of January might belong to the last week of the previous year\n";
}

// Run the test
testSemaineIsoCalculation();