<?php

namespace App\ApiResource\Activite;

use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Get;
use App\State\UfSingleActeStatsStateProvider;

#[ApiResource(
    operations: [
        new Get(
            uriTemplate: '/actes/{id}/uf-single-stats',
            provider: UfSingleActeStatsStateProvider::class
        )
    ]
)]
class UfSingleActeStatsResource
{
    public function __construct(
        public readonly string $id
    ) {}
}
