<?php
namespace App\Entity\Praticien\Trait;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

trait DamTrait
{
    #[ORM\Column(length: 255, nullable: true)]
    private ?string $nomNaissance = null;

    #[ORM\Column(length: 1, nullable: true)]
    private ?string $sexe = null;

    public function getNomNaissance(): ?string
    {
        return $this->nomNaissance;
    }

    public function setNomNaissance(?string $nomNaissance): static
    {
        $this->nomNaissance = $nomNaissance;
        return $this;
    }

    public function getSexe(): ?string
    {
        return $this->sexe;
    }

    public function setSexe(?string $sexe): static
    {
        $this->sexe = $sexe;
        return $this;
    }
}