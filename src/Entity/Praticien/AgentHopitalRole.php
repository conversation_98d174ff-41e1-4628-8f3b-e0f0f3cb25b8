<?php

namespace App\Entity\Praticien;

use App\Domain\Enum\AgentRoleType;
use App\Entity\Base\BaseEntity;
use App\Entity\Structure\EntiteJuridique;
use App\Entity\Structure\Tenant;
use App\Repository\AgentHopitalRoleRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: AgentHopitalRoleRepository::class)]
class AgentHopitalRole extends BaseEntity
{

    #[ORM\ManyToOne(targetEntity: Agent::class, inversedBy: "hopitalRoles")]
    #[ORM\JoinColumn(nullable: false)]
    private Agent $agent;

    #[ORM\ManyToOne(targetEntity: EntiteJuridique::class, inversedBy: "agentRoles")]
    #[ORM\JoinColumn(nullable: false)]
    private EntiteJuridique $hopital;

    #[ORM\Column(length: 50, nullable: true)]
    private ?string $role = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE,nullable: true)]
    private \DateTimeInterface $dateAffectation;


    // Dans AgentHopitalRole.php - Compatible PHP 8.4
    public function __construct(?Agent $agent = null, ?EntiteJuridique $hopital = null, ?string $role = null)
    {
        parent::__construct();

        if ($agent !== null) {
            if ($role !== null && !in_array($role, AgentRoleType::ALL_ROLES, true)) {
                throw new \InvalidArgumentException("Rôle invalide : $role");
            }

            $this->agent = $agent;
            $this->hopital = $hopital;
            $this->role = $role;
            $this->dateAffectation = new \DateTimeImmutable('now');
        }
        // Si $agent est null, les propriétés seront définies via le formulaire EasyAdmin
    }

    //  Ajout d'une méthode `initialize()` pour permettre EasyAdmin d'instancier l'objet

    public static function initialize(EntityManagerInterface $entityManager): self
    {
        $agent = $entityManager->getRepository(Agent::class)->findOneBy([]);
        $hopital = $entityManager->getRepository(EntiteJuridique::class)->findOneBy([]);

        if (!$agent || !$hopital) {
            throw new \LogicException("Aucun Agent ou EntiteJuridique trouvé pour l'initialisation.");
        }

        return new self($agent, $hopital, AgentRoleType::USER);
    }

    public function getRole(): ?string
    {
        return $this->role;
    }

    public function setRole(?string $role): static
    {
        $this->role = $role;

        return $this;
    }

    public function getAgent(): ?Agent
    {
        return $this->agent ?? null; // Retourne null si l'agent n'est pas encore défini c'est pour l'Admin
    }
    
    public function setAgent(Agent $agent): static
    {
        $this->agent = $agent;
        
        return $this;
    }

    public function getHopital(): EntiteJuridique
    {
        return $this->hopital;
    }
    
    public function setHopital(EntiteJuridique $hopital): static
    {
        $this->hopital = $hopital;
        
        return $this;
    }

    public function getDateAffectation(): \DateTimeInterface
    {
        return $this->dateAffectation;
    }

    public function setDateAffectation(\DateTimeInterface $dateAffectation): self
    {
        $this->dateAffectation = $dateAffectation;
        return $this;
    }

    public function __toString(): string
    {
        return sprintf(
            '%s - %s (%s)',
            $this->agent ? $this->agent->getNom() : 'Agent inconnu',
            $this->hopital ? $this->hopital->getNom() : 'EntiteJuridique inconnu',
            $this->role ?? 'Rôle non défini'
        );
    }

}
