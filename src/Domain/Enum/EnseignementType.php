<?php

namespace App\Domain\Enum;


enum EnseignementType: string
{
    case CM = 'CM';
    case TD = 'TD';
    case TP = 'TP';
    case AUTRE = 'AUTRE';

    public static function choices(): array
    {
        return [
            'Cours magistral' => self::CM->value,
            'Travaux dirigés' => self::TD->value,
            'Travaux pratiques' => self::TP->value,
            'Autre' => self::AUTRE->value,
        ];
    }

    public static function badges(): array
    {
        return [
            self::CM->value => 'primary',
            self::TD->value => 'success',
            self::TP->value => 'warning',
            self::AUTRE->value => 'dark',
        ];
    }

    public static function isValid(string $value): bool
    {
        return in_array($value, array_column(self::cases(), 'value'), true);
    }
}