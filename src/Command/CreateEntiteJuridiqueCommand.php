<?php

namespace App\Command;

use App\Entity\Structure\EntiteJuridique;
use App\Entity\Structure\Tenant;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

/**
 * Commande pour créer une nouvelle EntiteJuridique.
 * @Command php bin/console app:create:ej tenant-code code nom [email] [telephone] [adresse]
 * i.e php bin/console app:create:ej TENANT-001 EJ-001 "Entité Juridique 1" "
 */
//  php bin/console app:create:ej dtnib-supra EJ-001 "CHRU de nancy"

#[AsCommand(
    name: 'app:create:ej',
    description: 'Crée une nouvelle EntiteJuridique',
)]
class CreateEntiteJuridiqueCommand extends Command
{
    public function __construct(
        private readonly EntityManagerInterface $em,
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addArgument('tenant-code', InputArgument::REQUIRED, 'Code du tenant existant')
            ->addArgument('code', InputArgument::REQUIRED, 'Code de l\'entité juridique')
            ->addArgument('nom', InputArgument::REQUIRED, 'Nom de l\'entité juridique')
            ->addArgument('email', InputArgument::OPTIONAL, 'Email de l\'entité juridique')
            ->addArgument('telephone', InputArgument::OPTIONAL, 'Téléphone de l\'entité juridique')
            ->addArgument('adresse', InputArgument::OPTIONAL, 'Adresse de l\'entité juridique');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        try {
            // Vérification si le tenant existe
            $tenant = $this->em->getRepository(Tenant::class)
                ->findOneBy(['code' => $input->getArgument('tenant-code')]);

            if (!$tenant) {
                $io->error('Le Tenant spécifié n\'existe pas. Créez-le d\'abord avec app:create:tenant');
                return Command::FAILURE;
            }

            // Vérification si l'EJ existe déjà
            $existingEj = $this->em->getRepository(EntiteJuridique::class)
                ->findOneBy(['code' => $input->getArgument('code')]);

            if ($existingEj) {
                $io->error('Une EntiteJuridique avec ce code existe déjà');
                return Command::FAILURE;
            }

            // Création de l'EJ
            $ej = new EntiteJuridique();
            $ej->setCode($input->getArgument('code'));
            $ej->setNom($input->getArgument('nom'));

            // Gestion de la relation bidirectionnelle
            $ej->setTenant($tenant);
            $tenant->addHopital($ej);  // Ajout de cette ligne pour maintenir la bidirectionnalité

            if ($input->getArgument('email')) {
                $ej->setEmail($input->getArgument('email'));
            }
            if ($input->getArgument('telephone')) {
                $ej->setTelephone($input->getArgument('telephone'));
            }
            if ($input->getArgument('adresse')) {
                $ej->setAdresse($input->getArgument('adresse'));
            }

            $this->em->persist($ej);
            $this->em->flush();

            $io->success('EntiteJuridique créée avec succès');
            return Command::SUCCESS;

        } catch (\Throwable $e) {
            $io->error('Erreur lors de la création : ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}