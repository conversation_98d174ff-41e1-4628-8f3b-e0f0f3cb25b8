<?php

namespace App\Domain\Extension;

use ApiPlatform\Doctrine\Orm\Extension\QueryCollectionExtensionInterface;
use ApiPlatform\Doctrine\Orm\Util\QueryNameGeneratorInterface;
use Doctrine\ORM\QueryBuilder;
use Symfony\Component\HttpFoundation\RequestStack;
use App\Entity\Praticien\AgentUfs;

/**
 * Extension Doctrine pour filtrer la ressource /api/affectations sur plusieurs périodes dynamiques.
 *
 * Cette extension permet de retourner toutes les affectations d'agents à des UFs :
 *  - dont la période de validité [date_debut, date_fin] recouvre au moins l'une des périodes demandées (p1, p2, p3, ...)
 *
 * Exemple d'appel frontend :
 *   [GET] /api/affectations?p1Start=2024-10-01&p1End=2025-03-31&p2Start=2023-10-01&p2End=2024-03-31&p3Start=2022-10-01&p3End=2022-03-31
 *
 * Logique :
 *   Pour chaque période, l'affectation est retenue si :
 *     date_debut <= periodeEnd
 *     AND (date_fin IS NULL OR date_fin >= periodeStart)
 *   (on combine toutes les périodes avec OR)
 *
 * Cette extension ne fait rien si aucun paramètre pXStart/pXEnd n'est passé dans la requête.
 */
class AgentUfsPeriodeExtension implements QueryCollectionExtensionInterface
{
    /**
     * @param RequestStack $requestStack Pour accéder à la requête courante (query params)
     */
    public function __construct(
        private readonly RequestStack $requestStack
    ) {}

    /**
     * Ajoute dynamiquement des conditions WHERE sur le QueryBuilder pour filtrer les affectations sur plusieurs périodes.
     *
     * @param QueryBuilder $qb
     * @param QueryNameGeneratorInterface $queryNameGenerator
     * @param string $resourceClass
     * @param \ApiPlatform\Metadata\Operation|null $operation
     * @param array $context
     */
    public function applyToCollection(
        QueryBuilder $qb,
        QueryNameGeneratorInterface $queryNameGenerator,
        string $resourceClass,
        ?\ApiPlatform\Metadata\Operation $operation = null,
        array $context = []
    ): void {
        // Ne s'applique que sur la ressource AgentUfs (évite de filtrer sur d'autres entités)
        if ($resourceClass !== AgentUfs::class) {
            return;
        }

        $request = $this->requestStack->getCurrentRequest();
        if (!$request) {
            return;
        }

        $periods = [];

        // Récupère dynamiquement tous les couples pXStart/pXEnd présents dans la requête
        // Exemple : p1Start/p1End, p2Start/p2End, ...
        foreach ($request->query->all() as $key => $value) {
            if (preg_match('/^p\d+Start$/', $key)) {
                $index = substr($key, 1, -5); // extrait le numéro (ex: '1' dans 'p1Start')
                $start = $value;
                $endKey = "p{$index}End";
                $end = $request->query->get($endKey);
                if ($end) {
                    $periods[] = [$start, $end];
                }
            }
        }

        // S'il n'y a pas de période passée, on ne filtre rien
        if (count($periods) === 0) {
            return;
        }

        // On va construire un WHERE (... OR ... OR ...) pour toutes les périodes
        $orX = $qb->expr()->orX();
        $i = 0;
        foreach ($periods as [$start, $end]) {
            $paramStart = "period_start_$i";
            $paramEnd   = "period_end_$i";

            // Pour chaque période, on ajoute la condition :
            // date_debut <= periodeEnd
            // AND (date_fin IS NULL OR date_fin >= periodeStart)
            $orX->add(
                $qb->expr()->andX(
                    $qb->expr()->orX(
                        $qb->expr()->isNull('o.date_debut'),
                        $qb->expr()->lte('o.date_debut', ":$paramEnd")
                    ),
                    $qb->expr()->orX(
                        $qb->expr()->isNull('o.date_fin'),
                        $qb->expr()->gte('o.date_fin', ":$paramStart")
                    )
                )
            );

            $qb->setParameter($paramStart, $start);
            $qb->setParameter($paramEnd, $end);
            $i++;
        }

        // Ajoute la condition globale au QueryBuilder (WHERE (... OR ... OR ...))
        $qb->andWhere($orX);
    }
}