<?php

namespace App\Security;

use App\Domain\Service\JwtTokenService;
use Psr\Log\LoggerInterface;
use Symfony\Component\Security\Core\Exception\BadCredentialsException;
use Symfony\Component\Security\Http\AccessToken\AccessTokenHandlerInterface;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\UserBadge;

/**
 * Handler for JWT tokens in API requests.
 * 
 * This class validates JWT tokens from the Authorization header and extracts the user identifier.
 */
class ApiTokenHandler implements AccessTokenHandlerInterface
{
    public function __construct(
        private readonly JwtTokenService $jwtTokenService,
        private readonly ?LoggerInterface $logger = null
    ) {
    }

    /**
     * Validates the JWT token and returns a UserBadge with the user identifier.
     * 
     * @param string $accessToken The JWT token from the Authorization header
     * @return UserBadge
     * @throws BadCredentialsException If the token is invalid
     */
    public function getUserBadgeFrom(#[\SensitiveParameter] string $accessToken): UserBadge
    {
        try {
            // Check if token is empty or missing
            if (empty($accessToken)) {
                throw new \Exception('Missing token');
            }
            
            // Basic token structure analysis
            $tokenParts = explode('.', $accessToken);
            if (count($tokenParts) !== 3) {
                throw new \Exception('Malformed token structure (should have 3 parts separated by dots)');
            }
            
            // Try to decode the header to check if it's a valid JWT
            $headerJson = base64_decode(strtr($tokenParts[0], '-_', '+/'));
            if (!$headerJson || !json_decode($headerJson)) {
                throw new \Exception('Invalid token header format');
            }
            
            // Validate the token and get the payload
            $payload = $this->jwtTokenService->validate($accessToken);
            
            // Extract the user identifier (subject) from the token
            if (!isset($payload['sub'])) {
                throw new \Exception('Token does not contain user ID (sub claim)');
            }
            
            // Check for other required claims
            if (!isset($payload['iss'])) {
                throw new \Exception('Token does not contain issuer (iss claim)');
            }
            
            if (!isset($payload['exp'])) {
                throw new \Exception('Token does not contain expiration time (exp claim)');
            }
            
            // Use the email as the user identifier if available, otherwise use the subject
            if (isset($payload['email'])) {
                $userIdentifier = $payload['email'];
            } else {
                $userIdentifier = $payload['sub'];
            }
            
            // Log successful token validation
            $this->logger?->info('JWT token validated successfully', [
                'user_id' => $userIdentifier,
                'roles' => $payload['roles'] ?? [],
                'email' => $payload['email'] ?? null,
            ]);
            
            // Return a UserBadge with the user identifier and the full payload as attributes
            return new UserBadge($userIdentifier, null, $payload);
            
        } catch (\Firebase\JWT\ExpiredException $e) {
            // Token has expired
            $this->logTokenError($e, 'Token has expired');
            throw new BadCredentialsException('JWT Token Error: Token has expired. Please obtain a new token.', $e->getCode(), $e);
            
        } catch (\Firebase\JWT\SignatureInvalidException $e) {
            // Token signature is invalid
            $this->logTokenError($e, 'Invalid token signature');
            throw new BadCredentialsException('JWT Token Error: Invalid token signature. The token may have been tampered with.', $e->getCode(), $e);
            
        } catch (\Firebase\JWT\BeforeValidException $e) {
            // Token is not yet valid
            $this->logTokenError($e, 'Token not yet valid');
            throw new BadCredentialsException('JWT Token Error: Token not yet valid. Please check the token\'s "not before" (nbf) claim.', $e->getCode(), $e);
            
        } catch (\DomainException | \InvalidArgumentException | \UnexpectedValueException $e) {
            // Various token format/content errors
            $this->logTokenError($e, 'Invalid token format or content');
            throw new BadCredentialsException('JWT Token Error: Invalid token format or content. ' . $e->getMessage(), $e->getCode(), $e);
            
        } catch (\Exception $e) {
            // Generic error handling for other exceptions
            $this->logTokenError($e);
            
            // Create a more specific error message based on the exception message
            $errorMessage = 'JWT Token Error: ';
            
            // Check for common error patterns in the message
            if (str_contains($e->getMessage(), 'Missing token')) {
                $errorMessage .= 'No token provided. Please include a valid JWT token in the Authorization header.';
            } elseif (str_contains($e->getMessage(), 'Malformed token')) {
                $errorMessage .= 'Malformed token. The token does not follow the JWT format (header.payload.signature).';
            } elseif (str_contains($e->getMessage(), 'Invalid token header')) {
                $errorMessage .= 'Invalid token header. The token header could not be decoded.';
            } elseif (str_contains($e->getMessage(), 'sub claim')) {
                $errorMessage .= 'The token does not contain a user ID (sub claim). This is required for authentication.';
            } elseif (str_contains($e->getMessage(), 'iss claim')) {
                $errorMessage .= 'The token does not contain an issuer (iss claim). This is required for validation.';
            } elseif (str_contains($e->getMessage(), 'exp claim')) {
                $errorMessage .= 'The token does not contain an expiration time (exp claim). This is required for security.';
            } elseif (str_contains($e->getMessage(), 'email claim')) {
                $errorMessage .= 'The token does not contain an email. This is required for user identification.';
            } elseif (str_contains($e->getMessage(), 'User not found')) {
                $errorMessage .= 'User not found. The email in the token does not match any user in the database.';
            } else {
                // If no specific pattern is matched, use the original error message
                $errorMessage .= $e->getMessage();
            }
            
            // Throw a BadCredentialsException with the specific error message
            throw new BadCredentialsException($errorMessage, $e->getCode(), $e);
        }
    }
    
    /**
     * Logs token validation errors with additional context
     * 
     * @param \Exception $exception The exception that was thrown
     * @param string|null $errorType Optional error type for categorization
     */
    private function logTokenError(\Exception $exception, ?string $errorType = null): void
    {
        $context = [
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ];
        
        if ($errorType) {
            $context['error_type'] = $errorType;
        }
        
        $this->logger?->error('JWT token validation failed', $context);
    }
}