<?php

// This is a simple test script to verify the fix for the dateRealisation issue in ActesBatchProcessor

// Simulate the createDtoFromArray method with different input scenarios
// Run with: php tests/RapideTest/test_actes_batch.php

function testCreateDtoFromArray() {
    echo "Testing dateRealisation handling in ActesBatchProcessor\n";
    echo "=====================================================\n\n";

    // Test Case 1: With dateRealisation provided
    $item1 = [
        'code' => 'CCAM001',
        'description' => 'Consultation cardiologique',
        'dateRealisation' => '2025-08-27T14:30:00+02:00',
        'annee' => 2025,
        'mois' => 8
    ];
    
    echo "Test Case 1: With dateRealisation provided\n";
    echo "Input: dateRealisation = {$item1['dateRealisation']}, annee = {$item1['annee']}, mois = {$item1['mois']}\n";
    $dateRealisation1 = new \DateTime($item1['dateRealisation']);
    echo "Expected dateRealisation: " . $dateRealisation1->format('Y-m-d H:i:s') . "\n\n";

    // Test Case 2: Without dateRealisation but with annee and mois provided
    $item2 = [
        'code' => 'CCAM002',
        'description' => 'Échographie cardiaque',
        'annee' => 2025,
        'mois' => 9
    ];
    
    echo "Test Case 2: Without dateRealisation but with annee and mois provided\n";
    echo "Input: annee = {$item2['annee']}, mois = {$item2['mois']}\n";
    $dateRealisation2 = new \DateTime("{$item2['annee']}-{$item2['mois']}-01");
    echo "Expected dateRealisation: " . $dateRealisation2->format('Y-m-d H:i:s') . "\n\n";

    // Test Case 3: Without any date-related fields
    $item3 = [
        'code' => 'CCAM003',
        'description' => 'Radiographie thoracique'
    ];
    
    echo "Test Case 3: Without any date-related fields\n";
    echo "Input: No date-related fields\n";
    echo "Expected dateRealisation: null\n\n";

    echo "To verify the fix, please run the actual batch processing with these test cases\n";
    echo "and check if the dateRealisation in the response matches the expected values.\n";
}

// Run the test
testCreateDtoFromArray();