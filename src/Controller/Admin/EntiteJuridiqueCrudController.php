<?php

namespace App\Controller\Admin;

use App\Entity\Structure\EntiteJuridique;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ArrayField;
use EasyCorp\Bundle\EasyAdminBundle\Field\BooleanField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateField;
use EasyCorp\Bundle\EasyAdminBundle\Field\EmailField;
use EasyCorp\Bundle\EasyAdminBundle\Field\IdField;
use EasyCorp\Bundle\EasyAdminBundle\Field\IntegerField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TelephoneField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextEditorField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use EasyCorp\Bundle\EasyAdminBundle\Field\FormField;

class EntiteJuridiqueCrudController extends AbstractCrudController
{
    public static function getEntityFqcn(): string
    {
        return EntiteJuridique::class;
    }

    public function configureActions(Actions $actions): Actions
    {
        return $actions
            ->add(Crud::PAGE_INDEX, Action::DETAIL);
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setEntityLabelInSingular('Entité Juridique')
            ->setEntityLabelInPlural('Entités Juridiques')
            ->setPageTitle('index', 'Gestion des entités juridiques')
            ->setPageTitle('detail', 'Détail de l\'entité juridique')
            ->setPageTitle('new', 'Créer une nouvelle entité juridique')
            ->setPageTitle('edit', 'Modifier l\'entité juridique')
            ->showEntityActionsInlined();
    }

    public function configureFields(string $pageName): iterable
    {
        if ($pageName === Crud::PAGE_INDEX) {
            return [
                TextField::new('id')->hideOnForm(),
                TextField::new('nom')->setLabel('Nom'),
                TextField::new('code')->setLabel('Code'),
                BooleanField::new('isActif')->setLabel('Actif'),
            ];
        }
        // Tous les champs pour la vue détail (show), edit et new
        return [
            TextField::new('id')->hideOnForm(),
            TextField::new('nom')->setLabel('Nom')
                ->setHelp('Nom de l’hôpital'),

            TextField::new('code')
                ->setLabel('Code')
                ->setHelp('Le code de l’hôpital'),

            BooleanField::new('isActif')->setLabel('Actif')
                ->setHelp('Indique si l’entité est active'),

            TextField::new('adresse')
                ->setLabel('Adresse')
                ->setHelp('Adresse de l’hôpital')
                ->hideOnIndex(), // 🔍 Masqué sur la liste pour éviter l'encombrement

            TelephoneField::new('telephone')
                ->setLabel('Téléphone')
                ->setHelp('Numéro de téléphone de l’hôpital')
                ->hideOnIndex(),

            EmailField::new('email')
                ->setLabel('E-mail')
                ->setHelp('Adresse e-mail de contact')
                ->hideOnIndex(),

            DateField::new('date_creation')
                ->setLabel('Date de Création')
                ->setHelp('Date officielle de création')
                ->setDisabled(true),

            AssociationField::new('tenant')
                ->setLabel('Tenant')
                ->setHelp('Tenant auquel appartient cet hôpital'),

            IntegerField::new('polesCount', 'Pôles associés')
                ->setFormTypeOption('disabled', true)
                ->formatValue(function ($value, $entity) {
                    $count = $entity->getPoles()->count();
                    return sprintf('%d pôle(s) associé(s)', $count);
                })
                ->setHelp('Pôles rattachés à cet hôpital. Utilisez l\'onglet "Pôles" pour gérer les associations.')
                ->hideOnIndex(),

            IntegerField::new('usersCount', 'Agents rattachés')
                ->setFormTypeOption('disabled', true)
                ->formatValue(function ($value, $entity) {
                    $count = $entity->getUsers()->count();
                    return sprintf('%d agent(s) rattaché(s)', $count);
                })
                ->setHelp('Agents qui travaillent dans cet hôpital. Utilisez l\'onglet "Agents" pour gérer les associations.')
                ->hideOnIndex(),

            // LDAP Configuration Section
            FormField::addPanel('Configuration LDAP')
                ->setIcon('fas fa-server')
                ->setHelp('Configuration de la connexion LDAP pour cet hôpital'),

            TextField::new('ldapHost')
                ->setLabel('Serveur LDAP')
                ->setHelp('URL du serveur LDAP (ex: ldap://serveur.domaine.fr:389)')
                ->hideOnIndex(),

            TextField::new('ldapDomain')
                ->setLabel('Domaine LDAP')
                ->setHelp('Domaine utilisé pour l\'authentification LDAP')
                ->hideOnIndex(),

            TextField::new('ldapBaseDn')
                ->setLabel('Base DN')
                ->setHelp('Base DN pour les recherches LDAP (ex: DC=domaine,DC=fr)')
                ->hideOnIndex(),

            DateField::new('ldapLastCheck')
                ->setLabel('Dernière vérification')
                ->setHelp('Date de la dernière vérification de la connexion LDAP')
                ->setDisabled(true)
                ->hideOnIndex(),

            // Configuration des paramètres SUPRA
            FormField::addPanel('Configuration SUPRA - Adresses et Chemins')
                ->setIcon('fas fa-folder')
                ->setHelp('Configuration des chemins et dossiers utilisés par SUPRA (À défaut, une route API est fournie pour cette fonctionnalité)'),

            TextField::new('affectationAgentFilesPaths')
                ->setLabel('Chemin fichiers affectations')
                ->setHelp('Chemin du dossier où sont déposés les fichiers CSV d\'affectation pour l\'import automatique')
                ->hideOnIndex(),

            BooleanField::new('chargementAutomatiqueDesAffectationEstActif')
                ->setLabel('Import automatique affectations')
                ->setHelp('Active l\'importation automatique des fichiers CSV d\'affectation')
                ->hideOnIndex(),

            // Configuration des notifications
            FormField::addPanel('Configuration SUPRA - Notifications Email')
                ->setIcon('fas fa-envelope')
                ->setHelp('Configuration des emails de notification'),

            EmailField::new('adminEmail')
                ->setLabel('Email administrateur')
                ->setHelp('Adresse email de l\'administrateur pour les notifications')
                ->hideOnIndex(),

            EmailField::new('damResponssableDataAffectionEmail')
                ->setLabel('Email responsable DAM affectations')
                ->setHelp('Adresse email du responsable DAM pour les notifications d\'affectation')
                ->hideOnIndex(),

            BooleanField::new('notifierErreurAuResponssableDamAffectation')
                ->setLabel('Notifier responsable DAM')
                ->setHelp('Envoyer les notifications d\'erreur d\'affectation au responsable DAM')
                ->hideOnIndex(),

            BooleanField::new('notifierErreurAuAdminPourAffectation')
                ->setLabel('Notifier admin affectations')
                ->setHelp('Envoyer les notifications d\'erreur d\'affectation à l\'administrateur')
                ->hideOnIndex(),

            BooleanField::new('notifierAdminDesErreurProduite')
                ->setLabel('Notifier admin erreurs générales')
                ->setHelp('Envoyer les notifications d\'erreur générale SUPRA à l\'administrateur')
                ->hideOnIndex(),

            // Configuration des API endpoints
            FormField::addPanel('Configuration SUPRA - API Endpoints')
                ->setIcon('fas fa-link')
                ->setHelp('Information des URLs des services connexes'),

            TextField::new('dataIntegratorApiEndpoint')
                ->setLabel('URL API Data Integrator ')
                ->setHelp('URL de l\'API du service data_integrator (titre informatif)')
                ->hideOnIndex(),

            TextField::new('backendUrlEndpoint')
                ->setLabel('URL Backend SUPRA ')
                ->setHelp('URL du backend SUPRA (titre informatif)')
                ->hideOnIndex(),
        ];
    }
}
