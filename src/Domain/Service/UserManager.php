<?php
namespace App\Domain\Service;

use App\Entity\Praticien\Agent;
use App\Entity\Structure\EntiteJuridique;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Security\Core\Exception\AuthenticationException;

/**
 * Service for managing user operations
 */
class UserManager
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private UserPasswordHasherInterface $passwordHasher
    ) {}

    /**
     * Find or create a user based on username
     *
     *  MODIFIÉ : Retourne maintenant needsLdapInit pour optimiser les mises à jour
     *
     * @param string $username L'identifiant utilisateur (email ou hrUser)
     * @param EntiteJuridique $hopital L'hôpital associé
     * @return array ['agent' => Agent, 'isNew' => bool, 'needsLdapInit' => bool]
     */
    public function findOrCreateUser(string $username, EntiteJuridique $hopital): array
    {
        // Sanitize input
        $username = strtolower(trim($username));

        // Find existing user
        $agent = $this->entityManager->getRepository(Agent::class)
            ->findByIdentifier($username);

        $isNew = false;
        $needsLdapInit = true; // Par défaut, on assume qu'une init LDAP est nécessaire

        if (!$agent) {
            $isNew = true;
            $agent = $this->createNewUser($username);
            // Un nouvel utilisateur a forcément besoin d'init LDAP
            $needsLdapInit = true;
        } else {
            //  NOUVEAU : Vérifier si l'utilisateur existant a besoin d'init LDAP
            $needsLdapInit = !$agent->getIsLdapInitialized();
        }

        // Set hospital only if user is new or doesn't have one
        if ($isNew || null === $agent->getHopital()) {
            $agent->setHopital($hopital);
        }

        return [
            'agent' => $agent,
            'isNew' => $isNew,
            'needsLdapInit' => $needsLdapInit  //  NOUVEAU : Info pour décider de la mise à jour
        ];
    }

    /**
     * Create a new user with basic information
     *
     *  MODIFIÉ : Initialise isLdapInitialized à false
     */
    private function createNewUser(string $username): Agent
    {
        $agent = new Agent();

        // Set email or hrUser based on username format
        if (strpos($username, '@') !== false) {
            $agent->setEmail($username);
            // hrUser will be set later from LDAP attributes
        } else {
            $agent->setHrUser($username);
        }

        $agent->setRoles(['ROLE_USER']);

        //  NOUVEAU : Par défaut, l'utilisateur n'est pas initialisé LDAP
        $agent->setIsLdapInitialized(false);

        return $agent;
    }

    /**
     * Update user with LDAP attributes
     */
    public function updateUserFromLdapAttributes(Agent $agent, array $attributes): void
    {
        // Update basic user information if not already set
        if ($agent->getNom() === null && isset($attributes['sn'])) {
            $agent->setNom($attributes['sn']);
        }

        if ($agent->getPrenom() === null && isset($attributes['givenname'])) {
            $agent->setPrenom($attributes['givenname']);
        }

        if ($agent->getFullName() === null && isset($attributes['displayname'])) {
            $agent->setFullName($attributes['displayname']);
        }

        if ($agent->getEmail() === null && isset($attributes['mail'])) {
            $agent->setEmail($attributes['mail']);
        }

        // Set hrUser from cn if not already set (important for email logins)
        if ($agent->getHrUser() === null && isset($attributes['cn'])) {
            $agent->setHrUser(strtolower($attributes['cn']));
        }

        if ($agent->getEtablissement() === null && isset($attributes['department'])) {
            // Truncate department to 10 characters to fit in the etablissement field
            $agent->setEtablissement(substr($attributes['department'], 0, 10));
        }

        if ($agent->getCreateurFiche() === null) {
            $agent->setCreateurFiche("Via LDAP AD first login");
        }

        // Update LDAP attributes via the trait
        $agent->updateLdapAttributes($attributes);
    }

    /**
     * Update user password
     */
    public function updateUserPassword(Agent $agent, string $plainPassword): void
    {
        $hashedPassword = $this->passwordHasher->hashPassword($agent, $plainPassword);
        $agent->setPassword($hashedPassword);
    }

    /**
     * Save user to database
     */
    public function saveUser(Agent $agent): void
    {
        $this->entityManager->persist($agent);
        $this->entityManager->flush();
    }

    /**
     * Update connection statistics for user
     */
    public function updateConnectionStats(Agent $agent): void
    {
        $agent->incrementConnectionStats();
        $this->entityManager->flush();
    }

    /**
     *  NOUVELLE MÉTHODE : Marquer un utilisateur comme initialisé LDAP
     *
     * Appelée après une mise à jour complète des attributs LDAP
     * pour indiquer que l'utilisateur n'a plus besoin de mise à jour systématique.
     */
    public function markAsLdapInitialized(Agent $agent): void
    {
        $agent->setIsLdapInitialized(true);
    }

    /**
     *  NOUVELLE MÉTHODE : Vérifier si un utilisateur a besoin d'une initialisation LDAP complète
     *
     * @param Agent $agent L'utilisateur à vérifier
     * @return bool True si l'utilisateur a besoin d'une initialisation LDAP
     */
    public function needsLdapInitialization(Agent $agent): bool
    {
        return !$agent->getIsLdapInitialized();
    }
}