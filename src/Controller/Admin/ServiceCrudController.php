<?php

namespace App\Controller\Admin;

use App\Entity\Structure\Service;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\BooleanField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateTimeField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ChoiceField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;

class ServiceCrudController extends AbstractCrudController
{
    public static function getEntityFqcn(): string
    {
        return Service::class;
    }

    public function configureActions(Actions $actions): Actions
    {
        $importCsv = Action::new('importCsv', '📥 Importer CSV')
            ->linkToRoute('admin_import_structures_csv', ['entity' => $this->getEntityFqcn()])
            ->setCssClass('btn btn-primary');

        return $actions
            ->add(Crud::PAGE_NEW, $importCsv)
            ->add(Crud::PAGE_INDEX, Action::DETAIL);
    }

    public function configureFields(string $pageName): iterable
    {
        $fields = [
            TextField::new('id')->hideOnForm(),

            TextField::new('etab')
                ->setLabel('Établissement')
                ->setHelp('Code de l\'établissement (max 3 caractères)')
                ->setFormTypeOption('attr', ['maxlength' => 3])
                ->hideOnIndex(),

            TextField::new('secode')
                ->setLabel('Code du Service')
                ->setHelp('Code unique du service (max 4 caractères)')
                ->setFormTypeOption('attr', ['maxlength' => 4]),

            TextField::new('libelle')
                ->setLabel('Libellé du Service')
                ->setHelp('Nom du service hospitalier'),

            DateTimeField::new('datdeb')
                ->setLabel('Date de début')
                ->setFormat('d/M/Y')
                ->hideOnIndex(),

            DateTimeField::new('datfin')
                ->setLabel('Date de fin')
                ->setFormat('d/M/Y'),

            TextField::new('cdcode')
                ->setLabel('Code CD')
                ->setHelp('Code CD du service (max 3 caractères)')
                ->setFormTypeOption('attr', ['maxlength' => 3])
                ->hideOnIndex(),

            TextField::new('tacode')
                ->setLabel('Code TA')
                ->setHelp('Code TA du service (max 2 caractères)')
                ->setFormTypeOption('attr', ['maxlength' => 2])
                ->hideOnIndex(),

            TextField::new('pfuser')
                ->setLabel('Utilisateur PF')
                ->setHelp('Ex: Utilisateur ayant créé/modifié l\'enregistrement. Vous pouvez utiliser ce champ pour garder une information pertinente pour le service.')
                ->setFormTypeOption('attr', ['maxlength' => 8])
                ->hideOnIndex(),

            DateTimeField::new('dmdacre')
                ->setLabel('Date de création')
                ->setFormat('d/M/Y')
                ->setFormTypeOption('mapped', false)
                ->setValue(new \DateTime('now'))
                ->hideOnForm()
                ->hideOnIndex(),

            BooleanField::new('isActif')
                ->setLabel('Est actif')
                ->hideOnIndex(),

            ChoiceField::new('periodeType')
                ->setLabel('Type de période')
                ->setChoices([
                    'Hebdomadaire' => 'HEBDOMADAIRE',
                    'Mensuel' => 'MENSUEL',
                    'Annuel' => 'ANNUEL',
                ])
                ->hideOnIndex(),

            TextField::new('source')
                ->setLabel('Source')
                ->setHelp('Origine de la donnée')
                ->hideOnIndex(),

            AssociationField::new('hopital')
                ->setLabel('Hôpital')
                ->setHelp('Hôpital auquel ce service est rattaché'),

            AssociationField::new('ufs')
                ->setLabel('Unités Fonctionnelles')
                ->setHelp('Unités fonctionnelles rattachées à ce service')
                ->setFormTypeOption('by_reference', false)
                ->hideOnIndex(),
        ];

        // Add dmdamaj field only for edit and detail pages
        if ($pageName !== Crud::PAGE_NEW) {
            $fields[] = DateTimeField::new('dmdamaj')
                ->setLabel('Date de mise à jour')
                ->setFormat('d/M/Y')
                ->setValue(new \DateTime('now'))
                ->hideOnIndex();
        }

        return $fields;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setEntityLabelInSingular('Service')
            ->setEntityLabelInPlural('Services')
            ->setPageTitle('index', 'Gestion des Services')
            ->setPageTitle('detail', 'Détails du Service')
            ->setPageTitle('new', 'Créer un nouveau Service')
            ->setPageTitle('edit', 'Modifier le Service')
            ->setHelp('index', '<strong>Astuce :</strong> Pour gagner de la place, double-cliquez sur la barre de séparation pour masquer ou afficher le menu latéral.')
            ->showEntityActionsInlined();
    }
    
    /**
     * Custom logic to handle entity creation
     * 
     * Cette méthode génère automatiquement les champs validFrom et validTo
     * à partir des champs datdeb et datfin lors de la création d'un service.
     */
    public function persistEntity(\Doctrine\ORM\EntityManagerInterface $entityManager, $entityInstance): void
    {
        if ($entityInstance instanceof \App\Entity\Structure\Service) {
            // Set dmdacre (date de création) to current date if not already set
            if ($entityInstance->getDmdacre() === null) {
                $entityInstance->setDmdacre(new \DateTime('now'));
            }
            
            // Generate validFrom from datdeb
            if ($entityInstance->getDatdeb() !== null) {
                $entityInstance->setValidFrom($entityInstance->getDatdeb());
            }
            
            // Generate validTo from datfin
            if ($entityInstance->getDatfin() !== null) {
                $entityInstance->setValidTo($entityInstance->getDatfin());
            }
        }
        
        // Call parent method to persist the entity
        parent::persistEntity($entityManager, $entityInstance);
    }
    
    /**
     * Custom logic to handle entity updates
     * 
     * Cette méthode met à jour les champs validFrom et validTo
     * à partir des champs datdeb et datfin lors de la modification d'un service.
     */
    public function updateEntity(\Doctrine\ORM\EntityManagerInterface $entityManager, $entityInstance): void
    {
        if ($entityInstance instanceof \App\Entity\Structure\Service) {
            // Generate validFrom from datdeb
            if ($entityInstance->getDatdeb() !== null) {
                $entityInstance->setValidFrom($entityInstance->getDatdeb());
            }
            
            // Generate validTo from datfin
            if ($entityInstance->getDatfin() !== null) {
                $entityInstance->setValidTo($entityInstance->getDatfin());
            }
        }
        
        // Call parent method to update the entity
        parent::updateEntity($entityManager, $entityInstance);
    }
}
