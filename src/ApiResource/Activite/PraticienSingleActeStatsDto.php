<?php

namespace App\ApiResource\Activite;

/**
 * DTO pour les statistiques Praticien d'un acte spécifique sur 3 périodes
 * 
 * Cette classe représente les données de répartition d'un acte médical 
 * par Praticien sur 3 périodes temporelles (P1, P2, P3).
 * 
 * Route API: GET /api/actes/{id}/praticien-single-stats
 * 
 * Exemple d'utilisation Postman:
 * GET http://localhost:8000/api/actes/46C2DXI8VA/praticien-single-stats?p1Start=2024-01-01&p1End=2024-09-31&p2Start=2023-01-01&p2End=2023-09-31&p3Start=2022-01-01&p3End=2022-09-31
 * 
 * Réponse attendue:
 * {
 *   "member": [
 *     {
 *       "praticienId": "1f03ba47-b851-69dc-a627-213d52d1a758",
 *       "praticienNom": "Hickle",
 *       "praticienPrenom": "Denis",
 *       "praticienTitre": "Pr",
 *       "praticienFullName": "Pr <PERSON>",
 *       "p1Count": 0,
 *       "p1Frequency": 0,
 *       "p2Count": 0,
 *       "p2Frequency": 0,
 *       "p3Count": 1,
 *       "p3Frequency": 100,
 *       "totalCount": 1,
 *       "globalFrequency": 100
 *     }
 *   ]
 * }
 */
class PraticienSingleActeStatsDto
{
    public function __construct(
        /** @var string ID unique du praticien */
        public readonly string $praticienId,
        
        /** @var string Nom de famille du praticien */
        public readonly string $praticienNom,
        
        /** @var string Prénom du praticien */
        public readonly string $praticienPrenom,
        
        /** @var string Titre du praticien (Dr, Pr, etc.) */
        public readonly string $praticienTitre,
        
        /** @var string Nom complet formaté (ex: "Pr Denis Hickle") */
        public readonly string $praticienFullName,
        
        /** @var int Nombre de réalisations en période P1 (actuelle) */
        public readonly int $p1Count,
        
        /** @var float Fréquence en % pour P1 (p1Count/totalP1 * 100) */
        public readonly float $p1Frequency,
        
        /** @var int Nombre de réalisations en période P2 (année précédente) */
        public readonly int $p2Count,
        
        /** @var float Fréquence en % pour P2 (p2Count/totalP2 * 100) */
        public readonly float $p2Frequency,
        
        /** @var int Nombre de réalisations en période P3 (il y a 2 ans) */
        public readonly int $p3Count,
        
        /** @var float Fréquence en % pour P3 (p3Count/totalP3 * 100) */
        public readonly float $p3Frequency,
        
        /** @var int Total des réalisations sur les 3 périodes */
        public readonly int $totalCount,
        
        /** @var float Fréquence globale en % (totalCount/totalGlobal * 100) */
        public readonly float $globalFrequency
    ) {}
}
