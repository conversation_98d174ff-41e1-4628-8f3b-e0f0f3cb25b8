<?php

namespace App\Command;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Email;

#[AsCommand(
    name: 'app:test-mailer',
    description: 'Teste l\'envoi d\'un email via le mailer configuré (ex: Mailtrap).',
)]
class TestMailerCommand extends Command
{
    public function __construct(private readonly MailerInterface $mailer)
    {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln('<info>Envoi d\'un email de test via le mailer...</info>');

        $email = (new Email())
            ->from('<EMAIL>')
            ->to('<EMAIL>') // Remplace par ton adresse Mailtrap cible
            ->subject('Test Mailer Symfony')
            ->text('Ceci est un email de test envoyé par la commande Symfony app:test-mailer.')
            ->html('<p>Ceci est un <strong>email de test</strong> envoyé par la commande Symfony <code>app:test-mailer</code>.</p>');

        try {
            $this->mailer->send($email);
            $output->writeln('<info>Email envoyé avec succès !</info>');
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $output->writeln('<error>Erreur lors de l\'envoi de l\'email : ' . $e->getMessage() . '</error>');
            return Command::FAILURE;
        }
    }
}

