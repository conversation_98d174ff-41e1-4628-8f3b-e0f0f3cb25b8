<?php

namespace App\Domain\Service;

use App\Domain\Enum\AgentRoleType;
use App\Entity\Praticien\Agent;
use App\Entity\Praticien\AgentHopitalRole;
use App\Entity\Structure\EntiteJuridique;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;

class AgentRoleAssignmentService
{
    private array $predefinedRoles;

    public function __construct(
        private EntityManagerInterface $entityManager,
        private LoggerInterface $logger,
        private string $projectDir
    ) {
        $this->loadPredefinedRoles();
    }

    private function loadPredefinedRoles(): void
    {
        $configFile = $this->projectDir . '/config/predefined_roles.php';

        if (!file_exists($configFile)) {
            $this->predefinedRoles = [];
            $this->logger->warning('Fichier de rôles prédéfinis non trouvé: ' . $configFile);
            return;
        }

        $this->predefinedRoles = require $configFile;
    }

    /**
     * Assigne automatiquement les rôles prédéfinis à un agent lors de sa création
     */
    public function assignPredefinedRoles(Agent $agent): void
    {
        $hrUser = $agent->getHrUser();

        if (!$hrUser) {
            return;
        }

        // Normalise le hrUser (en minuscules pour la comparaison)
        $normalizedHrUser = strtolower($hrUser);

        foreach ($this->predefinedRoles as $role => $hrUsers) {
            // Normalise tous les hrUsers de la configuration
            $normalizedHrUsers = array_map('strtolower', $hrUsers);

            if (in_array($normalizedHrUser, $normalizedHrUsers, true)) {
                $this->assignRoleToAgent($agent, $role);

                $this->logger->info('Rôle prédéfini assigné automatiquement', [
                    'agent_hrUser' => $hrUser,
                    'role' => $role,
                    'agent_id' => $agent->getId()
                ]);

                // Un agent peut avoir plusieurs rôles, donc on continue la boucle
            }
        }
    }

    /**
     * Assigne un rôle spécifique à un agent dans son hôpital
     */
    private function assignRoleToAgent(Agent $agent, string $role): void
    {
        // Vérifie que le rôle existe dans notre enum
        if (!in_array($role, AgentRoleType::ALL_ROLES, true)) {
            $this->logger->error('Tentative d\'assignation d\'un rôle invalide', [
                'role' => $role,
                'agent_hrUser' => $agent->getHrUser()
            ]);
            return;
        }

        // Ajoute le rôle au champ roles de l'agent pour les performances
        $currentRoles = $agent->getRoles();
        if (!in_array($role, $currentRoles, true)) {
            $currentRoles[] = $role;
            $agent->setRoles($currentRoles);
        }

        // Crée aussi un AgentHopitalRole pour la traçabilité et la granularité par hôpital
        $hopital = $agent->getHopital();
        if ($hopital) {
            $this->createAgentHopitalRole($agent, $hopital, $role);
        }
    }

    /**
     * Crée un AgentHopitalRole pour la traçabilité
     */
    private function createAgentHopitalRole(Agent $agent, EntiteJuridique $hopital, string $role): void
    {
        // Vérifie qu'il n'existe pas déjà
        $existingRole = $this->entityManager->getRepository(AgentHopitalRole::class)
            ->findOneBy([
                'agent' => $agent,
                'hopital' => $hopital,
                'role' => $role
            ]);

        if ($existingRole) {
            return; // Le rôle existe déjà
        }

        // Crée un nouveau AgentHopitalRole avec l'agent comme paramètre obligatoire
        $agentHopitalRole = new AgentHopitalRole($agent, $hopital, $role);
        $agentHopitalRole->setDateAffectation(new \DateTime());

        $this->entityManager->persist($agentHopitalRole);
    }

    /**
     * Vérifie si un hrUser a des rôles prédéfinis
     */
    public function hasPredefinedRoles(string $hrUser): bool
    {
        $normalizedHrUser = strtolower($hrUser);

        foreach ($this->predefinedRoles as $hrUsers) {
            $normalizedHrUsers = array_map('strtolower', $hrUsers);
            if (in_array($normalizedHrUser, $normalizedHrUsers, true)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Retourne les rôles prédéfinis pour un hrUser donné
     */
    public function getPredefinedRolesForHrUser(string $hrUser): array
    {
        $normalizedHrUser = strtolower($hrUser);
        $roles = [];

        foreach ($this->predefinedRoles as $role => $hrUsers) {
            $normalizedHrUsers = array_map('strtolower', $hrUsers);
            if (in_array($normalizedHrUser, $normalizedHrUsers, true)) {
                $roles[] = $role;
            }
        }

        return $roles;
    }
}
