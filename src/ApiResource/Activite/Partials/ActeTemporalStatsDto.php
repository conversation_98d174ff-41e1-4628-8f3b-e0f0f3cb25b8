<?php

namespace App\ApiResource\Activite\Partials;


/**
 * DTO pour les statistiques temporelles d'un acte sur 3 périodes
 *
 * Cette classe représente les données de répartition mensuelle d'un acte médical
 * sur 3 périodes temporelles (P1, P2, P3).
 *
 * Route API: GET /api/actes/{acteCode}/nb-realisation-par-periode-par-mois-stats
 */
class ActeTemporalStatsDto
{
    public function __construct(
        /** @var string Format YYYY-MM */
        public readonly string $mois,

        /** @var int Année */
        public readonly int $annee,

        /** @var int Numéro du mois (1-12) */
        public readonly int $moisNumero,

        /** @var int Nombre de réalisations en période P1 */
        public readonly int $p1Count,

        /** @var int Nombre de réalisations en période P2 */
        public readonly int $p2Count,

        /** @var int Nombre de réalisations en période P3 */
        public readonly int $p3Count
    ) {}
}