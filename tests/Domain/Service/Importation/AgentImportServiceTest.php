<?php

namespace App\Tests\Domain\Service\Importation;

use App\Domain\Service\Importation\AgentImportService;
use App\Entity\Praticien\Agent;
use App\Entity\Structure\EntiteJuridique;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;

it('can be instantiated', function () {
    $httpClient = $this->createMock(HttpClientInterface::class);
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $baseUri = 'http://data-integrator.example.com';
    
    $service = new AgentImportService($httpClient, $entityManager, $baseUri);
    
    expect($service)->toBeInstanceOf(AgentImportService::class);
});

it('imports agents successfully', function () {
    // Mock HTTP client
    $httpClient = $this->createMock(HttpClientInterface::class);
    $refreshResponse = $this->createMock(ResponseInterface::class);
    $agentsResponse = $this->createMock(ResponseInterface::class);
    
    // Set up the refresh response
    $refreshResponse->method('toArray')
        ->willReturn(['status' => 'success']);
    
    // Set up the agents response
    $agentsResponse->method('toArray')
        ->willReturn([
            'data' => [
                [
                    'hrUser' => 'U123456',
                    'nom' => 'Dupont',
                    'prenom' => 'Jean',
                    'titre' => 'Dr',
                    'categorie' => 'PH',
                    'etablissement' => 'CHRU Nancy',
                    'dateDepart' => '2025-12-31',
                    'dateVenue' => '2020-01-01',
                    'dateMaj' => '2025-08-09',
                    'email' => '<EMAIL>',
                    'createurFiche' => 'ADMIN',
                    'isAdmin' => false,
                    'isAnesthesiste' => true
                ]
            ],
            'paginationTotal' => 1,
            'pageSize' => 50,
            'ejCode' => 'EJ001'
        ]);
    
    // Set up the request method to return different responses based on the URL
    $httpClient->method('request')
        ->willReturnCallback(function ($method, $url, $options = []) use ($refreshResponse, $agentsResponse) {
            if ($url === 'http://data-integrator.example.com/api/agents/refresh') {
                return $refreshResponse;
            }
            
            if ($url === '/api/agents') {
                return $agentsResponse;
            }
            
            return $this->createMock(ResponseInterface::class);
        });
    
    // Mock entity manager and repositories
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $entiteJuridiqueRepository = $this->createMock(EntityRepository::class);
    $agentRepository = $this->createMock(EntityRepository::class);
    
    $entityManager->method('getRepository')
        ->willReturnCallback(function ($entityClass) use ($entiteJuridiqueRepository, $agentRepository) {
            if ($entityClass === EntiteJuridique::class) {
                return $entiteJuridiqueRepository;
            }
            if ($entityClass === Agent::class) {
                return $agentRepository;
            }
            return null;
        });
    
    // Mock EntiteJuridique
    $entiteJuridique = $this->createMock(EntiteJuridique::class);
    $entiteJuridiqueRepository->method('findOneBy')
        ->willReturn($entiteJuridique);
    
    // Mock Agent - return null to simulate new agent
    $agentRepository->method('findOneBy')
        ->willReturn(null);
    
    // Expect persist to be called for a new agent
    $entityManager->expects($this->once())
        ->method('persist')
        ->with($this->isInstanceOf(Agent::class));
    
    // Expect flush to be called at the end
    $entityManager->expects($this->once())
        ->method('flush');
    
    $baseUri = 'http://data-integrator.example.com';
    
    $service = new AgentImportService($httpClient, $entityManager, $baseUri);
    
    // No exception should be thrown
    $service->importAgents();
});

it('handles refresh error', function () {
    // Mock HTTP client
    $httpClient = $this->createMock(HttpClientInterface::class);
    $response = $this->createMock(ResponseInterface::class);
    
    // Mock response for refresh endpoint with error
    $response->method('toArray')
        ->willReturn([
            'status' => 'error',
            'message' => 'Refresh failed'
        ]);
    
    $httpClient->method('request')
        ->willReturn($response);
    
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $baseUri = 'http://data-integrator.example.com';
    
    $service = new AgentImportService($httpClient, $entityManager, $baseUri);
    
    expect(fn() => $service->importAgents())
        ->toThrow(\Exception::class, 'Erreur lors du refresh des agents : Refresh failed');
});

it('handles missing EntiteJuridique', function () {
    // Mock HTTP client
    $httpClient = $this->createMock(HttpClientInterface::class);
    $refreshResponse = $this->createMock(ResponseInterface::class);
    $agentsResponse = $this->createMock(ResponseInterface::class);
    
    // Set up the refresh response
    $refreshResponse->method('toArray')
        ->willReturn(['status' => 'success']);
    
    // Set up the agents response
    $agentsResponse->method('toArray')
        ->willReturn([
            'data' => [
                [
                    'hrUser' => 'U123456',
                    'nom' => 'Dupont',
                    'prenom' => 'Jean'
                ]
            ],
            'paginationTotal' => 1,
            'pageSize' => 50,
            'ejCode' => 'EJ001'
        ]);
    
    // Set up the request method to return different responses based on the URL
    $httpClient->method('request')
        ->willReturnCallback(function ($method, $url, $options = []) use ($refreshResponse, $agentsResponse) {
            if ($url === 'http://data-integrator.example.com/api/agents/refresh') {
                return $refreshResponse;
            }
            
            if ($url === '/api/agents') {
                return $agentsResponse;
            }
            
            return $this->createMock(ResponseInterface::class);
        });
    
    // Mock entity manager and repositories
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $entiteJuridiqueRepository = $this->createMock(EntityRepository::class);
    
    $entityManager->method('getRepository')
        ->willReturnCallback(function ($entityClass) use ($entiteJuridiqueRepository) {
            if ($entityClass === EntiteJuridique::class) {
                return $entiteJuridiqueRepository;
            }
            return null;
        });
    
    // Mock EntiteJuridique - return null to simulate missing entity
    $entiteJuridiqueRepository->method('findOneBy')
        ->willReturn(null);
    
    $baseUri = 'http://data-integrator.example.com';
    
    $service = new AgentImportService($httpClient, $entityManager, $baseUri);
    
    expect(fn() => $service->importAgents())
        ->toThrow(\Exception::class, 'EntiteJuridique non trouvée pour l\'Agent U123456');
});