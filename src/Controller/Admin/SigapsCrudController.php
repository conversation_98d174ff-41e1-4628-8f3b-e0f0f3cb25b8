<?php

namespace App\Controller\Admin;

use App\Entity\Activite\Sigaps;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\ArrayField;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\BooleanField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ChoiceField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateTimeField;
use EasyCorp\Bundle\EasyAdminBundle\Field\IntegerField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;

class SigapsCrudController extends AbstractCrudController
{
    public static function getEntityFqcn(): string
    {
        return Sigaps::class;
    }

    public function configureActions(Actions $actions): Actions
    {
        return $actions
            ->add(Crud::PAGE_INDEX, Action::DETAIL);
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setEntityLabelInSingular('Sigaps')
            ->setEntityLabelInPlural('Sigaps')
            ->setPageTitle('index', 'Gestion des Sigaps')
            ->setPageTitle('detail', 'Détail du Sigaps')
            ->setPageTitle('new', 'Créer un nouveau Sigaps')
            ->setPageTitle('edit', 'Modifier le Sigaps')
            ->showEntityActionsInlined();
    }

    public function configureFields(string $pageName): iterable
    {
        if ($pageName === Crud::PAGE_INDEX) {
            return [
                TextField::new('id')->hideOnForm(),
                BooleanField::new('isActif')->setLabel('Actif'),
                DateTimeField::new('dateDebut')->setLabel('Début')->setFormat('dd/MM/yyyy HH:mm'),
                DateTimeField::new('dateFin')->setLabel('Fin')->setFormat('dd/MM/yyyy HH:mm'),
                TextField::new('score')->setLabel('Score'),
                IntegerField::new('nombre_publication')->setLabel('Nb publications'),
                AssociationField::new('agent')->setLabel('Agent'),
            ];
        }
        // Tous les champs pour la vue détail (show), edit et new
        return [
            TextField::new('id')->hideOnForm(),
            BooleanField::new('isActif')->setLabel('Est actif'),
            DateTimeField::new('dateCreation')->setLabel('Date de création')->setDisabled(true),
            DateTimeField::new('dateDebut')->setLabel('Date de début')->setFormat('dd/MM/yyyy HH:mm')->setHelp('Date de début de la période SIGAPS'),
            DateTimeField::new('dateFin')->setLabel('Date de fin')->setFormat('dd/MM/yyyy HH:mm')->setHelp('Date de fin de la période SIGAPS'),
            TextField::new('score')->setLabel('Score SIGAPS')->setHelp('Score obtenu pour la période'),
            TextField::new('categorie')->setLabel('Catégorie')->setHelp('Catégorie de publication SIGAPS'),
            IntegerField::new('nombre_publication')->setLabel('Nombre de publications')->setHelp('Nombre total de publications pour cette période'),
            ArrayField::new('repartition_par_categorie')->setLabel('Répartition par catégorie')->setHelp('Détail de la répartition des publications par catégorie')->hideOnIndex(),
            AssociationField::new('agent')->setLabel('Agent')->setHelp('Agent ayant réalisé les publications')->setRequired(true),
            DateTimeField::new('validFrom')->setLabel('Validité - Début')->hideOnIndex()->hideOnForm(),
            DateTimeField::new('validTo')->setLabel('Validité - Fin')->hideOnIndex()->hideOnForm(),
            ChoiceField::new('periodeType')->setLabel('Période')->setChoices([
                'Hebdomadaire' => 'HEBDOMADAIRE',
                'Mensuel' => 'MENSUEL',
                'Annuel' => 'ANNUEL',
            ])->renderAsBadges([
                'HEBDOMADAIRE' => 'primary',
                'MENSUEL' => 'success',
                'ANNUEL' => 'warning',
            ]),
            TextField::new('source')->setLabel('Source des données')->setHelp('Origine de ces données SIGAPS')->hideOnIndex(),
        ];
    }
    
    /**
     * Custom logic to handle entity creation
     * 
     * Cette méthode génère automatiquement les champs validFrom et validTo
     * à partir des champs dateDebut et dateFin lors de la création d'un SIGAPS.
     */
    public function persistEntity(\Doctrine\ORM\EntityManagerInterface $entityManager, $entityInstance): void
    {
        if ($entityInstance instanceof \App\Entity\Activite\Sigaps) {
            // Generate validFrom from dateDebut
            if ($entityInstance->getDateDebut() !== null) {
                $entityInstance->setValidFrom($entityInstance->getDateDebut());
            }
            
            // Generate validTo from dateFin
            if ($entityInstance->getDateFin() !== null) {
                $entityInstance->setValidTo($entityInstance->getDateFin());
            }
        }
        
        // Call parent method to persist the entity
        parent::persistEntity($entityManager, $entityInstance);
    }
    
    /**
     * Custom logic to handle entity updates
     * 
     * Cette méthode met à jour les champs validFrom et validTo
     * à partir des champs dateDebut et dateFin lors de la modification d'un SIGAPS.
     */
    public function updateEntity(\Doctrine\ORM\EntityManagerInterface $entityManager, $entityInstance): void
    {
        if ($entityInstance instanceof \App\Entity\Activite\Sigaps) {
            // Generate validFrom from dateDebut
            if ($entityInstance->getDateDebut() !== null) {
                $entityInstance->setValidFrom($entityInstance->getDateDebut());
            }
            
            // Generate validTo from dateFin
            if ($entityInstance->getDateFin() !== null) {
                $entityInstance->setValidTo($entityInstance->getDateFin());
            }
        }
        
        // Call parent method to update the entity
        parent::updateEntity($entityManager, $entityInstance);
    }
}