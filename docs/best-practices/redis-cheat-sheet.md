# Redis Cheat Sheet - Guide Pratique

## 📋 Introduction
Redis est utilisé dans notre backend pour gérer le cache des données, notamment pour les résultats de recherche et les ressources API. Ce document présente les commandes Redis les plus utiles pour le développement et le débogage.

## 🔧 Configuration dans le projet
- **Adaptateur de cache**: `cache.adapter.redis` (configuré dans `config/packages/cache.yaml`)
- **Durée d'expiration par défaut**: `API_CACHE_TTL` secondes (3600 secondes / 1 heure par défaut)
- **Variables d'environnement**: 
  - `CACHE_DSN` (connexion Redis, définie dans `.env.local`)
  - `API_CACHE_TTL` (durée de vie du cache, définie dans `.env.local`)

## Commandes de base

###  Connexion au CLI Redis
#### D'abord, entrez dans le conteneur si vous utilisez Docker:

```bash
docker run -d --name redis-local -p 6379:6379 redis:latest
docker exec -it redis-local sh  # pas besoin en prod car redis est déjà installé
```

# Une fois dans le shell du conteneur (vous verrez un prompt comme #)
redis-cli

###  Exploration des clés
```bash
# Lister toutes les clés
KEYS *

# Lister les clés avec un motif spécifique
KEYS *ActesDto*

# Compter le nombre total de clés
DBSIZE
```

###  Inspection des données
```bash
# Voir le type d'une clé
TYPE nom_de_la_clé

# Voir la valeur d'une clé (pour les strings uniquement)
GET nom_de_la_clé
# Note: Pour les objets Symfony/JSON, l'affichage sera binaire/illisible

# Voir le TTL (temps avant expiration) d'une clé
TTL nom_de_la_clé
# Retourne: nombre de secondes, -1 (pas d'expiration), ou -2 (clé inexistante)

# Exemple:
TTL eusIW6x-MY:collection_App_ApiResource_Activite_ActesDto_p1_n10_aba437f211f2957faed050220ffb9873
```

###  Gestion de l'expiration
```bash
# Créer une clé avec expiration
SETEX nom_de_la_clé 3600 "valeur"  # Expire dans 1h (3600 secondes)

# Modifier l'expiration d'une clé existante
EXPIRE nom_de_la_clé 1800  # Expire dans 30min
```

###  Nettoyage des données
```bash
# Supprimer une clé spécifique
DEL nom_de_la_clé

# Supprimer toutes les clés de la base courante
FLUSHDB  # ⚠️ Attention: efface tout dans la DB sélectionnée (par défaut db 0)

# Supprimer toutes les clés de toutes les bases
FLUSHALL  # ⚠️ Attention: efface tout dans toutes les DBs
```

###  Navigation entre bases
```bash
# Changer de base Redis
SELECT 0  # Sélectionne la DB 0 (par défaut)
SELECT 1  # Sélectionne la DB 1
```

##  Workflow de test du cache

1. **Vider le cache**:
   ```bash
   FLUSHDB
   ```

2. **Effectuer une requête API** (depuis le front, ou avec curl/postman)

3. **Vérifier les nouvelles clés créées**:
   ```bash
   KEYS *
   ```

4. **Vérifier l'expiration d'une clé**:
   ```bash
   TTL nom_de_la_clé
   ```
   Si `$item->expiresAfter(3600)` a été utilisé, le TTL devrait être proche de 3600s.

## 🔧 Commandes avancées

###  Informations système
```bash
# Voir les informations du serveur Redis
INFO

# Voir les statistiques de mémoire
INFO memory

# Voir les statistiques des clients
INFO clients
```

###  Débogage
```bash
# Surveiller les commandes en temps réel
MONITOR

# Voir les clés les plus lentes
SLOWLOG GET 10
```

##  Ressources
- [Documentation officielle Redis](https://redis.io/documentation)
- [Redis en 5 minutes](https://redis.io/topics/data-types-intro)
- [Redis et Symfony Cache](https://symfony.com/doc/current/cache/redis_adapter.html)
