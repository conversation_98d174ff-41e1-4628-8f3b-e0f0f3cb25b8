<?php
namespace App\Entity\Praticien\Trait;

use Doctrine\ORM\Mapping as ORM;

trait LdapAttributesTrait
{
    #[ORM\Column(type: 'json', nullable: true)]
    private ?array $ldapAttributes = [
        'status' => [
            'success' => false,
            'last_sync' => null,
            'message' => 'Non synchronisé avec LDAP'
        ],
        'directory_info' => [
            'cn' => null,
            'sn' => null,
            'givenname' => null,
            'displayname' => null,
            'email' => null,
            'department' => null,
            'user_principal_name' => null,
            'distinguished_name' => null,
            'groups' => []
        ]
    ];

    public function getLdapAttributes(): ?array
    {
        return $this->ldapAttributes;
    }

    public function updateLdapAttributes(array $ldapData): void
    {
        if ($this->ldapAttributes === null) {
            $this->ldapAttributes = [
                'status' => [
                    'success' => false,
                    'last_sync' => null,
                    'message' => 'Non synchronisé avec LDAP'
                ],
                'directory_info' => []
            ];
        }

        $this->ldapAttributes['status']['success'] = true;
        $this->ldapAttributes['status']['last_sync'] = (new \DateTime())->format('Y-m-d H:i:s');
        $this->ldapAttributes['status']['message'] = 'Synchronisé avec LDAP';

        $this->ldapAttributes['directory_info'] = [
            'cn' => $ldapData['cn'] ?? null,
            'sn' => $ldapData['sn'] ?? null,
            'givenname' => $ldapData['givenname'] ?? null,
            'displayname' => $ldapData['displayname'] ?? null,
            'email' => $ldapData['email'] ?? null,
            'department' => $ldapData['department'] ?? null,
            'user_principal_name' => $ldapData['userPrincipalName'] ?? null,
            'distinguished_name' => $ldapData['distinguishedName'] ?? null,
            'groups' => $ldapData['groups'] ?? []
        ];
    }
}