{% extends 'base.html.twig' %}

{% block title %}Page non trouvée{% endblock %}

{% block body %}
<div class="min-h-screen flex items-center justify-center bg-gray-50">
    <div class="max-w-md w-full p-6">
        <div class="text-center">
            <div class="mb-6">
                <img src="{{ asset('assets/images/ght.png') }}" alt="Logo" class="h-16 mx-auto">
            </div>
            
            <h1 class="text-4xl font-bold text-indigo-600 mb-4">404</h1>
            <h2 class="text-2xl font-semibold text-gray-800 mb-2">Page non trouvée</h2>
            
            <div class="mb-8">
                <p class="text-gray-600 mb-4">Oups ! La page que vous recherchez semble avoir disparu.</p>
                
                {% if app.environment == 'dev' %}
                    <div class="bg-blue-50 border border-blue-200 rounded-md p-4 mb-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-blue-800">Information pour les développeurs</h3>
                                <div class="mt-2 text-sm text-blue-700">
                                    <p>Cette erreur s'est produite car la route <code class="bg-blue-100 px-1 py-0.5 rounded">{{ app.request.pathInfo }}</code> n'existe pas.</p>
                                    <p class="mt-2">Vérifiez que :</p>
                                    <ul class="list-disc list-inside mt-1">
                                        <li>L'URL est correctement écrite</li>
                                        <li>Le contrôleur et la route sont bien définis</li>
                                        <li>Les paramètres de route sont valides</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endif %}
                
                <div class="flex flex-col sm:flex-row justify-center gap-4 mt-6">
                    <a href="{{ path('admin') }}" class="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                        Retour à l'administration
                    </a>
                    <button onclick="window.history.back()" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Page précédente
                    </button>
                </div>
            </div>
            
            <div class="mt-12">
                <div class="relative">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-gray-300"></div>
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <span class="px-2 bg-gray-50 text-gray-500">Besoin d'aide ?</span>
                    </div>
                </div>
                <p class="mt-4 text-sm text-gray-500">
                    Si vous pensez qu'il s'agit d'une erreur, veuillez contacter l'administrateur système.
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}