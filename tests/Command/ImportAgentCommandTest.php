<?php

namespace App\Tests\Command;

use App\Command\ImportAgentCommand;
use App\Domain\Service\Importation\AgentImportService;
use App\Domain\Service\Notification\ErrorNotificationEmailService;
use App\Entity\Structure\EntiteJuridique;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Doctrine\ORM\QueryBuilder;
use Doctrine\ORM\AbstractQuery;
use Symfony\Component\Console\Tester\CommandTester;

it('can be instantiated', function () {
    $importService = $this->createMock(AgentImportService::class);
    $errorNotificationService = $this->createMock(ErrorNotificationEmailService::class);
    $entityManager = $this->createMock(EntityManagerInterface::class);
    
    $command = new ImportAgentCommand($importService, $errorNotificationService, $entityManager);
    
    expect($command)->toBeInstanceOf(ImportAgentCommand::class);
});

it('configures the command correctly', function () {
    $importService = $this->createMock(AgentImportService::class);
    $errorNotificationService = $this->createMock(ErrorNotificationEmailService::class);
    $entityManager = $this->createMock(EntityManagerInterface::class);
    
    $command = new ImportAgentCommand($importService, $errorNotificationService, $entityManager);
    
    // Use getName() method to get the command name
    expect($command->getName())->toBe('app:import:agent');
});

it('executes successfully', function () {
    $importService = $this->createMock(AgentImportService::class);
    $errorNotificationService = $this->createMock(ErrorNotificationEmailService::class);
    $entityManager = $this->createMock(EntityManagerInterface::class);
    
    // Mock importAgents to do nothing (void return)
    $importService->expects($this->once())
        ->method('importAgents');
    
    $command = new ImportAgentCommand($importService, $errorNotificationService, $entityManager);
    $commandTester = new CommandTester($command);
    
    $exitCode = $commandTester->execute([]);
    
    expect($exitCode)->toBe(0);
    expect($commandTester->getDisplay())->toContain('Début de l\'importation des agents');
    expect($commandTester->getDisplay())->toContain('Import des agents');
    expect($commandTester->getDisplay())->toContain('Importation des agents terminée avec succès');
});

it('handles errors from import service', function () {
    $importService = $this->createMock(AgentImportService::class);
    $errorNotificationService = $this->createMock(ErrorNotificationEmailService::class);
    $entityManager = $this->createMock(EntityManagerInterface::class);
    
    // Mock importAgents to throw an exception
    $importService->method('importAgents')
        ->willThrowException(new \Exception('Test error'));
    
    // Instead of overriding the protected method, let's mock the dependencies it uses
    // Mock the repository to return an empty array to avoid the foreach issue
    $repository = $this->createMock(EntityRepository::class);
    $entityManager->method('getRepository')
        ->with(EntiteJuridique::class)
        ->willReturn($repository);
    
    // Create a mock query builder
    $queryBuilder = $this->createMock(QueryBuilder::class);
    $repository->method('createQueryBuilder')
        ->willReturn($queryBuilder);
    
    // Configure query builder to return itself for where() method
    $queryBuilder->method('where')
        ->willReturnSelf();
    
    // Create a mock query
    $query = $this->getMockBuilder(\Doctrine\ORM\Query::class)
        ->disableOriginalConstructor()
        ->getMock();
    
    // Configure query builder to return the mock query
    $queryBuilder->method('getQuery')
        ->willReturn($query);
    
    // Configure query to return an empty array (no entities to notify)
    $query->method('getResult')
        ->willReturn([]);
    
    $command = new ImportAgentCommand($importService, $errorNotificationService, $entityManager);
    $commandTester = new CommandTester($command);
    
    $exitCode = $commandTester->execute([]);
    
    expect($exitCode)->toBe(1);
    expect($commandTester->getDisplay())->toContain('Erreur lors de l\'importation : Test error');
});

it('notifies admins of errors', function () {
    $importService = $this->createMock(AgentImportService::class);
    $errorNotificationService = $this->createMock(ErrorNotificationEmailService::class);
    $entityManager = $this->createMock(EntityManagerInterface::class);
    
    // Create a mock EntiteJuridique
    $entiteJuridique = $this->createMock(EntiteJuridique::class);
    
    // Create a mock repository
    $repository = $this->createMock(EntityRepository::class);
    $entityManager->method('getRepository')
        ->with(EntiteJuridique::class)
        ->willReturn($repository);
    
    // Create a mock query builder
    $queryBuilder = $this->createMock(QueryBuilder::class);
    $repository->method('createQueryBuilder')
        ->willReturn($queryBuilder);
    
    // Configure query builder to return itself for where() method
    $queryBuilder->method('where')
        ->willReturnSelf();
    
    // Create a mock query
    $query = $this->getMockBuilder(\Doctrine\ORM\Query::class)
        ->disableOriginalConstructor()
        ->getMock();
    
    // Configure query builder to return the mock query
    $queryBuilder->method('getQuery')
        ->willReturn($query);
    
    // Configure query to return our mock entity
    $query->method('getResult')
        ->willReturn([$entiteJuridique]);
    
    // Expect sendErrorNotificationToAdmin to be called
    $errorNotificationService->expects($this->once())
        ->method('sendErrorNotificationToAdmin')
        ->with(
            $this->equalTo($entiteJuridique),
            $this->equalTo('Import des agents'),
            $this->equalTo('Test error'),
            $this->callback(function ($context) {
                return isset($context['type']) && $context['type'] === 'Commande console' &&
                       isset($context['command']) && $context['command'] === 'app:import:agent';
            })
        );
    
    $command = new ImportAgentCommand($importService, $errorNotificationService, $entityManager);
    
    // Use reflection to call the protected method
    $reflectionMethod = new \ReflectionMethod(ImportAgentCommand::class, 'notifyAdminsOfError');
    $reflectionMethod->setAccessible(true);
    $reflectionMethod->invoke($command, new \Exception('Test error'));
});

it('handles errors in notification service', function () {
    $importService = $this->createMock(AgentImportService::class);
    $errorNotificationService = $this->createMock(ErrorNotificationEmailService::class);
    $entityManager = $this->createMock(EntityManagerInterface::class);
    
    // Create a mock EntiteJuridique
    $entiteJuridique = $this->createMock(EntiteJuridique::class);
    
    // Create a mock repository
    $repository = $this->createMock(EntityRepository::class);
    $entityManager->method('getRepository')
        ->with(EntiteJuridique::class)
        ->willReturn($repository);
    
    // Create a mock query builder
    $queryBuilder = $this->createMock(QueryBuilder::class);
    $repository->method('createQueryBuilder')
        ->willReturn($queryBuilder);
    
    // Configure query builder to return itself for where() method
    $queryBuilder->method('where')
        ->willReturnSelf();
    
    // Create a mock query
    $query = $this->getMockBuilder(\Doctrine\ORM\Query::class)
        ->disableOriginalConstructor()
        ->getMock();
    
    // Configure query builder to return the mock query
    $queryBuilder->method('getQuery')
        ->willReturn($query);
    
    // Configure query to return our mock entity
    $query->method('getResult')
        ->willReturn([$entiteJuridique]);
    
    // Mock sendErrorNotificationToAdmin to throw an exception
    $errorNotificationService->method('sendErrorNotificationToAdmin')
        ->willThrowException(new \Exception('Notification error'));
    
    $command = new ImportAgentCommand($importService, $errorNotificationService, $entityManager);
    
    // Use reflection to call the protected method
    $reflectionMethod = new \ReflectionMethod(ImportAgentCommand::class, 'notifyAdminsOfError');
    $reflectionMethod->setAccessible(true);
    
    // This should not throw an exception
    $reflectionMethod->invoke($command, new \Exception('Test error'));
    
    // We can't easily test that error_log was called, but we can verify that the method completes without throwing an exception
    expect(true)->toBeTrue();
});