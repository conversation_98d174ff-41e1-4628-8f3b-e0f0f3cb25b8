[PHP]
; Configuration identique à la VM de production
; Limites essentielles pour Symfony (20 minutes)
max_execution_time = 1200
memory_limit = 1024M
max_input_time = 1200
default_socket_timeout = 1200

; Upload
post_max_size = 100M
upload_max_filesize = 100M
max_file_uploads = 20

; Erreurs
error_reporting = E_ALL & ~E_DEPRECATED & ~E_STRICT
display_errors = On
display_startup_errors = On
log_errors = On

; Timezone
date.timezone = Europe/Paris

; Optimisations pour Symfony
realpath_cache_size = 4096K
realpath_cache_ttl = 600

[opcache]
opcache.enable = 1
opcache.enable_cli = 1
opcache.memory_consumption = 256
opcache.interned_strings_buffer = 8
opcache.max_accelerated_files = 20000
opcache.validate_timestamps = 1
opcache.revalidate_freq = 2

[Session]
session.save_handler = files
session.save_path = "/tmp"
session.use_strict_mode = 1
session.cookie_httponly = 1
session.gc_maxlifetime = 1440

; Extensions PHP nécessaires
extension=pgsql
extension=pdo_pgsql
extension=mbstring
extension=xml
extension=zip
extension=intl
extension=gd
extension=curl
extension=ldap
extension=opcache
