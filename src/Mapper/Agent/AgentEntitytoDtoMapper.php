<?php

namespace App\Mapper\Agent;

use App\ApiResource\Praticien\AgentDto;
use App\ApiResource\Praticien\AgentUfsDto;
use App\ApiResource\Structure\EntiteJuridiqueDto;
use App\Entity\Praticien\Agent;
use Symfonycasts\MicroMapper\AsMapper;
use Symfonycasts\MicroMapper\MapperInterface;
use Symfonycasts\MicroMapper\MicroMapperInterface;

#[AsMapper(from: Agent::class, to: AgentDto::class)]
class AgentEntitytoDtoMapper implements MapperInterface
{
    public function __construct(private MicroMapperInterface $microMapper) {}

    public function load(object $from, string $toClass, array $context): object
    {
        $entity = $from;
        assert($entity instanceof Agent);

        $dto = new AgentDto();
        $dto->id = $entity->getId();

        return $dto;
    }

    public function populate(object $from, object $to, array $context): object
    {
        $agent = $from;
        $dto = $to;
        assert($agent instanceof Agent);
        assert($dto instanceof AgentDto);

        $dto->dateCreation = $agent->getDateCreation();

        // CollecteurTrait fields
        $dto->email = $agent->getEmail();
        $dto->nom = $agent->getNom();
        $dto->prenom = $agent->getPrenom();
        $dto->matricule = $agent->getMatricule();
        $dto->hrUser = $agent->getHrUser();
        $dto->titre = $agent->getTitre();
        $dto->categorie = $agent->getCategorie();
        $dto->etablissement = $agent->getEtablissement();
        $dto->dateDepart = $agent->getDateDepart();
        $dto->dateVenue = $agent->getDateVenue();
        $dto->dateMaj = $agent->getDateMaj();
        $dto->createurFiche = $agent->getCreateurFiche();
        $dto->isAdmin = $agent->isAdmin();
        $dto->isAnesthesiste = $agent->isAnesthesiste();

        // NativeFieldTrait fields
        $dto->fullName = $agent->getFullName();
        $dto->imgUrl = $agent->getImgUrl();
        $dto->badge = $agent->getBadge();
        $dto->roles = $agent->getRoles();

        // Relations
        if ($agent->getHopital()) {
            $dto->hopital = $this->microMapper->map($agent->getHopital(), EntiteJuridiqueDto::class, [
                MicroMapperInterface::MAX_DEPTH => 1,
            ]);
        }

        if ($agent->getAgentUfs()) {
            $dto->affectations = array_map(
                function($agentUf) {
                    $mappedAgentUf = $this->microMapper->map($agentUf, AgentUfsDto::class, [
                        MicroMapperInterface::MAX_DEPTH => 1,
                    ]);
                    $mappedAgentUf->agent = null;  // On retire la référence à l'agent
                    return $mappedAgentUf;
                },
                $agent->getAgentUfs()->toArray()
            );
        }
        return $dto;
    }
}