<?php

namespace App\Entity\Activite;

use App\Entity\Base\BaseEntity;
use App\Entity\Praticien\Agent;
use App\Entity\Trait\HorodatageTrait;
use App\Repository\LiberalRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: LiberalRepository::class)]
#[ORM\Table(name: "liberal", indexes: [
    new ORM\Index(name: "idx_valid_from", columns: ["validFrom"]),
    new ORM\Index(name: "idx_valid_to", columns: ["validTo"]),
    new ORM\Index(name: "idx_periode_type", columns: ["periodeType"])
])]
class Liberal extends BaseEntity
{
    use HorodatageTrait;
    #[ORM\Column(length: 255, nullable: true)]
    private ?string $nom_acte = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $date_realisation = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $dateDebut = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $dateFin = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $tarif = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $code_acte = null;

    #[ORM\Column(length: 255)]
    private ?string $type_acte = null;

    #[ORM\ManyToOne(inversedBy: 'liberals')]
    private ?Agent $agent = null;

    public function getNomActe(): ?string
    {
        return $this->nom_acte;
    }

    public function setNomActe(?string $nom_acte): static
    {
        $this->nom_acte = $nom_acte;

        return $this;
    }

    public function getDateRealisation(): ?\DateTimeInterface
    {
        return $this->date_realisation;
    }

    public function setDateRealisation(?\DateTimeInterface $date_realisation): static
    {
        $this->date_realisation = $date_realisation;

        return $this;
    }

    public function getDateDebut(): ?\DateTimeInterface
    {
        return $this->dateDebut;
    }

    public function setDateDebut(?\DateTimeInterface $dateDebut): static
    {
        $this->dateDebut = $dateDebut;
        return $this;
    }

    public function getDateFin(): ?\DateTimeInterface
    {
        return $this->dateFin;
    }

    public function setDateFin(?\DateTimeInterface $dateFin): static
    {
        $this->dateFin = $dateFin;
        return $this;
    }

    public function getTarif(): ?string
    {
        return $this->tarif;
    }

    public function setTarif(?string $tarif): static
    {
        $this->tarif = $tarif;
        return $this;
    }

    public function getCodeActe(): ?string
    {
        return $this->code_acte;
    }

    public function setCodeActe(?string $code_acte): static
    {
        $this->code_acte = $code_acte;
        return $this;
    }

    public function getTypeActe(): ?string
    {
        return $this->type_acte;
    }

    public function setTypeActe(string $type_acte): static
    {
        $this->type_acte = $type_acte;

        return $this;
    }

    public function getAgent(): ?Agent
    {
        return $this->agent;
    }

    public function setAgent(?Agent $agent): static
    {
        $this->agent = $agent;
        return $this;
    }
}
