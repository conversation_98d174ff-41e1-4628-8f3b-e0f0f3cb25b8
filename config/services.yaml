# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

imports:
    - { resource: services_batch_optimization.yaml }

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices.html#use-parameters-for-application-configuration
parameters:
    # URL de base pour l'intégration des données
    # Utilisé pour les appels API vers le service d'intégration des données (DATA-INTEGRATOR)
    data_integrator_base_uri: '%env(DATA_INTEGRATOR_BASE_URI)%'

services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/'
        exclude:
            - '../src/DependencyInjection/'
            - '../src/Entity/'
            - '../src/Kernel.php'

    # Configuration spécifique pour AgentRoleAssignmentService
    App\Domain\Service\AgentRoleAssignmentService:
        arguments:
            $projectDir: '%kernel.project_dir%'

    # Configuration du service LDAP
    Symfony\Component\Ldap\Ldap:
        factory: [ 'Symfony\Component\Ldap\Ldap', 'create' ]
        arguments:
            - 'ext_ldap'
            - host: '%env(LDAP_HOST)%'
              port: 389  # ou 636 pour LDAPS
              encryption: none  # none, ssl, ou tls
              options:
                  protocol_version: 3
                  referrals: false
    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones

    # Custom filters
    App\Domain\Filter\MultiFieldSearchFilter:
        tags: ['api_platform.filter']

    # API Platform Extensions
    App\Domain\Extension\AgentUfsPeriodeExtension:
        tags: ['api_platform.doctrine.orm.query_extension.collection']

    App\Domain\Extension\ActesPeriodesExtension:
        tags: ['api_platform.doctrine.orm.query_extension.collection']

    App\Domain\Extension\ActesCascadeFiltersExtension:
        tags: ['api_platform.doctrine.orm.query_extension.collection']

    App\Domain\Extension\CrExtension:
        tags: ['api_platform.doctrine.orm.query_extension.collection']

    App\Domain\Extension\ServicesExtension:
        tags: ['api_platform.doctrine.orm.query_extension.collection']

    App\Domain\Extension\UfsExtension:
        tags: ['api_platform.doctrine.orm.query_extension.collection']

    App\Domain\Extension\PoleExtension:
        tags: ['api_platform.doctrine.orm.query_extension.collection']

    App\Domain\Extension\CoreSupraFilter\Uf\UfsCategoryFilterExtension:
        tags: ['api_platform.doctrine.orm.query_extension.collection']

    App\Domain\Extension\CoreSupraFilter\Cr\CrCategoryFilterExtension:
        tags: ['api_platform.doctrine.orm.query_extension.collection']

    App\Domain\Extension\CoreSupraFilter\Pole\PoleCategoryFilterExtension:
        tags: ['api_platform.doctrine.orm.query_extension.collection']

    App\Domain\Extension\CoreSupraFilter\Agent\AgentCategoryFilterExtension:
        tags: ['api_platform.doctrine.orm.query_extension.collection']

    App\Domain\Extension\CoreSupraFilter\Service\ServiceCategoryFilterExtension:
        tags: ['api_platform.doctrine.orm.query_extension.collection']

    # Service de cache d'authentification ultra-rapide
    App\Domain\Service\Security\AuthCacheService:
        arguments:
            $cache: '@cache.app'
