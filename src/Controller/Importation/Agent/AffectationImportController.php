<?php

namespace App\Controller\Importation\Agent;

use App\Controller\Importation\Agent\Dto\AffectationAgentUfDto;
use App\Domain\Service\Importation\AffectationImportService;
use App\Domain\Service\Importation\AffectationImportOptimizedService;
use App\Domain\Service\Notification\ErrorNotificationEmailService;
use App\Entity\Structure\EntiteJuridique;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;

/**
 * Gestion des importations d'affectations agents/UF.
 *
 * * @see AffectationImportService::importAffectations()
 * * Ce contrôleur permet d'importer des affectations d'agents à des UF
 * * en utilisant un DTO spécifique.
 * * Il attend un JSON contenant un tableau d'objets AffectationAgentUfDto.
 * * Chaque objet doit contenir les informations nécessaires pour l'affectation.
 * * Le service AffectationImportService gère la logique d'importation.
 * * En cas de succès, il retourne un JSON avec le statut, le nombre d'affectations traitées,
 * * créées, mises à jour et les erreurs rencontrées.
 */
class AffectationImportController extends AbstractController
{
    public function __construct(
        private readonly AffectationImportService $importService,
        private readonly ?AffectationImportOptimizedService $optimizedImportService,
        private readonly SerializerInterface $serializer,
        private readonly ErrorNotificationEmailService $errorNotificationService,
        private readonly EntityManagerInterface $em
    ) {}

    #[Route('/api/admin/import/{ejCode}/affectations', name: 'import_affectations', methods: ['POST'])]
    public function importAffectations(Request $request, string $ejCode): JsonResponse
    {
        $entiteJuridique = null;
        
        try {
            // Récupérer l'entité juridique pour les notifications
            $entiteJuridique = $this->em->getRepository(EntiteJuridique::class)
                ->findOneBy(['code' => $ejCode]);

            if (!$entiteJuridique) {
                throw new \Exception("Entité juridique non trouvée : {$ejCode}");
            }

            $jsonData = $request->getContent();

            if (empty($jsonData)) {
                return $this->json(['status' => 'error', 'message' => 'Aucune donnée fournie'], 400);
            }

            /** @var AffectationAgentUfDto[] $dtos */
            $dtos = $this->serializer->deserialize(
                $jsonData,
                AffectationAgentUfDto::class . '[]',
                'json'
            );

            // Utiliser le service optimisé si disponible, sinon le service standard
            if ($this->optimizedImportService) {
                $result = $this->optimizedImportService->importAffectationsOptimized($dtos, $ejCode);
                $detailedStats = $this->optimizedImportService->getDetailedStats($result);
                $optimizationsEnabled = true;
            } else {
                // Fallback vers le service standard
                $result = $this->importService->importAffectations($dtos, $ejCode);
                $detailedStats = [];
                $optimizationsEnabled = false;
            }

            return $this->json([
                'status' => 'success',
                'message' => 'Import des affectations terminé avec optimisations',
                'processed' => $result['processed'],
                'created' => $result['created'],
                'updated' => $result['updated'],
                'errors' => $result['errors'],
                'stats' => $detailedStats,
                'optimizations_enabled' => $optimizationsEnabled
            ]);

        } catch (\Throwable $e) {
            // Envoyer une notification d'erreur à l'admin si configuré
            if ($entiteJuridique) {
                $this->errorNotificationService->sendAffectationImportErrorNotification(
                    $entiteJuridique,
                    $e->getMessage(),
                    [
                        'ejCode' => $ejCode,
                        'dataSize' => strlen($request->getContent()),
                        'userAgent' => $request->headers->get('User-Agent'),
                        'clientIp' => $request->getClientIp()
                    ],
                    $this->getUser()?->getUserIdentifier()
                );
            }

            return $this->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
