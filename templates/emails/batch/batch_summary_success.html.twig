<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Import Batch Réussi - {{ entiteJuridique.code }}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background-color: #28a745; color: white; padding: 20px; margin: -30px -30px 30px -30px; border-radius: 8px 8px 0 0; }
        .header h1 { margin: 0; font-size: 24px; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin: 20px 0; }
        .stat-card { background-color: #f8f9fa; padding: 15px; border-radius: 6px; text-align: center; border-left: 4px solid #28a745; }
        .stat-number { font-size: 24px; font-weight: bold; color: #28a745; margin-bottom: 5px; }
        .stat-label { font-size: 12px; color: #6c757d; text-transform: uppercase; }
        .info-section { margin: 20px 0; padding: 15px; background-color: #e9ecef; border-radius: 6px; }
        .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6; font-size: 12px; color: #6c757d; }
        .duration { color: #007bff; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>✅ Import Batch Réussi</h1>
            <p style="margin: 10px 0 0 0; opacity: 0.9;">
                {{ batchStats.resource_type }} - {{ entiteJuridique.code }}
            </p>
        </div>

        <div class="info-section">
            <h3 style="margin-top: 0; color: #495057;">Informations du batch</h3>
            <p><strong>ID du batch :</strong> {{ batchId }}</p>
            <p><strong>Type de ressource :</strong> {{ batchStats.resource_type }}</p>
            <p><strong>Début :</strong> {{ batchStats.start_time|date('d/m/Y H:i:s') }}</p>
            <p><strong>Fin :</strong> {{ batchStats.end_time|date('d/m/Y H:i:s') }}</p>
            <p><strong>Durée :</strong> <span class="duration">{{ batchStats.duration.format('%H:%I:%S') }}</span></p>
            {% if batchStats.emails_disabled %}
            <p><strong>⚡ Optimisations activées :</strong> Notifications désactivées pendant l'import</p>
            {% endif %}
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">{{ batchStats.total_records }}</div>
                <div class="stat-label">Total</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ batchStats.processed }}</div>
                <div class="stat-label">Traités</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ batchStats.created }}</div>
                <div class="stat-label">Créés</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ batchStats.updated }}</div>
                <div class="stat-label">Mis à jour</div>
            </div>
        </div>

        {% if batchStats.total_records >= 1000 %}
        <div style="background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 15px; border-radius: 6px; margin: 20px 0;">
            <strong>🚀 Gros volume traité :</strong> 
            Ce batch de {{ batchStats.total_records }} enregistrements a été traité avec les optimisations automatiques activées.
        </div>
        {% endif %}

        <div class="info-section">
            <h3 style="margin-top: 0; color: #495057;">Résumé</h3>
            <p>L'import de <strong>{{ batchStats.total_records }}</strong> enregistrements de type <strong>{{ batchStats.resource_type }}</strong> s'est terminé avec succès.</p>
            
            {% if batchStats.created > 0 and batchStats.updated > 0 %}
                <p>{{ batchStats.created }} nouveaux enregistrements ont été créés et {{ batchStats.updated }} enregistrements existants ont été mis à jour.</p>
            {% elseif batchStats.created > 0 %}
                <p>{{ batchStats.created }} nouveaux enregistrements ont été créés.</p>
            {% elseif batchStats.updated > 0 %}
                <p>{{ batchStats.updated }} enregistrements existants ont été mis à jour.</p>
            {% endif %}
        </div>

        <div class="footer">
            <p>Cet email a été généré automatiquement par le système SUPRA.</p>
            <p>Entité juridique : {{ entiteJuridique.code }} | {{ batchStats.end_time|date('d/m/Y H:i:s') }}</p>
        </div>
    </div>
</body>
</html>
