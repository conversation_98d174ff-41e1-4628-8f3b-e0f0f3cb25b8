{% extends '@!ApiPlatform/SwaggerUi/index.html.twig' %}

{# REMPLACER le header par défaut #}
{% block header %}
    <header class="supra-header">
        <div class="supra-header__left">
            <img src="{{ asset('assets/images/chru-nancy-logo.png') }}" alt="CHRU de Nancy" class="supra-logo"/>
            <div class="supra-title">
                <div class="supra-title__main">SUPRA API</div>
                <div class="supra-title__sub">CHRU de Nancy — Documentation technique</div>
            </div>
        </div>
        <div class="supra-header__right">
            <span class="env-badge">Env: {{ app.environment|upper }}</span>
            <a href="/manager/dtnib/admin" target="_blank" class="btn ghost">Administration</a>
        </div>
    </header>
{% endblock %}

{# CSS pour masquer le header SWAGGER UI qui se génère dans #swagger-ui #}
{% block stylesheet %}
    {{ parent() }}
    <style>

        /* RESET complet du body et html */
        html, body {
            margin: 0 !important;
            padding: 0 !important;
        }


        /* Masquer TOUS les éléments qui créent de l'espace */
        .svg-icons,
        .web,
        .webby {
            display: none !important;
        }

        /* SUPPRIMER le faux header créé par ::before */
        header::before,
        .supra-header::before,
        body::before,
        *::before {
            display: none !important;
            content: none !important;
        }

        /* Styles pour ton header - COLLÉ EN HAUT */
        .supra-header {
            position: sticky;
            top: 0;
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px 24px;
            background: linear-gradient(90deg, #0e7490, #0b5a72);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin: 0 !important; /* IMPORTANT : pas de margin */
        }

        .supra-header__left { display: flex; align-items: center; gap: 16px; }
        .supra-logo { height: 40px; background: #fff; border-radius: 8px; padding: 8px; }
        .supra-title__main { font-weight: 700; font-size: 20px; color: #fff; margin: 0; }
        .supra-title__sub { font-size: 13px; color: rgba(255,255,255,0.85); margin: 0; }
        .supra-header__right { display: flex; align-items: center; gap: 12px; }

        .env-badge {
            font-size: 11px; font-weight: 600; padding: 6px 10px; border-radius: 999px;
            background: rgba(255,255,255,0.2); color: #fff; text-transform: uppercase;
        }

        .btn {
            font-size: 12px; padding: 8px 12px; border-radius: 6px;
            border: 1px solid rgba(255,255,255,0.3); color: #fff;
            background: transparent; text-decoration: none;
        }
        .btn:hover { background: rgba(255,255,255,0.1); }

    </style>
{% endblock %}