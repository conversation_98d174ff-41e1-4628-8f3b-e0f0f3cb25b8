<?php

namespace App\ApiResource\Structure;

use ApiPlatform\Doctrine\Orm\State\Options;
use ApiPlatform\Metadata\ApiProperty;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\GetCollection;
use App\ApiResource\Base\BaseDto;
use App\Entity\Structure\EntiteJuridique;
use App\State\EntityClassDtoStateProcessor;
use App\State\EntityToDtoStateProvider;

#[ApiResource(
    shortName: 'ej',
    operations: [
        new Get(),
        new GetCollection(),
    ],
    paginationItemsPerPage: 10,
    provider: EntityToDtoStateProvider::class,
    processor: EntityClassDtoStateProcessor::class,
    stateOptions: new Options(entityClass: EntiteJuridique::class),
)]
class EntiteJuridiqueDto extends BaseDto
{
    #[ApiProperty(description: "Nom de l'hôpital", example: "CHU Nancy")]
    public ?string $nom = null;

    #[ApiProperty(description: "Code de l'hôpital", example: "CHU-54")]
    public ?string $code = null;

    #[ApiProperty(description: "Adresse de l'hôpital", example: "1 Rue de la Santé, Nancy")]
    public ?string $adresse = null;

    #[ApiProperty(description: "Telephone de l'hôpital", example: "**********")]
    public ?string $telephone = null;

    #[ApiProperty(description: "email de l'hôpital", example: "<EMAIL>")]
    public ?string $email = null;

    #[ApiProperty(description: "Date de création de l'hôpital", example: "2000-01-01")]
    public ?\DateTimeInterface $dateCreation = null;

    #[ApiProperty(
        description: "Lien vers le Tenant de l'hôpital",
        example: "/api/tenants/123e4567-e89b-12d3-a456-426614174000"
    )]
    public ?TenantDto $tenant = null;

    #[ApiProperty(description: "Paramètres de configuration de l'hôpital", example: "{\"addresses\":{\"boAgentFilesPath\":\"/mnt/bo/files/agents\"},\"theme\":{\"primaryColor\":\"#0055A4\"}}")]
    public ?array $parametre = null;
}
