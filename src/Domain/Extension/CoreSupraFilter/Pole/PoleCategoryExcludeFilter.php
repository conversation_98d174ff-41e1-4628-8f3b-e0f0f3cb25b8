<?php

namespace App\Domain\Extension\CoreSupraFilter\Pole;

/**
 * Filtre de codes de pôles à exclure du retour de l'API.
 *
 * OBJECTIF : Restreindre l'accès aux listes de pôles pour masquer certains pôles
 * dans les endpoints de collection (GET /api/poles). Les accès individuels restent ouverts.
 *
 * FONCTIONNEMENT :
 * - Les codes listés dans EXCLUDED_POLECODES sont filtrés des collections
 * - La comparaison est insensible à la casse (UPPER() en SQL)
 * - Les pôles avec polecode = NULL sont INCLUS par défaut
 * - Le filtrage ne s'applique PAS aux accès individuels (GET /api/poles/{id})
 *
 * CONFIGURATION ACTUELLE :
 * - Exclus : Pôles identifiés comme non pertinents pour l'affichage public
 * - Inclus : Pôles opérationnels + pôles sans code défini
 *
 * @todo Affiner la liste des codes avec l'équipe métier
 * @todo Vérifier si d'autres critères de filtrage sont nécessaires (dates, statuts)
 *
 * @see PoleCategoryFilterExtension Pour l'implémentation Doctrine
 */
class PoleCategoryExcludeFilter
{
    /**
     * Codes de pôles à exclure du retour API
     *
     * ASTUCE : Maintenir cette liste triée par ordre croissant pour faciliter la maintenance
     * Lors d'un ajout, insérez le nouveau code à sa place dans l'ordre numérique, pas obligé mais utile
     */
    private const EXCLUDED_POLECODES = [
        '100', // DIRECTION GENERALE
        '300', // DIRECTION DES SOINS
        '310', // HOPITAUX URBAINS / OPERATIONNEL HOPITAUX URB
        '320', // HOPITAUX DE BRABOIS / OPERATIONNEL HOPITAUX DE BRABOIS
        '330', // DEPARTEMENT TERRITORIAL DES FINANCES / PÔLE FINANCES
        '340', // RESSOURCES HUMAINES ET AFFAIRES SOCIALES
        '350', // INGENIERIE - LOGISTIQUE - PATRIMOINE - PROXIMITE
        '360', // DEPARTEMENT TERRITORIAL DE LA QUALITE DES USAGERS
        '370', // STRATEGIE ET INNOVATION / DEPARTEMENT STRATEGIE INNOVATION
        '380', // DEPARTEM.TERRITORIAL VILLE MEDICO-SOCIAL HOPITAL
        '390', // TRANSFO NUM ET ING BIOMED
        '410', // DIRECTIONS / RESSOURCES ET SYSTEME D'I
        '420', // DPT TERRITORIAL ARCHITECT.INGENIER.NV.HOP.
        '510', // QUALITE SANTE PUBLIQUE SECURITE DU PATIENT
        '520', // DEPARTEMENT RECHERCHE INNOVATION / PROGRAMMES PROJETS DE RECHERCHE
        '900', // ETABLISSEMENTS EXTERIEURS
        // Ajoutez vos nouveaux codes à leur place dans l'ordre croissant

        /**
         * PÔLES EXCLUS PAR DÉFAUT :
         * Cette liste exclut automatiquement tous les pôles non-médicaux identifiés :
         * - Direction générale et directions opérationnelles (5 pôles)
         * - Services supports et transversaux (qualité, RH, finances) (4 pôles)
         * - Logistique, patrimoine et innovation (5 pôles)
         * - Recherche, stratégie et territoires (3 pôles)
         *
         * TOTAL : 17 pôles exclus pour ne conserver que les pôles de soins directs
         * aux patients dans les listes publiques de l'API.
         *
         */
    ];

    public static function isExcluded(?string $polecode): bool
    {
        if ($polecode === null) {
            return false;
        }

        return in_array(strtoupper($polecode), array_map('strtoupper', self::EXCLUDED_POLECODES), true);
    }

    public static function getExcludedPolecodes(): array
    {
        return self::EXCLUDED_POLECODES;
    }
}