<?php

namespace App\ApiResource\Activite;

use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Get;
use App\State\PraticienSingleActeStatsStateProvider;

#[ApiResource(
    operations: [
        new Get(
            uriTemplate: '/actes/{id}/praticien-single-stats',
            provider: PraticienSingleActeStatsStateProvider::class
        )
    ]
)]
class PraticienSingleActeStatsResource
{
    public function __construct(
        public readonly string $id
    ) {}
}
