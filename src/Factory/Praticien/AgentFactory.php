<?php

namespace App\Factory\Praticien;

use App\Entity\Praticien\Agent;
use App\Factory\Structure\EntiteJuridiqueFactory;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Uid\Uuid;
use Zenstruck\Foundry\Persistence\PersistentProxyObjectFactory;

/**
 * @extends PersistentProxyObjectFactory<Agent>
 */
final class AgentFactory extends PersistentProxyObjectFactory
{
    private UserPasswordHasherInterface $passwordHasher;

    public function __construct(UserPasswordHasherInterface $passwordHasher)
    {
        $this->passwordHasher = $passwordHasher;
    }

    public static function class(): string
    {
        return Agent::class;
    }

    protected function defaults(): array|callable
    {
        return [
            // Champs de base
            'matricule' => self::faker()->unique()->numerify('AG########'),
            // CollecteurTrait fields
            'email' => self::faker()->unique()->safeEmail(),
            'nom' => self::faker()->lastName(),
            'prenom' => self::faker()->firstName(),
            'hrUser' => self::faker()->unique()->numerify('HR######'),
            'titre' => self::faker()->randomElement(['Dr', 'Pr', 'M.', 'Mme']),
            'categorie' => self::faker()->randomElement(['PH', 'PU-PH', 'MCU-PH']),
            'etablissement' => self::faker()->numerify('ETB###'),
            'dateDepart' => self::faker()->optional(0.3)->dateTimeBetween('+1 year', '+5 years'),
            'dateVenue' => self::faker()->dateTimeBetween('-5 years', 'now'),
            'dateMaj' => self::faker()->dateTimeThisMonth(),
            'createurFiche' => self::faker()->userName(),
            'isAdmin' => false,
            'isAnesthesiste' => self::faker()->boolean(20),

            // NativeFieldTrait fields
            'fullName' => self::faker()->name(),
            'imgUrl' => self::faker()->imageUrl(),
            'badge' => self::faker()->numerify('BADGE####'),
            'password' => 'password',
            'roles' => ['ROLE_USER'],

            // Relations - on supprime la référence automatique qui cause les problèmes
            // 'hopital' sera défini explicitement dans les fixtures
        ];
    }

    protected function initialize(): static
    {
        return $this->afterInstantiate(function (Agent $user): void {
            if ($user->getPassword()) {
                $hashedPassword = $this->passwordHasher->hashPassword($user, $user->getPassword());
                $user->setPassword($hashedPassword);
            }
        });
    }

    public static function createAdmin(): Agent
    {
        return self::createOne([
            'email' => '<EMAIL>',
            'password' => 'Admin123',
            'roles' => ['ROLE_ADMIN'],
            'isAdmin' => true,
        ]);
    }
}