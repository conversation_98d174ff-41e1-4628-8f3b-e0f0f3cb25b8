<?php

namespace App\Domain\Service\Importation;

use App\Controller\Importation\Agent\Dto\AffectationAgentUfDto;
use App\Domain\Converter\DataConverter;
use App\Domain\Service\Core\AgentFinderService;
use App\Domain\Service\Core\UfsFinderService;
use App\Domain\Service\Notification\ErrorNotificationEmailService;
use App\Entity\Praticien\Agent;
use App\Entity\Praticien\AgentUfs;
use App\Entity\Structure\EntiteJuridique;
use App\Entity\Structure\Ufs;
use Doctrine\ORM\EntityManagerInterface;

class AffectationImportService
{
    public function __construct(
        private readonly EntityManagerInterface $em,
        private readonly  UfsFinderService $ufsFinderService,
        private readonly AgentFinderService $agentFinderService,
        private readonly ErrorNotificationEmailService $errorNotificationService,
    ) {}

    /**
     * @param AffectationAgentUfDto[] $dtos
     */
    public function importAffectations(array $dtos, string $ejCode): array
    {
        // Validation de l'entité juridique en début de traitement
        $entiteJuridique = $this->em->getRepository(EntiteJuridique::class)
            ->findOneBy(['code' => $ejCode]);

        if (!$entiteJuridique) {
            throw new \Exception("Entité juridique non trouvée : {$ejCode}");
        }

        $result = [
            'processed' => 0,
            'created' => 0,
            'updated' => 0,
            'errors' => []
        ];

        foreach ($dtos as $dto) {
            try {
                $this->processAffectation($dto, $result, $entiteJuridique);
                $result['processed']++;
            } catch (\Exception $e) {
                $result['errors'][] = [
                    'uConnexion' => $dto->uConnexion,
                    'codeUf' => $dto->codeUf,
                    'error' => $e->getMessage()
                ];
                
                // Envoyer les notifications d'erreur si configurées
                try {
                    // Notifier le responsable DAM
                    $this->notifyDamResponsable($entiteJuridique, $e);
                    
                    // Notifier l'admin des affectations
                    $this->notifyAdminAffectations($entiteJuridique, $e);
                    
                    // Notifier l'admin des erreurs générales
                    $this->notifyAdminsOfError($entiteJuridique, $e);
                } catch (\Exception $notificationError) {
                    // Si l'envoi de notification échoue, on ne fait que logger pour ne pas masquer l'erreur principale
                    error_log('Erreur lors de l\'envoi de notifications : ' . $notificationError->getMessage());
                }
            }
        }

        $this->em->flush();
        return $result;
    }

    private function processAffectation(AffectationAgentUfDto $dto, array &$result, EntiteJuridique $entiteJuridique): void
    {
        // 1. Validation et récupération de l'agent
        if (!$dto->uConnexion) {
            throw new \Exception("le uConnexion est obligotoire : {$dto->uConnexion}");
        }
        $agent = $this->agentFinderService->findAgentByConnexionAndEj($dto->uConnexion, $entiteJuridique);

        if (!$agent) {
            throw new \Exception("Agent non trouvé : {$dto->uConnexion}");
        }

        if (!empty($dto->matricule)) {
            $agent->setMatricule(DataConverter::cleanString($dto->matricule));
        }

        // 2. Validation des dates
        $dateDebut = DataConverter::parseDateFromFormat($dto->dateDebut, 'd/m/Y');
        $dateFin = DataConverter::parseDateFromFormat($dto->dateFin, 'd/m/Y');
        

        if (!$dateDebut || !$dateFin) {
            throw new \Exception("Dates invalides : {$dto->dateDebut} - {$dto->dateFin}");
        }

        // 3. Recherche UFS avec validation SCD Type 2
        $ufs = $this->ufsFinderService->findUfByCodeAndEj(
            $dto->codeUf,
            $entiteJuridique,
            null, // pas de dateReference
            $dateDebut, // période complète
            $dateFin
        );

        if (!$ufs) {
            throw new \Exception("UFS non trouvée ou invalide pour la période : {$dto->codeUf}");
        }

        // 4. Recherche ou création de l'affectation
        $agentUfs = $this->findOrCreateAgentUfs($agent, $ufs, $dateDebut, $dateFin);

        $isNew = $agentUfs->getId() === null;

        // 5. Mise à jour des données d'affectation
        $this->updateAffectationData($agentUfs, $dto, $dateDebut, $dateFin);

        if ($isNew) {
            $result['created']++;
            $this->em->persist($agentUfs);
        } else {
            $result['updated']++;
        }
    }


    private function findOrCreateAgentUfs(Agent $agent, Ufs $ufs, \DateTimeInterface $dateDebut, \DateTimeInterface $dateFin): AgentUfs
    {
        // Recherche d'une affectation existante qui se chevauche
        $existing = $this->em->getRepository(AgentUfs::class)
            ->createQueryBuilder('au')
            ->where('au.agent = :agent')
            ->andWhere('au.ufs = :ufs')
            ->andWhere('au.date_debut <= :dateFin')
            ->andWhere('au.date_fin >= :dateDebut')
            ->setParameter('agent', $agent)
            ->setParameter('ufs', $ufs)
            ->setParameter('dateDebut', $dateDebut)
            ->setParameter('dateFin', $dateFin)
            ->getQuery()
            ->getOneOrNullResult();

        if ($existing) {
            return $existing;
        }

        // Création d'une nouvelle affectation
        $agentUfs = new AgentUfs();
        $agentUfs->setAgent($agent);
        $agentUfs->setUfs($ufs);

        return $agentUfs;
    }

    private function updateAffectationData(AgentUfs $agentUfs, AffectationAgentUfDto $dto, \DateTimeInterface $dateDebut, \DateTimeInterface $dateFin): void
    {
       $this->populateAffectation($agentUfs,$dto, $dateDebut, $dateFin);
       //Cette logique assume qu'il ne peut y avoir qu'une seule affectation par agent/UFS pour une période donnée, ce qui est cohérent avec la gestion des affectations RH.
    }

    private function populateAffectation(AgentUfs $affectation, AffectationAgentUfDto $dto,\DateTimeInterface $dateDebut, \DateTimeInterface $dateFin): void
    {
        $affectation->setDateDebut($dateDebut);
        $affectation->setDateFin($dateFin);

        // Affectation de validFrom à dateDebut et validTo à null
        $affectation->setValidFrom($dateDebut);
        $affectation->setValidTo(null);

        // Calcul du type de période basé sur les dates converties (pas les DTOs)
//        $periodeType = \App\Domain\Enum\PeriodeType::ANNUEL;
        $interval = $dateDebut->diff($dateFin);
        $days = (int)$interval->format('%a');

        if ($days < 32) {
            $periodeType = \App\Domain\Enum\PeriodeType::HEBDOMADAIRE;
        } elseif ($days < 366) {
            $periodeType = \App\Domain\Enum\PeriodeType::MENSUEL;
        } else {
            $periodeType = \App\Domain\Enum\PeriodeType::ANNUEL;
        }

        $affectation->setPeriodeType($periodeType);
        $affectation->setSource($dto->source ?? 'IMPORTATION Manuelle');

        // Utilisation des méthodes statiques de DataConverter
        $affectation->setRgt(DataConverter::cleanString($dto->rgt));
        $affectation->setEtpStatutaire(DataConverter::toFloat($dto->etpStatutaire));
        $affectation->setTauxAffectation(DataConverter::toInt($dto->tauxAffectation));
        $affectation->setSommeEtp(DataConverter::toFloat($dto->sommeEtp));
        $affectation->setAffectationPrincipale(DataConverter::cleanString($dto->affectationPrincipale));
        $affectation->setTypeGrade(DataConverter::cleanString($dto->typeGrade));
        $affectation->setLibelleGrade(DataConverter::cleanString($dto->libelleGrade));
        $affectation->setAbsences(DataConverter::toInt($dto->absences));

        // Calcul automatique de l'ETP basé sur l'ETP statutaire et le taux d'affectation
        $etp = DataConverter::calculateEtp(
            DataConverter::toFloat($dto->etpStatutaire),
            DataConverter::toInt($dto->tauxAffectation)
        );
        $affectation->setEtp($etp);
    }
    
    /**
     * Notifie les admins des entités juridiques ayant activé les notifications d'erreur générale
     */
    private function notifyAdminsOfError(EntiteJuridique $entiteJuridique, \Throwable $exception): void
    {
        try {
            // Vérifier si les notifications d'erreur admin sont activées
            if (!$entiteJuridique->isNotifierAdminDesErreurProduite()) {
                return;
            }
            
            $this->errorNotificationService->sendErrorNotificationToAdmin(
                $entiteJuridique,
                'Import d\'affectations',
                $exception->getMessage(),
                [
                    'type' => 'Service d\'importation',
                    'service' => 'AffectationImportService',
                    'stackTrace' => $exception->getTraceAsString(),
                    'file' => $exception->getFile(),
                    'line' => $exception->getLine()
                ]
            );
        } catch (\Exception $notificationError) {
            // Si l'envoi de notification échoue, on ne fait que logger pour ne pas masquer l'erreur principale
            error_log('Erreur lors de l\'envoi de notifications d\'erreur : ' . $notificationError->getMessage());
        }
    }
    
    /**
     * Notifie les admins des entités juridiques ayant activé les notifications d'erreur d'affectation
     */
    private function notifyAdminAffectations(EntiteJuridique $entiteJuridique, \Throwable $exception): void
    {
        try {
            // Vérifier si les notifications d'erreur admin pour affectations sont activées
            if (!$entiteJuridique->isNotifierErreurAuAdminPourAffectation()) {
                return;
            }
            
            $this->errorNotificationService->sendAffectationImportErrorNotification(
                $entiteJuridique,
                $exception->getMessage(),
                [
                    'service' => 'AffectationImportService',
                    'stackTrace' => $exception->getTraceAsString(),
                    'file' => $exception->getFile(),
                    'line' => $exception->getLine()
                ]
            );
        } catch (\Exception $notificationError) {
            // Si l'envoi de notification échoue, on ne fait que logger pour ne pas masquer l'erreur principale
            error_log('Erreur lors de l\'envoi de notifications d\'erreur d\'affectation : ' . $notificationError->getMessage());
        }
    }
    
    /**
     * Notifie le responsable DAM des entités juridiques ayant activé les notifications
     */
    private function notifyDamResponsable(EntiteJuridique $entiteJuridique, \Throwable $exception): void
    {
        try {
            // Vérifier si les notifications au responsable DAM sont activées
            if (!$entiteJuridique->isNotifierErreurAuResponssableDamAffectation()) {
                return;
            }
            
            // Utiliser la méthode sendAffectationImportErrorNotification avec des données spécifiques pour le responsable DAM
            $this->errorNotificationService->sendAffectationImportErrorNotification(
                $entiteJuridique,
                $exception->getMessage(),
                [
                    'service' => 'AffectationImportService',
                    'destinataire' => 'Responsable DAM',
                    'stackTrace' => $exception->getTraceAsString(),
                    'file' => $exception->getFile(),
                    'line' => $exception->getLine()
                ],
                'Responsable DAM'
            );
        } catch (\Exception $notificationError) {
            // Si l'envoi de notification échoue, on ne fait que logger pour ne pas masquer l'erreur principale
            error_log('Erreur lors de l\'envoi de notifications au responsable DAM : ' . $notificationError->getMessage());
        }
    }
}