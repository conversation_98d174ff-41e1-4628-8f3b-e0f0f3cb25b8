<?php

namespace App\Domain\Service\Importation;

use App\Command\ActesImportCommand;
use App\Controller\Importation\Actes\Dto\ActeImportDto;
use App\Domain\Service\Notification\ErrorNotificationEmailService;
use App\Entity\Activite\Actes;
use App\Entity\Praticien\Agent;
use App\Entity\Structure\EntiteJuridique;
use App\Entity\Structure\Ufs;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Doctrine\ORM\EntityManagerInterface;

/**
 * Service d'importation des actes depuis le data_integrator avec optimisation mémoire.
 *
 * Comportement UPSERT :
 * - Création ou mise à jour des actes
 * - Liaison avec l'agent via hrUser
 * - Liaison avec les UFs via leurs codes
 * - Gestion de la périodicité et de l'horodatage
 * - Synchronisation avec les données du data_integrator
 *
 * OPTIMISATIONS MÉMOIRE IMPLÉMENTÉES :
 * - Cache d'entités pour réduire les requêtes redondantes à la base de données
 * - Requêtes optimisées avec QueryBuilder au lieu de findBy/findOneBy
 * - Requêtes avec LOWER() pour comparaison insensible à la casse sans charger toutes les entités
 * - Taille de batch dynamique qui s'ajuste selon l'utilisation mémoire
 * - Transactions plus petites (max 500 items) au lieu d'une seule grande transaction
 * - Nettoyage régulier du cache Doctrine et garbage collection forcé
 * - Mesures d'urgence en cas de consommation mémoire excessive (>1GB)
 * - Réinitialisation du cache d'entités après chaque transaction
 * - Utilisation de setMaxResults(1) pour limiter les résultats des requêtes
 * - Monitoring de l'augmentation de la mémoire pour ajuster les paramètres dynamiquement
 *
 * PARAMÈTRES RECOMMANDÉS :
 * - Environnements contraints en mémoire: pageSize=20, batchSize=20
 * - Environnements très contraints: pageSize=10, batchSize=10
 * - Environnements avec mémoire suffisante: pageSize=100, batchSize=50
 *
 * @see ActesImportCommand
 */
class ActeImportService
{
    /**
     * Indique si un agent par défaut a été utilisé pendant l'importation
     */
    private bool $defaultAgentUsed = false;
    
    /**
     * Liste des actes assignés à l'agent par défaut
     */
    private array $actesAssignedToDefaultAgent = [];
    
    public function __construct(
        private readonly HttpClientInterface $client,
        private readonly EntityManagerInterface $em,
        #[Autowire('%data_integrator_base_uri%')] private readonly string $baseUri,
        private readonly ?ErrorNotificationEmailService $errorNotificationService = null,
    ) {}

    /**
     * Importe les actes depuis le data_integrator
     * 
     * @param string|null $date Date spécifique pour l'importation (format Y-m-d)
     * @param callable|null $progressCallback Fonction de callback pour suivre la progression
     *        La signature du callback est: function(int $currentPage, int $totalPages, int $processedItems, int $totalItems, float $currentMemory, float $peakMemory)
     * @return array Statistiques de l'importation
     */
    /**
     * Cache for frequently accessed entities to reduce database queries
     */
    private array $entityCache = [
        'entiteJuridique' => [],
        'agent' => [],
        'uf' => [],
        'acte' => []
    ];
    
    /**
     * Importe les actes depuis le data_integrator avec optimisation mémoire
     * 
     * IMPORTANT: Cette méthode gère ses propres transactions pour optimiser la mémoire.
     * Elle découpe l'importation en plusieurs petites transactions (max 500 items par transaction)
     * pour éviter les problèmes de mémoire avec de grandes transactions.
     * 
     * Ne pas envelopper l'appel à cette méthode dans une transaction externe,
     * car cela créerait des transactions imbriquées qui ne fonctionnent pas comme prévu avec Doctrine.
     * Seule la transaction la plus externe serait réellement commitée à la fin.
     * 
     * @param string|null $date Date spécifique pour l'importation (format Y-m-d)
     * @param callable|null $progressCallback Fonction de callback pour suivre la progression
     * @param int $initialBatchSize Taille initiale du batch (sera ajustée dynamiquement)
     * @param int $pageSize Taille de page pour les requêtes API
     * @return array Statistiques de l'importation
     */
    public function importActes(
        ?string $date = null, 
        ?callable $progressCallback = null,
        int $initialBatchSize = 20,
        int $pageSize = 25
    ): array
    {
        // Initialize performance monitoring
        $startTime = microtime(true);
        $peakMemory = 0;
        $batchSize = $initialBatchSize; // Taille de batch dynamique, commence plus petit
        $batchCount = 0;
        $memoryReadings = [];
        $processedItems = 0;
        $lastMemoryUsage = 0;
        $memoryIncreaseRate = 0;
        
        // Réinitialiser le cache d'entités
        $this->entityCache = [
            'entiteJuridique' => [],
            'agent' => [],
            'uf' => [],
            'acte' => []
        ];
    
        // Log initial memory usage
        $initialMemory = memory_get_usage(true) / 1024 / 1024;
        $memoryReadings[] = $initialMemory;
        $lastMemoryUsage = $initialMemory;
        
        $refreshParams = [];
        if ($date) {
            $refreshParams['date'] = $date;
        }

        // Clear entity manager before starting to free memory
        $this->em->clear();
        if (gc_enabled()) {
            $collectedCycles = gc_collect_cycles();
            error_log(sprintf('Garbage collector: %d cycles collected before refresh', $collectedCycles));
        }

        // Log memory usage before API call
        error_log(sprintf('Memory usage before API refresh call: %.2f MB', memory_get_usage(true) / 1024 / 1024));
        
        $refresh = $this->client->request('GET', $this->baseUri . '/activites/refresh', [
            'query' => $refreshParams,
            'timeout' => 600,
            'max_duration'=> 600
        ])->toArray();
        
        // Check if the operation was successful (either success key is true or total exists and is > 0)
        $message = $refresh['message'] ?? '';
        $total = $refresh['total'] ?? 0;
        $isSuccess = ($refresh['success'] ?? false) === true || ($total > 0);
        
        if (!$isSuccess) {
            throw new \Exception('Erreur lors du refresh des actes : ' . $message);
        }

        // Force garbage collection after API call
        $this->em->clear();
        if (gc_enabled()) {
            gc_collect_cycles();
        }

        $page = 1;
        $totalPages = 1;
        $stats = [
            'processed' => 0, 
            'created' => 0, 
            'updated' => 0, 
            'errors' => [], 
            'performance' => [],
            'refresh' => $refresh, // Store the refresh information in the stats
            'memory' => [
                'initial' => $initialMemory,
                'readings' => $memoryReadings,
                'batchSizeAdjustments' => []
            ]
        ];

        // Utiliser des transactions plus petites au lieu d'une seule grande transaction
        $transactionCount = 0;
        $maxItemsPerTransaction = 500; // Maximum d'éléments par transaction
        $itemsInCurrentTransaction = 0;

        do {
            // Start a new transaction if needed
            if ($itemsInCurrentTransaction === 0) {
                $this->em->beginTransaction();
                $transactionCount++;
                error_log(sprintf('Starting transaction #%d', $transactionCount));
            }
            
            $queryParams = ['page' => $page];
            if ($date) {
                $queryParams['date'] = $date;
            }

            // Log memory usage before API call
            error_log(sprintf('Memory usage before API page call (page %d): %.2f MB', $page, memory_get_usage(true) / 1024 / 1024));
            
            // Use the provided pageSize parameter
            $res = $this->client->request('GET', $this->baseUri . '/activites', [
                'query' => [
                    'page' => $page,
                    'pageSize' => $pageSize
                ],
                'timeout' => 600,
                'max_duration'=> 600
            ])->toArray();
            
            // Use "ej0001" as default when API doesn't return ejCode
            $ejCode = $res['ejCode'] ?? "ej0001";
            
            // Get total items and pages
            $totalItems = $res['total'] ?? 0;
            $totalPages = $res['paginationTotal'] ?? 1;
            
            // Monitor memory before processing page
            $currentMemory = memory_get_usage(true) / 1024 / 1024;
            $memoryReadings[] = $currentMemory;
            $peakMemory = max($peakMemory, $currentMemory);
            
            // Calculate memory increase rate
            $memoryIncrease = $currentMemory - $lastMemoryUsage;
            $memoryIncreaseRate = $memoryIncrease > 0 ? $memoryIncrease : 0;
            $lastMemoryUsage = $currentMemory;
            
            // Adjust batch size dynamically based on memory usage
            if ($memoryIncreaseRate > 5) {
                // Memory is increasing too quickly, reduce batch size
                $newBatchSize = max(5, (int)($batchSize * 0.7));
                if ($newBatchSize !== $batchSize) {
                    $stats['memory']['batchSizeAdjustments'][] = [
                        'page' => $page,
                        'oldSize' => $batchSize,
                        'newSize' => $newBatchSize,
                        'reason' => 'High memory increase rate: ' . number_format($memoryIncreaseRate, 2) . ' MB'
                    ];
                    $batchSize = $newBatchSize;
                    error_log(sprintf('Reducing batch size to %d due to high memory increase rate (%.2f MB)', $batchSize, $memoryIncreaseRate));
                }
            } elseif ($memoryIncreaseRate < 1 && $batchCount > 0 && $batchSize < $initialBatchSize * 2) {
                // Memory is stable, we can try to increase batch size slightly
                $newBatchSize = min($initialBatchSize * 2, (int)($batchSize * 1.2));
                if ($newBatchSize !== $batchSize) {
                    $stats['memory']['batchSizeAdjustments'][] = [
                        'page' => $page,
                        'oldSize' => $batchSize,
                        'newSize' => $newBatchSize,
                        'reason' => 'Low memory increase rate: ' . number_format($memoryIncreaseRate, 2) . ' MB'
                    ];
                    $batchSize = $newBatchSize;
                    error_log(sprintf('Increasing batch size to %d due to low memory increase rate (%.2f MB)', $batchSize, $memoryIncreaseRate));
                }
            }
            
            // Call progress callback if provided
            if ($progressCallback !== null) {
                $progressCallback($page, $totalPages, $processedItems, $totalItems, $currentMemory, $peakMemory);
            }
            
            // Emergency memory check before processing page
            if ($currentMemory > 1000) { // 1GB threshold
                error_log('EMERGENCY MEMORY CLEANUP: Memory usage exceeds 1GB. Forcing cleanup...');
                $this->em->flush();
                $this->em->clear();
                $this->entityCache = [
                    'entiteJuridique' => [],
                    'agent' => [],
                    'uf' => [],
                    'acte' => []
                ];
                if (gc_enabled()) {
                    gc_collect_cycles();
                }
                $currentMemory = memory_get_usage(true) / 1024 / 1024;
                error_log(sprintf('Memory after emergency cleanup: %.2f MB', $currentMemory));
            }
            
            foreach ($res['data'] as $acteData) {
                try {
                    // Création du DTO
                    $dto = new ActeImportDto();
                    $dto->internum = $acteData['internum'] ?? null;
                    $dto->codeActe = $acteData['codeActe'] ?? null;
                    $dto->descriptionActe = $acteData['descriptionActe'] ?? null;
                    $dto->typeActe = $acteData['typeActe'] ?? null;
                    $dto->anneeActe = $acteData['anneeActe'] ?? null;
                    $dto->moisActe = $acteData['moisActe'] ?? null;
                    $dto->dateActe = $acteData['dateActe'] ?? null;
                    $dto->semaineIso = $acteData['semaineIso'] ?? null;
                    $dto->periodeType = $acteData['periodeType'] ?? null;
                    $dto->nombreActes = $acteData['nombreActes'] ?? null;
                    $dto->praticienMatricule = $acteData['praticienMatricule'] ?? null;
                    $dto->ufPrincipalCode = $acteData['ufPrincipalCode'] ?? null;
                    $dto->source = $acteData['source'] ?? null;
                    $dto->typeVenue = $acteData['typeVenue'] ?? null;
                    $dto->libTypeVenue = $acteData['libTypeVenue'] ?? null;
                    $dto->ufDemande = $acteData['ufDemande'] ?? null;
                    $dto->libUfDemande = $acteData['libUfDemande'] ?? null;
                    $dto->ufIntervention = $acteData['ufIntervention'] ?? null;
                    $dto->libUfIntervention = $acteData['libUfIntervention'] ?? null;
                    $dto->icrA = $acteData['icrA'] ?? null;
                    $dto->coefficient = $acteData['coefficient'] ?? null;
                    $dto->lettreCoef = $acteData['lettreCoef'] ?? null;
                    $dto->regroupement = $acteData['regroupement'] ?? null;
                    $dto->activite = $acteData['activite'] ?? null;
                    $dto->activiteLib = $acteData['activiteLib'] ?? null;

                    // Validation
                    $dto->validate();

                    // Persistance with optimized entity lookup
                    $isCreated = $this->upsertActeOptimized($acteData, $ejCode, $refresh);
                    $stats['processed']++;
                    $processedItems++;
                    $itemsInCurrentTransaction++;
                    
                    if ($isCreated) {
                        $stats['created']++;
                    } else {
                        $stats['updated']++;
                    }
                    
                    // Batch processing
                    $batchCount++;
                    if ($batchCount >= $batchSize) {
                        // Flush and clear periodically to free memory
                        $this->em->flush();
                        $this->em->clear(); // Clear ALL entities, not just Actes
                        
                        // Reset entity cache for non-essential entities
                        // Keep only EntiteJuridique in cache as it's frequently used
                        $this->entityCache['agent'] = [];
                        $this->entityCache['uf'] = [];
                        $this->entityCache['acte'] = [];
                        
                        // Force garbage collection
                        if (gc_enabled()) {
                            gc_collect_cycles();
                        }
                        
                        $batchCount = 0;
                        
                        // Monitor memory after batch processing
                        $currentMemory = memory_get_usage(true) / 1024 / 1024;
                        $memoryReadings[] = $currentMemory;
                        $peakMemory = max($peakMemory, $currentMemory);
                        
                        // Calculate memory increase rate for next adjustment
                        $memoryIncrease = $currentMemory - $lastMemoryUsage;
                        $memoryIncreaseRate = $memoryIncrease > 0 ? $memoryIncrease : 0;
                        $lastMemoryUsage = $currentMemory;
                        
                        // Update progress after batch
                        if ($progressCallback !== null) {
                            $progressCallback($page, $totalPages, $processedItems, $totalItems, $currentMemory, $peakMemory);
                        }
                    }
                    
                    // Check if we need to commit the current transaction
                    if ($itemsInCurrentTransaction >= $maxItemsPerTransaction) {
                        $this->em->flush();
                        $this->em->commit();
                        error_log(sprintf('Committed transaction #%d with %d items', $transactionCount, $itemsInCurrentTransaction));
                        
                        // Clear all entities and reset cache
                        $this->em->clear();
                        $this->entityCache = [
                            'entiteJuridique' => [],
                            'agent' => [],
                            'uf' => [],
                            'acte' => []
                        ];
                        
                        // Force garbage collection
                        if (gc_enabled()) {
                            gc_collect_cycles();
                        }
                        
                        // Reset counters for next transaction
                        $itemsInCurrentTransaction = 0;
                        $batchCount = 0;
                    }

                } catch (\Throwable $e) {
                    $stats['errors'][] = [
                        'codeActe' => $acteData['codeActe'] ?? 'N/A',
                        'praticienMatricule' => $acteData['praticienMatricule'] ?? 'N/A',
                        'error' => $e->getMessage()
                    ];
                }
            }

            $totalPages = $res['paginationTotal'] ?? 1;
            
            // Call progress callback at the end of page processing
            if ($progressCallback !== null) {
                $currentMemory = memory_get_usage(true) / 1024 / 1024;
                $memoryReadings[] = $currentMemory;
                $peakMemory = max($peakMemory, $currentMemory);
                $progressCallback($page, $totalPages, $processedItems, $totalItems, $currentMemory, $peakMemory);
            }
            
            $page++;

            if ($page > 10000) {
                throw new \Exception('Trop de pages à traiter, vérifiez la pagination de l\'API [data-integrator]');
            }

        } while ($page <= $totalPages);

        // Final flush for any remaining entities
        $this->em->flush();
        
        // Commit any active transaction if there are items in the current transaction that haven't been committed yet
        if ($itemsInCurrentTransaction > 0 && $this->em->getConnection()->isTransactionActive()) {
            $this->em->commit();
            error_log(sprintf('Committed final transaction #%d with %d items', $transactionCount, $itemsInCurrentTransaction));
        }
        
        // Calculate final performance metrics
        $endTime = microtime(true);
        $totalTime = $endTime - $startTime;
        $finalMemory = memory_get_usage(true) / 1024 / 1024;
        
        // Analyze memory trend
        $memoryTrend = $this->analyzeMemoryTrend($memoryReadings);
        
        // Final progress update
        if ($progressCallback !== null) {
            $progressCallback($totalPages, $totalPages, $processedItems, $totalItems, $finalMemory, $peakMemory);
        }
        
        // Add performance metrics to stats
        $stats['performance'] = [
            'executionTime' => $totalTime,
            'peakMemory' => $peakMemory,
            'finalMemory' => $finalMemory,
            'memoryTrend' => $memoryTrend,
            'recordsPerSecond' => $stats['processed'] > 0 ? $stats['processed'] / $totalTime : 0
        ];
        
        // Notifier les administrateurs si l'agent par défaut a été utilisé
        if ($this->defaultAgentUsed && !empty($this->actesAssignedToDefaultAgent)) {
            $this->notifyAdminsAboutDefaultAgentUsage();
            
            // Ajouter les informations sur l'utilisation de l'agent par défaut aux statistiques
            $stats['defaultAgentUsed'] = true;
            $stats['actesWithDefaultAgent'] = count($this->actesAssignedToDefaultAgent);
        }
        
        return $stats;
    }

    /**
     * Version optimisée de upsertActe avec cache d'entités et requêtes optimisées
     * pour réduire la consommation mémoire
     */
    private function upsertActeOptimized(array $data, string $ejCode, ?array $refresh): bool
    {
        // Use "ej0001" as default if ejCode is empty
        if (!$ejCode) {
            $ejCode = "ej0001";
            error_log('Using default ejCode "ej0001" in upsertActeOptimized method');
        }

        // 1. Récupérer l'EntiteJuridique depuis le cache ou la base de données
        if (!isset($this->entityCache['entiteJuridique'][$ejCode])) {
            $entiteJuridique = $this->em->getRepository(EntiteJuridique::class)->findOneBy(['code' => $ejCode]);
            
            // Try with default ejCode if the provided one doesn't exist
            if (!$entiteJuridique && $ejCode !== "ej0001") {
                $ejCode = "ej0001";
                $entiteJuridique = $this->em->getRepository(EntiteJuridique::class)->findOneBy(['code' => "ej0001"]);
            }
            
            // If still not found, throw exception
            if (!$entiteJuridique) {
                throw new \Exception('Entité juridique non trouvée, même avec le code par défaut "ej0001"');
            }
            
            // Store in cache
            $this->entityCache['entiteJuridique'][$ejCode] = $entiteJuridique;
        }
        
        $entiteJuridique = $this->entityCache['entiteJuridique'][$ejCode];

        // 2. Recherche de l'agent avec une requête optimisée (au lieu de charger TOUS les agents)
        $praticienMatricule = strtolower($data['praticienMatricule'] ?? '');
        
        // Si le praticienMatricule est vide, utiliser l'agent par défaut
        if (empty($praticienMatricule)) {
            $agent = $this->getOrCreateDefaultAgent($ejCode);
            
            // Enregistrer l'acte comme utilisant l'agent par défaut
            $this->defaultAgentUsed = true;
            $this->actesAssignedToDefaultAgent[] = [
                'code' => $data['codeActe'] ?? 'N/A',
                'internum' => $data['internum'] ?? 'N/A',
                'date' => $data['dateActe'] ?? 'N/A'
            ];
            
            // Log l'utilisation de l'agent par défaut
            error_log(sprintf(
                'Utilisation de l\'agent par défaut (U0001) pour l\'acte: %s (internum: %s)',
                $data['codeActe'] ?? 'N/A',
                $data['internum'] ?? 'N/A'
            ));
        } else {
            // Recherche normale de l'agent
            $agentCacheKey = $ejCode . '_' . $praticienMatricule;
            
            if (!isset($this->entityCache['agent'][$agentCacheKey])) {
                // Requête optimisée pour trouver l'agent directement par hrUser (insensible à la casse)
                $qb = $this->em->createQueryBuilder();
                $qb->select('a')
                   ->from(Agent::class, 'a')
                   ->where('a.hopital = :hopital')
                   ->andWhere('LOWER(a.hrUser) = :hrUser')
                   ->setParameter('hopital', $entiteJuridique)
                   ->setParameter('hrUser', $praticienMatricule)
                   ->setMaxResults(1);
                
                $agent = $qb->getQuery()->getOneOrNullResult();
                
                if (!$agent) {
                    // Si l'agent n'est pas trouvé, utiliser l'agent par défaut
                    $agent = $this->getOrCreateDefaultAgent($ejCode);
                    
                    // Enregistrer l'acte comme utilisant l'agent par défaut
                    $this->defaultAgentUsed = true;
                    $this->actesAssignedToDefaultAgent[] = [
                        'code' => $data['codeActe'] ?? 'N/A',
                        'internum' => $data['internum'] ?? 'N/A',
                        'date' => $data['dateActe'] ?? 'N/A',
                        'matricule' => $praticienMatricule
                    ];
                    
                    // Log l'utilisation de l'agent par défaut
                    error_log(sprintf(
                        'Agent non trouvé pour matricule %s, utilisation de l\'agent par défaut (U0001) pour l\'acte: %s (internum: %s)',
                        $praticienMatricule,
                        $data['codeActe'] ?? 'N/A',
                        $data['internum'] ?? 'N/A'
                    ));
                }
                
                // Store in cache
                $this->entityCache['agent'][$agentCacheKey] = $agent;
            }
            
            $agent = $this->entityCache['agent'][$agentCacheKey];
        }

        // 3. Conversion de la date d'acte
        $dateActe = !empty($data['dateActe']) ? new \DateTime($data['dateActe']) : null;

        // 4. Recherche des UFs avec cache
        // UF principale
        $ufPrincipalCode = $data['ufPrincipalCode'] ?? null;
        $ufPrincipal = null;
        
        if ($ufPrincipalCode) {
            $ufCacheKey = $ejCode . '_' . $ufPrincipalCode . '_' . ($dateActe ? $dateActe->format('Y-m-d') : 'null');
            
            if (!isset($this->entityCache['uf'][$ufCacheKey])) {
                $ufPrincipal = $this->findUfByCodeAndEjOptimized($ufPrincipalCode, $entiteJuridique, $dateActe);
                
                if (!$ufPrincipal) {
                    throw new \Exception('UF principale non trouvée : ' . $ufPrincipalCode . ' pour l\'EJ : ' . $ejCode . ' à la date : ' . ($dateActe?->format('Y-m-d') ?? 'N/A'));
                }
                
                // Store in cache
                $this->entityCache['uf'][$ufCacheKey] = $ufPrincipal;
            } else {
                $ufPrincipal = $this->entityCache['uf'][$ufCacheKey];
            }
        }

        // UF de demande
        $ufDemande = null;
        if (!empty($data['ufDemande'])) {
            $ufDemandeCode = $data['ufDemande'];
            $ufCacheKey = $ejCode . '_' . $ufDemandeCode . '_' . ($dateActe ? $dateActe->format('Y-m-d') : 'null');
            
            if (!isset($this->entityCache['uf'][$ufCacheKey])) {
                $ufDemande = $this->findUfByCodeAndEjOptimized($ufDemandeCode, $entiteJuridique, $dateActe);
                
                if ($ufDemande) {
                    // Store in cache only if found
                    $this->entityCache['uf'][$ufCacheKey] = $ufDemande;
                }
            } else {
                $ufDemande = $this->entityCache['uf'][$ufCacheKey];
            }
        }

        // UF d'intervention
        $ufIntervention = null;
        if (!empty($data['ufIntervention'])) {
            $ufInterventionCode = $data['ufIntervention'];
            $ufCacheKey = $ejCode . '_' . $ufInterventionCode . '_' . ($dateActe ? $dateActe->format('Y-m-d') : 'null');
            
            if (!isset($this->entityCache['uf'][$ufCacheKey])) {
                $ufIntervention = $this->findUfByCodeAndEjOptimized($ufInterventionCode, $entiteJuridique, $dateActe);
                
                if ($ufIntervention) {
                    // Store in cache only if found
                    $this->entityCache['uf'][$ufCacheKey] = $ufIntervention;
                }
            } else {
                $ufIntervention = $this->entityCache['uf'][$ufCacheKey];
            }
        }

        // 5. Recherche d'un acte existant avec cache
        $acte = null;
        if (!empty($data['internum'])) {
            $internum = $data['internum'];
            
            if (!isset($this->entityCache['acte'][$internum])) {
                // Requête optimisée pour ne charger que l'ID et l'internum
                $qb = $this->em->createQueryBuilder();
                $qb->select('a')
                   ->from(Actes::class, 'a')
                   ->where('a.internum = :internum')
                   ->setParameter('internum', $internum)
                   ->setMaxResults(1);
                
                $acte = $qb->getQuery()->getOneOrNullResult();
                
                // Store in cache if found
                if ($acte) {
                    $this->entityCache['acte'][$internum] = $acte;
                }
            } else {
                $acte = $this->entityCache['acte'][$internum];
            }
        }

        $isCreated = false;
        if (!$acte) {
            $acte = new Actes();
            $this->em->persist($acte);
            $isCreated = true;
        }

        // 6. Mise à jour des champs (sans logging des longueurs pour économiser de la mémoire)
        $acte->setInternum($data['internum'] ?? null);
        $acte->setCode($data['codeActe'] ?? '');
        $acte->setDescription($data['descriptionActe'] ?? '');
        $acte->setTypeActe($data['typeActe'] ?? null);
        $acte->setAnnee($data['anneeActe'] ?? null);
        $acte->setMois($data['moisActe'] ?? null);
        $acte->setSemaineIso($data['semaineIso'] ?? null);
        $acte->setNombreDeRealisation($data['nombreActes'] ?? 0);
        $acte->setTypeVenue($data['typeVenue'] ?? null);
        $acte->setLibTypeVenue($data['libTypeVenue'] ?? null);
        $acte->setIcrA($data['icrA'] ?? null);
        $acte->setCoefficient($data['coefficient'] ?? null);
        $acte->setLettreCoef($data['lettreCoef'] ?? null);
        $acte->setRegroupement($data['regroupement'] ?? null);
        $acte->setActivite($data['activite'] ?? null);
        $acte->setActiviteLib($data['activiteLib'] ?? null);

        // Gestion de la date
        if (!empty($data['dateActe'])) {
            $acte->setDateRealisation(new \DateTime($data['dateActe']));
        }

        // Gestion de l'horodatage (HorodatageTrait)
        if (!empty($data['periodeType'])) {
            $acte->setPeriodeType($data['periodeType']);
        }
        if (!empty($data['source'])) {
            $acte->setSource($data['source']);
        }
        
        // Définir validFrom et validTo à la date de l'acte pour tous les actes
        $acte->setValidFrom($dateActe ?: new \DateTime());
        $acte->setValidTo($dateActe ?: new \DateTime());

        // Relations
        $acte->setAgent($agent);
        $acte->setUfPrincipal($ufPrincipal);
        if ($ufDemande) {
            $acte->setUfDemande($ufDemande);
        }
        if ($ufIntervention) {
            $acte->setUfIntervention($ufIntervention);
        }

        return $isCreated;
    }
    
    /**
     * Version optimisée de findUfByCodeAndEj avec requête plus efficace
     */
    private function findUfByCodeAndEjOptimized(?string $ufCode, EntiteJuridique $entiteJuridique, ?\DateTime $dateReference = null): ?Ufs
    {
        if (empty($ufCode)) {
            return null;
        }

        // Requête optimisée pour ne charger que les champs nécessaires
        $qb = $this->em->createQueryBuilder();
        $qb->select('u')
           ->from(Ufs::class, 'u')
           ->join('u.cr', 'cr')
           ->join('cr.pole', 'p')
           ->where('u.ufcode = :ufCode')
           ->andWhere('p.hopital = :entiteJuridique')
           ->setParameter('ufCode', $ufCode)
           ->setParameter('entiteJuridique', $entiteJuridique);

        // Filtre temporel pour SCD type 2
        if ($dateReference) {
            $qb->andWhere('u.datdeb <= :dateRef')
               ->andWhere('(u.datfin IS NULL OR u.datfin >= :dateRef)')
               ->setParameter('dateRef', $dateReference);
        }

        // Ordre par date de fin décroissante pour prendre la version la plus récente
        $qb->orderBy('u.datfin', 'DESC')
           ->setMaxResults(1);

        return $qb->getQuery()->getOneOrNullResult();
    }
    
    /**
     * Version originale maintenue pour compatibilité
     */
    private function upsertActe(array $data, string $ejCode, ?array $refresh): bool
    {
        return $this->upsertActeOptimized($data, $ejCode, $refresh);
    }



    /**
     * Checks the lengths of string fields to help identify potential truncation issues
     * 
     * @param array $data The data array containing field values
     * @return void
     */
    private function logFieldLengths(array $data): void
    {
        // Fields to check (focusing on those that might cause truncation)
        $fieldsToCheck = [
            'internum' => 20,
            'codeActe' => 255,
            'descriptionActe' => 'TEXT',
            'typeActe' => 255,
            'libTypeVenue' => 255,
            'lettreCoef' => 10,
            'regroupement' => 255,
            'activite' => 10,
            'activiteLib' => 255,
            'libUfDemande' => 'N/A',
            'libUfIntervention' => 'N/A'
        ];
        
        // We're no longer logging field lengths, just checking for potential issues
        // The actual validation is done in the DTO's validate method
        foreach ($fieldsToCheck as $field => $maxLength) {
            if (isset($data[$field]) && is_string($data[$field]) && $maxLength !== 'TEXT' && $maxLength !== 'N/A') {
                $length = mb_strlen($data[$field]);
                
                // If a field is approaching its limit, we might want to handle it differently
                // but we don't need to log it anymore
                if ($length > ($maxLength * 0.95)) {
                    // Field is very close to its limit, but we're not logging it
                    // The descriptionActe field is now TEXT type, so no truncation issues
                }
            }
        }
    }

    /**
     * Analyzes memory usage trend to detect potential memory leaks
     * 
     * @param array $memoryReadings Array of memory readings in MB
     * @return string Description of the memory trend
     */
    private function analyzeMemoryTrend(array $memoryReadings): string
    {
        if (count($memoryReadings) < 2) {
            return 'Insufficient data for trend analysis';
        }
        
        $firstReading = $memoryReadings[0];
        $lastReading = $memoryReadings[count($memoryReadings) - 1];
        $maxReading = max($memoryReadings);
        
        $netChange = $lastReading - $firstReading;
        $percentChange = ($firstReading > 0) ? ($netChange / $firstReading) * 100 : 0;
        
        if ($percentChange > 50) {
            return sprintf('WARNING: Significant memory increase (%.1f%%). Possible memory leak.', $percentChange);
        } elseif ($percentChange > 20) {
            return sprintf('CAUTION: Moderate memory increase (%.1f%%). Monitor for potential issues.', $percentChange);
        } elseif ($percentChange < -20) {
            return sprintf('GOOD: Memory decreased (%.1f%%). Garbage collection is effective.', $percentChange);
        } else {
            return sprintf('STABLE: Memory usage stable (%.1f%% change).', $percentChange);
        }
    }
    
    /**
     * Notifie les administrateurs que des actes ont été assignés à l'agent par défaut
     * 
     * Cette méthode envoie un email aux administrateurs de chaque entité juridique
     * qui a activé les notifications d'erreur, pour les informer que certains actes
     * ont été assignés à l'agent par défaut en raison de praticienMatricule manquants
     * ou invalides.
     */
    private function notifyAdminsAboutDefaultAgentUsage(): void
    {
        // Vérifier que le service de notification est disponible
        if ($this->errorNotificationService === null) {
            error_log('Service de notification non disponible, impossible d\'envoyer les notifications pour agent par défaut');
            return;
        }
        
        // Vérifier qu'il y a des actes assignés à l'agent par défaut
        if (empty($this->actesAssignedToDefaultAgent)) {
            return;
        }
        
        try {
            // Récupérer toutes les entités juridiques avec notifications d'erreur activées
            $entitesJuridiques = $this->em->getRepository(EntiteJuridique::class)
                ->findAll();
                
            // Filtrer les entités qui ont les notifications d'erreur activées
            $entitesJuridiques = array_filter($entitesJuridiques, function($ej) {
                $parametre = $ej->getParametre();
                return isset($parametre['notifications']['NotifierAdminDesErreurProduite']) && 
                       $parametre['notifications']['NotifierAdminDesErreurProduite'] === true;
            });
            
            // Limiter le nombre d'actes à afficher dans l'email
            $maxActesToShow = 20;
            $totalActes = count($this->actesAssignedToDefaultAgent);
            $actesToShow = array_slice($this->actesAssignedToDefaultAgent, 0, $maxActesToShow);
            
            // Préparer le message
            $message = sprintf(
                "Lors de l'importation des actes, %d actes ont été assignés à l'agent par défaut (U0001) car le praticienMatricule était manquant ou invalide.",
                $totalActes
            );
            
            // Préparer les détails
            $details = [
                'type' => 'Importation d\'actes',
                'date' => (new \DateTime())->format('Y-m-d H:i:s'),
                'totalActesAffectes' => $totalActes,
                'exemples' => $actesToShow,
            ];
            
            if ($totalActes > $maxActesToShow) {
                $remaining = $totalActes - $maxActesToShow;
                $details['note'] = sprintf(
                    "Seuls les %d premiers actes sont affichés. %d actes supplémentaires ont été affectés.",
                    $maxActesToShow,
                    $remaining
                );
            }
            
            // Envoyer la notification à chaque entité juridique
            foreach ($entitesJuridiques as $entiteJuridique) {
                $this->errorNotificationService->sendErrorNotificationToAdmin(
                    $entiteJuridique,
                    'Utilisation de l\'agent par défaut lors de l\'importation des actes',
                    $message,
                    $details
                );
            }
            
            error_log(sprintf(
                "Notification envoyée aux administrateurs concernant l'utilisation de l'agent par défaut pour %d actes.",
                $totalActes
            ));
            
        } catch (\Exception $e) {
            error_log('Erreur lors de l\'envoi des notifications pour agent par défaut : ' . $e->getMessage());
        }
    }

    /**
     * Récupère ou crée un agent par défaut avec le hrUser "U0001"
     * 
     * Cet agent est utilisé comme fallback lorsqu'un acte n'a pas de praticienMatricule
     * valide. Cela permet d'éviter les erreurs d'importation tout en gardant une trace
     * des actes qui n'ont pas pu être associés à un praticien réel.
     * 
     * @param string|null $ejCode Code de l'entité juridique à utiliser pour l'agent par défaut
     * @return Agent L'agent par défaut
     * @throws \Exception Si aucune EntiteJuridique n'est trouvée
     */
    private function getOrCreateDefaultAgent(?string $ejCode = null): Agent
    {
        // Vérifier si l'agent par défaut existe déjà dans le cache
        $defaultAgentCacheKey = ($ejCode ?? 'default') . '_U0001';
        if (isset($this->entityCache['agent'][$defaultAgentCacheKey])) {
            return $this->entityCache['agent'][$defaultAgentCacheKey];
        }
        
        // Chercher l'agent par défaut dans la base de données
        $qb = $this->em->createQueryBuilder();
        $qb->select('a')
           ->from(Agent::class, 'a')
           ->where('a.hrUser = :hrUser')
           ->setParameter('hrUser', 'U0001')
           ->setMaxResults(1);
        
        $defaultAgent = $qb->getQuery()->getOneOrNullResult();
        
        if (!$defaultAgent) {
            // Récupérer l'EntiteJuridique correspondant à l'ejCode si fourni
            $entiteJuridique = null;
            if (!empty($ejCode)) {
                $entiteJuridique = $this->em->getRepository(EntiteJuridique::class)->findOneBy(['code' => $ejCode]);
            }
            
            // Si pas d'EntiteJuridique trouvée avec l'ejCode, prendre la première disponible
            if (!$entiteJuridique) {
                $entiteJuridique = $this->em->getRepository(EntiteJuridique::class)->findOneBy([]);
            }
            
            if (!$entiteJuridique) {
                throw new \Exception('Aucune EntiteJuridique trouvée pour créer l\'agent par défaut');
            }
            
            // Créer l'agent par défaut
            $defaultAgent = new Agent();
            $defaultAgent->setHrUser('U0001');
            $defaultAgent->setNom('AGENT');
            $defaultAgent->setPrenom('Défaut');
            $defaultAgent->setEmail('<EMAIL>');
            $defaultAgent->setTitre('DR');
            $defaultAgent->setCategorie('DEF');
            $defaultAgent->setMatricule('U0001');
            $defaultAgent->setHopital($entiteJuridique);
            $defaultAgent->setRoles(['ROLE_USER']);
            $defaultAgent->setIsActif(true);
            $defaultAgent->setCreateurFiche('SYSTEM');
            
            // Ajouter une description explicative
            $defaultAgent->setFullName('Agent par défaut pour actes sans praticien');
            
            $this->em->persist($defaultAgent);
            $this->em->flush($defaultAgent); // Flush immédiatement pour pouvoir l'utiliser
            
            // Marquer que l'agent par défaut a été créé
            $this->defaultAgentUsed = true;
        }
        
        // Stocker dans le cache
        $this->entityCache['agent'][$defaultAgentCacheKey] = $defaultAgent;
        
        return $defaultAgent;
    }

    private function findUfByCodeAndEj(?string $ufCode, EntiteJuridique $entiteJuridique, ?\DateTime $dateReference = null): ?Ufs
    {
        if (empty($ufCode)) {
            return null;
        }

        $qb = $this->em->createQueryBuilder()
            ->select('u')
            ->from(Ufs::class, 'u')
            ->join('u.cr', 'cr')
            ->join('cr.pole', 'p')
            ->where('u.ufcode = :ufCode')
            ->andWhere('p.hopital = :entiteJuridique')
            ->setParameter('ufCode', $ufCode)
            ->setParameter('entiteJuridique', $entiteJuridique);

        // Filtre temporel pour SCD type 2
        if ($dateReference) {
            $qb->andWhere('u.datdeb <= :dateRef')
                ->andWhere('(u.datfin IS NULL OR u.datfin >= :dateRef)')
                ->setParameter('dateRef', $dateReference);
        }

        // Ordre par date de fin décroissante pour prendre la version la plus récente
        $qb->orderBy('u.datfin', 'DESC');

        return $qb->getQuery()->getOneOrNullResult();
    }
}