# Configuration Docker FrankenPHP - Reprend le .env.dev.local
# Utilise votre PostgreSQL local depuis le conteneur

APP_ENV=dev
APP_DEBUG=true

## Base de données locale (accessible via host.docker.internal)
DATABASE_USER=postgres
DATABASE_PASSWORD=password
DATABASE_HOST=host.docker.internal
DATABASE_PORT=5432
DATABASE_NAME=supra_dev

DATABASE_URL="postgresql://postgres:<EMAIL>:5432/supra_dev?serverVersion=15&charset=utf8"

###> symfony/cache ###
CACHE_DSN=redis://host.docker.internal:6379
###< symfony/cache ###

# Cache TTL (Time To Live) in seconds for API entities
API_CACHE_TTL=25200 # 7 heures (7 * 60 * 60 = 25200 secondes)

###> JWT Secret Key ###
JWT_SECRET=wXKxTOZ7_I11zMqxuxb46I7_P28igbpjNQqb5MmZVqxmViTdYTlf3JvDWlwSjomv6TwP8P0bOCqPXxnuEVRjlw==
###< JWT Secret Key ###

# Base URI for the Data Integrator API - utilise host.docker.internal
DATA_INTEGRATOR_BASE_URI=http://host.docker.internal:3000/api

## en dev sur windows, on utilise mailtrap
MAILER_DSN="smtp://6bf81430b3fd29:<EMAIL>:2525"
MAILER_FROM_EMAIL="<EMAIL>"

# ssl
TRUSTED_PROXIES=127.0.0.1,::1
TRUSTED_HEADERS=x-forwarded-for,x-forwarded-host,x-forwarded-proto,x-forwarded-port

###> LDAP Configuration ###
LDAP_HOST=ldap://k12adc01.chu-nancy.fr:389
LDAP_DOMAIN=U001PRD
LDAP_BASE_DN=DC=u001prd,DC=local
###< LDAP Configuration ###

###> nelmio/cors-bundle ###
CORS_ALLOW_ORIGIN='^https?://(localhost|127\.0\.0\.1)(:[0-9]+)?$'
###< nelmio/cors-bundle ###
