<?php

namespace App\Domain\Extension\CoreSupraFilter\Service;

use ApiPlatform\Doctrine\Orm\Extension\QueryCollectionExtensionInterface;
use ApiPlatform\Doctrine\Orm\Util\QueryNameGeneratorInterface;
use ApiPlatform\Metadata\Operation;
use App\Domain\Extension\CoreSupraFilter\BaseCustomFilterExtension;
use App\Entity\Structure\Service;
use Doctrine\ORM\QueryBuilder;

class ServiceCategoryFilterExtension extends BaseCustomFilterExtension implements QueryCollectionExtensionInterface
{
    public function applyToCollection(
        QueryBuilder $queryBuilder,
        QueryNameGeneratorInterface $queryNameGenerator,
        string $resourceClass,
        ?Operation $operation = null,
        array $context = []
    ): void {
        if ($resourceClass !== Service::class) {
            return;
        }

        if (!$this->isApiPlatformContext($context)) {
            return;
        }

        $this->addSecodeFilter($queryBuilder, $queryNameGenerator);
    }

    private function addSecodeFilter(QueryBuilder $queryBuilder, QueryNameGeneratorInterface $queryNameGenerator): void
    {
        $rootAlias = $queryBuilder->getRootAliases()[0];
        $excludedSecodes = ServiceCategoryExcludeFilter::getExcludedSecodes();

        if (!empty($excludedSecodes)) {
            $parameterName = $queryNameGenerator->generateParameterName('excludedSecodes');

            $queryBuilder
                ->andWhere(sprintf(
                    '(UPPER(%s.secode) NOT IN (:%s) OR %s.secode IS NULL)',
                    $rootAlias,
                    $parameterName,
                    $rootAlias
                ))
                ->setParameter($parameterName, array_map('strtoupper', $excludedSecodes));
        }
    }

    /**
     * Fonction alternative si vous voulez IGNORER les services avec secode NULL
     *
     * Pour utiliser cette fonction :
     * 1. Remplacez l'appel à addSecodeFilter() par addSecodeFilter_SI_TU_VEUX_IGNORER_LES_SERVICES_AVEC_SECODE_NULL()
     * 2. Cette version exclura AUSSI les services avec secode = NULL
     *
     * Comportement :
     * -  Services avec secode dans EXCLUDED_SECODES
     * -  Services avec secode = NULL
     * -  Uniquement services avec secode valide et non exclu
     */
    /*
    private function addSecodeFilter_SI_TU_VEUX_IGNORER_LES_SERVICES_AVEC_SECODE_NULL(
        QueryBuilder $queryBuilder,
        QueryNameGeneratorInterface $queryNameGenerator
    ): void {
        $rootAlias = $queryBuilder->getRootAliases()[0];
        $excludedSecodes = ServiceCategoryExcludeFilter::getExcludedSecodes();

        if (!empty($excludedSecodes)) {
            $parameterName = $queryNameGenerator->generateParameterName('excludedSecodes');

            $queryBuilder
                ->andWhere(sprintf(
                    '%s.secode IS NOT NULL AND UPPER(%s.secode) NOT IN (:%s)',
                    $rootAlias,
                    $rootAlias,
                    $parameterName
                ))
                ->setParameter($parameterName, array_map('strtoupper', $excludedSecodes));
        } else {
            // Si pas de codes à exclure, on ignore quand même les NULL
            $queryBuilder->andWhere(sprintf('%s.secode IS NOT NULL', $rootAlias));
        }
    }
    */
}