<?php

namespace App\EventSubscriber;

use <PERSON><PERSON>fony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\Event\ResponseEvent;
use Symfony\Component\HttpKernel\KernelEvents;

/**
 * Event subscriber to add security headers to all responses
 * 
 * This subscriber adds various security headers to prevent XSS, clickjacking,
 * content sniffing, and other attacks.
 */
class SecurityHeadersSubscriber implements EventSubscriberInterface
{
    /**
     * Add security headers to the response
     */
    public function onKernelResponse(ResponseEvent $event): void
    {
        // Only add headers to the main request (not sub-requests)
        if (!$event->isMainRequest()) {
            return;
        }

        $response = $event->getResponse();
        $headers = $response->headers;

        // Prevent clickjacking
        $headers->set('X-Frame-Options', 'SAMEORIGIN');

        // Prevent content sniffing
        $headers->set('X-Content-Type-Options', 'nosniff');

        // Enable XSS protection in browsers
        $headers->set('X-XSS-Protection', '1; mode=block');

        // Prevent browsers from loading the site in an iframe
        $headers->set('Frame-Options', 'DENY');

        // Strict Transport Security (force HTTPS)
        $headers->set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');

        // Referrer Policy
        $headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');

        // Permissions Policy (formerly Feature Policy)
        $headers->set('Permissions-Policy', 'camera=(), microphone=(), geolocation=(), interest-cohort=()');

        // Content Security Policy
        $cspDirectives = [
            "default-src 'self'",
            "script-src 'self' 'unsafe-inline'", // Allow inline scripts for now
            "style-src 'self' 'unsafe-inline'", // Allow inline styles for now
            "img-src 'self' data:", // Allow data: URLs for images
            "font-src 'self'",
            "connect-src 'self'",
            "media-src 'self'",
            "object-src 'none'",
            "frame-src 'self'",
            "base-uri 'self'",
            "form-action 'self'",
        ];
        
        $headers->set('Content-Security-Policy', implode('; ', $cspDirectives));
    }

    /**
     * {@inheritdoc}
     */
    public static function getSubscribedEvents(): array
    {
        return [
            KernelEvents::RESPONSE => ['onKernelResponse', 0],
        ];
    }
}