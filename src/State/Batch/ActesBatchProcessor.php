<?php

namespace App\State\Batch;

use ApiPlatform\Metadata\Operation;
use App\ApiResource\Activite\ActesDto;
use App\Domain\Enum\PeriodeType;
use App\Domain\Service\Batch\BatchOptimizationConfigService;
use App\Domain\Service\Notification\BatchNotificationService;
use App\Entity\Activite\Actes;
use App\Entity\Structure\EntiteJuridique;
use App\Mapper\Actes\ActesDtoToEntityMapper;
use App\Repository\AgentRepository;
use App\Repository\UfsRepository;
use App\State\Batch\Base\BatchProcessorOptimized;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use stdClass;
use Symfony\Component\HttpFoundation\Exception\BadRequestException;
use Symfony\Component\HttpFoundation\RequestStack;

class ActesBatchProcessor extends BatchProcessorOptimized
{
    public function __construct(
        private ActesDtoToEntityMapper $mapper,
        private RequestStack $requestStack,
        private UfsRepository $ufsRepository,
        private AgentRepository $agentRepository,
        BatchOptimizationConfigService $configService,
        BatchNotificationService $notificationService,
        LoggerInterface $logger,
        EntityManagerInterface $entityManager
    ) {
        parent::__construct($configService, $notificationService, $logger, $entityManager);
    }

    public function process(mixed $data, Operation $operation, array $uriVariables = [], array $context = [])
    {
        // Récupérer le contenu JSON brut de la requête
        $request = $this->requestStack->getCurrentRequest();
        $content = $request->getContent();

        // Décoder le JSON manuellement pour garder la structure de tableau
        $arrayData = json_decode($content, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new BadRequestException('Invalid JSON: ' . json_last_error_msg());
        }

        if (!is_array($arrayData)) {
            throw new BadRequestException('Expected JSON array, got: ' . gettype($arrayData));
        }

        if (empty($arrayData)) {
            throw new BadRequestException('Array cannot be empty');
        }

        // Add ejcode to context if available
        if (isset($uriVariables['ejcode'])) {
            $context['ejcode'] = $uriVariables['ejcode'];
        }

        // Récupérer l'entité juridique si disponible
        $entiteJuridique = null;
        if (isset($uriVariables['ejcode'])) {
            $entiteJuridique = $this->entityManager->getRepository(EntiteJuridique::class)
                ->findOneBy(['code' => $uriVariables['ejcode']]);
        }

        // Utiliser le traitement optimisé
        return $this->processOptimizedBatch($arrayData, 'Actes', $entiteJuridique);
    }

    /**
     * Traite un élément individuel du batch
     */
    protected function processItem(array $item, int $index, string $resourceType): ?array
    {
        // Validation des champs requis
        $this->validateRequiredFields($item, ['dateRealisation', 'agentHrUser'], $index);

        // Créer le DTO à partir des données JSON
        $dto = $this->createDtoFromArray($item);

        // Utiliser le mapper existant
        $entity = $this->mapper->load($dto, Actes::class, []);
        $isNew = $entity->getId() === null;
        $entity = $this->mapper->populate($dto, $entity, []);

        $this->entityManager->persist($entity);

        // Créer une réponse simple sans objets DateTime
        $responseData = [
            'id' => $entity->getId(),
            'code' => $entity->getCode(),
            'description' => $entity->getDescription(),
            'typeActe' => $entity->getTypeActe(),
            'nombreDeRealisation' => $entity->getNombreDeRealisation(),
            'annee' => $entity->getAnnee(),
            'mois' => $entity->getMois(),
            'coefficient' => $entity->getCoefficient(),
            'dateRealisation' => $entity->getDateRealisation()?->format('Y-m-d H:i:s'),
            'validFrom' => $entity->getValidFrom()?->format('Y-m-d H:i:s'),
            'validTo' => $entity->getValidTo()?->format('Y-m-d H:i:s'),
            'periodeType' => $entity->getPeriodeType(),
            'source' => $entity->getSource(),
        ];

        return [
            'dto' => $responseData,
            'is_new' => $isNew,
        ];
    }

    private function createDtoFromArray(array $item): ActesDto
    {
        $dto = new ActesDto();

        // Champs obligatoires typés
        $dto->code = $item['code'] ?? '';
        $dto->description = $item['description'] ?? '';

        // Champs de base
        $dto->dateRealisation = isset($item['dateRealisation']) ? new \DateTime($item['dateRealisation']) : null;
        $dto->agentHrU = $item['agentHrUser'] ?? null; // Le front envoie "agentHrUser"
        $dto->codeActe = $item['codeActe'] ?? $item['code'] ?? null; // Fallback sur 'code' si pas de 'codeActe'
        $dto->nomActe = $item['nomActe'] ?? $item['description'] ?? null; // Fallback sur 'description'
        $dto->quantite = $item['quantite'] ?? $item['nombreDeRealisation'] ?? 1;
        $dto->coefficient = $item['coefficient'] ?? null;
        $dto->montant = $item['montant'] ?? null;
        $dto->typeActe = $item['typeActe'] ?? null;

        // Champs temporels obligatoires
        $dto->annee = isset($item['annee']) ? (int)$item['annee'] : null;
        $dto->mois = isset($item['mois']) ? (int)$item['mois'] : null;
        $dto->nombreDeRealisation = isset($item['nombreDeRealisation']) ? (int)$item['nombreDeRealisation'] : 1;

        // Calculer validFrom et validTo à partir des données disponibles
        if ($dto->dateRealisation) {
            // Utiliser dateRealisation comme validFrom
            $dto->validFrom = $dto->dateRealisation;
            $dto->validTo = null; // Pas de date de fin par défaut
        } elseif ($dto->annee && $dto->mois) {
            // Créer validFrom à partir de l'année et du mois
            $dto->validFrom = new \DateTime(sprintf('%d-%02d-01', $dto->annee, $dto->mois));
            // validTo = dernier jour du mois
            $dto->validTo = new \DateTime(sprintf('%d-%02d-%d', $dto->annee, $dto->mois, cal_days_in_month(CAL_GREGORIAN, $dto->mois, $dto->annee)));
        } else {
            // Fallback : utiliser la date actuelle
            $dto->validFrom = new \DateTime();
            $dto->validTo = null;
        }

        // Calculer le type de période
        if ($dto->validFrom && $dto->validTo) {
            $interval = $dto->validFrom->diff($dto->validTo);
            $days = (int)$interval->format('%a');

            if ($days < 32) {
                $dto->periodeType = PeriodeType::MENSUEL;
            } elseif ($days < 366) {
                $dto->periodeType = PeriodeType::ANNUEL;
            } else {
                $dto->periodeType = PeriodeType::ANNUEL;
            }
        } else {
            $dto->periodeType = PeriodeType::MENSUEL;
        }

        // Source de l'importation
        $dto->source = 'IMPORTATION Manuelle';

        return $dto;
    }

    private function createResponseDto(Actes $entity): ActesDto
    {
        $dto = new ActesDto();
        $dto->id = $entity->getId();
        $dto->dateRealisation = $entity->getDateRealisation();
        $dto->codeActe = $entity->getCodeActe();
        $dto->nomActe = $entity->getNomActe();
        $dto->quantite = $entity->getQuantite();
        $dto->coefficient = $entity->getCoefficient();
        $dto->montant = $entity->getMontant();
        $dto->validFrom = $entity->getValidFrom();
        $dto->validTo = $entity->getValidTo();
        $dto->periodeType = $entity->getPeriodeType();
        $dto->source = $entity->getSource();

        return $dto;
    }
}
