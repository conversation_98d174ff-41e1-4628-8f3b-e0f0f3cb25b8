<?php

namespace App\Domain\Enum;

final class AgentRoleType
{
    public const USER = 'ROLE_USER';
    public const ADMIN = 'ROLE_ADMIN';
    public const CHEF_DE_POLE = 'ROLE_CHEF_DE_POLE';
    public const MANAGER = 'ROLE_MANAGER';
    public const CME = 'ROLE_CME';
    public const FIFAP = 'ROLE_FIFAP';

    public const ALL_ROLES = [
        self::USER,
        self::ADMIN,
        self::CHEF_DE_POLE,
        self::MANAGER,
        self::CME,
        self::FIFAP,
    ];

    /**
     * Hiérarchie des rôles (du moins privilégié au plus privilégié)
     * ROLE_USER < ROLE_ADMIN < ROLE_CHEF_DE_POLE < ROLE_MANAGER < ROLE_CME <= ROLE_FIFAP
     */
    public const ROLE_HIERARCHY = [
        self::USER => 1,
        self::ADMIN => 2,
        self::CHEF_DE_POLE => 3,
        self::MANAGER => 4,
        self::CME => 5,
        self::FIFAP => 5, // Même niveau que CME
    ];

    /**
     * Vérifie si un rôle peut modifier un autre rôle
     * Un utilisateur peut modifier des rôles de niveau inférieur ou égal au sien
     */
    public static function canModifyRole(string $userRole, string $targetRole): bool
    {
        if (!isset(self::ROLE_HIERARCHY[$userRole]) || !isset(self::ROLE_HIERARCHY[$targetRole])) {
            return false;
        }

        return self::ROLE_HIERARCHY[$userRole] >= self::ROLE_HIERARCHY[$targetRole];
    }

    /**
     * Retourne tous les rôles qu'un utilisateur peut assigner
     */
    public static function getAssignableRoles(string $userRole): array
    {
        if (!isset(self::ROLE_HIERARCHY[$userRole])) {
            return [];
        }

        $userLevel = self::ROLE_HIERARCHY[$userRole];
        $assignableRoles = [];

        foreach (self::ROLE_HIERARCHY as $role => $level) {
            if ($level <= $userLevel) {
                $assignableRoles[] = $role;
            }
        }

        return $assignableRoles;
    }

    /**
     * Retourne le niveau hiérarchique d'un rôle
     */
    public static function getRoleLevel(string $role): int
    {
        return self::ROLE_HIERARCHY[$role] ?? 0;
    }
}
