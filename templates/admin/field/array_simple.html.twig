{# Template simple et robuste pour afficher les arrays et collections dans EasyAdmin #}
{% if field.value is defined and field.value is not null %}
    {% if field.value is iterable %}
        <div class="array-field-simple">
            {% set count = 0 %}
            {% for item in field.value %}
                {% set count = count + 1 %}
                <div class="mb-1 p-2 bg-light rounded border-start border-primary border-2">
                    {% if item is defined %}
                        {% if item is iterable %}
                            <small class="text-muted">Données complexes ({{ item|length }} propriétés)</small>
                        {% else %}
                            {{ item|e }}
                        {% endif %}
                    {% else %}
                        <span class="text-muted">-</span>
                    {% endif %}
                </div>
            {% endfor %}

            {% if count > 0 %}
                <small class="badge bg-info mt-1">{{ count }} élément{{ count > 1 ? 's' : '' }}</small>
            {% endif %}
        </div>
    {% else %}
        <span class="badge bg-secondary">{{ field.value|e }}</span>
    {% endif %}
{% else %}
    <span class="badge bg-secondary">0</span>
{% endif %}

<style>
.array-field-simple {
    max-width: 100%;
}
</style>
