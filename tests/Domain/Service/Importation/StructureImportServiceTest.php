<?php

namespace App\Tests\Domain\Service\Importation;

use App\Domain\Service\Importation\StructureImportService;
use App\Entity\Structure\Cr;
use App\Entity\Structure\EntiteJuridique;
use App\Entity\Structure\Pole;
use App\Entity\Structure\Service;
use App\Entity\Structure\Ufs;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;

it('can be instantiated', function () {
    $httpClient = $this->createMock(HttpClientInterface::class);
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $baseUri = 'http://data-integrator.example.com';
    
    $service = new StructureImportService($httpClient, $entityManager, $baseUri);
    
    expect($service)->toBeInstanceOf(StructureImportService::class);
});

it('imports poles successfully', function () {
    // Mock HTTP client
    $httpClient = $this->createMock(HttpClientInterface::class);
    
    // Create two separate response mocks for different endpoints
    $refreshResponse = $this->createMock(ResponseInterface::class);
    $refreshResponse->expects($this->once())
        ->method('toArray')
        ->willReturn(['status' => 'success']);
        
    $dataResponse = $this->createMock(ResponseInterface::class);
    $dataResponse->expects($this->once())
        ->method('toArray')
        ->willReturn([
            'data' => [
                [
                    'etab' => 'CHRU',
                    'poleCode' => 'P001',
                    'datDeb' => '2023-01-01',
                    'datFin' => '2025-12-31',
                    'libelle' => 'Pôle Chirurgie',
                    'pfUser' => 'ADMIN',
                    'dmdaCre' => '2025-08-09',
                    'dmdaMaj' => '2025-08-09'
                ]
            ],
            'paginationTotal' => 1,
            'pageSize' => 50,
            'ejCode' => 'EJ001'
        ]);
    
    // Mock request method to return appropriate response for each URL
    $httpClient->expects($this->exactly(2))
        ->method('request')
        ->willReturnCallback(function ($method, $url, $options = []) use ($refreshResponse, $dataResponse) {
            if ($url === 'http://data-integrator.example.com/ot-pole/refresh') {
                return $refreshResponse;
            }
            
            if ($url === '/ot-pole') {
                return $dataResponse;
            }
            
            throw new \Exception("Unexpected URL: $url");
        });
    
    // Mock entity manager and repositories
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $entiteJuridiqueRepository = $this->createMock(EntityRepository::class);
    $poleRepository = $this->createMock(EntityRepository::class);
    
    $entityManager->method('getRepository')
        ->willReturnCallback(function ($entityClass) use ($entiteJuridiqueRepository, $poleRepository) {
            if ($entityClass === EntiteJuridique::class) {
                return $entiteJuridiqueRepository;
            }
            if ($entityClass === Pole::class) {
                return $poleRepository;
            }
            return null;
        });
    
    // Mock EntiteJuridique
    $entiteJuridique = $this->createMock(EntiteJuridique::class);
    $entiteJuridiqueRepository->method('findOneBy')
        ->willReturn($entiteJuridique);
    
    // Mock Pole - return null to simulate new pole
    $poleRepository->method('findOneBy')
        ->willReturnCallback(function ($criteria) {
            if (isset($criteria['polecode']) && $criteria['polecode'] === 'P001') {
                return null;
            }
            return $this->createMock(Pole::class);
        });
    
    // Expect persist to be called for a new pole
    $entityManager->expects($this->once())
        ->method('persist')
        ->with($this->isInstanceOf(Pole::class));
    
    // Expect flush to be called at the end
    $entityManager->expects($this->once())
        ->method('flush');
    
    $baseUri = 'http://data-integrator.example.com';
    
    $service = new StructureImportService($httpClient, $entityManager, $baseUri);
    
    // No exception should be thrown
    $service->importPoles();
});

it('imports services successfully', function () {
    // Mock HTTP client
    $httpClient = $this->createMock(HttpClientInterface::class);
    
    // Create two separate response mocks for different endpoints
    $refreshResponse = $this->createMock(ResponseInterface::class);
    $refreshResponse->expects($this->once())
        ->method('toArray')
        ->willReturn(['status' => 'success']);
        
    $dataResponse = $this->createMock(ResponseInterface::class);
    $dataResponse->expects($this->once())
        ->method('toArray')
        ->willReturn([
            'data' => [
                [
                    'etab' => 'CHRU',
                    'seCode' => 'S001',
                    'datDeb' => '2023-01-01',
                    'datFin' => '2025-12-31',
                    'libelle' => 'Service Anesthésie',
                    'cdCode' => 'CD001',
                    'taCode' => 'TA001',
                    'pfUser' => 'ADMIN',
                    'dmdAcre' => '2025-08-09',
                    'dmdAmaj' => '2025-08-09'
                ]
            ],
            'paginationTotal' => 1,
            'pageSize' => 50,
            'ejCode' => 'EJ001'
        ]);
    
    // Mock request method to return appropriate response for each URL
    $httpClient->expects($this->exactly(2))
        ->method('request')
        ->willReturnCallback(function ($method, $url, $options = []) use ($refreshResponse, $dataResponse) {
            if ($url === 'http://data-integrator.example.com/ot-service/refresh') {
                return $refreshResponse;
            }
            
            if ($url === '/ot-service') {
                return $dataResponse;
            }
            
            throw new \Exception("Unexpected URL: $url");
        });
    
    // Mock entity manager and repositories
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $entiteJuridiqueRepository = $this->createMock(EntityRepository::class);
    $serviceRepository = $this->createMock(EntityRepository::class);
    
    $entityManager->method('getRepository')
        ->willReturnCallback(function ($entityClass) use ($entiteJuridiqueRepository, $serviceRepository) {
            if ($entityClass === EntiteJuridique::class) {
                return $entiteJuridiqueRepository;
            }
            if ($entityClass === Service::class) {
                return $serviceRepository;
            }
            return null;
        });
    
    // Mock EntiteJuridique
    $entiteJuridique = $this->createMock(EntiteJuridique::class);
    $entiteJuridiqueRepository->method('findOneBy')
        ->willReturnCallback(function ($criteria) use ($entiteJuridique) {
            if (isset($criteria['code']) && $criteria['code'] === 'EJ001') {
                return $entiteJuridique;
            }
            return null;
        });
    
    // Mock Service - return null to simulate new service
    $serviceRepository->method('findOneBy')
        ->willReturnCallback(function ($criteria) {
            if (isset($criteria['secode']) && $criteria['secode'] === 'S001') {
                return null;
            }
            return $this->createMock(Service::class);
        });
    
    // Expect persist to be called for a new service
    $entityManager->expects($this->once())
        ->method('persist')
        ->with($this->isInstanceOf(Service::class));
    
    // Expect flush to be called at the end
    $entityManager->expects($this->once())
        ->method('flush');
    
    $baseUri = 'http://data-integrator.example.com';
    
    $service = new StructureImportService($httpClient, $entityManager, $baseUri);
    
    // No exception should be thrown
    $service->importServices();
});

it('imports CRs successfully', function () {
    // Mock HTTP client
    $httpClient = $this->createMock(HttpClientInterface::class);
    
    // Create two separate response mocks for different endpoints
    $refreshResponse = $this->createMock(ResponseInterface::class);
    $refreshResponse->expects($this->once())
        ->method('toArray')
        ->willReturn(['status' => 'success']);
        
    $dataResponse = $this->createMock(ResponseInterface::class);
    $dataResponse->expects($this->once())
        ->method('toArray')
        ->willReturn([
            'data' => [
                [
                    'etab' => 'CHRU',
                    'crCode' => 'CR001',
                    'datDeb' => '2023-01-01',
                    'datFin' => '2025-12-31',
                    'libelle' => 'CR Chirurgie',
                    'nomResp' => 'Dr. Dupont',
                    'poleCode' => 'P001',
                    'pfUser' => 'ADMIN',
                    'dmdAcre' => '2025-08-09',
                    'dmdAmaj' => '2025-08-09'
                ]
            ],
            'paginationTotal' => 1,
            'pageSize' => 50
        ]);
    
    // Mock request method to return appropriate response for each URL
    $httpClient->expects($this->exactly(2))
        ->method('request')
        ->willReturnCallback(function ($method, $url, $options = []) use ($refreshResponse, $dataResponse) {
            if ($url === 'http://data-integrator.example.com/ot-cr/refresh') {
                return $refreshResponse;
            }
            
            if ($url === '/ot-cr') {
                return $dataResponse;
            }
            
            throw new \Exception("Unexpected URL: $url");
        });
    
    // Mock entity manager and repositories
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $poleRepository = $this->createMock(EntityRepository::class);
    $crRepository = $this->createMock(EntityRepository::class);
    
    $entityManager->method('getRepository')
        ->willReturnCallback(function ($entityClass) use ($poleRepository, $crRepository) {
            if ($entityClass === Pole::class) {
                return $poleRepository;
            }
            if ($entityClass === Cr::class) {
                return $crRepository;
            }
            return null;
        });
    
    // Mock Pole
    $pole = $this->createMock(Pole::class);
    $poleRepository->method('findOneBy')
        ->willReturnCallback(function ($criteria) use ($pole) {
            if (isset($criteria['polecode']) && $criteria['polecode'] === 'P001') {
                return $pole;
            }
            return null;
        });
    
    // Mock CR - return null to simulate new CR
    $crRepository->method('findOneBy')
        ->willReturnCallback(function ($criteria) {
            if (isset($criteria['crcode']) && $criteria['crcode'] === 'CR001') {
                return null;
            }
            return $this->createMock(Cr::class);
        });
    
    // Expect persist to be called for a new CR
    $entityManager->expects($this->once())
        ->method('persist')
        ->with($this->isInstanceOf(Cr::class));
    
    // Expect flush to be called at the end
    $entityManager->expects($this->once())
        ->method('flush');
    
    $baseUri = 'http://data-integrator.example.com';
    
    $service = new StructureImportService($httpClient, $entityManager, $baseUri);
    
    // No exception should be thrown
    $service->importCrs();
});

it('imports UFs successfully', function () {
    // Mock HTTP client
    $httpClient = $this->createMock(HttpClientInterface::class);
    
    // Create two separate response mocks for different endpoints
    $refreshResponse = $this->createMock(ResponseInterface::class);
    $refreshResponse->expects($this->once())
        ->method('toArray')
        ->willReturn(['status' => 'success']);
        
    $dataResponse = $this->createMock(ResponseInterface::class);
    $dataResponse->expects($this->once())
        ->method('toArray')
        ->willReturn([
            'data' => [
                [
                    'etab' => 'CHRU',
                    'ufCode' => 'UF001',
                    'crCode' => 'CR001',
                    'seCode' => 'S001',
                    'umCode' => 'UM001',
                    'datDeb' => '2023-01-01',
                    'datFin' => '2025-12-31',
                    'libelle' => 'UF Chirurgie Générale',
                    'taCode' => 'TA001',
                    'cdCode' => 'CD001',
                    'lettreBudg' => 'A',
                    'cgCode' => 'CG001',
                    'pfUser' => 'ADMIN',
                    'dmdAcre' => '2025-08-09',
                    'dmdAmaj' => '2025-08-09',
                    'topMedical' => '1',
                    'saCode' => 'SA001'
                ]
            ],
            'paginationTotal' => 1,
            'pageSize' => 50
        ]);
    
    // Mock request method to return appropriate response for each URL
    $httpClient->expects($this->exactly(2))
        ->method('request')
        ->willReturnCallback(function ($method, $url, $options = []) use ($refreshResponse, $dataResponse) {
            if ($url === 'http://data-integrator.example.com/ot-uf/refresh') {
                return $refreshResponse;
            }
            
            if ($url === '/ot-uf') {
                return $dataResponse;
            }
            
            throw new \Exception("Unexpected URL: $url");
        });
    
    // Mock entity manager and repositories
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $serviceRepository = $this->createMock(EntityRepository::class);
    $crRepository = $this->createMock(EntityRepository::class);
    $ufRepository = $this->createMock(EntityRepository::class);
    
    $entityManager->method('getRepository')
        ->willReturnCallback(function ($entityClass) use ($serviceRepository, $crRepository, $ufRepository) {
            if ($entityClass === Service::class) {
                return $serviceRepository;
            }
            if ($entityClass === Cr::class) {
                return $crRepository;
            }
            if ($entityClass === Ufs::class) {
                return $ufRepository;
            }
            return null;
        });
    
    // Mock Service
    $service = $this->createMock(Service::class);
    $serviceRepository->method('findOneBy')
        ->willReturnCallback(function ($criteria) use ($service) {
            if (isset($criteria['secode']) && $criteria['secode'] === 'S001') {
                return $service;
            }
            return null;
        });
    
    // Mock CR
    $cr = $this->createMock(Cr::class);
    $crRepository->method('findOneBy')
        ->willReturnCallback(function ($criteria) use ($cr) {
            if (isset($criteria['crcode']) && $criteria['crcode'] === 'CR001') {
                return $cr;
            }
            return null;
        });
    
    // Mock UF - return null to simulate new UF
    $ufRepository->method('findOneBy')
        ->willReturnCallback(function ($criteria) {
            if (isset($criteria['ufcode']) && $criteria['ufcode'] === 'UF001') {
                return null;
            }
            return $this->createMock(Ufs::class);
        });
    
    // Expect persist to be called for a new UF
    $entityManager->expects($this->once())
        ->method('persist')
        ->with($this->isInstanceOf(Ufs::class));
    
    // Expect flush to be called at the end
    $entityManager->expects($this->once())
        ->method('flush');
    
    $baseUri = 'http://data-integrator.example.com';
    
    $structureService = new StructureImportService($httpClient, $entityManager, $baseUri);
    
    // No exception should be thrown
    $structureService->importUfs();
});

it('handles refresh error for poles', function () {
    // Mock HTTP client
    $httpClient = $this->createMock(HttpClientInterface::class);
    $response = $this->createMock(ResponseInterface::class);
    
    // Mock response for refresh endpoint with error
    $httpClient->method('request')
        ->willReturnCallback(function ($method, $url, $options = []) use ($response) {
            if ($url === 'http://data-integrator.example.com/ot-pole/refresh') {
                $response->method('toArray')->willReturn([
                    'status' => 'error',
                    'message' => 'Refresh failed'
                ]);
                return $response;
            }
            
            return $response;
        });
    
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $baseUri = 'http://data-integrator.example.com';
    
    $service = new StructureImportService($httpClient, $entityManager, $baseUri);
    
    expect(fn() => $service->importPoles())
        ->toThrow(\Exception::class, 'Erreur lors du refresh des pôles : Refresh failed');
});

it('handles refresh error for services', function () {
    // Mock HTTP client
    $httpClient = $this->createMock(HttpClientInterface::class);
    $response = $this->createMock(ResponseInterface::class);
    
    // Mock response for refresh endpoint with error
    $httpClient->method('request')
        ->willReturnCallback(function ($method, $url, $options = []) use ($response) {
            if ($url === 'http://data-integrator.example.com/ot-service/refresh') {
                $response->method('toArray')->willReturn([
                    'status' => 'error',
                    'message' => 'Refresh failed'
                ]);
                return $response;
            }
            
            return $response;
        });
    
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $baseUri = 'http://data-integrator.example.com';
    
    $service = new StructureImportService($httpClient, $entityManager, $baseUri);
    
    expect(fn() => $service->importServices())
        ->toThrow(\Exception::class, 'Erreur lors du refresh des services : Refresh failed');
});

it('handles refresh error for CRs', function () {
    // Mock HTTP client
    $httpClient = $this->createMock(HttpClientInterface::class);
    $response = $this->createMock(ResponseInterface::class);
    
    // Mock response for refresh endpoint with error
    $httpClient->method('request')
        ->willReturnCallback(function ($method, $url, $options = []) use ($response) {
            if ($url === 'http://data-integrator.example.com/ot-cr/refresh') {
                $response->method('toArray')->willReturn([
                    'status' => 'error',
                    'message' => 'Refresh failed'
                ]);
                return $response;
            }
            
            return $response;
        });
    
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $baseUri = 'http://data-integrator.example.com';
    
    $service = new StructureImportService($httpClient, $entityManager, $baseUri);
    
    expect(fn() => $service->importCrs())
        ->toThrow(\Exception::class, 'Erreur lors du refresh des CR : Refresh failed');
});

it('handles refresh error for UFs', function () {
    // Mock HTTP client
    $httpClient = $this->createMock(HttpClientInterface::class);
    $response = $this->createMock(ResponseInterface::class);
    
    // Mock response for refresh endpoint with error
    $httpClient->method('request')
        ->willReturnCallback(function ($method, $url, $options = []) use ($response) {
            if ($url === 'http://data-integrator.example.com/ot-uf/refresh') {
                $response->method('toArray')->willReturn([
                    'status' => 'error',
                    'message' => 'Refresh failed'
                ]);
                return $response;
            }
            
            return $response;
        });
    
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $baseUri = 'http://data-integrator.example.com';
    
    $service = new StructureImportService($httpClient, $entityManager, $baseUri);
    
    expect(fn() => $service->importUfs())
        ->toThrow(\Exception::class, 'Erreur lors du refresh des UF : Refresh failed');
});

it('handles missing EntiteJuridique for services', function () {
    // Mock HTTP client
    $httpClient = $this->createMock(HttpClientInterface::class);
    
    // Create two separate response mocks for different endpoints
    $refreshResponse = $this->createMock(ResponseInterface::class);
    $refreshResponse->expects($this->once())
        ->method('toArray')
        ->willReturn(['status' => 'success']);
        
    $dataResponse = $this->createMock(ResponseInterface::class);
    $dataResponse->expects($this->once())
        ->method('toArray')
        ->willReturn([
            'data' => [
                [
                    'etab' => 'CHRU',
                    'seCode' => 'S001',
                    'libelle' => 'Service Anesthésie'
                ]
            ],
            'paginationTotal' => 1,
            'pageSize' => 50,
            'ejCode' => 'EJ001'
        ]);
    
    // Mock request method to return appropriate response for each URL
    $httpClient->expects($this->exactly(2))
        ->method('request')
        ->willReturnCallback(function ($method, $url, $options = []) use ($refreshResponse, $dataResponse) {
            if ($url === 'http://data-integrator.example.com/ot-service/refresh') {
                return $refreshResponse;
            }
            
            if ($url === '/ot-service') {
                return $dataResponse;
            }
            
            throw new \Exception("Unexpected URL: $url");
        });
    
    // Mock entity manager and repositories
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $entiteJuridiqueRepository = $this->createMock(EntityRepository::class);
    
    $entityManager->method('getRepository')
        ->willReturnCallback(function ($entityClass) use ($entiteJuridiqueRepository) {
            if ($entityClass === EntiteJuridique::class) {
                return $entiteJuridiqueRepository;
            }
            return null;
        });
    
    // Mock EntiteJuridique - return null to simulate missing entity
    $entiteJuridiqueRepository->method('findOneBy')
        ->willReturnCallback(function ($criteria) {
            if (isset($criteria['code']) && $criteria['code'] === 'EJ001') {
                return null;
            }
            return $this->createMock(EntiteJuridique::class);
        });
    
    $baseUri = 'http://data-integrator.example.com';
    
    $service = new StructureImportService($httpClient, $entityManager, $baseUri);
    
    expect(fn() => $service->importServices())
        ->toThrow(\Exception::class, 'EntiteJuridique non trouvée pour le Service S001');
});

it('handles missing Pole for CRs', function () {
    // Mock HTTP client
    $httpClient = $this->createMock(HttpClientInterface::class);
    
    // Create two separate response mocks for different endpoints
    $refreshResponse = $this->createMock(ResponseInterface::class);
    $refreshResponse->expects($this->once())
        ->method('toArray')
        ->willReturn(['status' => 'success']);
        
    $dataResponse = $this->createMock(ResponseInterface::class);
    $dataResponse->expects($this->once())
        ->method('toArray')
        ->willReturn([
            'data' => [
                [
                    'etab' => 'CHRU',
                    'crCode' => 'CR001',
                    'libelle' => 'CR Chirurgie',
                    'poleCode' => 'P001'
                ]
            ],
            'paginationTotal' => 1,
            'pageSize' => 50
        ]);
    
    // Mock request method to return appropriate response for each URL
    $httpClient->expects($this->exactly(2))
        ->method('request')
        ->willReturnCallback(function ($method, $url, $options = []) use ($refreshResponse, $dataResponse) {
            if ($url === 'http://data-integrator.example.com/ot-cr/refresh') {
                return $refreshResponse;
            }
            
            if ($url === '/ot-cr') {
                return $dataResponse;
            }
            
            throw new \Exception("Unexpected URL: $url");
        });
    
    // Mock entity manager and repositories
    $entityManager = $this->createMock(EntityManagerInterface::class);
    $poleRepository = $this->createMock(EntityRepository::class);
    $crRepository = $this->createMock(EntityRepository::class);
    
    $entityManager->method('getRepository')
        ->willReturnCallback(function ($entityClass) use ($poleRepository, $crRepository) {
            if ($entityClass === Pole::class) {
                return $poleRepository;
            }
            if ($entityClass === Cr::class) {
                return $crRepository;
            }
            return null;
        });
    
    // Mock Pole - return null to simulate missing pole
    $poleRepository->method('findOneBy')
        ->willReturnCallback(function ($criteria) {
            if (isset($criteria['polecode']) && $criteria['polecode'] === 'P001') {
                return null;
            }
            return $this->createMock(Pole::class);
        });
    
    // Mock CR - return null to simulate new CR
    $crRepository->method('findOneBy')
        ->willReturnCallback(function ($criteria) {
            if (isset($criteria['crcode']) && $criteria['crcode'] === 'CR001') {
                return null;
            }
            return $this->createMock(Cr::class);
        });
    
    $baseUri = 'http://data-integrator.example.com';
    
    $service = new StructureImportService($httpClient, $entityManager, $baseUri);
    
    expect(fn() => $service->importCrs())
        ->toThrow(\Exception::class, 'Pole non trouvé pour le CR CR001');
});