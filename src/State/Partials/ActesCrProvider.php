<?php

namespace App\State\Partials;

use ApiPlatform\Doctrine\Orm\Paginator;
use ApiPlatform\Doctrine\Orm\State\CollectionProvider;
use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\Pagination\TraversablePaginator;
use ApiPlatform\State\ProviderInterface;
use App\Entity\Activite\Actes;
use App\Entity\Structure\Cr;
use App\Repository\ActesRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Contracts\Cache\CacheInterface;
use Symfony\Contracts\Cache\ItemInterface;
use Symfonycasts\MicroMapper\MicroMapperInterface;

/**
 * Provider pour récupérer tous les actes d'un CR (Centre de Responsabilité) spécifique.
 * 
 * Cette classe filtre les actes en fonction de l'UUID du CR fourni dans l'URL.
 * Le chemin de relation utilisé est: Actes -> UF (ufIntervention/ufPrincipal/ufDemande) -> CR
 * 
 * Supporte également le filtrage par périodes via les paramètres de requête:
 * - p1Start/p1End, p2Start/p2End, p3Start/p3End, etc.
 * 
 * Supporte le filtrage par type d'acte via le paramètre de requête:
 * - typeActe=CCAM (ou NGAP, LABO)
 * 
 * Exemple d'appel:
 * /api/actes/cr/{uuid}?p1End=2025-06-30&p1Start=2025-01-01&p2End=2024-06-30&p2Start=2024-01-01&p3End=2023-06-30&p3Start=2023-01-01&typeActe=CCAM
 * 
 * Seuls les actes dont la date de réalisation (date_realisation) est comprise dans l'une des périodes spécifiées 
 * et dont le type correspond au typeActe demandé (si spécifié) seront retournés.
 */
class ActesCrProvider implements ProviderInterface
{
    public function __construct(
        #[Autowire(service: CollectionProvider::class)] private ProviderInterface $collectionProvider,
        private MicroMapperInterface $microMapper,
        private EntityManagerInterface $entityManager,
        private ActesRepository $actesRepository,
        private RequestStack $requestStack,
        private CacheInterface $cache,
        #[Autowire('%env(int:API_CACHE_TTL)%')] private int $apiCacheTtl = 3600
    ) {}

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): object|array|null
    {
        // Vérifier si l'ID du CR est fourni
        if (!isset($uriVariables['id'])) {
            throw new \RuntimeException('CR ID is required');
        }

        $crId = $uriVariables['id'];
        
        // Vérifier si le CR existe
        $cr = $this->entityManager->getRepository(Cr::class)->find($crId);
        if (!$cr) {
            throw new \RuntimeException('CR not found: ' . $crId);
        }

        // Récupérer les paramètres de périodes et le type d'acte de la requête
        $request = $this->requestStack->getCurrentRequest();
        $periods = [];
        $typeActe = null;
        $customItemsPerPage = null;
        
        if ($request) {
            // Récupérer le paramètre typeActe
            $typeActe = $request->query->get('typeActe');
            
            // Récupérer le paramètre itemsPerPage s'il existe
            if ($request->query->has('itemsPerPage')) {
                $itemsPerPageValue = $request->query->get('itemsPerPage');
                if (is_numeric($itemsPerPageValue) && (int)$itemsPerPageValue > 0) {
                    $customItemsPerPage = (int)$itemsPerPageValue;
                }
            }
            
            // Récupérer les périodes
            foreach ($request->query->all() as $key => $value) {
                if (preg_match('/^p\d+Start$/', $key)) {
                    $index = substr($key, 1, -5); // extrait le numéro (ex: '1' dans 'p1Start')
                    $start = $value;
                    $endKey = "p{$index}End";
                    $end = $request->query->get($endKey);
                    if ($end) {
                        $periods[] = [$start, $end];
                    }
                }
            }
        }
        
        // Clé de cache unique pour cette requête
        $resourceClass = $operation->getClass();
        $page = $context['pagination']["page"] ?? 1;
        $perPage = $customItemsPerPage ?? $context['pagination']["items_per_page"] ?? 10;
        $filters = $context['filters'] ?? [];
        ksort($filters);
        $filtersHash = $filters ? md5(json_encode($filters)) : 'no_filters';
        
        // Inclure les périodes dans la clé de cache
        $periodsHash = '';
        if (!empty($periods)) {
            ksort($periods);
            $periodsHash = '_periods_' . md5(json_encode($periods));
        }
        
        // Inclure le type d'acte dans la clé de cache
        $typeActeHash = '';
        if ($typeActe) {
            $typeActeHash = '_typeActe_' . $typeActe;
        }
        
        $cacheKey = sprintf(
            'supra_api_actes_cr_%s_p%d_n%d_%s%s%s',
            $crId,
            $page,
            $perPage,
            $filtersHash,
            $periodsHash,
            $typeActeHash
        );
        $cacheKey = preg_replace('/[^A-Za-z0-9_]/', '_', $cacheKey);

        return $this->cache->get($cacheKey, function (ItemInterface $item) use ($operation, $uriVariables, $context, $resourceClass, $cr, $page, $perPage, $periods, $typeActe) {
            $item->expiresAfter($this->apiCacheTtl);

            // Créer une requête personnalisée pour filtrer les actes par CR
            $qb = $this->actesRepository->createQueryBuilder('a');
            
            // Appliquer le filtre pour récupérer les actes du CR spécifié
            // Nous utilisons OR pour prendre en compte les trois chemins possibles via ufIntervention, ufPrincipal et ufDemande
            $qb->leftJoin('a.ufIntervention', 'ufIntervention')
               ->leftJoin('a.ufPrincipal', 'ufPrincipal')
               ->leftJoin('a.ufDemande', 'ufDemande')
               ->andWhere($qb->expr()->orX(
                   $qb->expr()->eq('ufIntervention.cr', ':cr'),
                   $qb->expr()->eq('ufPrincipal.cr', ':cr'),
                   $qb->expr()->eq('ufDemande.cr', ':cr')
               ))
               ->setParameter('cr', $cr);
               
            // Appliquer le filtre par type d'acte si spécifié
            if ($typeActe) {
                $qb->andWhere('a.typeActe = :typeActe')
                   ->setParameter('typeActe', $typeActe);
            }
               
            // Appliquer le filtre par périodes si des périodes sont spécifiées
            if (!empty($periods)) {
                $orX = $qb->expr()->orX();
                $i = 0;
                
                foreach ($periods as [$start, $end]) {
                    $paramStart = "period_start_$i";
                    $paramEnd = "period_end_$i";
                    
                    // Pour chaque période, on ajoute la condition :
                    // date_realisation >= periodeStart AND date_realisation <= periodeEnd
                    $orX->add(
                        $qb->expr()->andX(
                            $qb->expr()->gte('a.date_realisation', ":$paramStart"),
                            $qb->expr()->lte('a.date_realisation', ":$paramEnd")
                        )
                    );
                    
                    $qb->setParameter($paramStart, $start);
                    $qb->setParameter($paramEnd, $end);
                    $i++;
                }
                
                // Ajouter la condition globale au QueryBuilder (WHERE (... OR ... OR ...))
                $qb->andWhere($orX);
            }
            
            // Appliquer la pagination
            $qb->setFirstResult(($page - 1) * $perPage)
               ->setMaxResults($perPage);
            
            // Exécuter la requête
            $query = $qb->getQuery();
            $entities = $query->getResult();
            
            // Compter le nombre total d'éléments pour la pagination
            $countQb = clone $qb;
            $countQb->select('COUNT(a.id)')
                   ->setFirstResult(null)
                   ->setMaxResults(null);
            $totalItems = (int) $countQb->getQuery()->getSingleScalarResult();
            
            // Mapper les entités en DTOs
            $dtos = [];
            foreach ($entities as $entity) {
                $dtos[] = $this->microMapper->map($entity, $resourceClass);
            }
            
            // Retourner un TraversablePaginator pour la pagination
            return new TraversablePaginator(
                new \ArrayIterator($dtos),
                $page,
                $perPage,
                $totalItems
            );
        });
    }
}