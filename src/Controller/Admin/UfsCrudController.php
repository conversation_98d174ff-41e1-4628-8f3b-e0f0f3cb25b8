<?php

namespace App\Controller\Admin;

use App\Entity\Structure\Ufs;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\BooleanField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateTimeField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ChoiceField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;

class UfsCrudController extends AbstractCrudController
{
    public static function getEntityFqcn(): string
    {
        return Ufs::class;
    }

    public function configureActions(Actions $actions): Actions
    {
        $importCsv = Action::new('importCsv', '📥 Importer CSV')
            ->linkToRoute('admin_import_structures_csv', ['entity' => $this->getEntityFqcn()])
            ->setCssClass('btn btn-primary');

        return $actions
            ->add(Crud::PAGE_NEW, $importCsv)
            ->add(Crud::PAGE_INDEX, Action::DETAIL);
    }

    public function configureFields(string $pageName): iterable
    {
        $fields = [
            TextField::new('id')->hideOnForm(),

            TextField::new('etab')
                ->setLabel('Établissement')
                ->setHelp('Code de l\'établissement (max 3 caractères)')
                ->setFormTypeOption('attr', ['maxlength' => 3])
                ->hideOnIndex(),

            TextField::new('ufcode')
                ->setLabel('Code UF')
                ->setHelp('Code unique de l\'UF (max 4 caractères)')
                ->setFormTypeOption('attr', ['maxlength' => 4]),

            TextField::new('libelle')
                ->setLabel('Libellé de l\'UF')
                ->setHelp('Nom de l\'Unité Fonctionnelle (max 25 caractères)')
                ->setFormTypeOption('attr', ['maxlength' => 25]),

            DateTimeField::new('datdeb')
                ->setLabel('Date de début')
                ->setFormat('d/M/Y')
                ->hideOnIndex(),

            DateTimeField::new('datfin')
                ->setLabel('Date de fin')
                ->setFormat('d/M/Y'),

            DateTimeField::new('datclos')
                ->setLabel('Date de clôture')
                ->setFormat('d/M/Y')
                ->hideOnIndex(),

            TextField::new('tacode')
                ->setLabel('Code TA')
                ->setHelp('Code TA de l\'UF (max 2 caractères)')
                ->setFormTypeOption('attr', ['maxlength' => 2])
                ->hideOnIndex(),

            TextField::new('cdcode')
                ->setLabel('Code CD')
                ->setHelp('Code CD de l\'UF (max 3 caractères)')
                ->setFormTypeOption('attr', ['maxlength' => 3])
                ->hideOnIndex(),

            TextField::new('lettrebudg')
                ->setLabel('Lettre budgétaire')
                ->setHelp('Lettre budgétaire de l\'UF (max 10 caractères)')
                ->setFormTypeOption('attr', ['maxlength' => 10])
                ->hideOnIndex(),

            TextField::new('secode')
                ->setLabel('Code SE')
                ->setHelp('Code SE (Service) de l\'UF (max 10 caractères)')
                ->setFormTypeOption('attr', ['maxlength' => 10])
                ->hideOnIndex(),

            TextField::new('cgcode')
                ->setLabel('Code CG')
                ->setHelp('Code CG de l\'UF (max 10 caractères)')
                ->setFormTypeOption('attr', ['maxlength' => 10])
                ->hideOnIndex(),

            TextField::new('umcode')
                ->setLabel('Code UM')
                ->setHelp('Code UM de l\'UF (max 10 caractères)')
                ->setFormTypeOption('attr', ['maxlength' => 10])
                ->hideOnIndex(),

            TextField::new('pfuser')
                ->setLabel('Utilisateur PF')
                ->setHelp('Ex: Utilisateur ayant créé/modifié l\'enregistrement. Vous pouvez utiliser ce champ pour garder une information pertinente pour l\'UF. (max 8 caractères)')
                ->setFormTypeOption('attr', ['maxlength' => 8])
                ->hideOnIndex(),

            DateTimeField::new('dmdacre')
                ->setLabel('Date de création')
                ->setFormat('d/M/Y')
                ->setFormTypeOption('mapped', false)
                ->setValue(new \DateTime('now'))
                ->hideOnForm()
                ->hideOnIndex(),

            TextField::new('topmedical')
                ->setLabel('Top médical')
                ->setHelp('Indicateur médical (max 1 caractère)')
                ->setFormTypeOption('attr', ['maxlength' => 1])
                ->hideOnIndex(),

            TextField::new('crcode')
                ->setLabel('Code CR')
                ->setHelp('Code CR (Centre de Responsabilité) de l\'UF (max 10 caractères)')
                ->setFormTypeOption('attr', ['maxlength' => 10])
                ->hideOnIndex(),

            TextField::new('sacode')
                ->setLabel('Code SA')
                ->setHelp('Code SA de l\'UF (max 10 caractères)')
                ->setFormTypeOption('attr', ['maxlength' => 10])
                ->hideOnIndex(),

            BooleanField::new('is_actif')
                ->setLabel('Est actif')
                ->hideOnIndex(),

            ChoiceField::new('periodeType')
                ->setLabel('Type de période')
                ->setChoices([
                    'Hebdomadaire' => 'HEBDOMADAIRE',
                    'Mensuel' => 'MENSUEL',
                    'Annuel' => 'ANNUEL',
                ])
                ->hideOnIndex(),

            TextField::new('source')
                ->setLabel('Source')
                ->setHelp('Origine de la donnée')
                ->hideOnIndex(),

            AssociationField::new('cr')
                ->setLabel('Centre de Responsabilité')
                ->setHelp('Centre de Responsabilité auquel cette UF est rattachée'),

            AssociationField::new('service')
                ->setLabel('Service')
                ->setHelp('Service auquel cette UF est rattachée'),

            AssociationField::new('praticiensUfs')
                ->setLabel('Praticiens affectés')
                ->setHelp('Praticiens liés à cette UF')
                ->setFormTypeOption('by_reference', false)
                ->hideOnIndex(),

        ];
        
        // Add dmdamaj field only for edit and detail pages
        if ($pageName !== Crud::PAGE_NEW) {
            $fields[] = DateTimeField::new('dmdamaj')
                ->setLabel('Date de mise à jour')
                ->setFormat('d/M/Y')
                ->setValue(new \DateTime('now'))
                ->hideOnIndex();
        }
        
        return $fields;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setEntityLabelInSingular('Unité Fonctionnelle')
            ->setEntityLabelInPlural('Unités Fonctionnelles')
            ->setPageTitle('index', 'Gestion des Unités Fonctionnelles')
            ->setPageTitle('detail', 'Détails de l\'Unité Fonctionnelle')
            ->setPageTitle('new', 'Créer une nouvelle Unité Fonctionnelle')
            ->setPageTitle('edit', 'Modifier l\'Unité Fonctionnelle')
            ->setHelp('index', '💡 <strong>Astuce :</strong> Cliquez sur les trois points <code>...</code> puis sur <strong>👁️ Voir</strong> pour afficher tous les détails d\'une unité fonctionnelle.')
            ->showEntityActionsInlined();
    }
    
    /**
     * Custom logic to handle entity creation
     * 
     * Cette méthode génère automatiquement les champs validFrom et validTo
     * à partir des champs datdeb et datfin lors de la création d'une unité fonctionnelle.
     */
    public function persistEntity(\Doctrine\ORM\EntityManagerInterface $entityManager, $entityInstance): void
    {
        if ($entityInstance instanceof \App\Entity\Structure\Ufs) {
            // Set dmdacre (date de création) to current date if not already set
            if ($entityInstance->getDmdacre() === null) {
                $entityInstance->setDmdacre(new \DateTime('now'));
            }
            
            // Generate validFrom from datdeb
            if ($entityInstance->getDatdeb() !== null) {
                $entityInstance->setValidFrom($entityInstance->getDatdeb());
            }
            
            // Generate validTo from datfin
            if ($entityInstance->getDatfin() !== null) {
                $entityInstance->setValidTo($entityInstance->getDatfin());
            }
        }
        
        // Call parent method to persist the entity
        parent::persistEntity($entityManager, $entityInstance);
    }
    
    /**
     * Custom logic to handle entity updates
     * 
     * Cette méthode met à jour les champs validFrom et validTo
     * à partir des champs datdeb et datfin lors de la modification d'une unité fonctionnelle.
     */
    public function updateEntity(\Doctrine\ORM\EntityManagerInterface $entityManager, $entityInstance): void
    {
        if ($entityInstance instanceof \App\Entity\Structure\Ufs) {
            // Generate validFrom from datdeb
            if ($entityInstance->getDatdeb() !== null) {
                $entityInstance->setValidFrom($entityInstance->getDatdeb());
            }
            
            // Generate validTo from datfin
            if ($entityInstance->getDatfin() !== null) {
                $entityInstance->setValidTo($entityInstance->getDatfin());
            }
        }
        
        // Call parent method to update the entity
        parent::updateEntity($entityManager, $entityInstance);
    }
}
