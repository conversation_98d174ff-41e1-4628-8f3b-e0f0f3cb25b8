<?php

namespace App\Controller\Admin;

use App\Entity\Praticien\AgentUfs;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\BooleanField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ChoiceField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateTimeField;
use EasyCorp\Bundle\EasyAdminBundle\Field\IdField;
use EasyCorp\Bundle\EasyAdminBundle\Field\NumberField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use App\Domain\Enum\PeriodeType;

class AgentUfsCrudController extends AbstractCrudController
{
    public static function getEntityFqcn(): string
    {
        return AgentUfs::class;
    }

    public function configureActions(Actions $actions): Actions
    {
        return $actions
            ->add(Crud::PAGE_INDEX, Action::DETAIL);
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setEntityLabelInSingular('Affectation Agent-UF')
            ->setEntityLabelInPlural('Affectations Agents-UFs')
            ->setPageTitle('index', 'Liste des affectations d\'agents aux UFs')
            ->setPageTitle('detail', 'Détail de l\'affectation d\'un agent à une UF')
            ->setPageTitle('new', 'Nouvelle affectation d\'un agent à une UF')
            ->setPageTitle('edit', 'Modifier l\'affectation d\'un agent à une UF')
            ->setHelp('index', '💡 <strong>Information :</strong> Ce module permet de gérer les affectations des agents aux Unités Fonctionnelles (UFs). Un agent peut avoir plusieurs affectations dans des UFs différentes pour la même période.')
            ->showEntityActionsInlined();
    }

    public function configureFields(string $pageName): iterable
    {
        $fields = [];

        // Champs principaux pour la liste (index)
        if ($pageName === Crud::PAGE_INDEX) {
            $fields[] = IdField::new('id')->hideOnForm();
            $fields[] = AssociationField::new('ufs')->setLabel('UF');
            $fields[] = AssociationField::new('agent')->setLabel('Agent');
            $fields[] = DateTimeField::new('date_debut')->setLabel('Début');
            $fields[] = DateTimeField::new('date_fin')->setLabel('Fin');
            $fields[] = BooleanField::new('isActif')->setLabel('Actif');
        }

        // Tous les champs pour la vue détail (show)
        if ($pageName === Crud::PAGE_DETAIL || $pageName === Crud::PAGE_EDIT || $pageName === Crud::PAGE_NEW) {
            $fields[] = IdField::new('id')->hideOnForm();
            $fields[] = AssociationField::new('ufs')->setLabel('UF');
            $fields[] = AssociationField::new('agent')->setLabel('Agent');
            $fields[] = DateTimeField::new('date_debut')->setLabel('Début');
            $fields[] = DateTimeField::new('date_fin')->setLabel('Fin');
            $fields[] = BooleanField::new('isActif')->setLabel('Actif');
            $fields[] = NumberField::new('sommeEtp')
                ->setNumDecimals(9)
                ->setLabel('Somme ETP')
                ->setHelp('Exemple : 0.123456789 (max : 1)')
                ->setFormTypeOption('attr', [
                    'step' => '0.000000001',
                    'max' => '1',
                    'min' => '0',
                    'placeholder' => '0.123456789'
                ]);
            $fields[] = ChoiceField::new('rgt')
                ->setChoices([
                    'PERMANENT' => 'PERMANENT',
                    'TEMPORAIRES' => 'TEMPORAIRES',
                    'INTERNE' => 'INTERNE',
                    'ETUDIANTS' => 'ETUDIANTS',
                    'INTERNE-ETUDIANT' => 'INTERNE-ETUDIANT'
                ])
                ->setFormTypeOption('attr.data-ea-widget', 'ea-autocomplete')
                ->setFormTypeOption('attr.data-ea-autocomplete-allow-item-create', 'true')
                ->setHelp('Type de régime de travail. Vous pouvez également saisir une valeur personnalisée.');

            $fields[] = NumberField::new('etpStatutaire')->setNumDecimals(2);
            $fields[] = NumberField::new('tauxAffectation');
            $fields[] = NumberField::new('etp')->setNumDecimals(2);
            $fields[] = ChoiceField::new('affectationPrincipale')->setChoices([
                'Oui' => 'O',
                'Non' => 'N'
            ]);
            $fields[] = ChoiceField::new('typeGrade')->setChoices([
                'A' => 'A',
                'M' => 'M',
            ]);
            $fields[] = TextField::new('libelleGrade');
            $fields[] = NumberField::new('absences');
            
            // Add validFrom and validTo fields to all pages but hide them on forms
            $fields[] = DateTimeField::new('validFrom')->hideOnForm();
            $fields[] = DateTimeField::new('validTo')->hideOnForm();
            
            $fields[] = ChoiceField::new('periodeType')
                ->setChoices(PeriodeType::choices())
                ->setHelp('Type de période à laquelle cette affectation est amenée à changer (Hebdomadaire, Mensuel, Annuel).');
            $fields[] = TextField::new('source');
            $fields[] = DateTimeField::new('dateCreation')->setLabel('Date de création')->setDisabled(true);
        }

        return $fields;
    }
    
    /**
     * Custom logic to handle entity creation
     * 
     * Cette méthode génère automatiquement les champs validFrom et validTo
     * à partir des champs date_debut et date_fin lors de la création d'une affectation UF.
     */
    public function persistEntity(\Doctrine\ORM\EntityManagerInterface $entityManager, $entityInstance): void
    {
        if ($entityInstance instanceof \App\Entity\Praticien\AgentUfs) {
            // Validate sommeEtp value
            $sommeEtp = $entityInstance->getSommeEtp();
            if ($sommeEtp !== null && $sommeEtp > 1) {
                throw new \InvalidArgumentException('La valeur de Somme ETP doit être inférieure ou égale à 1. Valeur actuelle: ' . $sommeEtp);
            }
            
            // Set dateCreation to current date if not already set
            if ($entityInstance->getDateCreation() === null) {
                $entityInstance->setDateCreation(new \DateTime('now'));
            }
            
            // Generate validFrom from date_debut
            if ($entityInstance->getDateDebut() !== null) {
                $entityInstance->setValidFrom($entityInstance->getDateDebut());
            }
            
            // Generate validTo from date_fin
            if ($entityInstance->getDateFin() !== null) {
                $entityInstance->setValidTo($entityInstance->getDateFin());
            }
        }
        
        // Call parent method to persist the entity
        parent::persistEntity($entityManager, $entityInstance);
    }
    
    /**
     * Custom logic to handle entity updates
     * 
     * Cette méthode met à jour les champs validFrom et validTo
     * à partir des champs date_debut et date_fin lors de la modification d'une affectation UF.
     */
    public function updateEntity(\Doctrine\ORM\EntityManagerInterface $entityManager, $entityInstance): void
    {
        if ($entityInstance instanceof \App\Entity\Praticien\AgentUfs) {
            // Validate sommeEtp value
            $sommeEtp = $entityInstance->getSommeEtp();
            if ($sommeEtp !== null && $sommeEtp > 1) {
                throw new \InvalidArgumentException('La valeur de Somme ETP doit être inférieure ou égale à 1. Valeur actuelle: ' . $sommeEtp);
            }
            
            // Generate validFrom from date_debut
            if ($entityInstance->getDateDebut() !== null) {
                $entityInstance->setValidFrom($entityInstance->getDateDebut());
            }
            
            // Generate validTo from date_fin
            if ($entityInstance->getDateFin() !== null) {
                $entityInstance->setValidTo($entityInstance->getDateFin());
            }
        }
        
        // Call parent method to update the entity
        parent::updateEntity($entityManager, $entityInstance);
    }
}