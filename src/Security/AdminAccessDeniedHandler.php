<?php

namespace App\Security;

use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
use Symfony\Component\Security\Http\Authorization\AccessDeniedHandlerInterface;
use Symfony\Component\Security\Http\SecurityRequestAttributes;

/**
 * Custom access denied handler for the admin area
 * 
 * This handler redirects users back to the login page with an appropriate error message
 * when they try to access the admin area without the required roles.
 */
class AdminAccessDeniedHandler implements AccessDeniedHandlerInterface
{
    public function __construct(
        private UrlGeneratorInterface $urlGenerator
    ) {
    }

    public function handle(Request $request, AccessDeniedException $accessDeniedException): ?Response
    {
        // Only handle requests to the admin area
        if (!str_contains($request->getPathInfo(), '/manager/dtnib/admin')) {
            return null;
        }

        // Set a custom error message in the session
        $request->getSession()->set(
            SecurityRequestAttributes::AUTHENTICATION_ERROR,
            new \App\Domain\Exception\Security\InsufficientRoleException()
        );

        // Redirect to the login page
        return new RedirectResponse($this->urlGenerator->generate('app_login'));
    }
}